const Bot = require("../Models/Bot");
const User = require("../Models/User");

const getActionType = (requestType) => {
  const action_type =
    requestType?.toLowerCase() === "put"
      ? "UPDATE"
      : requestType?.toLowerCase() === "post"
      ? "CREATE"
      : requestType;
  return action_type;
};
const getBotLog = (user, action_type, category, names, bot) => {
  return {
    log: names || '',
    category: category,
    action_type: action_type,
  };
};
const getBot = async (req, botId) => {
  const bot_id =
    botId ||
    req?.body?.bot_id ||
    req?.query?.bot_id ||
    req?.body[0]?.bot_id ||
    req?.bot?.bot_id;
  const bot = await Bot.findOne({ where: { bot_id, deleted: 0 } }).then(
    (bot) => {
      if (bot) {
        return bot?.dataValues || bot;
      } else {
        return res.sendStatus(404);
      }
    }
  );

  const user_id = +req?.user?.user_id || bot?.user_id || null;
  const user = await User.findOne({ where: { user_id } }).then((user) => {
    if (user) {
      return user?.dataValues || user;
    } else {
      return res.sendStatus(404);
    }
  });

  return { bot, user };
};
module.exports = {
  getActionType,
  getBot,
  getBotLog,
};
