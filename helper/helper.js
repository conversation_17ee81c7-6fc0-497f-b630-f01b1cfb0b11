const serverURL = "https://farasa.qcri.org/analyze/";
const axios = require("axios")
const FormData = require("form-data");

const Call = async (data) => {
    const config = {
        headers: {
          "content-type": "multipart/form-data",
        },
      };
      return axios.post(
        serverURL,
        data,
        config
      );
};


const lemmatization = async (text) => {
    var form = new FormData();
    form.append('text', text);
    form.append('task', "lemmatization");
    form.append('API_KEY', "lpcsTkDIDf");
    const answer = await Call(form);
    return answer;
}
const distinct = (arr, by1) => 
  arr.reduce((acc, current) => {
    const x = acc.find((item) => item[by1] === current[by1]);
    if (!x) {
      return acc.concat([current]);
    } else {
      return acc;
    }
}, []);

const lemmtaizationLocal = async (text) => {
  let info = {
    "text":text
  }
  const config = {
    headers: {
      "content-type": "application/json",
    },
  };
  return axios.post(
    "http://i2i-text-processor.acdufqcxgubzbwcp.uaenorth.azurecontainer.io:8080/api/lemmatization",
    info,
    config
  );
};

module.exports = {
    lemmatization,
    distinct,
    lemmtaizationLocal
}