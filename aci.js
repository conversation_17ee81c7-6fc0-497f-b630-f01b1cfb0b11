//.matchAll(/(?:<span[\s\S]*?<\/span>|<div[\s\S]*?<\/div>)/gm)
//const Data = require("./vimeo.json");
const Data = require("./aci.json");
const fs = require("fs");
const tatum = [];
const vemo = [];

Data.map((sku) => {
  vemo.push({
    id: sku.ID,
    item_name: sku.Name,
    item_description: sku.Description,
    text: sku.Description,
    item_price: sku["Regular price"],
    item_qty: sku["In stock"],
    category_name: sku.Categories,
  });
});

vemo.map((sku) => {
  tatum.push({
    ...sku,
    item_description: [
      ...sku.text.matchAll(/(?:<span[\s\S]*?<\/span>|<div[\s\S]*?<\/div>)/gm),
    ]
      .map((a) => {
        return a[0]
          .replace(new RegExp("<[^>]*>", "gm"), "")
          .replace(new RegExp("\n", "gm"), "")
          .replace(new RegExp("\t", "gm"), "");
      })
      .join(" ")
      .slice(0, 499),
    text: undefined,
  });
});

fs.writeFile("./vimeo_1_1.json", JSON.stringify(tatum), "utf8", (err) => {});
