const health_center = require("./health_centers.json")
const sequelize = require("./db");


const  createDataHealth =()=>{
const keys = Object.keys(health_center[0]);
let yourDate = new Date()
const offset = yourDate.getTimezoneOffset()
yourDate = new Date(yourDate.getTime() - (offset*60*1000))

health_center.map((row)=>{
  var sql = "INSERT INTO " + "bot_designer_customs_HealthCenters" + "" + `(${keys})` + "VALUES" + "(";
  Object.keys(row).map((key,i)=>{
    if(row[key]){
            sql = sql +  "N'" +  row[key]+ "'"
    }
    else{
       if(key === "createdAt" || key === "updatedAt"){
        sql = sql +  "'" +  yourDate.toISOString().split('T')[0] + "'"
      }
      else if(key === "bot_id"){
        sql = sql + 7 
      }
      else if(key === "from_time" || key === "to_time"){
        sql = sql + 0 
      }
      else{
      sql = sql +  "'" + "." + "'"
      }
    }
    if(i+1 !== keys.length ){
    sql = sql + ","
    }
  })
  sql ="SET IDENTITY_INSERT" + " " + "bot_designer_customs_HealthCenters" + " " + "OFF" + ";" + "  " + sql + ")"
   sequelize.query(sql)
})
}
createDataHealth()
