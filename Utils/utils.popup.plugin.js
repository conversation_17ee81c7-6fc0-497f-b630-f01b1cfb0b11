const PopupPlugin = require("../Models/PopupPlugin");

const findPopupPluginByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  req.bot_id = bot_id;
  PopupPlugin.findOne({ where: { bot_id } }).then((popupPlugin) => {
    // if (botvoice) {
    req.popupPlugin = popupPlugin;
    return next();
    // } else {
    //   return res.sendStatus(404);
    // }
  });
};

const checkExistance = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  PopupPlugin.findOne({ where: { bot_id } }).then((popupPlugin) => {
    if (popupPlugin) {
      req.popupPlugin = popupPlugin;
      return res.status(409).send({ message: "Popup Plugin already exits" });
    } else {
      return next();
    }
  });
};

module.exports = { findPopupPluginByBotId, checkExistance };
