const SanadTrigger = require("../Models/Sanad.Trigger");
const sequelize = require("../db");
const { Op } = require("sequelize");
const { lemmtaizationLocal } = require("../helper/helper");

const findTriggerById = (req, res, next) => {
  const sanad_trigger_id = req.body.sanad_trigger_id || req.query.sanad_trigger_id;
  SanadTrigger.findOne({ where: { sanad_trigger_id } }).then((trigger) => {
    if (trigger) {
      req.trigger = trigger;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findTriggerByQueryId = (req, res, next) => {
  const sanad_trigger_id = req.body.sanad_trigger_id || req.query.sanad_trigger_id;
  req.sanad_trigger_id = sanad_trigger_id;
  SanadTrigger.findOne({ where: { sanad_trigger_id } }).then((trigger) => {
    if (trigger) {
      req.trigger = trigger;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findManyTriggers = (req, res, next) => {
  const data = req.body;
  SanadTrigger.findAll({
    where: {
      sanad_trigger_id: { [Op.in]: data.map((a) => a.sanad_trigger_id) },
    },
  }).then((triggers) => {
    req.triggers = triggers;
    return next();
  });
};

const findBotTriggersByName = (req, res, next) => {
  const trigger_name = req.query.trigger_name;
  req.trigger_name = trigger_name;
  SanadTrigger.findAll({ where: { trigger_name } }).then((triggers) => {
    req.triggers = triggers;
    return next();
  });
};

const findBotTriggersById = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id || req.body[0].bot_id;
  req.bot_id = bot_id;
  SanadTrigger.findAll({ where: { bot_id } }).then((triggers) => {
    req.triggers = triggers;
    return next();
  });
};

const findBotTriggersByQueryId = (req, res, next) => {
  const bot_id = req.query.bot_id;
  req.bot_id = bot_id;
  SanadTrigger.findAll({ where: { bot_id } }).then((triggers) => {
    req.triggers = triggers;
    return next();
  });
};

const findBotTriggersFirst = (req, res, next) => {
  const bot_id = req.query.bot_id;
  req.bot_id = bot_id;
  sequelize
    .query("designer_sanad_triggers_first :botID", {
      replacements: {
        botID: bot_id,
      },
    })
    .then((data) => {
      req.triggers = data[0];
      return next();
    });
};


const findBotTriggersAlternative = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const trigger = req.query.trigger;
  const url = req.query.url;
  req.bot_id = bot_id;
  sequelize
    .query("designer_sanad_triggers_alternative :botID , :userTrigger , :userUrl", {
      replacements: {
        botID: bot_id,
        userUrl: url,
        userTrigger: trigger,
      },
    })
    .then((data) => {
      req.triggers = data[0];
      return next();
    });
};

const findNotApprovedTriggers = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id || req.body[0].bot_id;
  sequelize
    .query("designer_not_approved_sanad_triggers :botID", {
      replacements: { botID: bot_id },
    })
    .then((data) => {
      req.triggers = data[0];
      return next();
    });
};

const findApprovedTriggers = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id || req.body[0].bot_id;
  sequelize
    .query("designer_approved_sanad_triggers :botID", {
      replacements: { botID: bot_id },
    })
    .then((data) => {
      req.triggers = data[0];
      return next();
    });
};

const lemmtaizationMany = async (req, res, next) => {
  try {
    const data = [...req.body];
    const newData = await Promise.all(
      data.map(async (ent) => {
        return {
          ...ent,
          lemmatized_trigger: ent?.lemmatized_trigger
            ? (await lemmtaizationLocal(ent?.lemmatized_trigger))?.data?.answer ||
              ent.question
            : "",
        };
      })
    );
    req.body = newData;
    next();
  } catch (error) {
    console.log(error)
  }
};
const lemmtaizationOne = async (req, res, next) => {
  try {
    const data = { ...req.body };

    const newData = {
      ...data,
      lemmatized_trigger: data?.lemmatized_trigger
        ? (await lemmtaizationLocal(data?.lemmatized_trigger))?.data?.answer ||
          data.question
        : "",
    };

    req.body = newData;

    next();
  } catch (error) {
    console.log(error);
  }
};


module.exports = {
  findTriggerById,
  findTriggerByQueryId,
  findBotTriggersById,
  findBotTriggersByQueryId,
  findBotTriggersByName,
  findManyTriggers,
  findBotTriggersFirst,
  findBotTriggersAlternative,
  findNotApprovedTriggers,
  findApprovedTriggers,
  lemmtaizationMany,
  lemmtaizationOne
};
