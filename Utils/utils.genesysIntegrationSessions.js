const sequelize = require("sequelize");
const GenesysIntegration = require("../Models/GenesysIntegration");
const GenesysIntegrationSession = require("../Models/GenesysIntegrationSession");


const findAllGenesysIntegrations = (req, res, next) => {
    GenesysIntegration.findAll().then((genesysIntegration) => {
    if (genesysIntegration) {
      req.genesysIntegration = genesysIntegration;
      return next();
    } else {
      return res.status(404).send({ message: "Genesys Integrations does not exist" });
    }
  });
};


const findGenesysIntegrationByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  GenesysIntegration.findOne({ where: { bot_id } }).then((genesysIntegration) => {
    if (genesysIntegration) {
      req.genesysIntegration = genesysIntegration;
      return next();
    } else {
      return res.status(404).send({ message: "Genesys Integration does not exist" });
    }
  });
};

const findGenesysIntegrationOneSession = (req, res, next) => {
  const conversation_id = req.body.conversation_id || req.query.conversation_id;
  const chat_id = req.body.chat_id || req.query.chat_id;
  if(conversation_id || chat_id){
      GenesysIntegrationSession.findOne(
          conversation_id ?   { where: { conversation_id } } :  { where: { chat_id } } 
        ).then((genesysIntegrationSession) => {
          if (genesysIntegrationSession) {
            req.genesysIntegrationSession = genesysIntegrationSession;
            return next();
          } else {
            return res.status(404).send({ message: "genesys Integration Session does not exist" });
          }
        });
  }
  else{
      return res.status(422).send({ message:  "please provide chat_id or conversation_id" });
  }
};

const preventDuplicateGenesysIntegration = async (req, res, next) => {
  const {body } = req; 
  const { bot_id, ...rest } = body;
  try {
    for (const key in rest) {
      if (rest.hasOwnProperty(key)) {
        const value = rest[key];

        const existingRecord = await GenesysIntegration.findOne({
          where: { [key]: value, bot_id: { [sequelize.Op.ne]: body.bot_id } },
        });

        if (existingRecord) {
          return res
            .status(400)
            .json({ message: `${key} is used in another bot, Please don't use same account for different bots` });
        }
      }
    }
    next();
  } catch (error) {
    res.status(500).json({ message: "Internal Server Error" + error });
  }
};


module.exports = {
    findGenesysIntegrationByBotId,
    findGenesysIntegrationOneSession,
    findAllGenesysIntegrations,
    preventDuplicateGenesysIntegration
};
