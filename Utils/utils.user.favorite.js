const UserFavorite = require("../Models/User.Favorite");

const checkExistance = (req, res, next) => {
  const user_id = req.body.user_id;
  const item_id = req.body.item_id;
  UserFavorite.findOne({ where: { user_id, item_id } }).then((userfavorite) => {
    if (!userfavorite) {
      return next();
    }
    return res.status(409).send({ message: "favorite already exists" });
  });
};

const findByUserId = (req, res, next) => {
  const user_id = req.body.user_id || +req.query.user_id;
  UserFavorite.findAll({ where: { user_id } }).then((userfavorites) => {
    req.userfavorites = userfavorites;
    return next();
  });
};

const findOne = (req, res, next) => {
  const user_id = req.body.user_id;
  const item_id = req.body.item_id;
  UserFavorite.findOne({ where: { user_id, item_id } }).then((userfavorite) => {
    if (!userfavorite) {
      return res.status(409).send({ message: "favorite already exists" });
    }
    req.userfavorite = userfavorite;
    return next();
  });
};

module.exports = {
  checkExistance,
  findByUserId,
  findOne,
};
