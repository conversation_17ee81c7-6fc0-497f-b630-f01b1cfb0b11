const Plan = require("../Models/Plan");

const findPlans = (req, res, next) => {
  Plan.findAll({ order: [["plan_order", "ASC"]] }).then((plans) => {
    req.plans = plans;
    return next();
  });
};

const findPlanById = (req, res, next) => {
  const plan_id = req.body.plan_id;
  Plan.findOne({ where: { plan_id } }).then((plan) => {
    req.plan = plan;
    return next();
  });
};

module.exports = {
  findPlans,
  findPlanById,
};
