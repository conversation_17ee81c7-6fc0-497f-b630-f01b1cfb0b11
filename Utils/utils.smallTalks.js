const SmallTalk = require("../Models/SmallTalk");

const sequelize = require("../db");

const findSmallTalkById = (req, res, next) => {
  const small_talk_id = req.body.small_talk_id || req.query.small_talk_id;
  SmallTalk.findOne({ where: { small_talk_id } }).then((smallTalk) => {
    if (smallTalk) {
      req.smallTalk = smallTalk;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findSmallTalksByBotId = (req, res, next) => {
    const bot_id = req.body.bot_id || req.query.bot_id || req.body[0].bot_id;
    SmallTalk.findAll({ where: { bot_id } }).then((smallTalks) => {
        if (smallTalks) {
            req.smallTalks = smallTalks;
            return next();
        } else {
            return res.sendStatus(404);
        }
    });
};

module.exports = {
    findSmallTalkById,
    findSmallTalksByBotId,
};
