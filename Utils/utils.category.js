const Category = require("../Models/Category");
const sequelize = require("../db");
const { Op } = require("sequelize");
const { lemmtaizationLocal } = require("../helper/helper");

const findCategoryById = (req, res, next) => {
  const category_id = req.body.category_id;
  req.category_id = category_id;
  Category.findOne({ where: { category_id } }).then((category) => {
    if (category) {
      req.category = category;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findBotCategoriesByQueryId = (req, res, next) => {
  const step = 9;
  const bot_id = req.query.bot_id;
  const category_ids = req.query.category_ids
    ? req.query.category_ids.split(",")
    : undefined;
  const limit = +req.query.limit;
  const slice = +req.query.slice || 1;
  const no_load_more = req.query.no_load_more;
  req.bot_id = bot_id;
  Category.findAll({
    limit: !no_load_more ? (limit ? limit : step) : 10,
    offset: !no_load_more ? (slice - 1) * step : 0,
    where: {
      bot_id,
      category_id: category_ids
        ? { [Op.in]: category_ids }
        : { [Op.between]: [1, 1000000000000] },
    },
  }).then((categories) => {
    req.categories = categories;
    return next();
  });
};

const findBotCategoriesByNames = (req, res, next) => {
  const items = req.body.items;
  Category.findAll({
    where: {
      bot_id: req.body.bot_id,
      category_name: {
        [Op.in]: items.map((a) => a.category_name.toLowerCase()),
      },
    },
  }).then((categories) => {
    req.categories = categories;
    return next();
  });
};

const lemmtaizationOne = async (req, res, next) => {
  try {
    const data = { ...req.body };

    const newData = {
      ...data,
      lemmatized_category_name: data?.lemmatized_category_name
        ? (await lemmtaizationLocal(data?.lemmatized_category_name))?.data
            ?.answer || data.question
        : "",
    };

    req.body = newData;

    next();
  } catch (error) {
    console.log(error);
  }
};

const lemmtaizationMany = async (req, res, next) => {
  try {
    const data = [...req.body.categories];
    const newData = await Promise.all(
      data.map(async (ent) => {
        return {
          ...ent,
          lemmatized_category_name: ent?.lemmatized_category_name
            ? (await lemmtaizationLocal(ent?.lemmatized_category_name))?.data
                ?.answer || ent.question
            : "",
        };
      })
    );
    req.body.categories = newData;
    next();
  } catch (error) {
    console.log(error);
    next()
  }
};

const findAllBotCategoriesByQueryId = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const category_ids = req.query.category_ids
    ? req.query.category_ids.split(",")
    : undefined;
  req.bot_id = bot_id;
  Category.findAll({
    where: {
      bot_id,
      category_id: category_ids
        ? { [Op.in]: category_ids }
        : { [Op.between]: [1, 1000000000000] },
    },
  }).then((categories) => {
    req.categories = categories;
    return next();
  });
};

module.exports = {
  findCategoryById,
  findBotCategoriesByQueryId,
  findBotCategoriesByNames,
  lemmtaizationOne,
  lemmtaizationMany,
  findAllBotCategoriesByQueryId,
};
