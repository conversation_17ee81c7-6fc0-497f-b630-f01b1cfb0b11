const SanadService = require("../Models/Sanad.Service");
const sequelize = require("../db");
const { Op } = require("sequelize");
const { lemmtaizationLocal } = require("../helper/helper");

const findSanadServiceByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  req.bot_id = bot_id;
  SanadService.findAll({ where: { bot_id } }).then((sanadServices) => {
    req.sanadServices = sanadServices;
    return next();
  });
};

const findSanadServiceById = (req, res, next) => {
  const sanad_service_id = req.body.sanad_service_id || req.query.sanad_service_id;
  SanadService.findOne({ where: { sanad_service_id } }).then((sanadService) => {
    req.sanadService = sanadService;
    return next();
  });
};

const findBotSanadServicesById = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id || req.body[0].bot_id;
  sequelize
    .query("designer_sanad_services_get :botID", {
      replacements: { botID: bot_id },
    })
    .then((data) => {
      req.sanadServices = data[0];
      return next();
    });
};

const findManyServices = (req, res, next) => {
  const data = req.body;
  SanadService.findAll({
    where: {
      sanad_service_id: { [Op.in]: data.map((a) => a.sanad_service_id) },
    },
  }).then((sanadServices) => {
    req.sanadServices = sanadServices;
    return next();
  });
};
const lemmtaizationMany = async (req, res, next) => {
  try {
    const data = [...req.body];
    const newData = await Promise.all(
      data.map(async (ent) => {
        return {
          ...ent,
          lemmatized_title: ent?.lemmatized_title
            ? (await lemmtaizationLocal(ent?.lemmatized_title))?.data?.answer ||
              ent.question
            : "",
        };
      })
    );
    req.body = newData;
    next();
  } catch (error) {
    console.log(error)
  }
};
const lemmtaizationOne = async (req, res, next) => {
  try {
    const data = { ...req.body };

    const newData = {
      ...data,
      lemmatized_title: data?.lemmatized_title
        ? (await lemmtaizationLocal(data?.lemmatized_title))?.data?.answer ||
          data.question
        : "",
    };

    req.body = newData;

    next();
  } catch (error) {
    console.log(error);
  }
};


module.exports = {
  findSanadServiceByBotId,
  findSanadServiceById,
  findBotSanadServicesById,
  findManyServices,
  lemmtaizationMany,
  lemmtaizationOne,
};
