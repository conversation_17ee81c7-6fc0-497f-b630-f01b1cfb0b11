const Trainee_Class = require("../Models/Custom.Class.Trainee");
const messages = require("../Messages/message.user");
const sequelize = require("../db");



const findClassTrainee= (req, res, next) => {
    const trainee_class_id = req.body.trainee_class_id || req.query.trainee_class_id;
    const bot_id = req.body.bot_id || req.query.bot_id
    Trainee_Class.findAll({ where: { trainee_class_id , bot_id} }).then((trainee_class) => {
      if (trainee_class) {
        req.trainee_class = trainee_class;
        return next();
      } else {
        return res.status(401).send(messages.notFound);
      }
    });
  };

  

const findTraineeClassInfo = async (req, res, next) => {
    const bot_id = req.body.bot_id || req.query.bot_id;
    const data = await sequelize.query("designer_custom_get_registrations :botID", {
      replacements: {
        botID: bot_id,
      },
    });
    return res.send(data[0]);
  };

  
  const findOneTraineeClassInfo = async (req, res, next) => {
    const bot_id = req.body.bot_id || req.query.bot_id;
    const first_name = req.body.first_name || req.query.first_name;
    const family_name = req.body.family_name || req.query.family_name;
    const data = await sequelize.query("designer_custom_search_by_trainee_name :bot_id , :first_name , :family_name ", {
      replacements: {
        bot_id: bot_id,
        first_name:first_name,
        family_name:family_name
      },
    });
    return res.send(data[0]);
  };


  module.exports = {
    findTraineeClassInfo,
    findClassTrainee,
    findOneTraineeClassInfo
};
