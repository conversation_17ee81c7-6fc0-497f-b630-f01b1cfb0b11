const BotTemplate = require("../Models/BotTemplate");

const findBotTemplateBySlug = (req, res, next) => {
  const bot_template_slug = req.query.bot_template_slug;
  BotTemplate.findOne({ where: { bot_template_slug } }).then((botTemplate) => {
    if (botTemplate) {
      req.botTemplate = botTemplate;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};


const findBotTemplateByID = (req, res, next) => {
    const bot_template_id = req.query.bot_template_id || req.body.bot_template_id;
    console.log("bot_template_id",bot_template_id)
    BotTemplate.findOne({ where: { bot_template_id } }).then((botTemplate) => {
      if (botTemplate) {
        req.botTemplate = botTemplate;
        return next();
      } else {
        return res.sendStatus(404);
      }
    });
};

const findAlBotTemplate = (req, res, next) => {
    BotTemplate.findAll({ }).then((botTemplates) => {
    if (botTemplates) {
      req.botTemplates = botTemplates;
      return next();
    } else {
        return res.sendStatus(404);
    }
  });
};

module.exports = { findBotTemplateBySlug, findAlBotTemplate, findBotTemplateByID };
