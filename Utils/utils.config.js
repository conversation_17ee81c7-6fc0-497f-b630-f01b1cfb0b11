const Config = require("../Models/Bot.Config");
const sequelize = require("../db");

const findBotConfigById = (req, res, next) => {
  const bot_id = req.body.bot_id;
  req.bot_id = bot_id;
  Config.findOne({ where: { bot_id } }).then((config) => {
    if (config) {
      req.config = config;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findBotConfigByQueryId = (req, res, next) => {
  const bot_id = req.query.bot_id;
  req.bot_id = bot_id;
  Config.findOne({ where: { bot_id } }).then((config) => {
    if (config) {
      req.config = config;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const checkConfigExistance = (req, res, next) => {
  const bot_id = req.body.bot_id;
  req.bot_id = bot_id;
  Config.findOne({ where: { bot_id } }).then((config) => {
    if (config) {
      return res.status(409).send({ message: "config already exist" });
    } else {
      return next();
    }
  });
};

const findBotConfigByQueryIdProcedure = (req, res, next) => {
  const bot_id = +req.query.bot_id;
  req.bot_id = bot_id;
  sequelize
    .query("designer_configs_get :botID", {
      replacements: {
        botID: bot_id,
      },
    })
    .then((data) => {
      req.config = data[0][0];
      if (req.config?.welcome_message_set) {
        req.config.welcome_message_set = JSON.parse(
          req.config.welcome_message_set
        );
      }
      if (req.config?.suggested_actions) {
        req.config.suggested_actions = req.config.suggested_actions.split(",");
      }
      if(req.config?.welcome_dialog){
        console.log("leggggt")
        req.config.welcome_dialog = req.config.welcome_dialog
      } 
      if(req.config?.globals){
        req.config.globals = JSON.parse(req.config.globals)
      }
      if(req.config?.bad_word_config){
        req.config.bad_word_config = JSON.parse(req?.config?.bad_word_config || '{}')
      }
      return next();
    });
};

module.exports = {
  findBotConfigById,
  findBotConfigByQueryId,
  checkConfigExistance,

  findBotConfigByQueryIdProcedure,
};
