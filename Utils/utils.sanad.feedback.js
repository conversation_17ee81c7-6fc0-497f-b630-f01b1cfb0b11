const SanadFeedback = require("../Models/Sanad.Feedback");

const findAll = (req, res, next) => {
  SanadFeedback.findAll({ where: {} }).then((feedbacks) => {
    req.feedbacks = feedbacks;
    return next();
  });
};

const find = (req, res, next) => {
  const sanad_feedback_id = req.body.sanad_feedback_id || req.query.sanad_feedback_id;
  SanadFeedback.findOne({ where: { sanad_feedback_id } }).then((feedback) => {
    req.feedback = feedback;
    return next();
  });
}

module.exports = {
  findAll,
  find,
};
