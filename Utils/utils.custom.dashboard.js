const CustomDashboard = require("../Models/Custom.Dashboard");
const sequelize = require("../db");

const findCustomDashboardBotId = async (req, res, next) => {
    try {
      const bot_id = req.query.bot_id || req.body.bot_id;
      req.bot_id = bot_id;
  
      const customDashboards = await CustomDashboard.findAll({
        where: {
          bot_id,
        },
      });
  
      if (!customDashboards || customDashboards.length === 0) {
        req.customDashboard = [];
        return next();
      }

      const customDashboardData = await Promise.all(
        customDashboards.map(async (dashboard) => {
          const [queryResults] = await sequelize.query(dashboard.query);
          return {
            ...dashboard.dataValues,
            queryResults: queryResults.map((result) => ({ ...result })),
          };
        })
      );

      req.customDashboard = customDashboardData;
      next();
    } catch (error) {
      console.error('Error fetching custom dashboards:', error);
      next(error);
    }
  };
  const findCustomDashboardById = (req, res, next) => {
    const custom_dashboard_id = req.body.custom_dashboard_id || req.query.custom_dashboard_id;
    CustomDashboard.findOne({ where: { custom_dashboard_id } }).then(async (customDashboard) => {
      if (customDashboard) {
        req.customDashboard = customDashboard;
        return next();
      } else {
        return res.sendStatus(404);
      }
    });
  };

module.exports = {
    findCustomDashboardBotId,
    findCustomDashboardById
};
