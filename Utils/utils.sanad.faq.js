const SanadFaq = require("../Models/Sanad.Faq");
const sequelize = require("../db");
const { Op } = require("sequelize");


const findSanadFaqByBotId = (req, res, next) => {
    const bot_id = req.body.bot_id || req.query.bot_id;
    req.bot_id = bot_id;
    SanadFaq.findAll({ where: { bot_id } }).then((sanadFaqs) => {
        req.sanadFaqs = sanadFaqs;
        return next();
    });
};

const findSanadFaqById = (req, res, next) => {
    const sanad_faq_id = req.body.sanad_faq_id || req.query.sanad_faq_id;
    SanadFaq.findOne({ where: { sanad_faq_id } }).then((sanadFaq) => {
        req.sanadFaq = sanadFaq;
        return next();
    });
};

const findBotSanadFAQsById = (req, res, next) => {
    const bot_id = req.body.bot_id || req.query.bot_id || req.body[0].bot_id;
    sequelize
      .query("designer_sanad_faqs_get :botID", {
        replacements: { botID: bot_id },
      })
      .then((data) => {
        req.sanadFaqs = data[0];
        return next();
      });
  };

  const findManySanadFAQs = (req, res, next) => {
    const data = req.body;
    SanadFaq.findAll({
      where: {
        sanad_faq_id: { [Op.in]: data.map((a) => a.sanad_faq_id) },
      },
    }).then((sanadFaqs) => {
      req.sanadFaqs = sanadFaqs;
      return next();
    });
  };


  const findQuestionAlternatives = (req, res, next) => {
    const bot_id = +req.query.bot_id;
    const sanad_faq_id = +req.query.sanad_faq_id;
    // const question = req.query.question;
    sequelize
      .query("designer_sanad_faqs_answer_alternatives :botID , :sanad_faq_id", {
        replacements: {
          botID: bot_id,
          sanad_faq_id: sanad_faq_id,
        },
      })
      .then((data) => {
        res.send(data[0]);
      });
  };

  const findNotApprovedFaqs = (req, res, next) => {
    const bot_id = req.body.bot_id || req.query.bot_id || req.body[0].bot_id;
    sequelize
      .query("designer_not_approved_sanad_faqs :botID", {
        replacements: { botID: bot_id },
      })
      .then((data) => {
        req.sanadFaqs = data[0];
        return next();
      });
  };

  const findApprovedFaqs = (req, res, next) => {
    const bot_id = req.body.bot_id || req.query.bot_id || req.body[0].bot_id;
    sequelize
      .query("designer_approved_sanad_faqs :botID", {
        replacements: { botID: bot_id },
      })
      .then((data) => {
        req.sanadFaqs = data[0];
        return next();
      });
  };


module.exports = {
    findSanadFaqByBotId,
    findSanadFaqById,
    findBotSanadFAQsById,
    findManySanadFAQs,
    findQuestionAlternatives,
    findApprovedFaqs,
    findNotApprovedFaqs
};
