const Custom_User = require("../Models/Custom.User");
const secret = require("../config/appConfig").secret;
const jwt = require("jsonwebtoken");
const messages = require("../Messages/message.user");

const findUserById = (req, res, next) => {
  const custom_user_id = req.body.custom_user_id || req.query.custom_user_id;
  Custom_User.findOne({ where: { custom_user_id } }).then((custom_user) => {
    if (custom_user) {
      req.custom_user = custom_user;
      return next();
    } else {
      return res.status(401).send(messages.notFound);
    }
  });
};
const findUserByEmail = (req, res, next) => {
  const email = req.body.email;
  Custom_User.findOne({ where: { email: email } }).then((custom_user) => {
    if (custom_user) {
      req.custom_user = custom_user;
      return next();
    } else {
      return res.status(401).send(messages.notFound);
    }
  });
};

const findUserByQueryEmail = (req, res, next) => {
  const email = req.query.email;
  Custom_User.findOne({ where: { email } }).then((user) => {
    if (user) {
      req.user = user;
      return next();
    } else {
      return res.status(401).send(messages.notFound);
    }
  });
};
const findUserByQueryId = (req, res, next) => {
  const custom_user_id = +req.query.custom_user_id || req.body.custom_user_id;
  Custom_User.findOne({ where: { custom_user_id } }).then((custom_user) => {
    if (custom_user) {
      req.custom_user = custom_user;
      return next();
    } else {
      return res.status(401).send(messages.notFound);
    }
  });
};

const authenticatePassword = (req, res, next) => {
  const custom_user = req.custom_user;
  const password = req.body.password;
  if (custom_user.authenticate(password)) {
    return next();
  } else {
    res.status(401).send(messages.wrongPassword);
  }
};

const checkEmailVerification = (req, res, next) => {
  const custom_user = req.custom_user;
  if (custom_user.email_verification) {
    return next();
  } else {
    return res.status(401).send(messages.verify);
  }
};

const verifyEmail = (req, res, next) => {
  const custom_user = req.custom_user;
  custom_user.email_verification = true;
  return next();
};

const authenticateToken = (req, res, next) => {
  const custom_user = req.custom_user;
  const authHeader = req.headers["authorization"];
  const token = authHeader ? authHeader.split(" ")[1] : null;
  if (token == null) return res.sendStatus(401);
  jwt.verify(token, secret, (err, tokenInfo) => {
    if (tokenInfo.name === custom_user.user_name) {
      return next();
    }
    return res.sendStatus(403);
  });
};

const checkEmailExistance = (req, res, next) => {
  const email = req.body.email;
  Custom_User.findOne({ where: { email } }).then((custom_user) => {
    if (!custom_user) {
      return next();
    }
    return res.status(409).send(messages.exists);
  });
};

module.exports = {
  checkEmailExistance,
  findUserById,
  findUserByEmail,
  findUserByQueryEmail,
  findUserByQueryId,
  authenticatePassword,
  checkEmailVerification,
  verifyEmail,
  authenticateToken,
};
