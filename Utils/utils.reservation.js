const Reservation = require("../Models/Reservation");

const findReservationByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  req.bot_id = bot_id;
  Reservation.findOne({ where: { bot_id } }).then((reservation) => {
    if (reservation) {
      req.reservation = reservation;
      return next();
    } else {
      res.send({ message: "plugin not set" });
    }
  });
};

const checkExistance = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  Reservation.findOne({ where: { bot_id } }).then((reservation) => {
    if (reservation) {
      req.reservation = reservation;
      return res
        .status(409)
        .send({ message: "Reservation plugin already exits" });
    } else {
      return next();
    }
  });
};

module.exports = { findReservationByBotId, checkExistance };
