const PlanFunction = require("../Models/Plan.Function");

const findPlanFunctions = (req, res, next) => {
  PlanFunction.findAll({}).then((planfunctions) => {
    req.planfunctions = planfunctions;
    return next();
  });
};

const findFunctionByPlanById = (req, res, next) => {
  const plan_id = req.body.plan_id || req.query.plan_id || req.bot.plan_id || 3;
  PlanFunction.findOne({ where: { plan_id } }).then((planfunction) => {
    req.planfunction = planfunction;
    return next();
  });
};

module.exports = {
  findPlanFunctions,
  findFunctionByPlanById,
};
