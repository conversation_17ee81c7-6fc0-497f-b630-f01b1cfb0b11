const WhatsApp = require("../Models/WhatsApp");

const checkExistance = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  WhatsApp.findOne({ where: { bot_id } }).then((whatsapp) => {
    if (whatsapp) {
      req.whatsapp = whatsapp;
      return res.status(409).send({ message: "Whatsapp already exits" });
    } else {
      return next();
    }
  });
};

const findAll = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  WhatsApp.findAll({ where: { bot_id } }).then((whatsapp) => {
    req.whatsapp = whatsapp;
    return next();
  });
};

const findByID = (req, res, next) => {
  const whatsapp_id =
    req.body.whatsapp_id || req.query.whatsapp_id;
    WhatsApp.findOne({ where: { whatsapp_id } }).then((whatsapp) => {
    req.whatsapp = whatsapp;
    return next();
  });
};

const findOneByPhoneOrBotId = (req, res, next) => {
  const phone = req.body.phone || req.query.phone;
  const bot_id = req.body.bot_id || req.query.bot_id;
  WhatsApp.findOne(bot_id ? { where: {bot_id} } : { where: { phone } }).then((whatsapp) => {
    if (whatsapp) {
      req.whatsapp = whatsapp;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
  
};

const findOneByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  WhatsApp.findOne({ where: { bot_id } }).then((whatsapp) => {
    if (whatsapp) {
      req.whatsapp = whatsapp;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
  
};

module.exports = { checkExistance, findOneByPhoneOrBotId,findAll,findByID,findOneByBotId };
