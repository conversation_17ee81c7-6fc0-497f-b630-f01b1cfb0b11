const sequelize = require("../db");
const Dialogs = require("../Models/Dialogs");
const DialogCheckpoint = require("../Models/DialogCheckpoint");

const findCheckpointsByBotID = async (req, res, next) => {
  try {
    const bot_id = req.body.bot_id || req.query.bot_id;
    const page = +req.query.page || req.body.page || 1;
    const pageSize = +req.query.pageSize || req.body.pageSize || 5;
    const { start_day, start_month, start_year, end_day, end_month, end_year } =
      req.body;

    // Use optimized raw SQL query with proper indexing for date filtering
    if (start_day && start_month && start_year) {
      // Prepare date parameters
      const startDate = `${start_year}-${start_month
        .toString()
        .padStart(2, "0")}-${start_day.toString().padStart(2, "0")}`;
      const endDate =
        end_day && end_month && end_year
          ? `${end_year}-${end_month.toString().padStart(2, "0")}-${end_day
              .toString()
              .padStart(2, "0")} 23:59:59.999`
          : new Date().toISOString();

      // Execute optimized query with parameters
      const results = await sequelize.query(
        `
        SELECT
          dc.dialog_checkpoint_id,
          dc.conversation_id,
          dc.tag,
          dc.channel,
          dc.bot_id,
          dc.dialog_id,
          dc.createdAt,
          dc.updatedAt,
          d.dialog_name
        FROM bot_designer_dialog_checkpoints dc
        JOIN bot_designer_dialogs d ON dc.dialog_id = d.dialog_id
        WHERE dc.bot_id = :bot_id
          AND dc.createdAt BETWEEN :startDate AND :endDate
        ORDER BY dc.createdAt DESC
        OFFSET :offset ROWS
        FETCH NEXT :limit ROWS ONLY
      `,
        {
          replacements: {
            bot_id,
            startDate,
            endDate,
            offset: (page - 1) * pageSize,
            limit: pageSize,
          },
          type: sequelize.QueryTypes.SELECT,
        }
      );

      // Get total count for pagination
      const [countResult] = await sequelize.query(
        `
        SELECT COUNT(DISTINCT dialog_checkpoint_id) as total
        FROM bot_designer_dialog_checkpoints
        WHERE bot_id = :bot_id
          AND createdAt BETWEEN :startDate AND :endDate
      `,
        {
          replacements: {
            bot_id,
            startDate,
            endDate,
          },
          type: sequelize.QueryTypes.SELECT,
        }
      );

      const total = countResult.total;

      req.res = {
        data: results,
        pagination: {
          totalItems: total,
          totalPages: Math.ceil(total / pageSize),
          currentPage: page,
          pageSize: pageSize,
          hasNextPage: page < Math.ceil(total / pageSize),
          hasPrevPage: page > 1,
        },
      };
    } else {
      // If no date filtering, use the original Sequelize approach
      // Build where clause
      const whereClause = { bot_id };

      const response = await DialogCheckpoint.findAll({
        include: [{ model: Dialogs, attributes: ["dialog_name"] }],
        where: whereClause,
        order: [["createdAt", "DESC"]],
        limit: pageSize,
        offset: (page - 1) * pageSize,
      });

      const total = await DialogCheckpoint.count({
        distinct: true,
        col: "dialog_checkpoint_id",
        where: whereClause,
      });

      req.res = {
        data: response,
        pagination: {
          totalItems: total,
          totalPages: Math.ceil(total / pageSize),
          currentPage: page,
          pageSize: pageSize,
          hasNextPage: page < Math.ceil(total / pageSize),
          hasPrevPage: page > 1,
        },
      };
    }
  } catch (error) {
    console.log(error);
    req.res = [];
  } finally {
    next();
  }
};

const findDialogCheckpointById = (req, res, next) => {
  const dialog_checkpoint_id =
    req.body.dialog_checkpoint_id || req.query.dialog_checkpoint_id;
  DialogCheckpoint.findOne({
    where: { dialog_checkpoint_id },
    include: [{ model: Dialogs, attributes: ["dialog_name"] }],
  }).then(async (dialogCheckpoint) => {
    if (dialogCheckpoint) {
      req.dialogCheckpoint = dialogCheckpoint;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findDialogsWithCheckpoints = async (req, res, next) => {
  try {
    const bot_id = req.body.bot_id || req.query.bot_id;
    const { start_day, start_month, start_year, end_day, end_month, end_year } =
      req.body;

    if (!bot_id) {
      req.res = [];
      return next();
    }

    // Use optimized direct SQL query for better performance with date filtering
    if (start_day && start_month && start_year) {
      // Prepare date parameters
      const startDate = `${start_year}-${start_month
        .toString()
        .padStart(2, "0")}-${start_day.toString().padStart(2, "0")}`;
      const endDate =
        end_day && end_month && end_year
          ? `${end_year}-${end_month.toString().padStart(2, "0")}-${end_day
              .toString()
              .padStart(2, "0")} 23:59:59.999`
          : new Date().toISOString();

      // Execute optimized query with parameters
      const results = await sequelize.query(
        `
        SELECT DISTINCT d.dialog_id, d.dialog_name
        FROM bot_designer_dialogs d
        INNER JOIN bot_designer_dialog_checkpoints dc
          ON d.dialog_id = dc.dialog_id
        WHERE d.bot_id = :bot_id
          AND dc.bot_id = :bot_id
          AND dc.createdAt BETWEEN :startDate AND :endDate
      `,
        {
          replacements: {
            bot_id,
            startDate,
            endDate,
          },
          type: sequelize.QueryTypes.SELECT,
        }
      );

      req.res = results;
    } else {
      // If no date filtering, use a simpler but still optimized query
      const results = await sequelize.query(
        `
        SELECT DISTINCT d.dialog_id, d.dialog_name
        FROM bot_designer_dialogs d
        INNER JOIN bot_designer_dialog_checkpoints dc
          ON d.dialog_id = dc.dialog_id
        WHERE d.bot_id = :bot_id
          AND dc.bot_id = :bot_id
      `,
        {
          replacements: { bot_id },
          type: sequelize.QueryTypes.SELECT,
        }
      );

      req.res = results;
    }
  } catch (error) {
    console.error("Error fetching dialog checkpoints:", error);
    req.res = [];
  } finally {
    next();
  }
};

const findCheckpointsByDialogID = async (req, res, next) => {
  try {
    const dialog_id = req.body.dialog_id || req.query.dialog_id;
    const { start_day, start_month, start_year, end_day, end_month, end_year } =
      req.body;

    if (!dialog_id) {
      req.res = [];
      return next();
    }

    // Use optimized direct SQL query for better performance with date filtering
    if (start_day && start_month && start_year) {
      // Prepare date parameters
      const startDate = `${start_year}-${start_month
        .toString()
        .padStart(2, "0")}-${start_day.toString().padStart(2, "0")}`;
      const endDate =
        end_day && end_month && end_year
          ? `${end_year}-${end_month.toString().padStart(2, "0")}-${end_day
              .toString()
              .padStart(2, "0")} 23:59:59.999`
          : new Date().toISOString();

      // Execute optimized query with parameters
      const results = await sequelize.query(
        `
        SELECT
          dc.dialog_checkpoint_id,
          dc.conversation_id,
          dc.tag,
          dc.channel,
          dc.bot_id,
          dc.dialog_id,
          dc.createdAt,
          dc.updatedAt,
          d.dialog_name
        FROM bot_designer_dialog_checkpoints dc
        JOIN bot_designer_dialogs d ON dc.dialog_id = d.dialog_id
        WHERE dc.dialog_id = :dialog_id
          AND dc.createdAt BETWEEN :startDate AND :endDate
        ORDER BY dc.createdAt DESC
      `,
        {
          replacements: {
            dialog_id,
            startDate,
            endDate,
          },
          type: sequelize.QueryTypes.SELECT,
        }
      );

      req.res = results;
    } else {
      // If no date filtering, use a simpler but still optimized query
      const results = await sequelize.query(
        `
        SELECT
          dc.dialog_checkpoint_id,
          dc.conversation_id,
          dc.tag,
          dc.channel,
          dc.bot_id,
          dc.dialog_id,
          dc.createdAt,
          dc.updatedAt,
          d.dialog_name
        FROM bot_designer_dialog_checkpoints dc
        JOIN bot_designer_dialogs d ON dc.dialog_id = d.dialog_id
        WHERE dc.dialog_id = :dialog_id
        ORDER BY dc.createdAt DESC
      `,
        {
          replacements: { dialog_id },
          type: sequelize.QueryTypes.SELECT,
        }
      );

      req.res = results;
    }
  } catch (error) {
    console.error("Error fetching dialog checkpoints:", error);
    req.res = [];
  } finally {
    next();
  }
};

const searchCheckpoints = async (req, _res, next) => {
  try {
    const {
      bot_id,
      dialog_id,
      conversation_id,
      tag,
      channel,
      start_day,
      start_month,
      start_year,
      end_day,
      end_month,
      end_year,
    } = req.body;

    const page = +req.query.page || req.body.page || 1;
    const pageSize = +req.query.pageSize || req.body.pageSize|| 10;

    // Build the base query
    let query = `
      SELECT
        dc.dialog_checkpoint_id,
        dc.conversation_id,
        dc.tag,
        dc.channel,
        dc.bot_id,
        dc.dialog_id,
        dc.createdAt,
        dc.updatedAt,
        d.dialog_name
      FROM bot_designer_dialog_checkpoints dc
      JOIN bot_designer_dialogs d ON dc.dialog_id = d.dialog_id
      WHERE 1=1
    `;

    // Build the count query
    let countQuery = `
      SELECT COUNT(DISTINCT dc.dialog_checkpoint_id) as total
      FROM bot_designer_dialog_checkpoints dc
      WHERE 1=1
    `;

    // Initialize replacements object
    const replacements = {
      offset: (page - 1) * pageSize,
      limit: pageSize,
    };

    // Add conditions based on provided parameters
    if (bot_id) {
      query += " AND dc.bot_id = :bot_id";
      countQuery += " AND dc.bot_id = :bot_id";
      replacements.bot_id = bot_id;
    }

    if (dialog_id) {
      query += " AND dc.dialog_id = :dialog_id";
      countQuery += " AND dc.dialog_id = :dialog_id";
      replacements.dialog_id = dialog_id;
    }

    if (conversation_id) {
      query += " AND dc.conversation_id LIKE :conversation_id";
      countQuery += " AND dc.conversation_id LIKE :conversation_id";
      replacements.conversation_id = `%${conversation_id}%`;
    }

    if (tag) {
      query += " AND dc.tag LIKE :tag";
      countQuery += " AND dc.tag LIKE :tag";
      replacements.tag = `%${tag}%`;
    }

    if (channel) {
      query += " AND dc.channel = :channel";
      countQuery += " AND dc.channel = :channel";
      replacements.channel = channel;
    }

    // Add date range filtering if provided
    if (start_day && start_month && start_year) {
      const startDate = `${start_year}-${start_month
        .toString()
        .padStart(2, "0")}-${start_day.toString().padStart(2, "0")}`;
      const endDate =
        end_day && end_month && end_year
          ? `${end_year}-${end_month.toString().padStart(2, "0")}-${end_day
              .toString()
              .padStart(2, "0")} 23:59:59.999`
          : new Date().toISOString();

      query += " AND dc.createdAt BETWEEN :startDate AND :endDate";
      countQuery += " AND dc.createdAt BETWEEN :startDate AND :endDate";

      replacements.startDate = startDate;
      replacements.endDate = endDate;
    }

    // Add order by and pagination
    query +=
      " ORDER BY dc.createdAt DESC OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY";

    // Execute the queries
    const results = await sequelize.query(query, {
      replacements,
      type: sequelize.QueryTypes.SELECT,
    });

    const [countResult] = await sequelize.query(countQuery, {
      replacements,
      type: sequelize.QueryTypes.SELECT,
    });

    const total = countResult.total;

    req.res = {
      data: results,
      pagination: {
        totalItems: total,
        totalPages: Math.ceil(total / pageSize),
        currentPage: page,
        pageSize: pageSize,
        hasNextPage: page < Math.ceil(total / pageSize),
        hasPrevPage: page > 1,
      },
    };
  } catch (error) {
    console.error("Error searching checkpoints:", error);
    req.res = {
      data: [],
      pagination: {
        totalItems: 0,
        totalPages: 0,
        currentPage: 1,
        pageSize: 10,
        hasNextPage: false,
        hasPrevPage: false,
      },
      error: error.message,
    };
  } finally {
    next();
  }
};

module.exports = {
  findCheckpointsByBotID,
  findDialogCheckpointById,
  findDialogsWithCheckpoints,
  findCheckpointsByDialogID,
  searchCheckpoints,
};
