const FAQ_Logs = require("../Models/Faq.Logs");

const findFaqTypesByUserId = (req, res, next) => {
  const user_id = req.body.user_id || req.query.user_id;
  FAQ_Logs.findOne({ where: {  user_id } }).then((faq_logs) => {
    req.faq_logs = faq_logs;
    if (!req.faq_logs) {
      return res.status(404).send({ message: " Not Found" });
    }
    return next();
  });
};

module.exports = { findFaqTypesByUserId };
