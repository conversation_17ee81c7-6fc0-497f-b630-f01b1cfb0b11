const Promotion = require("../Models/Promotion");
const jwt = require("jsonwebtoken");
const secret = require("../config/appConfig").secret;

const findPromotionById = (req, res, next) => {
  const promotion_id = req.body.bot_id || req.query.bot_id;
  Promotion.findOne({ where: { promotion_id } }).then((promotion) => {
    if (promotion) {
      req.promotion = promotion;
      return next();
    }
    return res.status(404).send({ message: "promotion does not exist" });
  });
};

const findPromotionByToken = (req, res, next) => {
  const promotion_token = req.body.promotion_token || req.query.promotion_token;
  const decoded = jwt.verify(promotion_token, secret);
  const promotion_id = decoded.promotion_id;
  Promotion.findOne({ where: { promotion_id } }).then((promotion) => {
    if (promotion) {
      req.promotion = promotion;
      return next();
    }
    return res.status(404).send({ message: "promotion does not exist" });
  });
};

const checkPromotionExistance = (req, res, next) => {
  const promotion_id = req.body.promotion_id || req.query.promotion_id;
  if (promotion_id) {
    Promotion.findOne({ where: { promotion_id } }).then((promotion) => {
      req.promotion = promotion;
    });
  }
  else {
    req.promotion = {}
  }
  return next();
};


module.exports = {
  findPromotionById,
  checkPromotionExistance,
  findPromotionByToken
};
