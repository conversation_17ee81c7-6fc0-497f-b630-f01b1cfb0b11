const { toEnglishTokenize } = require("../Controllers/controllers.engine");
const Phone = require("../Models/Phone");

const findPhone = (req, res, next) => {
  const phone_number = toEnglishTokenize(req.body.phone);
  Phone.findOne({
    where: { phone: phone_number },
    order: [["createdAt", "DESC"]],
  }).then((phone) => {
    if (phone) {
      req.phone = phone;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findPhonesByBotId = (req, res, next) => {
  const bot_id = req.query.bot_id;
  Phone.findAll({
    where: { bot_id },
    order: [["createdAt", "DESC"]],
  }).then((phones) => {
    req.phones = phones;
    return next();
  });
};

module.exports = { findPhone, findPhonesByBotId };
