const DialogHistory = require ("../Models/DialogHistory");
const Bot = require ("../Models/Bot");

const findLogById = (req, res, next) => {
  console.log('test');
  const dialog_id = req.body.dialog_id || req.query.dialog_id;
  DialogHistory.findOne({ where: { dialog_id }, 
  include: [
    {
      model: Bo<PERSON>,
      attributes: ['bot_name']
    }
  ]
    
  }).then((dialoghistory) => {
    if (dialoghistory) {
      req.dialoghistory = dialoghistory;
      return next();
    } else {
      return res.status(401).send({messages: 'dialog not Found'});
    }
  });
};


module.exports = {findLogById}