const Employer = require("../Models/Employer.ILO");

const findById = (req, res, next) => {
  const employer_id = req.body.employer_id || req.query.employer_id;
  Employer.findOne({ where: { employer_id } }).then((employer) => {
    if (employer) {
      req.employer = employer;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findAll = (req, res, next) => {
  Employer.findAll({ where: {} }).then((employers) => {
    req.employers = employers;
    return next();
  });
};

module.exports = {
  findAll,
  findById,
};
