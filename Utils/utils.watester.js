const WATester = require("../Models/WaTester");

const checkExistance = (req, res, next) => {
  const phone = req.body.phone || req.query.phone;
  WATester.findOne({ where: { phone } }).then((watester) => {
    if (watester) {
      req.watester = watester;
      return res.status(409).send({ message: "WA Tester already exits" });
    } else {
      return next();
    }
  });
};

const findAll = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  WATester.findAll({ where: { bot_id } }).then((watesters) => {
    req.watesters = watesters;
    return next();
  });
};

const findAllNumber = (req, res, next) => {
  const phone = req.body.phone || req.query.phone;
  WATester.findOne({ where: { phone } }).then((watesters) => {
    if (watesters) {
      req.watesters = watesters;
      return next();
    } else {
      return res.status(401).send({ message: "can't find number" });
    }
    // console.log(watesters, "watesters");
    // req.watesters = watesters;
    // return next();
  });
};
const findOne = (req, res, next) => {
  const whatsapp_tester_id =
    req.body.whatsapp_tester_id || req.query.whatsapp_tester_id;
  WATester.findOne({ where: { whatsapp_tester_id } }).then((watester) => {
    req.watester = watester;
    return next();
  });
};

module.exports = { checkExistance, findAll, findOne, findAllNumber };
