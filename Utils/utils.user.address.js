const UserAddress = require("../Models/User.Address");

const findById = (req, res, next) => {
  const address_id = req.body.address_id;
  UserAddress.findOne({ where: { address_id } }).then((address) => {
    if (address) {
      req.address = address;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findByUserId = (req, res, next) => {
  const user_id = req.body.user_id || req.query.user_id;
  UserAddress.findAll({ where: { user_id } }).then((addresses) => {
    req.addresses = addresses;
    return next();
  });
};

module.exports = {
  findById,
  findByUserId,
};
