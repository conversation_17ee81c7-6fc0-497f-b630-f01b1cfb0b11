const Shortcut = require("../Models/Shortcut");

const preprocess = (req, res, next) => {
  req.body.shortcuts = JSON.stringify(req.body.shortcuts);
  next();
};

const findUserShortcutsById = (req, res, next) => {
  const user_id = req.body.user_id || req.query.user_id;
  req.user_id = user_id;
  Shortcut.findOne({ where: { user_id } }).then((shortcuts) => {
    req.shortcuts = shortcuts;
    return next();
  });
};

const findShortcutById = (req, res, next) => {
  const shortcut_id = req.body.shortcut_id;
  Shortcut.findOne({ where: { shortcut_id } }).then((shortcut) => {
    if (!shortcut)
      return res.status(404).send({ message: "Shortcut does not exist" });
    req.shortcut = shortcut;
    return next();
  });
};

const findManyShortcuts = (req, res, next) => {
  const data = req.body;
  Shortcuts.findAll({
    where: {
      shortcut_id: { [Op.in]: data.map((a) => a.shortcut_id) },
    },
  }).then((shortcuts) => {
    req.shortcuts = shortcuts;
    return next();
  });
};

module.exports = {
  findUserShortcutsById,
  findShortcutById,
  findManyShortcuts,
  preprocess,
};
