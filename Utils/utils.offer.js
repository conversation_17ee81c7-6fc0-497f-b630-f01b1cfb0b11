const Offer = require("../Models/Offer");
const { Op } = require("sequelize");
const { lemmtaizationLocal } = require("../helper/helper");

const preprocessOffer = (req, res, next) => {
  req.body.offer_date_start = req.body.offer_date_start
    ? new Date(req.body.offer_date_start)
    : null;
  req.body.offer_date_end = req.body.offer_date_end
    ? new Date(req.body.offer_date_end)
    : null;
  return next();
};

const postprocessOffer = (req, res, next) => {
  return next();
};

const findOfferById = (req, res, next) => {
  const offer_id = req.body.offer_id;
  req.offer_id = offer_id;
  Offer.findOne({ where: { offer_id } }).then((offer) => {
    if (offer) {
      req.offer = offer;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findOfferByQueryId = (req, res, next) => {
  const offer_id = req.query.offer_id;
  req.offer_id = offer_id;
  Offer.findOne({ where: { offer_id } }).then((offer) => {
    if (offer) {
      req.offer = offer;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

// const findOffersByBotId = (req, res, next) => {
//   const bot_id = req.body.bot_id;
//   req.bot_id = bot_id;
//   Offer.findAll({ where: { bot_id } }).then((offers) => {
//     req.offers = offers;
//     return next();
//   });
// };

const findOffersByBotId = (req, res, next) => {
  const absDate = new Date(
    new Date() - 1000 * 60 * 60 * 24 * 365 * 5
  ).toISOString();
  const bot_id = req.query.bot_id || req.body.bot_id;
  const offer_ids = req.query.offer_ids
    ? req.query.offer_ids.split(",")
    : undefined;
  const offer_status = req.query.offer_status;
  req.bot_id = bot_id;
  Offer.findAll({
    where: {
      bot_id,
      offer_id: offer_ids
        ? { [Op.in]: offer_ids }
        : { [Op.between]: [1, 1000000000000] },
      [Op.and]: [
        {
          offer_status: offer_status
            ? offer_status
            : { [Op.between]: ["active", "inactive"] },
        },
        {
          offer_date_start: offer_status
            ? { [Op.lte]: new Date().toISOString() }
            : { [Op.gte]: absDate },
        },
        {
          offer_date_end: offer_status
            ? { [Op.gte]: new Date().toISOString() }
            : { [Op.gte]: absDate },
        },
      ],
    },
  }).then((offers) => {
    req.offers = offers;
    return next();
  });
};

const findOffersByOfferItem = (req, res, next) => {
  const offeritems = req.offeritems;
  Offer.findAll({
    where: {
      offer_id: { [Op.in]: offeritems.map((a) => a.offer_id) },
      offer_status: "active",
      offer_date_start: { [Op.lte]: new Date().toISOString() },
      offer_date_end: { [Op.gte]: new Date().toISOString() },
    },
  }).then((offers) => {
    req.offers = offers;
    return next();
  });
};

const lemmtaizationOne = async (req, res, next) => {
  try {
    const data = { ...req.body };

    const newData = {
      ...data,
      lemmatized_offer_description: data?.lemmatized_offer_description
        ? (await lemmtaizationLocal(data?.lemmatized_offer_description))?.data
            ?.answer || data.question
        : "",
    };

    req.body = newData;

    next();
  } catch (error) {
    console.log(error);
  }
};
const findAllByBot = (req, res, next) => {
  const bot_id = req.query.bot_id;
  Offer.findAll({
    where: {
      bot_id: +bot_id,
    },
  }).then((offers) => {
    if (!offers) {
      return res.status(401).send({ message: "offers not found" });
    } else {
      req.offers = offers;
      return next();
    }
  });
};
module.exports = {
  findOfferById,
  findOfferByQueryId,
  findOffersByBotId,
  preprocessOffer,
  postprocessOffer,
  findOffersByOfferItem,
  lemmtaizationOne,
  findAllByBot,
};
