const Bot_Custom = require("../Models/Bot.Custom");
const Customs_Trainee = require("../Models/Customs.Trainee");
const messages = require("../Messages/message.user");
const sequelize = require("../db");



const findCustomBotById = (req, res, next) => {
  const table_id = req.body.table_id || req.query.table_id;
  Bot_Custom.findOne({ where: { table_id } }).then((custom) => {
    if (custom) {
      req.custom = custom;
      return next();
    } else {
      return res.status(401).send(messages.notFound);
    }
  });
};

const findIdNumberTrainee = (req, res, next) => {
  const unique_code = req.body.unique_code || req.query.unique_code;
  Customs_Trainee.findOne({ where: { unique_code } }).then((custom_trainee) => {
    if (custom_trainee) {
      req.custom = custom_trainee;
      res.send(custom_trainee)
    } else {
      return res.status(401).send(messages.notFound);
    }
  });
};

const findAll = (req, res, next) => {
  Bot_Custom.findAll({
    where: { bot_id: req.query.bot_id },
  }).then((custom) => {
    if (custom) {
      req.custom = custom.map(a => {
        return {
          ...a.dataValues,
          form_details: JSON.parse(a.dataValues.form_details)
        }
      });
      return next();
    }
  });
};

//   const findTablesByBotId = (req, res, next) => {
//     const bot_id = req.body.bot_id || req.query.bot_id;
//     Bot_Custom.findOne({ where: { bot_id  } }).then((custom) => {
//       if (custom) {
//         req.custom = custom;
//         return next();
//       } else {
//         return res.status(401).send(messages.notFound);
//       }
//     });
//   };

const findTraineesInClass = async (req, res) => {
  const bot_id = req.query.bot_id;
  const className = req.query.className;
  const from_time = req.query.from_time;
  var dt = new Date(from_time);
  const data = await sequelize.query("designer_custom_get_trainees_in_class :bot_id , :className  ", {
    replacements: {
      bot_id: bot_id,
      className: className,
    },
  });
  return res.send(data[0]);
};

const checkAttendance = async (req, res) => {
  const bot_id = req.query.bot_id;
  const trainee_class_id = req.query.trainee_class_id;
  const date_year = req.query.date_year;
  const date_month = req.query.date_month;
  const date_day = req.query.date_day
  const data = await sequelize.query("designer_custom_check_attendance_trainee :bot_id , :trainee_class_id , :date_year , :date_month , :date_day ", {
    replacements: {
      bot_id: bot_id,
      trainee_class_id: trainee_class_id,
      date_year:date_year,
      date_month:date_month,
      date_day:date_day
    },
  });
  return res.send(data[0]);
};

const getNotRegisteredTrainees = async (req, res) => {
  const bot_id = req.query.bot_id;
  const data = await sequelize.query("designer_custom_not_registered_trainees :bot_id  ", {
    replacements: {
      bot_id: bot_id,
    },
  });
  return res.send(data[0]);
};

const trialTrainee = async (req, res) => {
  const bot_id = req.query.bot_id;
  const data = await sequelize.query("designer_custom_get_trial_trainees :bot_id ", {
    replacements: {
      bot_id: bot_id,
    },
  });
  return res.send(data[0]);
};

const activeTrainees = async (req, res) => {
  const bot_id = req.query.bot_id;
  const data = await sequelize.query("designer_custom_active_trainees :bot_id ", {
    replacements: {
      bot_id: bot_id,
    },
  });
  return res.send(data[0]);
};

const notActiveTrainees = async (req, res) => {
  const bot_id = req.query.bot_id;
  const data = await sequelize.query("designer_custom_not_active_trainees :bot_id ", {
    replacements: {
      bot_id: bot_id,
    },
  });
  return res.send(data[0]);
};
const reminderActiveTrainees = async (req, res) => {
  const bot_id = req.query.bot_id;
  const day = req.query.day;
  const data = await sequelize.query("designer_custom_expired_trainees :bot_id ,:day", {
    replacements: {
      bot_id: bot_id,
      day:day
    },
  });
  return res.send(data[0]);
};

const getAllTrainees = async (req, res) => {
  const bot_id = req.query.bot_id;
  const data = await sequelize.query("designer_custom_get_all_trainees :bot_id ", {
    replacements: {
      bot_id: bot_id
    },
  });
  return res.send(data[0]);
};


module.exports = {
  findCustomBotById,
  findAll,
  findTraineesInClass,
  checkAttendance,
  trialTrainee,
  findIdNumberTrainee,
  getNotRegisteredTrainees,
  activeTrainees,
  notActiveTrainees,
  reminderActiveTrainees,
  getAllTrainees
};
