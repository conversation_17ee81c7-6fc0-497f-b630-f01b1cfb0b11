const Bot = require("../Models/Bot");
const sequelize = require("../db");
const fetch = require("node-fetch");
const {
serverUrl
} = require("../constants")

const findBotById = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  const user_id = req.body.user_id || req.query.user_id;
  req.bot_id = bot_id;
  Bot.findOne({ where: { bot_id, deleted: 0 } }).then((bot) => {
    if (bot) {
      req.bot = bot;
      return next();
    } else {
      return res.status(404).send({ error: "bot not found" });
    }
  });
};

const findBotByQueryId = (req, res, next) => {
  const bot_id = req.query.bot_id;
  req.bot_id = bot_id;
  Bot.findOne({ where: { bot_id, deleted: 0 } }).then((bot) => {
    if (bot) {
      req.bot = bot;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findUserBotsById = (req, res, next) => {
  const user_id = req.body.user_id;
  req.user_id = user_id;
  Bot.findAll({ where: { user_id, deleted: 0 } }).then((bots) => {
    req.bots = bots;
    return next();
  });
};

const findUserBotsByQueryId = (req, res, next) => {
  const user_id = req.query.user_id;
  req.user_id = user_id;
  Bot.findAll({ where: { user_id, deleted: 0 } }).then((bots) => {
    console.log(bots.length);
    req.bots = bots;
    return next();
  });
};

const findBotByIdSP = (req, res, next) => {
  const bot_id = req.body.bot_id;
  req.bot_id = bot_id;

  sequelize
    .query("designer_bot_get :botID", {
      replacements: {
        botID: bot_id,
      },
    })
    .then((data) => {
      const bot = data[0][0];
      if (bot && !bot.deleted) {
        req.bot = bot;
        return next();
      } else {
        return res.sendStatus(404);
      }
    });
};

const checkUniqueName = (req, res, next) => {
  const bots = req.bots;
  const bot_name = req.body.bot_name;
  const dup_bot = bots.find((a) => a.bot_name === bot_name);
  if (dup_bot) {
    return res.send({ message: "this bot name is duplicated per user" });
  } else {
    return next();
  }
};

const findAllBots = (req, res, next) => {
  sequelize.query("designer_get_all_bots_ids", {}).then((data) => {
    const bot_ids = data[0];
    req.bot_ids = bot_ids;
    return next();
  });
};

const restartAll = (req, res, next) => {
  req.bot_ids.map((bot) => {
    fetch(serverUrl.concat("/api/bot/restart"), {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        bot_id: bot.bot_id,
      }),
    })
      .then((res) => res.json())
      .catch((e) => console.log(e));
  });
  res.send({ message: "proceeding" });
};

module.exports = {
  findBotById,
  findBotByQueryId,
  findUserBotsById,
  findUserBotsByQueryId,
  findBotByIdSP,
  checkUniqueName,
  findAllBots,
  restartAll,
};
