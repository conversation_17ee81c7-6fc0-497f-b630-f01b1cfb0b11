const Triggers_Logs = require("../Models/Triggers.Logs");

const findFaqTypesByUserId = (req, res, next) => {
  const user_id = req.body.user_id || req.query.user_id;
  Triggers_Logs.findOne({ where: {  user_id } }).then((triggers_logs) => {
    req.triggers_logs = triggers_logs;
    if (!req.triggers_logs) {
      return res.status(404).send({ message: " Not Found" });
    }
    return next();
  });
};


module.exports = { findFaqTypesByUserId };
