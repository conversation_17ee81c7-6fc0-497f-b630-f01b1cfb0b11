const Monitor = require("../Models/Monitor");
const secret = require("../config/appConfig").secret;
const jwt = require("jsonwebtoken");

const authenticatePassword = (req, res, next) => {
  const monitor = req.monitor;
  const password = req.body.password;
  if (monitor.authenticate(password)) {
    return next();
  } else {
    res.status(401).send({ message: "incorrect password" });
  }
};

const checkEmailVerification = (req, res, next) => {
  const monitor = req.monitor;
  if (monitor.email_verification) {
    return next();
  } else {
    return res.status(401).send({ message: "please verify your email" });
  }
};

const verifyEmail = (req, res, next) => {
  const monitor = req.monitor;
  monitor.email_verification = true;
  return next();
};

const checkEmailExistance = (req, res, next) => {
  const email = req.body.email;
  Monitor.findOne({ where: { email } }).then((user) => {
    if (!user) {
      return next();
    }
    return res.status(409).send({ message: "User already exists" });
  });
};

const checkLoginExistance = (req, res, next) => {
  const email = req.body.email;
  Monitor.findOne({ where: { email } }).then((monitor) => {
    if (monitor) {
      req.monitor = monitor;
      return next();
    }
    return res.status(409).send({ message: "User does not exist" });
  });
};

module.exports = {
  checkLoginExistance,
  checkEmailExistance,
  verifyEmail,
  checkEmailVerification,
  authenticatePassword,
};
