const Item = require("../Models/Item");
const ItemOption = require("../Models/Item.Option");
const ItemFeature = require("../Models/Item.Feature");
const Feature = require("../Models/Feature");
const Category = require("../Models/Category");
const { Op } = require("sequelize");
const Offer = require("../Models/Offer");
const OfferItem = require("../Models/Offer.Item");
const sequelize = require("../db");
const { lemmtaizationLocal } = require("../helper/helper");

function shuffle(array) {
  var currentIndex = array.length,
    randomIndex;

  // While there remain elements to shuffle...
  while (0 !== currentIndex) {
    // Pick a remaining element...
    randomIndex = Math.floor(Math.random() * currentIndex);
    currentIndex--;

    // And swap it with the current element.
    [array[currentIndex], array[randomIndex]] = [
      array[randomIndex],
      array[currentIndex],
    ];
  }

  return array;
}

const preprocessItem = (req, res, next) => {
  req.body.item_icons = req.body.item_icons.join(",");
  // req.body.item_discount_date_start = req.body.item_discount_date_start
  //   ? new Date(req.body.item_discount_date_start)
  //   : null;
  // req.body.item_discount_date_end = req.body.item_discount_date_end
  //   ? new Date(req.body.item_discount_date_end)
  //   : null;
  return next();
};

const postprocessItem = (req, res, next) => {
  // req.item.item_icons = req.item.item_icons.length
  //   ? req.item.item_icons.split(",")
  //   : [];
  return next();
};

const findItemById = (req, res, next) => {
  const item_id = req.body.item_id;
  req.item_id = item_id;
  Item.findOne({ where: { item_id } }).then((item) => {
    if (item) {
      req.item = item;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findItemByQueryId = (req, res, next) => {
  const item_id = req.query.item_id;
  req.item_id = item_id;
  Item.findOne({ where: { item_id } }).then((item) => {
    if (item) {
      req.item = item;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findItemsByBotId = (req, res, next) => {
  const item_id = req.query.item_id;
  req.item_id = item_id;
  Item.findAll({
    where: {
      bot_id,
    },
  }).then((items) => {
    req.items = items;
    return next();
  });
};

const findItemFromBotItems = (req, res, next) => {
  const items = req.items;
  const item_id = req.query.item_id;

  req.item = items.find((a) => a.item_id === +item_id);
  return next();
};

const findItemsByQueryBotId = async (req, res, next) => {
  const bot_id = req.query.bot_id;
  const search_terms = req.query.search_terms
    ? req.query.search_terms.split(",")
    : [];

  const step = 9;
  const slice = +req.query.slice || 1;
  const limit = req.query.limit
    ? +req.query.limit
    : req.query.slice
    ? step
    : 90;

  const include_item_features = req.query.include_item_features;

  const offset = req.query.slice ? step * (slice - 1) : 0;
  if (Object.keys(req.query).length === 1 && bot_id) {
    return sequelize
      .query("designer_get_items :botID", {
        replacements: {
          botID: +bot_id,
        },
      })
      .then((data) => {
        return res.send(
          data[0].map((a) => {
            return {
              ...a,
              item_icons: a.item_icons?.split(","),
            };
          })
        );
      });
  }
  const doSingleFeatureExist = req.query.feature_value && +req.query.feature_id;

  const feature_value = req.query.feature_value;
  const feature_id = +req.query.feature_id;

  const category_name = req.query.category_name
    ? req.query.category_name.replace("{AND}", "&").replace("{PLUS}", "+")
    : undefined;
  const item_title = req.query.item_title
    ? req.query.item_title.replace("{AND}", "&")
    : undefined;
  const offer_description = req.query.offer_description;
  var itemIds = req.query.item_ids ? req.query.item_ids.split(",") : undefined;
  var category = req.query.category_id
    ? { category_id: +req.query.category_id }
    : undefined;
  var category_ids = req.query.category_ids
    ? req.query.category_ids.split(",")
    : undefined;

  if (category_name) {
    category = await Category.findOne({
      where: {
        bot_id: +bot_id,
        category_name: {
          [Op.like]: "%" + category_name + "%",
        },
      },
    });
  }
  if (offer_description) {
    var offer = await Offer.findOne({
      where: { offer_description, bot_id },
      include: OfferItem,
    });
    itemIds = offer.bot_designer_offer_items.map((a) => a.item_id);
  } else if (doSingleFeatureExist) {
    const featuresStuff = await sequelize.query(
      "designer_get_features_item_ids :feature_id , :feature_value",
      {
        replacements: {
          feature_id,
          feature_value: "%%",
        },
      }
    );
    // return res.send(featuresStuff[0].map(a => a.item_id))
    itemIds = featuresStuff[0].map((a) => a.item_id);
  }
  const include_item_hide = req.query.include_item_hide ? true : false;

  const description = req.query.description;
  const price_to = +req.query.price_to || 10000000000000;
  const price_from = +req.query.price_from || 0;
  const condition = req.query.condition;
  req.bot_id = bot_id;

  Item.findAll({
    limit,
    offset,
    where: {
      bot_id: +bot_id,
      item_description: {
        [Op.like]: description ? "%" + description + "%" : "%%",
      },
      item_title:
        search_terms.length === 0
          ? {
              [Op.like]: item_title ? "%" + item_title + "%" : "%%",
            }
          : {
              [Op.and]: search_terms.map((a) => {
                return { [Op.like]: `%${a}%` };
              }),
            },
      item_hide: include_item_hide ? { [Op.between]: [false, true, 0] } : false,
      category_id: category?.category_id
        ? category.category_id
        : category_ids
        ? { [Op.in]: category_ids }
        : { [Op.between]: [1, 1000000000000] },

      item_id: itemIds
        ? { [Op.in]: itemIds }
        : { [Op.between]: [1, 1000000000000] },

      item_price: {
        [Op.and]: [{ [Op.gte]: price_from }, { [Op.lte]: price_to }],
      },
      item_condition:
        condition && condition !== "All"
          ? condition
          : { [Op.between]: ["New", "Used"] },
    },
    order: [["item_price", "ASC"]],
    include: [
      { model: ItemOption /*attributes: ["item_option_id"]*/ },
      {
        model: ItemFeature /*attributes: ["item_feature_id"]*/,
      },
    ],
  }).then(async (items) => {
    //FIXME BIG BIG FIXME
    if (include_item_features) {
      const theArr = [];
      items.map((itm) => {
        itm.bot_designer_item_features.map((itmftr) => {
          if (theArr.indexOf(itmftr.feature_id) === -1) {
            theArr.push(itmftr.feature_id);
          }
        });
      });
      const features = await Feature.findAll({
        where: { bot_id, feature_id: { [Op.in]: theArr } },
      });
      const mapped_items = items.map((itm, itmIndex) => {
        if (itm.bot_designer_item_features?.length) {
          var itmNew = itm.bot_designer_item_features.map((itmftr, ftrIndx) => {
            var ftr = features
              .find((b) => b.feature_id === itmftr.feature_id)
              ?.toJSON();
            return {
              ...itmftr.toJSON(),
              ...ftr,
            };
          });
          return {
            ...itm,
            bot_designer_item_features: itmNew,
          };
        } else {
          return itm;
        }
      });
      req.items = mapped_items.map((n) => {
        return {
          ...n.dataValues,
          bot_designer_item_features: undefined,
          bot_designer_item_options: undefined,
          item_features: n.bot_designer_item_features,
          item_options: n.bot_designer_item_options,
          item_icons: n.dataValues?.item_icons?.length
            ? n.dataValues.item_icons.split(",")
            : [],
        };
      });
      return next();
    }
    req.items = items;
    return next();
  });
};

const findItemsFromList = (req, res, next) => {
  const itemsArr = req.body.cart.map((a) => a.item_id);
  req.body.bot_id = req.body.cart[0].bot_id;
  if (!req.body.bot_id) {
    res.send({ message: "provide bot id" });
  }
  Item.findAll({
    where: {
      item_id: { [Op.in]: itemsArr },
    },
  })
    .then((items) => {
      if (items.length) {
        req.items = items;
        return next();
      }
      return res.send({ message: "Items not found" });
    })
    .catch(() => {
      res.send({ message: "an error occured" });
    });
};

const lemmtaizationOne = async (req, res, next) => {
  try {
    const data = { ...req.body };

    const newData = {
      ...data,
      lemmatized_item_title: data?.lemmatized_item_title
        ? (await lemmtaizationLocal(data?.lemmatized_item_title))?.data
            ?.answer || data.question
        : "",
    };

    req.body = newData;

    next();
  } catch (error) {
    console.log(error);
  }
};

const lemmtaizationMany = async (req, res, next) => {
  try {
    const data = [...req.body.items];
    const newData = await Promise.all(
      data.map(async (ent) => {
        return {
          ...ent,
          lemmatized_item_title: ent?.lemmatized_item_title
            ? (await lemmtaizationLocal(ent?.lemmatized_item_title))?.data
                ?.answer || ent.question
            : "",
        };
      })
    );
    req.body.items = newData;
    next();
  } catch (error) {
    console.log(error);
    next()
  }
};

const findItemsByBot = async (req, res, next) => {
  const bot_id = req.query.bot_id;
  Item.findAll({
    where: {
      bot_id: +bot_id,
    },
  }).then((items) => {
    if (!items) {
      return res.status(401).send({ message: "items not found" });
    } else {
      req.items = items;
      return next();
    }
  });
};



module.exports = {
  findItemById,
  findItemByQueryId,
  findItemsByBotId,
  findItemsByQueryBotId,
  preprocessItem,
  postprocessItem,
  findItemsFromList,
  findItemFromBotItems,
  lemmtaizationOne,
  lemmtaizationMany,
  findItemsByBot,
};
