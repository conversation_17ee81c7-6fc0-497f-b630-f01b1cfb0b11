const Weather = require("../Models/Weather");

const findWeatherByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  Weather.findOne({ where: { bot_id } }).then((weather) => {
    if (weather) {
      req.weather = weather;
      return next();
    } else {
      return res.status(404).send({ message: "weather does not exist" });
    }
  });
};

const findWeatherByBotIdNoValidation = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  Weather.findOne({ where: { bot_id } }).then((weather) => {
    req.weather = weather;
    return next();
  });
};

const checkExistance = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  Weather.findOne({ where: { bot_id } }).then((weather) => {
    if (weather) {
      req.weather = weather;
      return res.status(409).send({ message: "weather already exits" });
    } else {
      return next();
    }
  });
};

module.exports = {
  findWeatherByBotIdNoValidation,
  checkExistance,
  findWeatherByBotId,
};
