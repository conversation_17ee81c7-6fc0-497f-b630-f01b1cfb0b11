const OfferItem = require("../Models/Offer.Item");

const findById = (req, res, next) => {
  const item_id = req.body.item_id;
  req.item_id = item_id;
  OfferItem.findOne({ where: { item_id } }).then((item) => {
    if (item) {
      req.item = item;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findByQueryId = (req, res, next) => {
  const item_id = req.query.item_id;
  req.item_id = item_id;
  OfferItem.findOne({ where: { item_id } }).then((item) => {
    if (item) {
      req.item = item;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findByOfferId = (req, res, next) => {
  const offer_id = req.query.offer_id;
  req.offer_id = offer_id;
  if (!offer_id) {
    OfferItem.findAll({ where: { bot_id: req.query.bot_id } }).then(
      (offeritems) => {
        req.offeritems = offeritems;
        return next();
      }
    );
  } else {
    OfferItem.findAll({ where: { offer_id } }).then((offeritems) => {
      req.offeritems = offeritems;
      return next();
    });
  }
};
const findByQueryOfferId = (req, res, next) => {
  const offer_id = req.query.offer_id;
  req.offer_id = offer_id;
  OfferItem.findAll({ where: { offer_id } }).then((offeritems) => {
    req.offeritems = offeritems;
    return next();
  });
};

const findByQueryBotId = (req, res, next) => {
  const bot_id = req.query.bot_id;
  req.bot_id = bot_id;
  OfferItem.findAll({ where: { bot_id } }).then((offeritems) => {
    req.offeritems = offeritems;
    return next();
  });
};

const preprocessAll = (req, res, next) => {
  const data = req.body.offer_items;
  if (!data.length) {
    return res.status(422).send({ messsage: "array should not be empty" });
  }
  req.body.offer_items = data.map((element) => {
    return {
      bot_id: element.bot_id,
      item_id: element.item_id,
      offer_id: element.offer_id,
    };
  });
  return next();
};

module.exports = {
  findById,
  findByQueryId,
  findByOfferId,
  findByQueryOfferId,
  preprocessAll,
  findByQueryBotId,
};
