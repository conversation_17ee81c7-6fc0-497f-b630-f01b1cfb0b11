const Dashboard = require("../Models/Bot.Dashboard");

const findDashboardById = (req, res, next) => {
  const bot_id = req.body.bot_id;
  req.bot_id = bot_id;
  Dashboard.findOne({ where: { bot_id } }).then((dashboard) => {
    if (dashboard) {
      req.dashboard = dashboard;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findDashboardByQueryId = (req, res, next) => {
  const bot_id = req.query.bot_id;
  req.bot_id = bot_id;
  Dashboard.findOne({ where: { bot_id } }).then((dashboard) => {
    if (dashboard) {
      req.dashboard = dashboard;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

module.exports = {
  findDashboardById,
  findDashboardByQueryId,
};
