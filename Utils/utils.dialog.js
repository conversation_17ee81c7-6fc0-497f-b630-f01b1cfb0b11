const Dialog = require("../Models/Dialogs");
const { getFile } = require("../calls/file.call");
const Trigger = require("../Models/Trigger");
const Bot = require("../Models/Bot"); 

const findDialogsdBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  Dialog.findAll({ where: { bot_id } }).then((dialogs) => {
    if (dialogs) {
      req.dialogs = dialogs;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findDialogById = (req, res, next) => {
  const dialog_id = req.body.dialog_id || req.query.dialog_id;
  Dialog.findOne({ where: { dialog_id } })
    .then(async (dialog) => {
      if (dialog) {
        const bot = await Bot.findOne({ where: { bot_id: dialog.bot_id } });
        if (bot) {
          const dialog_config = await getFile(dialog.url);
          dialog.dataValues.user_id = bot.user_id; 
          req.dialog = dialog;
          req.dialog_config = dialog_config;
          return next();
        } else {
          return res.sendStatus(404);
        }
      } else {
        return res.sendStatus(404);
      }
    })
    .catch((error) => {
      console.error(error);
      res.sendStatus(500);
    });
};

const findTriggersV2 = (req, res, next) => {
  const dialog = req.dialog;
  Trigger.findAll({
    where: { dialog_id: dialog.dialog_id },
  })
    .then((triggers) => {
      console.log("--------------id:", dialog.dialog_id);
      if (triggers.length > 0) {
        req.triggers = triggers;
      } else {
        req.triggers = [];
      }

      return next();
    })
    .catch((error) => {
      console.error(error);
      res.sendStatus(500);
    });
};

const findTriggers = (req, res, next) => {
  const dialog = req.dialog;
  Trigger.findAll({
    where: { url: dialog.url },
  })
    .then((triggers) => {
      if (triggers.length > 0) {
        req.triggers = triggers;
      } else {
        req.triggers = [];
      }

      return next();
    })
    .catch((error) => {
      console.error(error);
      res.sendStatus(500);
    });
};

const findDialogByUrl = (req, res, next) => {
  const url = req.body.url || req.query.url;
  Dialog.findOne({ where: { url } }).then(async (dialog) => {
    if (dialog) {
      const dialog_config = await getFile(dialog.url);
      req.dialog = dialog;
      req.dialog_config = dialog_config;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

module.exports = {
  findDialogById,
  findDialogsdBotId,
  findTriggers,
  findDialogByUrl,
  findTriggersV2
};
