const sequelize = require("sequelize");
const LiveChatIntegration = require("../Models/LiveChatIntegration");
const LiveChatIntegrationSession = require("../Models/LiveChatIntegrationSession");


const findAllLiveChatIntegrations = (req, res, next) => {
  LiveChatIntegration.findAll().then((livechatIntegrations) => {
    if (livechatIntegrations) {
      req.livechatIntegrations = livechatIntegrations;
      return next();
    } else {
      return res.status(404).send({ message: "LiveChat Integrations does not exist" });
    }
  });
};


const findLiveChatIntegrationByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  LiveChatIntegration.findOne({ where: { bot_id } }).then((livechatIntegration) => {
    if (livechatIntegration) {
      req.livechatIntegration = livechatIntegration;
      return next();
    } else {
      return res.status(404).send({ message: "LiveChat Integration does not exist" });
    }
  });
};

const findLiveChatIntegrationOneSession = (req, res, next) => {
    const conversation_id = req.body.conversation_id || req.query.conversation_id;
    const chat_id = req.body.chat_id || req.query.chat_id;
    const livechat_integration_id = req.body.livechat_integration_id || req.query.livechat_integration_id;
    if(conversation_id || chat_id || livechat_integration_id){
        LiveChatIntegrationSession.findOne(
            conversation_id ?   { where: { conversation_id } } : livechat_integration_id ? { where: {livechat_integration_id}} : { where: { chat_id } } 
          ).then((livechatIntegrationSession) => {
            if (livechatIntegrationSession) {
              req.livechatIntegrationSession = livechatIntegrationSession;
              return next();
            } else {
              return res.status(404).send({ message: "LiveChat Integration Session does not exist" });
            }
          });
    }
    else{
        return res.status(422).send({ message:  "please provide chat_id or conversation_id" });
    }
};

const preventDuplicateLiveChatIntegration = async (req, res, next) => {
  const {body } = req; 
  const { bot_id, ...rest } = body;
  try {
    for (const key in rest) {
      if (rest.hasOwnProperty(key)) {
        const value = rest[key];

        const existingRecord = await LiveChatIntegration.findOne({
          where: { [key]: value, bot_id: { [sequelize.Op.ne]: body.bot_id } },
        });

        if (existingRecord) {
          return res
            .status(400)
            .json({ message: `${key} is used in another bot, Please don't use same account for different bots` });
        }
      }
    }
    next();
  } catch (error) {
    res.status(500).json({ message: "Internal Server Error" + error });
  }
};


module.exports = {
    findLiveChatIntegrationByBotId,
    findLiveChatIntegrationOneSession,
    findAllLiveChatIntegrations,
    preventDuplicateLiveChatIntegration
};
