const WordScheme = require("../Models/WordScheme");

const findById = (req, res, next) => {
  const word_id = req.body.word_id || req.query.word_id;
  WordScheme.findOne({ where: { word_id } }).then((intent) => {
    if (wordscheme) {
      req.wordscheme = wordscheme;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findAll = (req, res, next) => {
  WordScheme.findAll({ where: {} }).then((wordschemes) => {
    req.wordschemes = wordschemes;
    return next();
  });
};

module.exports = {
  findAll,
  findById,
};
