const ReservationTable = require("../Models/Reservation.Table");
const sequelize = require("../db");

const findTablesByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  req.bot_id = bot_id;
  ReservationTable.findAll({ where: { bot_id } }).then((tables) => {
    req.tables = tables;
    return next();
  });
};

const findTableById = (req, res, next) => {
  const table_id = req.body.table_id || req.query.table_id;
  req.table_id = table_id;
  ReservationTable.findOne({ where: { table_id } }).then((table) => {
    if (table) {
      req.table = table;
      return next();
    } else {
      res.send({ message: "tables not found" });
    }
  });
};

const findTables = (req, res, next) => {
  const room_id = req.body.room_id || +req.query.room_id;
  const bot_id = req.body.bot_id || +req.query.bot_id;
  req.room_id = room_id;
  if (room_id) {
    ReservationTable.findAll({ where: { room_id } }).then((tables) => {
      if (tables) {
        req.tables = tables;
        return next();
      } else {
        res.send({ message: "tables not found" });
      }
    });
  } else if (bot_id) {
    ReservationTable.findAll({ where: { bot_id } }).then((tables) => {
      if (tables) {
        req.tables = tables;
        return next();
      } else {
        res.send({ message: "tables not found" });
      }
    });
  }
};


const checkAvailability = (req, res, next) => {
  const data = req.body;
  const tables = req.tables;
  sequelize
    .query(
      "designer_get_table_orders :botID , :roomID , :guests , :date_from , :date_to",
      {
        replacements: {
          botID: data.bot_id,
          roomID: data.room_id,
          guests: data.guests,
          date_from: data.from,
          date_to: data.to,
        },
      }
    )
    .then((busyTablesData) => {
      const busy = busyTablesData[0];
      res.send({
        available: tables.filter(
          (a) => busy.findIndex((b) => b.table_id === a.table_id) === -1
        ),
        busy,
        tables,
      });
    });
};

module.exports = {
  findTablesByBotId,
  findTableById,
  findTables,
  checkAvailability,
};
