const ReportPlugin = require("../Models/ReportPlugin");

const findReportPluginByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  req.bot_id = bot_id;
  ReportPlugin.findOne({ where: { bot_id } }).then((reportplugin) => {
    req.reportplugin = reportplugin;
    if (!req.reportplugin) {
      return res.status(404).send({ message: "Plugin Not Found" });
    }
    return next();
  });
};

const checkExistance = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  ReportPlugin.findOne({ where: { bot_id } }).then((reportplugin) => {
    if (reportplugin) {
      req.reportplugin = reportplugin;
      return res.status(409).send({ message: "Report Plugin already exits" });
    } else {
      return next();
    }
  });
};

module.exports = { findReportPluginByBotId, checkExistance };
