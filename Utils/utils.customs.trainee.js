const Customs_Trainee = require("../Models/Customs.Trainee");
const messages = require("../Messages/message.user");


const findStudentById = (req, res, next) => {
  const trainee_id = req.body.trainee_id || req.query.trainee_id;
  Customs_Trainee.findOne({ where: { trainee_id  } }).then((customs_trainee) => {
    if (customs_trainee) {
      req.customs_trainee = customs_trainee;
      return next()
    } else {
      return res.status(401).send(messages.notFound);
    }
  });
};

const findAll = (req, res, next) => {
    Customs_Trainee.findAll({
      where: { bot_id: req.query.bot_id || req.body.bot_id },
    }).then((customs_trainee) => {
      if (customs_trainee) {
        req.customs_trainee = customs_trainee;
        return next();
      }
    });
  };


module.exports = {
findStudentById,
findAll
};
