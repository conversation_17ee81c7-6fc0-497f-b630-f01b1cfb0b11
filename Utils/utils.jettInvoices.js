const { QueryTypes } = require("sequelize");
const sequelize = require("../db");
const JettInvoice = require("../Models/Jett.Invoice");
const JettTicketsType = require("../Models/Jett.TicketsType");
const JettBookingTicket = require("../Models/Jett.BookingTicket");
const JettKHBMasterTicket = require("../Models/Jett.KHBMasterTicket");
const JettKHBSlaveTicket = require("../Models/Jett.KHBSlaveTicket");
const JettCardRecharge = require("../Models/Jett.CardRecharge");
const JettTracks = require("../Models/Jett.Tracks");
const { Op } = require("sequelize");

const findInvoice = (req, res, next) => {
  const invoice_id = req.query.invoice_id;
  // get with related tickets
  JettInvoice.findOne({
    where: { invoice_id },
    include: {
      model: JettTicketsType,
      as: "type",
    },
  }).then(async (invoice) => {
    if (invoice) {
      const tickets = await invoice.getInvoiceDetails();

      req.invoice = {
        ...invoice.dataValues,
        tickets,
      };
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findAllInvoices = (req, res, next) => {
  // find all invoices with tickets based on type for each one
  JettInvoice.findAll({
    include: [
      {
        model: JettTicketsType,
        as: "type",
      },
      {
        model: JettBookingTicket,
        as: "bookingTickets",
        required: false,
      },
      {
        model: JettKHBMasterTicket,
        as: "khbMasterTickets",
        required: false,
        include: [
          {
            model: JettKHBSlaveTicket,
            as: "slaveTickets",
          },
        ],
      },
      {
        model: JettCardRecharge,
        as: "cardRecharges",
        required: false,
      },
    ],
  }).then((invoices) => {
    const allInvoices = invoices.map((invoice) => {
      const { type, bookingTickets, khbMasterTickets, cardRecharges } = invoice;
      let tickets = [];

      switch (type.type) {
        case "Booking":
          tickets = bookingTickets;
          break;
        case "KHB":
          tickets = khbMasterTickets.map((master) => ({
            ...master.toJSON(),
            // slaves: master.slaveTickets,
          }));
          break;
        case "CardRecharge":
          tickets = cardRecharges;
          break;
      }

      return {
        ...invoice.toJsonData(),
        tickets,
      };
    });

    req.invoices = allInvoices;
    return next();
  });
};

const findUnpaidInvoices = async (req, res, next) => {
  try {
    const invoices = await JettInvoice.findAll({
      where: {
        status: ["deleted"],
        // collected_data_url not null
        collected_data_url: {
          [Op.not]: null,
        },
        entity_id: 1
      },

    });  
    req.invoices = invoices;
    return next();
  } catch (error) {
    console.error(error);
  }
};

const findPaidInvoices = async (req, res, next) => {
  try {
    const invoices = await JettInvoice.findAll({
      where: {
        status: ["paid", "refunded", "incomplete"],
      },
      include: [
        {
          model: JettTicketsType,
          as: "type",
        },
        {
          model: JettBookingTicket,
          as: "bookingTickets",
          required: false,
        },
        {
          model: JettKHBMasterTicket,
          as: "khbMasterTickets",
          required: false,
          include: [
            {
              model: JettKHBSlaveTicket,
              as: "slaveTickets",
            },
          ],
        },
        {
          model: JettCardRecharge,
          as: "cardRecharges",
          required: false,
        },
      ],
    });

    // Process the data
    const allInvoices = invoices.map((invoice) => {
      const { type, bookingTickets, khbMasterTickets, cardRecharges } = invoice;
      let tickets = [];
      let totalPassengers = 0;
      switch (type.type) {
        case "Booking":
          tickets = bookingTickets;
          totalPassengers = tickets?.length;
          break;
        case "KHB":
          tickets = khbMasterTickets.map((master) => ({
            ...master.toJSON(),
            // slaves: master.slaveTickets,
          }));
          totalPassengers =
            +tickets[0]?.extra_adult_count + +tickets[0]?.extra_child_count + 1;
          break;
        case "CardRecharge":
          tickets = cardRecharges;
          break;
      }

      return {
        ...invoice.toJsonData(),
        tickets,
        totalPassengers,
      };
    });

    req.invoices = allInvoices;
    return next();
  } catch (error) {
    return next(error);
  }
};

const findTrack = (req, res, next) => {
  const jett_track_id = req.query.jett_track_id;
  JettTracks.findOne({ where: { jett_track_id } }).then((track) => {
    if (track) {
      req.track = track;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findTracks = async (req, res, next) => {
  try {
    const tracks = await JettTracks.findAll({
      where: {
        enabled: true,
      },
    });

    req.tracks = tracks;
    return next();
  } catch (error) {
    return next(error);
  }
};

module.exports = {
  findInvoice,
  findAllInvoices,
  findPaidInvoices,
  findTrack,
  findTracks,
  findUnpaidInvoices
};
