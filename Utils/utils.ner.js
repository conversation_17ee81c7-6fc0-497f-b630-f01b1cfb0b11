const NER = require("../Models/NER");

const findById = (req, res, next) => {
  const ner_id = req.body.ner_id || req.query.ner_id;
  req.ner_id = ner_id;
  NER.findOne({ where: { ner_id } }).then((ner) => {
    if (ner) {
      req.ner = ner;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findAll = (req, res, next) => {
  NER.findAll({ where: {} }).then((ners) => {
    req.ners = ners;
    return next();
  });
};

module.exports = {
  findAll,
  findById,
};
