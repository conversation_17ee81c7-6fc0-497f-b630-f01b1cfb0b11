const Custom_Payment = require("../Models/Custom.Payment");
const messages = require("../Messages/message.user");
const sequelize = require("../db");


const findPaymentByID = (req, res, next) => {
    const bot_id = req.query.bot_id || req.body.bot_id;
    const payment_id = req.body.payment_id || req.query.payment_id ;
    Custom_Payment.findOne({ where: { bot_id, payment_id } }).then(
      (custom_payment) => {
        if (!custom_payment) {
          return res.status(401).send({ message: " not found" });
        }
        req.custom_payment = custom_payment;
        return next();
      }
    );
  };


  const findPayingTrainees= async (req, res, next) => {
    const bot_id = req.body.bot_id || req.query.bot_id;
    const data = await sequelize.query("get_paying_trainees :botID", {
      replacements: {
        botID: bot_id,
      },
    });
    return res.send(data[0]);
  };

  const findNonPayingTrainees= async (req, res, next) => {
    const bot_id = req.body.bot_id || req.query.bot_id;
    const data = await sequelize.query("designer_custom_non_paying_trainees :botID", {
      replacements: {
        botID: bot_id,
      },
    });
    return res.send(data[0]);
  };

  
  const findPaymentInfo = async (req, res, next) => {
    const bot_id = req.body.bot_id || req.query.bot_id;
    const data = await sequelize.query("get_info_payments :botID", {
      replacements: {
        botID: bot_id,
      },
    });
    return res.send(data[0]);
  };


  module.exports = {
    findPaymentByID,
    findNonPayingTrainees,
    findPayingTrainees,
    findPaymentInfo
};
