const ItemOption = require("../Models/Item.Option");

const preprocess = (req, res, next) => {
  req.body.option = JSON.stringify(req.body.option);
  next();
};

const findOptionById = (req, res, next) => {
  const item_option_id = req.body.item_option_id;
  ItemOption.findOne({ where: { item_option_id } }).then((itemoption) => {
    if (itemoption) {
      req.itemoption = itemoption;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findOptionByItemId = (req, res, next) => {
  const item_id = req.body.item_id || req.query.item_id;
  ItemOption.findAll({ where: { item_id } }).then((itemoptions) => {
    req.itemoptions = itemoptions;
    return next();
  });
};

module.exports = {
  findOptionByItemId,
  findOptionById,
  preprocess,
};
