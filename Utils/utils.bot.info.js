const BotInfo = require("../Models/Bot.Info");
const sequelize = require("../db");

const findBotBotInfoById = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  req.bot_id = bot_id;
  BotInfo.findOne({ where: { bot_id } }).then((botinfo) => {
    if (botinfo) {
      req.botinfo = botinfo;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const checkBotInfoExistance = (req, res, next) => {
  const bot_id = req.body.bot_id;
  req.bot_id = bot_id;
  BotInfo.findOne({ where: { bot_id } }).then((botinfo) => {
    if (botinfo) {
      return res.status(409).send({ message: "BotInfo already exist" });
    } else {
      return next();
    }
  });
};

module.exports = {
  findBotBotInfoById,
  checkBotInfoExistance,
};
