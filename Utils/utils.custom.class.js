const Customs_Class = require("../Models/Custom.Class");
const messages = require("../Messages/message.user");



const findClassById = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  const class_id = req.body.class_id || req.query.class_id;
  Customs_Class.findOne({ where: { bot_id ,class_id} }).then((custom_class) => {
    if (custom_class) {
      req.custom_class = custom_class;
      return next();
    } else {
      return res.status(401).send(messages.notFound);
    }
  });
};


const findAll = (req, res, next) => {
    Customs_Class.findAll({
      where: { bot_id: req.query.bot_id },
    }).then((custom_class) => {
      if (custom_class) {
        req.custom_class = custom_class;
        return next();
      }
    });
  };

module.exports = {
    findClassById,
    findAll
};
