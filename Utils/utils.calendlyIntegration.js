const CalendlyIntegration = require("../Models/CalendlyIntegration");

const findCalendlyIntegrationByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  CalendlyIntegration.findOne({ where: { bot_id } }).then(
    (calendlyIntegration) => {
      if (calendlyIntegration) {
        req.calendlyIntegration = calendlyIntegration;
        return next();
      } else {
        return res
          .status(404)
          .send({ message: "Calendly Integration does not exist" });
      }
    }
  );
};

module.exports = {
  findCalendlyIntegrationByBotId,
};
