const Trigger = require("../Models/Trigger");
const sequelize = require("../db");
const { Op } = require("sequelize");
const User = require("../Models/User");

const findTriggerById = (req, res, next) => {
  const trigger_id = req.body.trigger_id;
  req.trigger_id = trigger_id;
  Trigger.findOne({ where: { trigger_id } }).then((trigger) => {
    if (trigger) {
      req.trigger = trigger;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findTriggerByQueryId = (req, res, next) => {
  const trigger_id = req.query.trigger_id;
  req.trigger_id = trigger_id;
  Trigger.findOne({ where: { trigger_id } }).then((trigger) => {
    if (trigger) {
      req.trigger = trigger;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findManyTriggers = (req, res, next) => {
  const data = req.body;
  Trigger.findAll({
    where: {
      trigger_id: { [Op.in]: data.map((a) => a.trigger_id) },
    },
  }).then((triggers) => {
    req.triggers = triggers;
    return next();
  });
};

const findBotTriggersByName = (req, res, next) => {
  const trigger_name = req.query.trigger_name;
  req.trigger_name = trigger_name;
  Trigger.findAll({ where: { trigger_name } }).then((triggers) => {
    req.triggers = triggers;
    return next();
  });
};

const findBotTriggersById = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id || req.body[0].bot_id;
  req.bot_id = bot_id;
  sequelize
    .query("designer_triggers_get :botID", {
      replacements: {
        botID: bot_id,
      },
    })
    .then((data) => {
      req.triggers = data[0];
      return next();
    });
};

const findBotTriggersByQueryId = (req, res, next) => {
  const bot_id = req.query.bot_id;
  req.bot_id = bot_id;
  sequelize
    .query("designer_triggers_get :botID", {
      replacements: {
        botID: bot_id,
      },
    })
    .then((data) => {
      req.triggers = data[0];
      return next();
    });
};

const findBotTriggersFirst = (req, res, next) => {
  const bot_id = req.query.bot_id;
  req.bot_id = bot_id;
  sequelize
    .query("designer_triggers_first :botID", {
      replacements: {
        botID: bot_id,
      },
    })
    .then((data) => {
      req.triggers = data[0];
      return next();
    });
};

const checkExistance = (req,res,next) => {
  const user_id= req.body.user_id;
  User.findOne({ where: { user_id }}).then((user) => {
    if (!user) {
      return res.status(404).send({ message: "User Not Found" });
    }
    return next();
    
  });
}
const findTriggersLogs = (req, res, next) => {
  const bot_id = +req.query.bot_id;
  sequelize
    .query("bot_designer_get_triggers_logs :botID", {
      replacements: { botID: bot_id },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const findBotTriggersAlternative = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const trigger = req.query.trigger;
  const url = req.query.url;
  req.bot_id = bot_id;
  sequelize
    .query("designer_triggers_alternative :botID , :userTrigger , :userUrl", {
      replacements: {
        botID: bot_id,
        userUrl: url,
        userTrigger: trigger,
      },
    })
    .then((data) => {
      req.triggers = data[0];
      return next();
    });
};

module.exports = {
  findTriggerById,
  findTriggerByQueryId,
  findBotTriggersById,
  findBotTriggersByQueryId,
  findBotTriggersByName,
  findManyTriggers,
  findBotTriggersFirst,
  findBotTriggersAlternative,
  checkExistance,
  findTriggersLogs
};
