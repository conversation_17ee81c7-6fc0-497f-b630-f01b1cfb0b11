const BadWord = require("../Models/BadWord");

const sequelize = require("../db");

const findBadWordById = (req, res, next) => {
  const bad_word_id = req.body.bad_word_id || req.query.bad_word_id;
  BadWord.findOne({ where: { bad_word_id } }).then((badWord) => {
    if (badWord) {
      req.badWord = badWord;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findBadWordsByBotId = (req, res, next) => {
    const bot_id = req?.body?.bot_id || req?.query?.bot_id || req?.body?.[0]?.bot_id;
    BadWord.findAll({ where: { bot_id } }).then((badWords) => {
        if (badWords) {
            req.badWords = badWords;
            return next();
        } else {
            return res.sendStatus(404);
        }
    });
};

module.exports = {
    findBadWordById,
    findBadWordsByBotId,
};
