const sequelize = require("../db");
const ItemFeature = require("../Models/Item.Feature");
const { Op } = require("sequelize");

var isoDateRegExp = new RegExp(
  /(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))/
);

function isISODate(str) {
  return isoDateRegExp.test(str);
}

const preprocess = (req, res, next) => {
  const feature_value = req.body.feature_value;
  const feature_type = req.body.feature_type || req.feature.feature_type;
  const feature_types = ["Date", "Number", "String"];
  if (feature_type === feature_types[0]) {
    if (isISODate(feature_value)) {
      return next();
    } else {
      res.send({
        message: "invalid input, the feature_value is not a ISO Date",
      });
    }
  } else if (feature_type === feature_types[1]) {
    if (!isNaN(feature_value)) {
      req.body.feature_value = `${feature_value}`;
      return next();
    } else {
      res.send({
        message: "invalid input, the feature_value is not a Number",
      });
    }
  } else {
    return next();
  }
};

const postprocess = (req, res, next) => {
  const feature_value = req.body.feature_value;
  const feature_type = req.feature.feature_type;
  const feature_types = ["Date", "Number", "String"];
  if (feature_type === feature_types[0]) {
    if (isISODate(feature_value)) {
      return next();
    } else {
      res.send({
        message: "invalid input, the feature_value is not a ISO Date",
      });
    }
  } else if (feature_type === feature_types[1]) {
    if (!isNaN(feature_value)) {
      req.itemfeature.feature_value = +feature_value;
      return next();
    } else {
      res.send({
        message: "invalid input, the feature_value is not a Number",
      });
    }
  } else {
    return next();
  }
};

const parseAll = (req, res, next) => {
  const itemfeatures = req.itemfeatures;
  req.itemfeatures = itemfeatures.map((itemfeature) => {
    if (itemfeature.feature_type === "Number") {
      return { ...itemfeature, feature_value: +itemfeature.feature_value };
    } else {
      return { ...itemfeature };
    }
  });
  return next();
};
const parseOne = (req, res, next) => {
  const itemfeature = req.itemfeature;
  const feature_type = req.body.feature_type || req.feature.feature_type;
  if (feature_type === "Number") {
    itemfeature.feature_value = +itemfeature.feature_value;
  } else {
  }
  req.itemfeatures = itemfeature;
  return next();
};

const findById = (req, res, next) => {
  const item_feature_id = req.body.item_feature_id || req.query.item_feature_id;
  ItemFeature.findOne({ where: { item_feature_id } }).then((itemfeature) => {
    if (itemfeature) {
      req.itemfeature = itemfeature;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findAll = (req, res, next) => {
  const item_id = req.body.item_id || +req.query.item_id;
  const bot_id = req.body.bot_id || +req.query.bot_id;
  if (item_id) {
    sequelize
      .query("designer_get_item_features :item_id", {
        replacements: { item_id },
      })
      .then((data) => {
        req.itemfeatures = data[0];
        return next();
      });
  } else if (bot_id) {
    sequelize
      .query("designer_get_item_features_botID :bot_id", {
        replacements: { bot_id },
      })
      .then((data) => {
        req.itemfeatures = data[0];
        return next();
      });
  } else {
    return res.send({ message: "item nor bot were provided" });
  }
};

const findByFeatureCriteria = (req, res, next) => {
  const bot_id = +req.query.bot_id;
  const feature_id = req.query.feature_id;

  ItemFeature.findAll({
    where: {
      bot_id,
      feature_id: feature_id
        ? feature_id
        : { [Op.between]: [1, 1000000000000] },
    },
  }).then((itemfeatures) => {
    req.itemfeatures = itemfeatures.map((a) => a.toJSON());
    return next();
  });
};

const findAllByBot = (req, res, next) => {
  const bot_id = req.query.bot_id;
  ItemFeature.findAll({
    where: {
      bot_id: +bot_id,
    },
  }).then((itemfeatures) => {
    if (!itemfeatures) {
      return res.status(401).send({ message: "itemfeatures not found" });
    } else {
      req.itemfeatures = itemfeatures;
      return next();
    }
  });
};

module.exports = {
  findAll,
  findById,
  preprocess,
  parseAll,
  parseOne,
  postprocess,
  findByFeatureCriteria,
  findAllByBot,
};
