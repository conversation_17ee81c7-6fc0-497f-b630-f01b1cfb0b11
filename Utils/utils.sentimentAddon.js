const SentimentAddon = require("../Models/SentimentAddon");

const findByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  SentimentAddon.findOne({ where: { bot_id } }).then((sentimentAddon) => {
    if (sentimentAddon) {
      req.sentimentAddon = sentimentAddon;
      return next();
    } else {
      return res
        .status(404)
        .send({ message: "Sentiment Addon does not exist" });
    }
  });
};

module.exports = {
  findByBotId,
};
