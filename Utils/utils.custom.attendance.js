const Custom_Attendance = require("../Models/Custom.Attendance");
const messages = require("../Messages/message.user");
const sequelize = require("../db");


const findAttendaceByID = (req, res, next) => {
    const bot_id = req.query.bot_id || req.body.bot_id;
    const trainee_id = req.body.trainee_id || req.query.trainee_id ;
    const class_id = req.body.class_id || req.query.class_id ;
    Custom_Attendance.findOne({ where: { bot_id, class_id,trainee_id } }).then(
      (custom_attendance) => {
        
        if (!custom_attendance) {
          return res.status(401).send({ message: " not found" });
        }
        req.custom_attendance = custom_attendance;
        return next();
      }
    );
  };

  const findAttendedTrainees= async (req, res) => {
    const bot_id =  req.query.bot_id;
    const date_year = req.query.date_year;
  const date_month = req.query.date_month;
  const date_day = req.query.date_day
    const className = req.query.className;
    const data = await sequelize.query("designer_custom_get_attended_trainees :bot_id , :className , :date_year , :date_month , :date_day", {
      replacements: {
        bot_id: bot_id,
        className:className,
        date_year:date_year,
        date_month:date_month,
        date_day:date_day
      },

    });
    console.log(data[0],"fsefe")
    return res.send(data[0]);
  };
  
  const findNonAttendedTrainees= async (req, res, next) => {
    const bot_id =  req.query.bot_id;
    const date_year = req.query.date_year;
  const date_month = req.query.date_month;
  const date_day = req.query.date_day
    const className = req.query.className;
    const data = await sequelize.query("designer_custom_non_attended_trainees :bot_id , :className , :date_year , :date_month , :date_day", {
      replacements: {
        bot_id: bot_id,
        className:className,
        date_year:date_year,
        date_month:date_month,
        date_day:date_day
      },
    });
    return res.send(data[0]);
  };


  const findAttendanceInfo = async (req, res, next) => {
    const bot_id = req.body.bot_id || req.query.bot_id;
    const data = await sequelize.query("get_info_attendance :botID", {
      replacements: {
        botID: bot_id,
      },
    });
    return res.send(data[0]);
  };

  module.exports = {
    findAttendaceByID,
    findAttendedTrainees,
    findNonAttendedTrainees,
    findAttendanceInfo
};
