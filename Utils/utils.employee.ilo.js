const Employee = require("../Models/Employee.ILO");

const findById = (req, res, next) => {
  const employee_id = req.body.employee_id || req.query.employee_id;
  Employee.findOne({ where: { employee_id } }).then((employee) => {
    if (employee) {
      req.employee = employee;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findAll = (req, res, next) => {
    Employee.findAll({ where: {} }).then((employees) => {
    req.employees = employees;
    return next();
  });
};

module.exports = {
  findAll,
  findById,
};
