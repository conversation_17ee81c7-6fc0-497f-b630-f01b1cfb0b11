const Entity = require("../Models/NamedEntity");

const findEntityById = (req, res, next) => {
  const entity_id = req.body.entity_id;
  req.entity_id = entity_id;
  Entity.findOne({ where: { entity_id } }).then((entity) => {
    if (entity) {
      req.entity = entity;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findEntityByQueryId = (req, res, next) => {
  const entity_id = req.query.entity_id;
  req.entity_id = entity_id;
  Entity.findOne({ where: { entity_id } }).then((entity) => {
    if (entity) {
      req.entity = entity;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findEntities = (req, res, next) => {
  Entity.findAll({ where: {} }).then((entities) => {
    req.entities = entities;
    return next();
  });
};

module.exports = {
  findEntityById,
  findEntityByQueryId,
  findEntities,
};
