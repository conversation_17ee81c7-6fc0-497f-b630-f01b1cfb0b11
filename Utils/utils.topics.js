const FAQ = require("../Models/FAQ");
const Topic = require("../Models/Topic");
const Trigger = require("../Models/Trigger");
const { lemmtaizationLocal } = require("../helper/helper");

const find = (req, res, next) => {
  const topic_id = req.body.topic_id || req.query.topic_id;
  Topic.findOne({ where: { topic_id } }).then((topic) => {
    if (!topic) {
      return res.status(401).send({ message: "topic not found" });
    } else {
      req.topic = topic;
      return next();
    }
  });
};

const findMany = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  Topic.findAll({ where: { bot_id } }).then((topics) => {
    if (!topics) {
      return res.status(401).send({ message: "topics not found" });
    } else {
      req.topics = topics;
      return next();
    }
  });
};

const isArabic = (str) => {
  const result = /[\u0600-\u06FF\u0750-\u077F]/.test(str)
  return result}

const lemmatizeKeywords = async (req, res, next) => {
  const data = { ...req.body };
  const lemmatized_keywords = await Promise.all(
   data.keywords?.map(async (keyword) => {
    const answer = isArabic(keyword) ? await lemmtaizationLocal(keyword): null;
    return answer?.data?.answer || keyword;
  })
    );
  req.body.lemmatized_keywords = lemmatized_keywords;
  next();
};

const findTopicKB = async (req, res) => {
  const topic_id = req.body.topic_id || req.query.topic_id;
  const topic = await Topic.findOne({ where: { topic_id } });
  const faqs = await FAQ.findAll({ where: { topic_id } });
  const triggers = await Trigger.findAll({ where: { topic_id } });

  return res.send({ topic, faqs, triggers });
};

module.exports = {
  find,
  findMany,
  lemmatizeKeywords,
  findTopicKB
};
