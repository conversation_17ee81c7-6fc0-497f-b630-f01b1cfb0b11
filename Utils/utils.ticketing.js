const Bot = require("../Models/Bot");
const Editor = require("../Models/Editor");
const TicketingAgentDepartment = require("../Models/TicketingAgentDepartment");
const TicketingDepartment = require("../Models/TicketingDepartment");
const TicketingComment = require("../Models/TicketingComment");
const TicketingCommentAttachment = require("../Models/TicketingCommentAttachment");
const TicketingSupportTicket = require("../Models/TicketingSupportTicket");
const TicketingSupportAgent = require("../Models/TicketingSupportAgent");
const TicketingCategory = require("../Models/TicketingCategory");
const TicketingIntegration = require("../Models/TicketingIntegration");
const { Op, or } = require("sequelize");
const TicketingRating = require("../Models/TicketingRating");
const sequelize = require("../db");

const findTicketByUUID = async (req, res, next) => {
  const ticket_uuid =
    req.body.ticket_uuid || req.query.ticket_uuid || req.params.ticket_uuid;

  if (!ticket_uuid) {
    return res.status(400).send({ error: "Ticket UUID is required" });
  }

  try {
    const ticket = await TicketingSupportTicket.findOne({
      where: {
        ticket_uuid,
      },
      include: [
        {
          model: TicketingComment,
          as: "comments",
          include: [
            {
              model: TicketingCommentAttachment,
              as: "attachments",
            },
            {
              model: TicketingSupportAgent,
              as: "support_agent",
              include: [
                {
                  model: Editor,
                  as: "editor",
                },
              ],
            },
          ],
        },
        {
          model: Bot,
          as: "bot",
        },
      ],
    });

    if (!ticket) {
      return res.status(201).send({ error: "Ticket not found" });
    }

    req.ticket = ticket;

    return next();
  } catch (error) {
    console.log(error);
    return res.status(500).send({ error: "Error finding ticket" });
  }
};

const findAllTicketComments = async (req, res, next) => {
  const ticket_id = req.body.ticket_id || req.query.ticket_id;

  if (!ticket_id) {
    return res.status(400).send({ error: "Ticket ID is required" });
  }

  try {
    const comments = await TicketingComment.findAll({
      where: {
        ticket_id,
      },
      include: [
        {
          model: TicketingCommentAttachment,
          as: "attachments",
        },
        {
          model: TicketingSupportAgent,
          as: "support_agent",
          include: [
            {
              model: Editor,
              as: "editor",
            },
          ],
        },
      ],
    });

    req.comments = comments;

    return next();
  } catch (error) {
    console.log(error);
    return res.status(500).send({ error: "Error finding comments" });
  }
};

const findSupportAgentByEditor = (req, res, next) => {
  const editor = req.editor;
  if (!editor) {
    return res.status(200).json({ error: "Editor not found" });
  }

  TicketingSupportAgent.findOne({
    where: {
      editor_id: editor.editor_id,
    },
    include: [
      {
        model: TicketingDepartment,
        as: "departments",
        through: { attributes: [] },
      },
    ],
  })
    .then((agent) => {
      if (agent) {
        const agentData = {
          ...agent.dataValues,
          user: req.user,
          bot: req.bot,
        };
        req.supportAgent = agentData;
        next();
      } else {
        res.status(404).json({ error: "Agent not found" });
      }
    })
    .catch((error) => {
      res.status(500).json({ error: error.message });
    });
};

const findTicketsByAgent = async (req, res, next) => {
  const { support_agent_id } = req.params;

  if (!support_agent_id) {
    return res.status(400).send({ error: "Support Agent ID is required" });
  }

  try {
    const tickets = await TicketingSupportTicket.findAll({
      where: {
        support_agent_id,
      },
      include: [
        {
          model: TicketingComment,
          as: "comments",
          include: [
            {
              model: TicketingCommentAttachment,
              as: "attachments",
            },
            {
              model: TicketingSupportAgent,
              as: "support_agent",
              include: [
                {
                  model: Editor,
                  as: "editor",
                },
              ],
            },
          ],
        },
        {
          model: Bot,
          as: "bot",
        },
      ],
    });

    req.tickets = tickets;

    return next();
  } catch (error) {
    console.log(error);
    return res.status(500).send({ error: "Error finding tickets" });
  }
};

const findTicketsByAgentDepartments = async (req, res, next) => {
  const { support_agent_id } = req.params;

  if (!support_agent_id) {
    return res.status(400).send({ error: "Support Agent ID is required" });
  }

  try {
    const agent = await TicketingSupportAgent.findOne({
      where: {
        support_agent_id,
      },
      include: [
        {
          model: TicketingDepartment,
          as: "departments",
          through: { attributes: [] },
          include: {
            model: TicketingCategory,
            as: "categories",
            attributes: ["department_category_id"],
          },
        },
      ],
    });

    if (!agent) {
      return res.status(404).send({ error: "Agent not found" });
    }

    // Flatten categories from departments
    const categoryIds = agent.departments.flatMap(department =>
      department.categories.map(category => category.department_category_id)
    );

    const tickets = await TicketingSupportTicket.findAll({
      where: {
        category_id: categoryIds,
        support_agent_id: {
          [Op.or]: {
            [Op.eq]: support_agent_id,
            [Op.is]: null,
          },
        },
      },
      include: [
        {
          model: TicketingComment,
          as: "comments",
        },
        {
          model: TicketingCategory,
          as: "category",
        },
      ],
    });

    req.tickets = tickets.map((ticket) => {
      const ticketData = ticket.toJSON();
      ticketData.isLastCommentByCustomer = ticketData.comments?.length
        ? ticketData.comments[ticketData.comments.length - 1].sender === "customer"
        : false;
      return ticketData;
    });
    req.categoryIds = categoryIds;
    return next();
  } catch (error) {
    console.log(error);
    return res.status(500).send({ error: "Error finding tickets" });
  }
};


const findIntegrationByBotId = async (req, res, next) => {
  const { bot_id } = req.query;

  if (!bot_id) {
    return res.status(400).send({ error: "Bot ID is required" });
  }

  try {
    const integration = await TicketingIntegration.findOne({
      where: {
        bot_id,
      },
    });

    if (!integration) {
      return res.status(404).send({ error: "Integration not found" });
    }

    req.integration = integration;

    return next();
  } catch (error) {
    console.log(error);
    return res.status(500).send({ error: "Error finding integration" });
  }
}
const findCategoriesFromDepartment = async (req, res, next) => {
  const { bot_id } = req.query;
  const ticket_integration = await TicketingIntegration.findOne({
    where: { bot_id }
  });
  if(!ticket_integration){
    return res.status(400).send({ error: "Ticket Integration doesn't exist" });
  }
  const departments = await TicketingDepartment.findAll({
    where: {
      [Op.or]: [
        { ticketing_integration_id: ticket_integration.ticketing_integration_id },
        { is_native: true }
      ]
    },
    include: {
      model: TicketingCategory,
      as: 'categories',
      attributes: ['department_category_id', 'category', 'category_ar'],
    }
  });

  if (departments.length === 0) {
    return res.status(400).send({ error: "There are no Departments" });
  }

  const categoryList = departments.flatMap(department =>
    department.categories.map(category => ({
      category_id: category.department_category_id,
      en: category.category,
      ar: category.category_ar,
    }))
  );

  if (categoryList.length === 0) {
    return res.status(400).send({ error: "There are no Categories" });
  }

  req.categories = categoryList;
  next();
};



const findDepartmentsByBotId = async (req, res, next) =>{
  const {bot_id} = req.query;
  const ticket_integration = await TicketingIntegration.findOne({
    where: { bot_id }
  });
  if(!ticket_integration){
    return res.status(400).send({ error: "Ticket Integration doesn't exist" });
  }
  const departments = await TicketingDepartment.findAll({
    where: {
      [Op.or]: [
        { ticketing_integration_id: ticket_integration.ticketing_integration_id },
        { is_native: true }
      ]
    }
  });
  const departmentList = departments.map((a, i) => ({
    department_id: a.dataValues.department_id,
    en: a.dataValues.department,
    ar: a.dataValues.department_ar,
    is_native: a.dataValues.is_native,
  }));
  req.departments = departmentList;
  return next();
}

const createIntegration = async (req, res, next) => {
  try {
    const { bot_id, time_zone } = req.body;
    const integration = await TicketingIntegration.create({
      bot_id,
      status: "active",
      time_zone: time_zone  || "Asia/Amman"
    });
    return res.send(integration);

  } catch (error) {
    next(error);
  }
};


const updateIntegration = async (req, res, next) => {
  try {
    const { bot_id } = req.body;
    const integration = await TicketingIntegration.findOne({
      where: { bot_id },
    });
    if (!integration) {
      return res.status(404).json({ error: "Integration not found" });
    }
    integration.status = integration.status === "active" ? "inactive" : "active";
    await integration.save();
    return res.send(integration);
  } catch (error) {
    next(error);
  }
};

const createDepartmentWithCategories = async (req, res, next) => {
  const { bot_id, createDepartments, createCategories } = req.body;

  try {
    const integration = await TicketingIntegration.findOne({
      where: { bot_id }
    });

    if (!integration) {
      return res.status(404).json({ error: "Integration not found" });
    }

    const departmentsData = createDepartments.map(dep => ({
      ticketing_integration_id: integration.ticketing_integration_id,
      department: dep.en || null,
      department_ar: dep.ar || null,
      is_native: 0,
    }));

    const departments = await TicketingDepartment.bulkCreate(departmentsData, { returning: true });

    let catToCreate = [];
    createCategories.forEach(cat => {
      const department = departments.find(dep => dep.department === (createDepartments[cat.index]?.en));
      if (department) {
        catToCreate.push({
          department_id: department.dataValues.department_id,
          category: cat.en || null,
          category_ar: cat.ar || null,
        });
      }
    });
    if (catToCreate.length > 0) {
      await TicketingCategory.bulkCreate(catToCreate);
    }

    res.send({ message: "Created Successfully" });
  } catch (error) {
    console.log(error);
    res.status(500).send({ error: "An error occurred while creating the department and categories" });
  }
};


const findEditorsAgentsByBotID = (req, res, next) => {
  const { bot_id } = req.query;

  Editor.findAll({
    where: {
      bot_id,
      ticketing_privilege: true,
    },
    include: {
      model: TicketingSupportAgent,
      as: "support_agent",
    },
    distinct: true,
  })
    .then((editors) => {
      req.editorsAgents = editors;
      next();
    })
    .catch((error) => {
      res.status(500).json({ error: error.message });
    });
};

const createAgent = async (req, res, next) => {
  const { bot_id, editor, time_zone, departments } = req.body;
  const transaction = await sequelize.transaction();
  try {
    const supportAgentsData = editor.map((id) => ({
      bot_id,
      editor_id: id.value,
      status: "inactive",
      time_zone: time_zone || "Asia/Amman",
    }));

    const supportAgents = await TicketingSupportAgent.bulkCreate(supportAgentsData, { transaction });

    if (!departments || departments.length === 0) {
      await transaction.commit();
      return res.send({ message: "Support Agent Created without department." });
    } else {
      const agentDepartments = [];
      supportAgents.forEach(agent => {
        departments.forEach(dep => {
          agentDepartments.push({
            department_id: dep.value,
            support_agent_id: agent.dataValues.support_agent_id,
          });
        });
      });
      await TicketingAgentDepartment.bulkCreate(agentDepartments, { transaction });
      await transaction.commit();
      return res.send({ message: `Support Agent and ${departments.length === 1 ? 'department' : 'multiple departments'} created.` });
    }
  } catch (error) {
    if (transaction) {
      await transaction.rollback();
    }
    return res.status(500).send({ message: "An error occurred while creating the support agent." });
  }
};

const findTicketRating = async (req, res, next) => {
  const ticket_id = req.query.ticket_id || req.body.ticket_id;

  if (!ticket_id) {
    return res.status(400).send({ error: "Ticket ID is required" });
  }

  try {
    const rating = await TicketingRating.findOne({
      where: {
        ticket_id,
      },
    });

    if (!rating) {
      return res.status(200).send({ message: "Rating not found" });
    }

    req.rating = rating;

    return next();
  } catch (error) {
    console.log(error);
    return res.status(500).send({ error: "Error finding rating" });
  }
};

const findAgentDepartment = async(req, res, next)=>{

  const {editor_id} = req.query;
  const agent = await TicketingSupportAgent.findOne({
    where: {editor_id}
  })
  const support_agent_id = agent.dataValues.support_agent_id
  const agentDepartments = await TicketingAgentDepartment.findAll({
    where: {support_agent_id}
  }) 
  const departmentIds = agentDepartments.map(department => department.dataValues.department_id);
  const departments = await TicketingDepartment.findAll({
    where: { department_id: departmentIds },
  });
  const departmentList = departments.map((a, i) => ({
    department_id: a.dataValues.department_id,
    en: a.dataValues.department,
    ar: a.dataValues.department_ar,
  }));
  req.departments = departmentList;
  return next();
}


const updateAgent = async (req, res, next) => {
  try {
    const { departments, time_zone, editor_id } = req.body;
    const agent = await TicketingSupportAgent.findOne({
      where: { editor_id }
    });

    if (!agent) {
      return res.status(404).send({ error: "Agent not found" });
    }
    const agentDepartments = await TicketingAgentDepartment.findAll({
      where: { support_agent_id: agent.support_agent_id }
    });
    if (!agentDepartments || agentDepartments.length === 0) {
      const newAgentDepartments = await TicketingAgentDepartment.bulkCreate(
        departments.map((department_id) => ({ support_agent_id: agent.support_agent_id, department_id }))
      );
      req.supportAgent = newAgentDepartments; 
      return next();
    }

    if (time_zone && agent.time_zone !== time_zone) {
      agent.time_zone = time_zone;
      await agent.save();
    }
    const currentDepartmentIds = agentDepartments.map((dep) => dep.department_id);
    const newDepartmentIds = departments;
    const departmentsToDelete = agentDepartments.filter(
      (dep) => !newDepartmentIds.includes(dep.department_id)
    );

    const departmentsToAdd = newDepartmentIds.filter(
      (id) => !currentDepartmentIds.includes(id)
    );

    if (departmentsToDelete.length > 0) {
      await TicketingAgentDepartment.destroy({
        where: {
          support_agent_id: agent.support_agent_id,
          department_id: departmentsToDelete.map((dep) => dep.department_id)
        }
      });
    }

    if (departmentsToAdd.length > 0) {
      await TicketingAgentDepartment.bulkCreate(
        departmentsToAdd.map((department_id) => ({
          support_agent_id: agent.support_agent_id,
          department_id
        }))
      );
    }
    return res.send({ message: "Agent updated successfully" });
  } catch (error) {
    console.error("Error updating agent:", error);
    return res.status(500).send({ error: "An error occurred while updating the agent" });
  }
};

const purge = async(req, res, next)=>{
  const {editor_id}= req.body;
  try{
    await TicketingSupportAgent.destroy({
      where: {editor_id}
    });
  
    return res.send({message: 'agent was deleted successfully'})

  }catch (error) {
    console.error("Error deleting agent:", error);
    return res.status(500).send({ message: "An error occurred while deleting the agent." });
  }
}

const getCategories = async (req, res, next) => {
  const { department_id } = req.query;
  try {
    const categories = await TicketingCategory.findAll({
      where: { department_id }
    });
    if (!categories || categories.length === 0) {
      req.categories = [];
      next();
    }
    const categoriesToSend = categories.map(category => ({
      en: category.category,
      ar: category.category_ar,
      department_id: category.department_id,
      category_id: category.department_category_id,
    }));
    req.categories = categoriesToSend;
    next();
  } catch (error) {
    console.error("Error fetching categories: ", error);
    res.status(500).send({ error: "An error occurred while fetching categories" });
  }
};

const purgeDepartment = async (req, res, next) => {
  const { department_id } = req.body;
  try {
    const [department, category] = await Promise.all([
      TicketingDepartment.destroy({
        where: { department_id }
      }),
      TicketingCategory.destroy({
        where: { department_id }
      })
    ]);
    if (!department) {
      return res.status(404).json({ message: "Department not found" });
    }
    return res.send({ message: "Department and its categories deleted successfully" });
  } catch (error) {
    console.error("Error deleting department or categories:", error);
    return res.status(500).json({ error: "An error occurred while deleting the department and its categories" });
  }
};


const updateCategories = async (req, res, next) => {
  const { department_id, editCategories, addCategories } = req.body;

  try {
    if (editCategories?.length > 0) {
      const existingCategories = await TicketingCategory.findAll({
        where: { department_id }
      });

      const existingCategoryMap = existingCategories.reduce((acc, cat) => {
        acc[cat.department_category_id] = cat;
        return acc;
      }, {});

      for (const editCategory of editCategories) {
        const existingCategory = existingCategoryMap[editCategory.category_id];

        if (existingCategory) {
          const [updatedCount] = await TicketingCategory.update(
            { category: editCategory.en, category_ar: editCategory.ar },
            {
              where: { department_category_id: existingCategory.department_category_id }
            }
          );
        }
      }

      const idsToKeep = editCategories.map(cat => cat.category_id);
      const deletedCount = await TicketingCategory.destroy({
        where: {
          department_id,
          department_category_id: { [Op.notIn]: idsToKeep }
        }
      });

    }

    if (addCategories?.length > 0) {
      const existingCategories = await TicketingCategory.findAll({
        where: { department_id }
      });
      const existingCategoryIds = new Set(existingCategories.map(cat => cat.department_category_id));

      const newCategories = addCategories
        .filter(cat => cat.ar || cat.en && !existingCategoryIds.has(cat.index))
        .map(cat => ({
          department_id, 
          category: cat.en || null,
          category_ar: cat.ar || null,
      }));
      TicketingCategory.bulkCreate(newCategories);
    }

    res.send({ message: "Categories updated successfully" });
  } catch (error) {
    console.error("Error updating categories: ", error);
    res.status(500).send({ error: "An error occurred while updating categories" });
  }
};





module.exports = {
  findTicketByUUID,
  findAllTicketComments,
  findSupportAgentByEditor,
  findTicketsByAgent,
  findTicketsByAgentDepartments,
  findIntegrationByBotId,
  findCategoriesFromDepartment,
  findDepartmentsByBotId,
  createIntegration,
  updateIntegration,
  createDepartmentWithCategories,
  findEditorsAgentsByBotID,
  createAgent,
  findTicketRating,
  findAgentDepartment,
  updateAgent,
  purge,
  getCategories,
  purgeDepartment,
  updateCategories,

};
