const fetch = require('node-fetch');

const hubUrl = 'https://hub.360dialog.io'
const mockUrl = 'https://mock-engine.azurewebsites.net/api/wa/messages'


const setWABAfun = async (APIKEY,PhoneNumber)=>{
    const WABAurl=`https://waba.360dialog.io/v1/configs/webhook`
  
    const WebhookURL = `${mockUrl}/${PhoneNumber}`
   
    const data ={"url": WebhookURL}
    try{
      const response = await fetch(WABAurl,{
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'D360-API-KEY':`${APIKEY}`
        },
        body:
         JSON.stringify(data) 
        
    
      })
      const result = await response.json()
      return result
    }catch{
      console.error('Error set WABA ', error.message);
      throw error;
    }
  }
  const setWebhookFun = async (accessToken,PhoneNumber,parnterID) =>{
    const setWebHookUrl =`${hubUrl}/api/v2/partners/${parnterID}/webhook_url`
  
    const WebhookURL = `${mockUrl}/${PhoneNumber}`
   
    const data ={"webhook_url": WebhookURL}
    try{
      const response = await fetch(setWebHookUrl,{
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization':`Bearer ${accessToken}`
        },
        body:
         JSON.stringify(data) 
        
    
      })
      const result = await response.json()
      return result
    }catch{
      console.error('Error set web hook url ', error.message);
      throw error;
    }
    
  }
  const getPhoneNumber= async (accessToken,CHANNEL_ID,parnterID)=>{
    const getPhoneNumberUrl = `${hubUrl}/api/v2/partners/${parnterID}/channels?filters=%7B%22id%22%3A%20%22${CHANNEL_ID}%22%7D`
    try{
      const response = await fetch(getPhoneNumberUrl,{
        method: 'get',
        headers: {
          'Authorization':`Bearer ${accessToken}`
        },
    
      })
      const result = await response.json()
  
      return result.partner_channels[0].setup_info.phone_number
  
  
    }catch{
      console.error('Error in get number phone ', error.pmessage);
      throw error;
    }
    
  }
  const generteKey = async(accessToken,CHANNEL_ID,parnterID)=>{
    const generateKeyUrl = `${hubUrl}/api/v2/partners/${parnterID}/channels/${CHANNEL_ID}/api_keys`
  
  try{
    const response = await fetch(generateKeyUrl,{
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization':`Bearer ${accessToken}`
      },
  
    })
    const result = await response.json()
    return result
  }catch{
    console.error('Error in generate key ', error.message);
    throw error;
  }
  
  }
  const loginToHub = async ()=>{
  
      const loginHubUrl = `${hubUrl}/api/v2/token`;
  
  
      const data = {
          username: "<EMAIL>",
      password: "2R@b!ts230770"
  
      };
    
      try {
        const response = await fetch(loginHubUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(data)
        });
       
        const result = await response.json();
  
        return result.access_token
      } catch (error) {
        console.error('Error login to hub', error.message);
        throw error;
      }
  
    
  }
  module.exports= {
    loginToHub,generteKey,getPhoneNumber,setWebhookFun,setWABAfun
  }