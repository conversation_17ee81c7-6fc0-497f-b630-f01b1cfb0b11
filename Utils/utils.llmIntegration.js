const { getFile, uploadFile } = require("../calls/file.call");
const { testLLM, refine, testStore } = require("../calls/rag.calls");
const sequelize = require("../db");
const llmIntegration = require("../Models/LLMIntegration");
const { HNSWLib } = require("@langchain/community/vectorstores/hnswlib");
const { OpenAIEmbeddings } = require("@langchain/openai");
const translate = require("google-translate-api-x");
const fetch = require("node-fetch");

const set = async (req, res, next) => {
  try {
    const {
      bot_id,
      llm_type,
      llm_model,
      llm_key,
      llm_temperature,
      chunk_size,
      chunk_overlap,
      chat_history,
      store_url,
      status_active,
      store_type,
      collection_name,
      personal_vector_db,
      top_k,
      chunk_methodology,
      persona,
      llm_embedding_model,
      fetch_k,
      lambda_mult,
    } = req.body;

    const llm = await llmIntegration.create({
      bot_id,
      llm_type,
      llm_model,
      llm_key,
      llm_temperature,
      chunk_size,
      chunk_overlap,
      chat_history,
      store_url,
      status_active,
      store_type,
      collection_name,
      personal_vector_db,
      top_k,
      chunk_methodology,
      persona,
      llm_embedding_model,
      fetch_k,
      lambda_mult,
    });

    req.llmIntegration = llm;
    return next();
  } catch (error) {
    console.error("Error creating LLM integration:", error);
    return res.status(500).json({ error: "Failed to create LLM integration." });
  }
};

const getByBotID = async (req, res, next) => {
  const { bot_id } = req.query || req.body;
  try {
    const integration = await llmIntegration.findOne({
      where: { bot_id },
    });

    if (!integration) {
      return res.status(404).json({ message: "Integration not found" });
    }

    req.llmIntegration = integration;
    next();
  } catch (error) {
    console.error("Error fetching integration:", error);
    res.status(500).json({ message: "Internal Server Error", error });
  }
};

const updateIntegration = async (req, res, next) => {
  try {
    const {
      LLM_integration_id,
      bot_id,
      llm_type,
      llm_model,
      llm_key,
      llm_temperature,
      chunk_size,
      chunk_overlap,
      chat_history,
      store_url,
      status_active,
      store_type,
      collection_name,
      personal_vector_db,
      top_k,
      chunk_methodology,
      persona,
      llm_embedding_model,
      fetch_k,
      lambda_mult,
    } = req.body;
    const integration = await llmIntegration.findOne({
      where: { bot_id, LLM_integration_id },
    });

    if (!integration) {
      return res.status(404).json({ error: "LLM integration not found" });
    }
    await integration.update({
      ...(llm_type !== undefined && { llm_type }),
      ...(llm_model !== undefined && { llm_model }),
      ...(llm_key !== undefined && { llm_key }),
      ...(llm_temperature !== undefined && { llm_temperature }),
      ...(chunk_size !== undefined && { chunk_size }),
      ...(chunk_overlap !== undefined && { chunk_overlap }),
      ...(chat_history !== undefined && { chat_history }),
      ...(store_url !== undefined && { store_url }),
      ...(status_active !== undefined && { status_active }),
      ...(store_type !== undefined && { store_type }),
      ...(collection_name !== undefined && { collection_name }),
      ...(personal_vector_db !== undefined && { personal_vector_db }),
      ...(top_k !== undefined && { top_k }),
      ...(chunk_methodology !== undefined && { chunk_methodology }),
      ...(persona !== undefined && { persona }),
      ...(llm_embedding_model !== undefined && { llm_embedding_model }),
      ...(fetch_k !== undefined && { fetch_k }),
      ...(lambda_mult !== undefined && { lambda_mult }),
    });
    req.llmIntegration = integration;
    next();
  } catch (error) {
    console.error("Error updating LLM integration:", error);
    return res.status(500).json({ error: "Failed to update LLM integration." });
  }
};

const deleteIntegration = async (req, res, next) => {
  try {
    const { bot_id, LLM_integration_id } = req.body;
    const deleted = await llmIntegration.destroy({
      where: { bot_id, LLM_integration_id },
    });

    if (deleted) {
      return res
        .status(200)
        .json({ message: "LLM integration deleted successfully." });
    } else {
      return res.status(404).json({ error: "LLM integration not found." });
    }
  } catch (error) {
    console.error("Error deleting LLM integration:", error);
    return res.status(500).json({ error: "Failed to delete LLM integration." });
  }
};

const checkLLM = async (req, res, next) => {
  const { llm_config } = req.body;
  const llm = await testLLM({ llm_config });
  res.send(llm);
};

const getAllTags = async (req, res, next) => {
  const { bot_id } = req.query || req.body;
  const { session_uuid } = req.body;
  console.log("-------------0000000--------------");
  console.dir(req.body, { depth: null });
  console.log("-----------0000000----------------");
  // eb2d0696-473f-4ce6-89a4-50b189bd68f8  864
  // https://infotointell.fra1.digitaloceanspaces.com/cache/bot864/eb2d0696-473f-4ce6-89a4-50b189bd68f8.json
  const file = await fetch(
    `https://infotointell.fra1.digitaloceanspaces.com/cache/bot${bot_id}/${session_uuid}.json`
  )
    .then((res) => res.json())
    .catch((e) => null);
  if (file) {
    console.log("found the file from cache");
    req.LLMAllTags = file;
    next();
  } else {
    sequelize
      .query("designer_get_all_tags_v3 :botID", {
        replacements: {
          botID: bot_id,
        },
      })
      .then((data) => {
        uploadFile({
          path: `cache/bot${bot_id}/${session_uuid}.json`,
          file_body: JSON.stringify(data[0]),
        });
        req.LLMAllTags = data[0];
        next();
      });
  }
};

const performSimilaritySearch = async (req, res, next) => {
  const { LLMAllTags } = req;
  const { history, llm } = req.body;

  const documents = LLMAllTags.map((item) => ({
    pageContent:
      item.trigger_type === "qna"
        ? `${item.question} - ${item.answer}`
        : item.question,
    metadata: {
      source_id: item.src_id.toString(),
      type: item.trigger_type,
      answer: item.answer,
    },
  }));
  function isEnglish(text) {
    return /^[A-Za-z0-9\s.,?!]+$/.test(text);
  }
  const user_question = history[history?.length - 1].question;
  let q;
  if (isEnglish(user_question)) {
    q = await translate(user_question, { from: "en", to: "ar" });
  } else {
    q = await translate(user_question, { from: "ar", to: "en" });
  }
  q = q.text;

  const embeddings = new OpenAIEmbeddings({
    model: "text-embedding-3-small",
    openAIApiKey: llm?.llm_key,
  });

  const vectorStore = await HNSWLib.fromDocuments(documents, embeddings);
  const temp = q + " - " + user_question;
  const similaritySearchResults = await vectorStore.similaritySearch(temp, 10);

  req.similaritySearchResults = similaritySearchResults.map((result) => ({
    page_content: result.pageContent,
    metadata: {
      source_id: result.metadata.source_id,
      type: result.metadata.type,
      answer: result.metadata.answer,
    },
  }));
  next();
};

const refineLLM = async (req, res, next) => {
  const { similaritySearchResults } = req;
  const { history, llm } = req.body;
  try {
    const afterRefine = await refine({
      llm_config: {
        llm_key: llm?.llm_key,
        llm_type: llm?.llm_type,
        llm_model: llm?.llm_model,
        llm_temperature: llm?.llm_temperature || 0,
      },
      store_config: {
        store_url: llm?.personal_vector_db
          ? llm?.store_url
          : "http://20.233.150.214:8000/",
        collection_name: llm?.personal_vector_db
          ? llm?.collection_name
          : `collection_${Math.random().toString(36).substring(2, 10)}`,
        store_type: llm?.personal_vector_db ? llm?.store_type : "chroma",
      },
      config: {
        top_k: llm?.top_k,
        fetch_k: llm?.fetch_k,
        chunk_overlap: llm?.chunk_overlap || 50,
        chunk_size: llm?.chunk_size || 300,
        persona: llm?.persona,
        lambda_mult: llm?.lambda_mult,
      },
      query_history: history,
      refine: similaritySearchResults,
    });
    console.log("llm response ", afterRefine);

    req.afterRefine = afterRefine || [];
  } catch (error) {
    console.error("Error in refineLLM:", error);
    req.afterRefine = [];
  }
  next();
};

const checkStore = async (req, res, next) => {
  const { store_config } = req.body;
  const store = await testStore({ store_config });
  res.send(store);
};

module.exports = {
  set,
  getByBotID,
  updateIntegration,
  deleteIntegration,
  checkLLM,
  getAllTags,
  performSimilaritySearch,
  refineLLM,
  checkStore,
};
