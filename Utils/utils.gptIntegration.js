const GPTIntegration = require("../Models/GPTIntegration");
const fetch = require("node-fetch");

const findGPTIntegrationByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  GPTIntegration.findOne({ where: { bot_id } }).then((gptIntegration) => {
    if (gptIntegration) {
      req.gptIntegration = gptIntegration;
      return next();
    } else {
      return res
        .status(404)
        .send({ message: "GPT Integration does not exist" });
    }
  });
};

const validateApiKey = async (req, res, next) => {
  const apiKey = req.body.openai_key;
  if (!apiKey) {
    return next();
  }
  try {
    const response = await fetch("https://api.openai.com/v1/engines", {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
      },
    });

    if (response.status === 200) {
      return next();
    } else {
      return res.status(401).send({ message: "Invalid OpenAI API key" });
    }
  } catch (error) {
    // An error occurred while validating the API key
    return res.status(500).send({ message: "Error validating OpenAI API key" });
  }
};

module.exports = {
  findGPTIntegrationByBotId,
  validateApiKey,
};
