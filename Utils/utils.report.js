const Report = require("../Models/Report");


const findReportsByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  Report.findAll({ where: { bot_id } }).then((reports) => {
    if (reports) {
      req.reports = reports;
      return next();
    }
    return res.status(404).send({ message: "reports do not exist" });
  });
};




module.exports = {
    findReportsByBotId
};
