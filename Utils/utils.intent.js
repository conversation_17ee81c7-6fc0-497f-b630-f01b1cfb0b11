const Intent = require("../Models/Intent");

const findById = (req, res, next) => {
  const intent_id = req.body.intent_id || req.query.intent_id;
  req.intent_id = intent_id;
  Intent.findOne({ where: { intent_id } }).then((intent) => {
    if (intent) {
      req.intent = intent;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findAll = (req, res, next) => {
  Intent.findAll({ where: {} }).then((intents) => {
    req.intents = intents;
    return next();
  });
};

module.exports = {
  findAll,
  findById,
};
