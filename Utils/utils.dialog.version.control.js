const { Op } = require('sequelize');
const DialogVersionControl = require('../Models/DialogVersionControl')

const findLogs = (req, res, next) => {
    const dialog_id = req.body.dialog_id || req.query.dialog_id;
    DialogVersionControl.findAll({ where: { dialog_id: dialog_id}, 
    }).then((dialogversioncontrol) => {
      if (dialogversioncontrol) {
        req.dialogversioncontrol = dialogversioncontrol;
        return next();
      } else {
        return res.sendStatus(404);
      }
    });
};

const findLogsfalse = (req, res, next) => {
    const dialog_id = req.body.dialog_id || req.query.dialog_id;
    const is_staging = DialogVersionControl.is_staging;
    DialogVersionControl.findAll({ where: { dialog_id: dialog_id, is_staging:true}, 
    }).then((dialogversioncontrol) => {
      if (dialogversioncontrol) {
        req.dialogversioncontrol = dialogversioncontrol;
        return next();
      } else {
        return res.sendStatus(404);
      }
    });
};

const getDvcByBotId = async (req, res, next) => {
  try {
    const dialogs = req.dialogs;
    if (dialogs.length === 0) {
      req.dvc=[];
      return next();
    }
    const dialogIds = dialogs.map(dialog => dialog.dataValues.dialog_id);

    const DVC = await DialogVersionControl.findAll({
      where: {
        dialog_id: dialogIds,
        [Op.or]: [
          { is_live: true },
          { is_staging: true }
        ]
      }
    });
    req.dvc = DVC;
    return next();
  } catch (error) {
    console.error("Error in getDvcByBotId:", error);
    return res.status(500).json({ error: "An error occurred while fetching DVC data." });
  }
};

module.exports = {findLogs, findLogsfalse, getDvcByBotId};