const sequelize = require("../db");
const Feature = require("../Models/Feature");

const preprocess = (req, res, next) => {
  const feature_type = req.body.feature_type;
  const feature_types = ["Date", "String", "Number"];
  if (feature_types.indexOf(feature_type) === -1) {
    return res.send({ message: "invalid feature_type" });
  } else {
    return next();
  }
};

const findById = (req, res, next) => {
  const feature_id = req.body.feature_id || req.query.feature_id;
  Feature.findOne({ where: { feature_id } }).then((feature) => {
    if (feature) {
      req.feature = feature;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findAll = (req, res, next) => {
  const bot_id = req.body.bot_id || +req.query.bot_id;
  sequelize
    .query("designer_get_features :bot_id", {
      replacements: { bot_id },
    })
    .then((data) => {
      req.features = data[0];
      return next();
    });
};

module.exports = {
  findAll,
  findById,
  preprocess,
};
