const LeadPlugin = require("../Models/LeadPlugin");

const findByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  LeadPlugin.findOne({ where: { bot_id } }).then((leadplugin) => {
    if (leadplugin) {
      req.leadplugin = leadplugin;
      return next();
    } else {
      return res.status(404).send({ message: "Lead Plugin does not exist" });
    }
  });
};

const findByBotIdNoValidation = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  LeadPlugin.findOne({ where: { bot_id } }).then((leadplugin) => {
    req.leadplugin = leadplugin;
    return next();
  });
};

const checkExistance = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  LeadPlugin.findOne({ where: { bot_id } }).then((leadplugin) => {
    if (leadplugin) {
      req.leadplugin = leadplugin;
      return res.status(409).send({ message: "Lead Plugin already exits" });
    } else {
      return next();
    }
  });
};

module.exports = {
  findByBotId,
  checkExistance,
  findByBotIdNoValidation,
};
