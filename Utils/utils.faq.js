const FAQ = require("../Models/FAQ");
const User = require("../Models/User");
const sequelize = require("../db");
const { Op } = require("sequelize");

const findFAQById = (req, res, next) => {
  const faq_id = req.body.faq_id;
  FAQ.findOne({ where: { faq_id } }).then((faq) => {
    req.faq = faq;
    return next();
  });
};

const findFAQByQueryId = (req, res, next) => {
  const faq_id = +req.query.faq_id;
  FAQ.findOne({ where: { faq_id } }).then((faq) => {
    req.faq = faq;
    return next();
  });
};

const findBotFAQsById = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id || req.body[0].bot_id;
  console.log("COME");
  sequelize
    .query("designer_faqs_get :botID", {
      replacements: { botID: bot_id },
    })
    .then((data) => {
      req.faqs = data[0];
      return next();
    });
};

const findBotFAQsByQueryId = (req, res, next) => {
  const bot_id = +req.query.bot_id;
  const offset = +req.query.offset;
  const limit = +req.query.limit;
  FAQ.findAll({ where: { bot_id }, limit, offset }).then((faqs) => {
    req.faqs = faqs;
    return next();
  });
};

const findManyFAQs = (req, res, next) => {
  const data = req.body;
  FAQ.findAll({
    where: {
      faq_id: { [Op.in]: data.map((a) => a.faq_id) },
    },
  }).then((faqs) => {
    req.faqs = faqs;
    return next();
  });
};

const findFirstAnswerFAQ = (req, res, next) => {
  const bot_id = +req.query.bot_id;
  sequelize
    .query("designer_grouped_faqs_first :botID", {
      replacements: { botID: bot_id },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const findFAQLogs = (req, res, next) => {
  const faq_id = +req.query.faq_id;
  sequelize
    .query("bot_designer_get_faq_logs :faq_id ", {
      replacements: { faq_id: faq_id },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const findQuestionAlternatives = (req, res, next) => {
  const bot_id = +req.query.bot_id;
  const faq_id = +req.query.faq_id;
  // const question = req.query.question;
  sequelize
    .query("designer_faqs_answer_alternatives :botID , :faq_id", {
      replacements: {
        botID: bot_id,
        faq_id: faq_id,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const findHiddenFAQs = (req, res, next) => {
  const bot_id = req.body.bot_id || +req.query.bot_id || req.bot.bot_id;
  sequelize
    .query("designer_get_hidden_qna :botID", {
      replacements: {
        botID: bot_id,
      },
    })
    .then((data) => {
      req.faqs = data[0];
      return next();
    });
};

const checkExistance = (req, res, next) => {
  const user_id = req.body.user_id;
  User.findOne({ where: { user_id } }).then((user) => {
    if (!user) {
      return res.status(404).send({ message: "User Not Found" });
    }
    return next();
  });
};

const findAllBotFAQsByQueryId = (req, res, next) => {
  const bot_id = +req.query.bot_id;

  FAQ.findAll({ where: { bot_id } }).then((faqs) => {
    req.faqs = faqs;
    return next();
  });
};

module.exports = {
  findFAQById,
  findFAQByQueryId,
  findBotFAQsById,
  findBotFAQsByQueryId,
  findManyFAQs,
  findFirstAnswerFAQ,
  findQuestionAlternatives,
  findHiddenFAQs,
  checkExistance,
  findFAQLogs,
  findAllBotFAQsByQueryId,
};
