const DialogExpiryLog = require("../Models/DialogExpiryLog");

const findDialogExpiryLogById = (req, res, next) => {
  const dialog_expiry_log_id =
    req.body.dialog_expiry_log_id || req.query.dialog_expiry_log_id;
  DialogExpiryLog.findOne({ where: { dialog_expiry_log_id } }).then(
    async (dialogExpiryLog) => {
      if (dialogExpiryLog) {
        req.dialogExpiryLog = dialogExpiryLog;
        return next();
      } else {
        return res.sendStatus(404);
      }
    }
  );
};

module.exports = {
  findDialogExpiryLogById,
};
