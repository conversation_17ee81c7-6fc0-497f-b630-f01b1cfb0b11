const MonitorBot = require("../Models/Monitor.bot");
const Bot = require("../Models/Bot");
const { Op } = require("sequelize");

const findMonitorBotById = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  MonitorBot.findAll({ where: { bot_id } }).then((monitorbots) => {
    if (monitorbots) {
      req.monitorbots = monitorbots;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const checkExistance = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  const email = req.body.email;
  MonitorBot.findOne({ where: { bot_id, email } }).then((monitorbot) => {
    if (!monitorbot) {
      return next();
    } else {
      return res.status(409).send({ message: "already exists" });
    }
  });
};

const findAllMonitorBots = (req, res, next) => {
  const email = req.body.email || req.query.email;
  MonitorBot.findAll({ where: { email, active: true } }).then((monitorbots) => {
    req.monitorbots = monitorbots;
    return next();
  });
};

const findMonitorBots = (req, res, next) => {
  const monitorbots = req.monitorbots;
  Bot.findAll({
    where: { bot_id: { [Op.in]: monitorbots.map((a) => a.bot_id) } },
  }).then((bots) => {
    req.bots = bots;
    return next();
  });
};

const findOneMonitorBot = (req, res, next) => {
  const email = req.body.email || req.query.email;
  const bot_id = req.body.bot_id || req.query.bot_id;
  MonitorBot.findOne({ where: { email, bot_id } }).then((monitorbot) => {
    req.monitorbot = monitorbot;
    return next();
  });
};

module.exports = {
  checkExistance,
  findMonitorBotById,
  findAllMonitorBots,
  findMonitorBots,
  findOneMonitorBot,
};
