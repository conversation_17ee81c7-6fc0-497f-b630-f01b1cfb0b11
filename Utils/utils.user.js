const User = require("../Models/User");
const EmailTemplate = require("../Models/EmailTemplates");
const secret = require("../config/appConfig").secret;
const jwt = require("jsonwebtoken");
const messages = require("../Messages/message.user");
const { resetPwdSecret } = require("../config/appConfig");
const { localBuilderUrl, stagingBuilderUrl } = require("../constants");

const findUserById = (req, res, next) => {
  const user_id = req.body.user_id || req.query.user_id;
  User.findOne({ where: { user_id } }).then((user) => {
    if (user) {
      req.user = user;
      return next();
    } else {
      return res.status(401).send(messages.notFound);
    }
  });
};
const findUserByEmail = (req, res, next) => {
  const email = req.body.email;
  User.findOne({ where: { email: email } }).then((user) => {
    if (user) {
      req.user = user;
      return next();
    } else {
      return res.status(401).send(messages.notFound);
    }
  });
};

const findUserByQueryEmail = (req, res, next) => {
  const email = req.query.email;
  User.findOne({ where: { email } }).then((user) => {
    if (user) {
      req.user = user;
      return next();
    } else {
      return res.status(401).send(messages.notFound);
    }
  });
};
const findUserByQueryId = (req, res, next) => {
  const user_id = +req.query.user_id || req.body.user_id;
  User.findOne({ where: { user_id } }).then((user) => {
    if (user) {
      req.user = user;
      return next();
    } else {
      return res.status(401).send(messages.notFound);
    }
  });
};

const authenticatePassword = (req, res, next) => {
  const user = req.user;
  const password = req.body.password;
  if (user.authenticate(password)) {
    return next();
  } else {
    res.status(401).send(messages.wrongPassword);
  }
};

const checkEmailVerification = (req, res, next) => {
  const user = req.user;
  if (user.email_verification) {
    return next();
  } else {
    return res.status(401).send(messages.verify);
  }
};

const verifyEmail = (req, res, next) => {
  const user = req.user;
  user.email_verification = true;
  return next();
};

const authenticateToken = (req, res, next) => {
  const user = req.user;
  const authHeader = req.headers["authorization"];
  const token = authHeader ? authHeader.split(" ")[1] : null;
  if (token == null) return res.sendStatus(401);
  jwt.verify(token, secret, (err, tokenInfo) => {
    if (tokenInfo.name === user.user_name) {
      return next();
    }
    return res.sendStatus(403);
  });
};

const checkEmailExistance = (req, res, next) => {
  const email = req.body.email;
  User.findOne({ where: { email } }).then((user) => {
    if (!user) {
      return next();
    }
    return res.status(409).send(messages.exists);
  });
};

const checkPwdReset = (req, res, next) => {
  const user = req.user;
  if (user.reset) {
    return res.status(401).send({ message: "Please check your email" });
  }
  return next()
}

const generateResetPasswordToken = (req, res, next) => {
  const user = req.user;

  const pwdSecret = resetPwdSecret + user.hash;

  const token = jwt.sign({ user_id: user.user_id, email: user.email }, pwdSecret, {
    expiresIn: "1h",
  });

  const link = `${stagingBuilderUrl}/reset-password?user_id=${user.user_id}&token=${token}`;

  req.token = token;
  req.link = link;
  return next();
}

const generateResetPasswordEmail = (req, res, next) => {
  const user = req.user;
  const link = req.link;
  const title = 'Reset Password'
  const mainText ='If you lost your password, you can reset it by clicking the button below.'
  const footerText = "If you didn't request a password reset, you can safely ignore this email. Only a person with access to your email can reset your account password."


EmailTemplate.findOne({ where: { email_template_id: 1 } }).then((emailTemplate) => {
  console.log(emailTemplate)
  if (emailTemplate && typeof emailTemplate?.template === 'string') {
      const temp = emailTemplate.template.toString()
      console.log('temp:', temp); 
      const emailBody = temp.replaceAll("{cta}", link).replaceAll("{title}", title).replaceAll("{username}", user.user_name)
      .replaceAll("{mainText}", mainText).replaceAll("{ctaText}", title).replaceAll("{footerText}", footerText);
      req.emailBody = emailBody;
      return next();
    } else {
      return res.sendStatus(404);
    }
  }
  );

}

const validateResetPasswordToken = async (req, res, next) => {
  const token = req.token || req.body.token;
  const user = req.user;
  const pwdSecret = resetPwdSecret + user.hash;

   jwt.verify(token, pwdSecret,async (err, tokenInfo) => {
    if (err) {
      user.reset = false;
     await user.save();
      return res.send(err);
    }
    req.tokenInfo = tokenInfo;
    return next();
  });

}

module.exports = {
  checkEmailExistance,
  findUserById,
  findUserByEmail,
  findUserByQueryEmail,
  findUserByQueryId,
  authenticatePassword,
  checkEmailVerification,
  verifyEmail,
  authenticateToken,
  generateResetPasswordToken,
  generateResetPasswordEmail,
  validateResetPasswordToken,
  checkPwdReset
};
