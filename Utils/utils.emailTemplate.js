const EmailTemplate = require("../Models/EmailTemplates");

const findEmailTemplateById = (req, res, next) => {
    const email_template_id = req.body.email_template_id || req.query.email_template_id;
    EmailTemplate.findOne({ where: { email_template_id } }).then((emailTemplate) => {
      if (emailTemplate) {
        req.emailTemplate = emailTemplate;
        return next();
      } else {
        return res.sendStatus(404);
      }
    });
  };


module.exports = {
    findEmailTemplateById
};