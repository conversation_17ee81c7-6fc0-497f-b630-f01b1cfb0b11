const BotUniqueUser = require("../Models/Bot.UniqueUser");


const findBotUniqueUsers = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  BotUniqueUser.findAll({ where: { bot_id } }).then((botUniqueUsers) => {
    if (botUniqueUsers) {
      req.botUniqueUsers = botUniqueUsers;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};



module.exports = {
    findBotUniqueUsers
};
