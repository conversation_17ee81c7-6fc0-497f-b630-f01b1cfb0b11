const ReservationRoom = require("../Models/Reservation.Room");
const { Op } = require("sequelize");

const findRoomsByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || +req.query.bot_id;
  req.bot_id = bot_id;
  const room_active = req.query.room_active
    ? req.query.room_active
    : { [Op.between]: [false, true] };
  const room_status = {
    [Op.like]: req.query.room_status ? "%" + req.query.room_status + "%" : "%%",
  };

  ReservationRoom.findAll({
    where: { bot_id, room_status, room_active },
  }).then((rooms) => {
    if (rooms) {
      req.rooms = rooms;
      return next();
    } else {
      res.send({ message: "plugin not set" });
    }
  });
};

const findRoomById = (req, res, next) => {
  const room_id = req.body.room_id || req.query.room_id;
  req.room_id = room_id;
  ReservationRoom.findOne({ where: { room_id } }).then((room) => {
    if (room) {
      req.room = room;
      return next();
    } else {
      res.send({ message: "Room not found" });
    }
  });
};

module.exports = { findRoomsByBotId, findRoomById };
