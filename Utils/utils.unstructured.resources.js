const UnstructuredResources = require("../Models/UnstructuredResources");

const findUnstructuredResourceByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  UnstructuredResources.findOne({ where: { bot_id } }).then(
    (unstructuredResource) => {
      if (unstructuredResource) {
        req.unstructuredResource = unstructuredResource;
        return next();
      } else {
        return res.status(404).send({ message: "Resources does not exist" });
      }
    }
  );
};

const findUnstructuredResourceById = (req, res, next) => {
  const resource_id = req.body.resource_id;
  req.resource_id = resource_id;
  UnstructuredResources.findOne({ where: { resource_id } }).then(
    (unstructuredResource) => {
      if (unstructuredResource) {
        req.unstructuredResource = unstructuredResource;
        return next();
      } else {
        return res.sendStatus(404);
      }
    }
  );
};

const findManyUnstructuredResources = (req, res, next) => {
  const data = req.body;
  UnstructuredResources.findAll({
    where: {
      resource_id: { [Op.in]: data.map((a) => a.resource_id) },
    },
  }).then((unstructeredResources) => {
    req.unstructeredResources = unstructeredResources;
    return next();
  });
};

const findByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  UnstructuredResources.findAll({ where: { bot_id } }).then(
    (unstructuredResources) => {
      if (unstructuredResources) {
        req.unstructuredResources = unstructuredResources;
        return next();
      } else {
        return res.status(404).send({ message: "Resources does not exist" });
      }
    }
  );
};

module.exports = {
  findUnstructuredResourceByBotId,
  findUnstructuredResourceById,
  findManyUnstructuredResources,
  findByBotId,
};
