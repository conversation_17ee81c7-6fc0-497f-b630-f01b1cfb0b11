const SendGrid = require("../Models/SendGrid");

const findEmail = (req, res, next) => {
  const email_address = req.body.email;
  SendGrid.findOne({
    where: { email: email_address },
    order: [["createdAt", "DESC"]],
  }).then((email) => {
    if (email) {
      req.email = email;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findEmailsByBotId = (req, res, next) => {
  const bot_id = req.query.bot_id || req.body.bot_id
  SendGrid.findAll({
    where: { bot_id },
    order: [["createdAt", "DESC"]],
  }).then((emails) => {
    req.emails = emails;
    return next();
  });
};

module.exports = { findEmail, findEmailsByBotId };
