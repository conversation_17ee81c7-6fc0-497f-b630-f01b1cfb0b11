const SearchSettings = require("../Models/SearchSettings");

const findSearchSettingsByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  SearchSettings.findOne({ where: { bot_id } }).then((searchSettings) => {
    if (searchSettings) {
      req.searchSettings = searchSettings;
      return next();
    } else {
      return res.status(404).send({ message: "Settings does not exist" });
    }
  });
};

module.exports = {
  findSearchSettingsByBotId,
};
