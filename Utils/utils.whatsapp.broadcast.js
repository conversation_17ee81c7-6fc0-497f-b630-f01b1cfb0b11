const WhatsappBroadCast = require("../Models/Whatsapp.broadcasting");
const WhatsappBroadCastLogs = require("../Models/Whatsapp.broadcast.logs");
const sequelize = require("../db");

const findBroadCastsByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  WhatsappBroadCast.findAll({ where: { bot_id } }).then((broadcasts) => {
    if (broadcasts) {
      req.broadcasts = broadcasts;
      return next();
    } else {
      return res.status(404).send({ message: "broadcasts does not exist" });
    }
  });
};


const findBroadCastById = (req, res, next) => {
    const broadcast_id = req.body.broadcast_id || req.query.broadcast_id;
    WhatsappBroadCast.findOne({ where: { broadcast_id } }).then((broadcast) => {
      if (broadcast) {
        req.broadcast = broadcast;
        return next();
      } else {
        return res.status(404).send({ message: "broadcast does not exist" });
      }
    });
};


const findBroadCastLogsByBotId = (req, res, next) => {
  const broadcast_id = req.body.broadcast_id || req.query.broadcast_id;
    WhatsappBroadCastLogs.findAll({ where: { broadcast_id } }).then((logs) => {
      if (logs) {
        req.logs = logs;
        return next();
      } else {
        return res.status(404).send({ message: "broadcast logs does not exist" });
      }
    });
};
const GetWhatsappPhoneNumbers = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  sequelize
    .query("Get_Whatsapp_Phone_Numbers_All :bot_id", {
      replacements: {
        bot_id,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

module.exports = {
    findBroadCastsByBotId,
    findBroadCastById,
    findBroadCastLogsByBotId,
    GetWhatsappPhoneNumbers
};
