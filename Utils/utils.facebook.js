const Facebook = require("../Models/Facebook");

const findFacebookByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  const pageId = req.body.pageId;
  const page_token = req.body.page_token;
  const channel = req.body.channel;

  Facebook.findOne({ where: { bot_id, channel } }).then((facebook) => {
    if (facebook) {
      req.facebook = facebook;
      return next();
    } else if (pageId && page_token) {
      Facebook.create({ bot_id, pageId, page_token }).then((facebook) => {
        req.facebook = facebook;
        return next();
      });
    } else {
      return res.sendStatus(404);
    }
  });
};

const findFacebookByQueryBotId = (req, res, next) => {
  const bot_id = req.query.bot_id;
  Facebook.findOne({ where: { bot_id } }).then((facebook) => {
    if (facebook) {
      req.entity = facebook;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findFacebookByQueryPageId = (req, res, next) => {
  const pageId = req.query.pageId;
  Facebook.findOne({ where: { pageId } }).then((facebook) => {
    if (facebook) {
      req.facebook = facebook;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};

const findPageByPageId = (req, res, next) => {
  const pageId = req.body.pageId;
  Facebook.findOne({ where: { pageId } }).then((facebook) => {
    if (facebook) {
      req.facebook = facebook;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};
const findAllFacebookPagesByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  const channel = req.body.channel || req.query.channel;
  Facebook.findAll(channel ? { where : {bot_id , channel}} : { where: { bot_id } }).then((facebookPages) => {
    if (facebookPages.length > 0) {
      req.facebookPages = facebookPages;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};
module.exports = {
  findFacebookByBotId,
  findFacebookByQueryBotId,
  findFacebookByQueryPageId,
  findPageByPageId,
  findAllFacebookPagesByBotId,
};
