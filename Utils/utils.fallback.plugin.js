const FallbackPlugin = require("../Models/FallbackPlugin");

const findFallbackPluginByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  req.bot_id = bot_id;
  FallbackPlugin.findOne({ where: { bot_id } }).then((fallbackplugin) => {
    // if (botvoice) {
    req.fallbackplugin = fallbackplugin;
    return next();
    // } else {
    //   return res.sendStatus(404);
    // }
  });
};

const checkExistance = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  FallbackPlugin.findOne({ where: { bot_id } }).then((fallbackplugin) => {
    if (fallbackplugin) {
      req.fallbackplugin = fallbackplugin;
      return res.status(409).send({ message: "Popup Plugin already exits" });
    } else {
      return next();
    }
  });
};

module.exports = { findFallbackPluginByBotId, checkExistance };
