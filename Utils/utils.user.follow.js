const UserFollow = require("../Models/User.Follow");

const checkExistance = (req, res, next) => {
  const user_id = req.body.user_id;
  const bot_id = req.body.bot_id;
  UserFollow.findOne({ where: { user_id, bot_id } }).then((userfollow) => {
    if (!userfollow) {
      return next();
    }
    return res.status(409).send({ message: "follow already exists" });
  });
};

const findByUserId = (req, res, next) => {
  const user_id = req.body.user_id || +req.query.user_id;
  UserFollow.findAll({ where: { user_id } }).then((userfollows) => {
    req.userfollows = userfollows;
    return next();
  });
};

module.exports = {
  checkExistance,
  findByUserId,
};
