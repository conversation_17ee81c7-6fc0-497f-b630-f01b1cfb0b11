const Cart = require("../Models/Cart");

const findCartByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  Cart.findOne({ where: { bot_id } }).then((cart) => {
    if (cart) {
      req.cart = cart;
      return next();
    } else {
      return res.status(404).send({ message: "cart does not exist" });
    }
  });
};

const findCartByBotIdNoValidation = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  Cart.findOne({ where: { bot_id } }).then((cart) => {
    req.cart = cart;
    return next();
  });
};

const checkExistance = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  Cart.findOne({ where: { bot_id } }).then((cart) => {
    if (cart) {
      req.cart = cart;
      return res.status(409).send({ message: "cart already exits" });
    } else {
      return next();
    }
  });
};

module.exports = {
  findCartByBotId,
  checkExistance,
  findCartByBotIdNoValidation,
};
