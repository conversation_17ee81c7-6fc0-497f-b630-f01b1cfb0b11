const PayoneRecord = require("../Models/PayoneRecord");

const findPayoneInvoiceByInvoiceID = (req, res, next) => {
  const invoice_id = req.body.invoice_id || req.query.invoice_id;
  PayoneRecord.findOne({ where: { invoice_id } }).then(
    (payoneRecord) => {
      if (payoneRecord) {
        req.payoneRecord = payoneRecord;
        return next();
      } else {
        return res
          .status(404)
          .send({ message: "Payone Invoice record does not exist" });
      }
    }
  );
};

module.exports = {
  findPayoneInvoiceByInvoiceID,
};
