const CartLog = require("../Models/Cart.Log");
const CartLogItem = require("../Models/Cart.Log.Item");

const findCartLogByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  CartLog.findAll({
    where: { bot_id },
    order: [["createdAt", "DESC"]],
    include: CartLogItem,
  }).then((cartlogs) => {
    req.cartlogs = cartlogs;
    return next();
  });
};

const findCartLog = (req, res, next) => {
  const cart_log_id = req.body.cart_log_id || req.query.cart_log_id;
  CartLog.findOne({
    where: { cart_log_id },
    include: CartLogItem,
  }).then((cartlog) => {
    if (cartlog) {
      req.cartlog = cartlog;
      return next();
    } else {
      return res.send({ message: "log not found" });
    }
  });
};

module.exports = { findCartLogByBotId, findCartLog };
