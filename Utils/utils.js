const sgEmail = require("../Services/Email");
const fetch = require("node-fetch");
const { storageServerUrl } = require("../constants");
const jwt = require("jsonwebtoken");
const { secret } = require("../config/appConfig");
const Bot = require("../Models/Bot");
const sequelize = require("../db");
// const { setPool } = require("../Services/azureBlobStorage");
const Trigger = require("../Models/Trigger");

const extractExtension = (str) => {
  var patt1 = /\.[0-9a-z]+$/i;
  return str.match(patt1)[0];
};
const validateQueryId = (req, res, next, key) => {
  if (req.query[key]) {
    return next();
  } else {
    res.status(422).send({ message: key + " not found" });
  }
};

const validate = (req, res, next, keys) => {
  const bodyKeys = Object.keys(req.body);
  var validate_error = false;
  keys.map((key) => {
    if (key.includes(".")) {
      //SECTION Checking sub entities
      var subKeys = key.split(".");
      if (!req.body[subKeys[0]][subKeys[1]] && !validate_error) {
        validate_error = true;
        c;
        return res.status(422).send({
          message: "please provide the ".concat(subKeys[0], " ", subKeys[1]),
        });
      }
    } else if (bodyKeys.indexOf(key) === -1 && !validate_error) {
      validate_error = true;
      //SECTION When entity is not found
      return res
        .status(422)
        .send({ message: "please provide the ".concat(key) });
    }
  });
  if (!validate_error) {
    return next();
  }
};

const validateModels = (req, res, next, keys) => {
  const bodyKeys = Object.keys(req);
  var validate_error = false;
  keys.map((key) => {
    if (key.includes(".")) {
      //SECTION Checking sub entities
      var subKeys = key.split(".");
      if (!req.body[subKeys[0]][subKeys[1]] && !validate_error) {
        validate_error = true;
        c;
        return res.status(422).send({
          message: "please provide the ".concat(subKeys[0], " ", subKeys[1]),
        });
      }
    } else if (bodyKeys.indexOf(key) === -1 && !validate_error) {
      validate_error = true;
      //SECTION When entity is not found
      return res
        .status(422)
        .send({ message: "please provide the ".concat(key) });
    }
  });
  if (!validate_error) {
    return next();
  }
};

const contactUS = (req, res) => {
  const info = req.body;
  const email = info.email;
  const subject = info.subject;
  const body = info.body;
  const first_name = info.first_name;
  const last_name = info.last_name || "";
  const phone_number = info.phone_number;

  var txt = "";
  if (info.phone_number) {
    txt = txt.concat("\n phone_number", phone_number);
  }
  if (info.first_name) {
    txt = txt.concat("\n name", first_name, last_name);
  }

  sgEmail
    .send({
      to: "<EMAIL>",
      subject: subject.concat(" from ", email),
      from: "<EMAIL>",
      text: body.concat(txt),
    })
    .then(() => {
      res.send({ message: "Thanks" });
    })
    .catch((e) => res.send(e));
};

const createBotStorage = async (req, res, next) => {
  const bot = req.bot;
  const design = req.design;
  const botvoice = req.botvoice;
  const triggers = req.triggers;
  const faqs = req.faqs;
  const bodyToSend = {
    bot: bot ? bot.toJSON() : undefined,
    design: design ? design.toJSON() : undefined,
    botvoice: botvoice ? botvoice.toJSON() : undefined,
    triggers: triggers ? triggers : undefined,
    faqs,
  };
  try {
    await fetch(`${storageServerUrl}/createBot`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(bodyToSend),
    }).then((resp) => resp.json());
  } catch (e) {}
  return next();
};

const duplicateBotStorage = (req, res, next) => {
  const src_bot = req.src_bot;
  const dest_bot = req.dest_bot;
  const bodyToSend = {
    source_directory: src_bot.file_name,
    destination_directory: dest_bot.file_name,
  };
  try {
    fetch(`${storageServerUrl}/bot/duplicate`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(bodyToSend),
    });
  } catch (e) {}

  return next();
};

const deleteBotStorage = (req, res, next) => {
  const file_name = req.bot.file_name;
  const bodyToSend = {
    destination_directory: file_name,
  };
  try {
    fetch(`${storageServerUrl}/api/bot`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(bodyToSend),
    })
      .then((resp) => resp.json())
      .then((data) => {});
  } catch (e) {}

  return next();
};

const validateQueryArray = (req, res, next, keys) => {
  console.log(req.query, "req.query");
  const bodyKeys = Object.keys(req.query);
  var validate_error = false;
  keys.map((key) => {
    if (key.includes(".")) {
      //SECTION Checking sub entities
      var subKeys = key.split(".");
      if (!req.body[subKeys[0]][subKeys[1]] && !validate_error) {
        validate_error = true;
        return res.status(422).send({
          message: "please provide the ".concat(subKeys[0], " ", subKeys[1]),
        });
      }
    } else if (bodyKeys.indexOf(key) === -1 && !validate_error) {
      validate_error = true;
      //SECTION When entity is not found
      return res
        .status(422)
        .send({ message: "please provide the ".concat(key) });
    }
  });
  if (!validate_error) {
    return next();
  }
};

const verifyToken = async (req, res, next) => {
  const { authorization } = req.headers;
  const token = authorization.split(" ")[1];
  try {
    const { user_id } = await jwt.verify(token, secret);
    const bot = await Bot.findOne({ where: { user_id } });

    //do something
    return next();
  } catch (error) {
    res.status(401).send("Unauthorized");
  }
};

// const //revalidateBotPoolStorage = async (req, res, next) => {
//   let bot_id = req.body.bot_id || req.query.bot_id || req.params.bot_id || req.bot_id || req.body?.[0]?.bot_id || req?.trigger?.bot_id || req?.smalltalk?.bot_id || req.category?.bot_id || req.item?.bot_id || req.offer?.bot_id;
//   console.log(bot_id, "bot_id",req.body.trigger_id, req.trigger, req.smalltalk)

//     if(!bot_id && req.body.trigger_id) {
//       const trigger_id  = req.body.trigger_id;
//       const trigger = await Trigger.findOne({ where: { trigger_id }})

//        bot_id = trigger?.bot_id;

//     } else if(!bot_id && req.trigger) {
//       bot_id = req.trigger.bot_id;
//     }
// console.log(bot_id, "bot_id")
// if(!bot_id) return next();
//   const pool =  await sequelize.query("designer_get_all_tags :bot_id", {
//     replacements: {
//       bot_id,
//     },
//   });

//   const key = "bot_pools/" + bot_id;
//  await setPool({
//     [key]: pool[0],
//   })

//   return next();
// }

// const pullBotPools = async (req, res, next) => {

//   req.bot_ids.forEach(async (bot) => {
//     console.log(bot.bot_id, "bot.bot_id")
//     const pool =  await sequelize.query("designer_get_all_tags :bot_id", {
//       replacements: {
//         bot_id: bot.bot_id,
//       },
//     });

//     const key = "bot_pools/" + bot.bot_id;
//     await setPool({
//       [key]: pool[0],
//     })

//     console.log(key, "key ----- done");
//   })
// }
const sendResponse = (req, res, next) => {
  const response = req?.res;
  if (!response) {
    return res.status(404).send({ message: "Resources does not exist" });
  } else if (Array.isArray(response)) {
    return res.status(200).send(response);
  } else {
    return res.status(200).send({ ...response });
  }
};

module.exports = {
  validate,
  contactUS,
  validateQueryId,
  createBotStorage,
  duplicateBotStorage,
  deleteBotStorage,
  validateQueryArray,
  validateModels,
  verifyToken,
  sendResponse,
  //revalidateBotPoolStorage,
  // pullBotPools
};
