const Popup = require("../Models/Popup");
const sequelize = require("../db");

const findPopup = (req, res, next) => {
  const popup_id = req.body.popup_id || req.query.popup_id;
  Popup.findOne({ where: { popup_id } }).then((popup) => {
    req.popup = popup;
    return next();
  });
};

const findPopupsByBotID = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  const active = req.query.active;
  const plugin_type = req.query.plugin_type || "marketing";
  sequelize
    .query("designer_popups_get :botID", {
      replacements: {
        botID: bot_id,
      },
    })
    .then((data) => {
      const popups = data[0];
      if (active) {
        req.popups = popups.filter((a) => {
          if (a.popup_lifetime) {
            return (
              a.popup_active &&
              a.plugin_type === plugin_type &&
              new Date(a.popup_lifetime) > new Date()
            );
          } else {
            return a.popup_active && a.plugin_type === plugin_type;
          }
        });
      } else {
        req.popups = popups;
      }
      return next();
    });
};

module.exports = {
  findPopup,
  findPopupsByBotID,
};
