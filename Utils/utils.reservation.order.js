const ReservationOrder = require("../Models/Reservation.Order");
const sequelize = require("../db");
//designer_get_reservation_orders

const findBotOrders = (req, res, next) => {
  const bot_id = req.query.bot_id || req.body.bot_id;
  sequelize
    .query("designer_get_reservation_orders :bot_id", {
      replacements: {
        bot_id,
      },
    })
    .then((data) => {
      const orders = data[0];
      req.orders = orders;
      return next();
    });
};

const findOrder = (req, res, next) => {
  const order_id = req.query.order_id || req.body.order_id;
  ReservationOrder.findOne({ where: { order_id } }).then((order) => {
    req.order = order;
    return next();
  });
};

module.exports = { findBotOrders, findOrder };
