const Log = require("../Models/Bot.Logs");
const User = require("../Models/User");

const findLogsByBotId = (req, res, next) => {
  const bot_id = req.query.bot_id || req.body.bot_id;
  req.bot_id = bot_id;
  Log.findAll({
    where: {
      bot_id,
    },
    include: {
      model: User,
      attributes: ['email'] 
    }
  }).then((logs) => {
    req.logs = logs;
    return next();
  });
};

module.exports = {
  findLogsByBotId,
};
