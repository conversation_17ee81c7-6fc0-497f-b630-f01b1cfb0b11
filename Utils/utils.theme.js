const Theme = require("../Models/Theme");

const findThems = (req, res, next) => {
  Theme.findAll({}).then((themes) => {
    req.themes = themes;
    return next();
  });
};

const findThemeByThemeId = (req, res, next) => {
  const theme_id = req.body.theme_id || req.query.theme_id;
  Theme.findOne({ where: { theme_id } }).then((Theme) => {
    req.theme = Theme;
    return next();
  });
};

module.exports = {
  findThems,
  findThemeByThemeId,
};
