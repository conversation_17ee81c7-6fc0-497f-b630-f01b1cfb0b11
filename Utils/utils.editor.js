const Editor = require("../Models/Editor");

const findEditorsByBotId = (req, res, next) => {
  const bot_id = req.query.bot_id || req.body.bot_id || req.body[0].bot_id;
  Editor.findAll({
    where: { bot_id },
  }).then((editors) => {
    req.editors = editors;
    return next();
  });
};

const findEditorById = (req, res, next) => {
  const editor_id = req.body.editor_id;
  Editor.findOne({
    where: { editor_id },
  }).then((editor) => {
    req.editor = editor;
    return next();
  });
};

const checkPrivilages = (req, res, next) => {
  const user = req.user;
  Editor.findAll({
    where: { email: user.email },
  }).then((editors) => {
    req.editors = editors;
    return next();
  });
};

const findOneByBot = (req, res, next) => {
  const bot = req.bot;
  const user = req.user;

  Editor.findOne({
    where: { email: user.email, bot_id: bot.bot_id },
  }).then((editor) => {
    req.editor = editor;
    return next();
  });
};

module.exports = {
  findEditorsByBotId,
  findEditorById,
  checkPrivilages,
  findOneByBot,
};
