const BotVoice = require("../Models/Bot.Voice");

const findBotVoiceByBotId = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  req.bot_id = bot_id;
  BotVoice.findOne({ where: { bot_id } }).then((botvoice) => {
    // if (botvoice) {
      req.botvoice = botvoice;
      return next();
    // } else {
    //   return res.sendStatus(404);
    // }
  });
};

const checkExistance = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  BotVoice.findOne({ where: { bot_id } }).then((botvoice) => {
    if (botvoice) {
      req.botvoice = botvoice;
      return res.status(409).send({ message: "bot voice already exits" });
    } else {
      return next();
    }
  });
};

module.exports = { findBotVoiceByBotId, checkExistance };
