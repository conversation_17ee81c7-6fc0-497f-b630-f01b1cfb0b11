const { Op } = require("sequelize");
const InternalLiveChatAgents = require("../Models/InternalLiveChatAgents");
const Editor = require("../Models/Editor");
const InternalLiveChatIntegration = require("../Models/InternalLiveChatIntegration");
const InternalLiveChatQueue = require("../Models/InternalLiveChatQueue");
const InternalLiveChatSession = require("../Models/InternalLiveChatSession");
const InternalLiveChatTransactions = require("../Models/InternalLiveChatTransactions");
const { sendToSignalR } = require ('../calls/messaging.call');


const findAgentByEditor = (req, res, next) => {
  const editor = req.editor;

  if(!editor) {
    return res.status(200).json({ error: "Editor not found" });
  }

  InternalLiveChatAgents.findOne({
    where: {
      editor_id: editor.editor_id,
    },
  })
    .then((agent) => {
      if (agent) {
        const agentData = {
          agent_id: agent.agent_id,
          editor_id: agent.editor_id,
          bot_id: agent.bot_id,
          status: agent.status,
          current_active_chats: agent.current_active_chats,
          createdAt: agent.createdAt,
          updatedAt: agent.updatedAt,
          user: req.user,
          time_zone: agent.time_zone
        };
        req.agent = agentData;
        next();
      } else {
        res.status(404).json({ error: "Agent not found" });
      }
    })
    .catch((error) => {
      res.status(500).json({ error: error.message });
    });
};

const findTransactionsBySessionID = async (req, res, next) => {
  try {
    const { session_id } = req.query;

    const transactions = await InternalLiveChatTransactions.findAll({
      where: {
        session_id,
      },
    });

    req.transactions = transactions;
    return next();
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

const findSessionsByAgentID = async (req, res, next) => {
  try {
    const { agent_id } = req.query;

    const sessions = await InternalLiveChatSession.findAll({
      where: {
        agent_id,
      },
      include: {
        model: InternalLiveChatQueue,
        as: "queue",
      },
    });

    req.sessions = sessions;
    return next();
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

const findIntegrationByBotID = (req, res, next) => {
  const { bot_id } = req.query;

  InternalLiveChatIntegration.findOne({
    where: {
      bot_id,
    },
  })
    .then((integration) => {
      if (integration) {
        req.integration = integration;
        next();
      } else {
        res.status(404).json({ error: "Integration not found" });
      }
    })
    .catch((error) => {
      res.status(500).json({ error: error.message });
    });
};

const findEditorsAgentsByBotID = (req, res, next) => {
  const { bot_id } = req.query;

  Editor.findAll({
    where: {
      bot_id,
      agent_privilege: true,
    },
    include: {
      model: InternalLiveChatAgents,
      as: "agent",
    },
  })
    .then((editors) => {
      req.editorsAgents = editors;
      next();
    })
    .catch((error) => {
      res.status(500).json({ error: error.message });
    });
};

const getSessionStatus = async (bot_id) =>{
  try {
    const agents = await InternalLiveChatAgents.findAll({
      where: { bot_id },
      attributes: ['agent_id']
    });
    const agentIds = agents.map(agent => agent.agent_id);
    
    const closedSessions = await InternalLiveChatSession.count({
      where: {
        agent_id: agentIds,
        status: {[Op.like]:'%closed%'}
      }
    });
    const activeSessions = await InternalLiveChatSession.count({
      where: {
        agent_id: agentIds,
        status: 'active'
      }
    });
    const pendingSessions = await InternalLiveChatSession.count({
      where: {
        agent_id: agentIds,
        status: 'pending'
      }
    });
    return{
      closedSessions: closedSessions,
      activeSessions: activeSessions,
      pendingSessions: pendingSessions
    }
  } catch (error) {
    console.error("Error retrieving closed sessions:", error);
    throw error;
  }
}

const getQueueStatus = async (bot_id) =>{
  try {
    const waitingQueues = await InternalLiveChatQueue.count({
      where: {
        bot_id: bot_id,
        status: 'waiting'
      }
    });
    const droppedQueues = await InternalLiveChatQueue.count({
      where: {
        bot_id: bot_id,
        status: 'dropped'
      }
    });
    return ({
      waitingQueues: waitingQueues,
      droppedQueues: droppedQueues
    })
  } catch (error) {
    console.error("Error retrieving Queues:", error);
    throw error;
  }
}

const getAgentStatus = async (bot_id) =>{
  try {
    const activeAgents = await InternalLiveChatAgents.count({
      where: {
        bot_id: bot_id,
        status: 'active'
      }
    })
    const activeChats = await InternalLiveChatAgents.count({
      where: {
        bot_id: bot_id,
        current_active_chats: {[Op.gt]: 0}
      }
    })

    return({
    activeAgents: activeAgents,
    activeChats: activeChats
  })
  } catch (error) {
    console.error("Error retrieving Agents:", error);
    throw error;
  }
}

const getSessionDataCount = async (req, res, next) =>{
  const bot_id = req.body.bot_id || req.query.bot_id;
  const sessions = await getSessionStatus(bot_id);
  
  await sendToSignalR({
    userId: `internalLivechatDashboard__${bot_id}`,
    message: {
      changed: "session",
      closedSessions: sessions.closedSessions,
      activeSessions: sessions.activeSessions,
      pendingSessions: sessions.pendingSessions
    },
  })
  if(!req.session){
    req.session = {
      closedSessions: sessions.closedSessions,
      activeSessions: sessions.activeSessions,
      pendingSessions: sessions.pendingSessions
    };
  }
  return next();
}

const getQueueDataCount = async(req, res, next) =>{
  const bot_id = req.body.bot_id || req.query.bot_id;
  const queues = await getQueueStatus(bot_id);
  
  await sendToSignalR({
    userId: `internalLivechatDashboard__${bot_id}`,
    message: {
      changed: "queue",
      waitingQueues: queues.waitingQueues,
      droppedQueues: queues.droppedQueues
    },
  })
  if(!req.queue){
    req.queue = {
      waitingQueues: queues.waitingQueues,
      droppedQueues: queues.droppedQueues
    }
  }
  return next();
}

const getAgentDataCount = async(req, res, next) =>{
  const bot_id = req.body.bot_id || req.query.bot_id;
  const agents = await getAgentStatus(bot_id);
  
  await sendToSignalR({
    userId: `internalLivechatDashboard__${bot_id}`,
    message: {
      changed: "agent",
      activeAgents: agents.activeAgents,
      activeChats: agents.activeChats
    },
  })
  if(!req.agent){
    req.agent ={
      activeAgents: agents.activeAgents,
      activeChats: agents.activeChats
    }
  }
  return next();
}

const getSessionsData = async (req, res, next) =>{
  const bot_id = req.body.bot_id || req.query.bot_id;
  const sessions = await getSessionStatus(bot_id);
  req.session = {
    closedSessions: sessions.closedSessions,
    activeSessions: sessions.activeSessions,
    pendingSessions: sessions.pendingSessions
  }
  return next();
}

const getQueuesData = async(req, res, next) =>{
  const bot_id = req.body.bot_id || req.query.bot_id;
  const queues = await getQueueStatus(bot_id);
  console.log("q", queues);
  req.queue = {
    waitingQueues: queues.waitingQueues,
    droppedQueues: queues.droppedQueues
  }
  return next();
}

const getAgentsData = async(req, res, next) =>{
  const bot_id = req.body.bot_id || req.query.bot_id;
  const agents = await getAgentStatus(bot_id);
  console.log("a", agents);
  req.agent = {
    activeAgents: agents.activeAgents,
    activeChats: agents.activeChats
  }
  return next();
}


module.exports = {
  findAgentByEditor,
  findTransactionsBySessionID,
  findSessionsByAgentID,
  findIntegrationByBotID,
  findEditorsAgentsByBotID,
  getSessionDataCount,
  getQueueDataCount,
  getAgentDataCount,
  getSessionsData,
  getQueuesData,
  getAgentsData,
};
