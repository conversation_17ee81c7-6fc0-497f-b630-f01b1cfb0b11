const FeedbackLog = require("../Models/FeedbackLogIlo");
const FeedbackQuestion = require("../Models/FeedbackQuestionsIlo");
const sequelize = require("../db");


const findById = async (req, res, next) => {
    const feedback_id = req.body.feedback_id || req.query.feedback_id;
    const data = await sequelize.query(`
    SELECT * FROM bot_designer_customer_ilo_feedbacks
    INNER JOIN bot_designer_dialogs
    on bot_designer_customer_ilo_feedbacks.dialog_id = bot_designer_dialogs.dialog_id
    where
    bot_designer_customer_ilo_feedbacks.feedback_id = ${feedback_id}
`);
    if (data[0]?.length) {
        req.feedback = data[0][0];
        return next();
    } else {
        return res.sendStatus(404);
    }

};

const findAll = async (req, res, next) => {
    const dialog_id = req.body.dialog_id || req.query.dialog_id;
    const data = await sequelize.query(`
    SELECT * FROM bot_designer_customer_ilo_feedbacks
    INNER JOIN bot_designer_dialogs
    on bot_designer_customer_ilo_feedbacks.dialog_id = bot_designer_dialogs.dialog_id
    where bot_designer_dialogs.dialog_id = ${dialog_id}
`);
    if (data[0]?.length) {
        req.feedbacks = data[0];
        return next();
    } else {
        return res.sendStatus(404);
    }

};
const findLogById = async (req, res, next) => {
    const feedback_log_id = req.body.feedback_log_id || req.query.feedback_log_id;
    const data = await sequelize.query(`
    SELECT * FROM bot_designer_customer_ilo_feedback_logs
    INNER JOIN bot_designer_customer_ilo_feedback_questions
    on bot_designer_customer_ilo_feedback_logs.feedback_question_id = bot_designer_customer_ilo_feedback_questions.feedback_question_id
    where
     bot_designer_customer_ilo_feedback_logs.feedback_log_id = ${feedback_log_id}
`);
    if (data[0]?.length) {
        req.feedbackLog = data[0][0];
        return next();
    } else {
        return res.sendStatus(404);
    }

};

const findLogAll = async (req, res, next) => {
    const feedback_id = req.body.feedback_id || req.query.feedback_id;
    const data = await sequelize.query(`
        SELECT * FROM bot_designer_customer_ilo_feedback_logs 
        left JOIN bot_designer_customer_ilo_feedbacks  
        on bot_designer_customer_ilo_feedbacks.feedback_id = bot_designer_customer_ilo_feedback_logs.feedback_id
        left JOIN bot_designer_customer_ilo_feedback_questions
        on bot_designer_customer_ilo_feedback_logs.feedback_question_id = bot_designer_customer_ilo_feedback_questions.feedback_question_id
        where bot_designer_customer_ilo_feedback_logs.feedback_id = ${feedback_id}
    `);
    if (data[0]?.length) {
        req.feedbackLogs = data[0];
        return next();
    } else {
        return res.sendStatus(404);
    }

};

const findQuestionById = (req, res, next) => {
    const feedback_question_id = req.body.feedback_question_id || req.query.feedback_question_id;
    FeedbackQuestion.findOne({ where: { feedback_question_id } }).then((feedbackQuestion) => {
        if (feedbackQuestion) {
            req.feedbackQuestion = feedbackQuestion;
            return next();
        } else {
            return res.sendStatus(404);
        }
    });
};

const findQuestionAll = (req, res, next) => {
    const dialog_id = req.body.dialog_id || req.query.dialog_id;
    FeedbackQuestion.findAll({ where: { dialog_id } }).then((feedbackQuestions) => {
        req.feedbackQuestions = feedbackQuestions;
        return next();
    });
};

module.exports = {
    findById,
    findAll,
    findLogById,
    findLogAll,
    findQuestionById,
    findQuestionAll,
    findById
};
