const express = require("express");
const path = require("path");
const bodyParser = require("body-parser");
const app = express();
const cors = require("cors");
const api = require("./API");
const setupAssociations = require('./Models/associations');
const dir = path.join(__dirname, "./blob");
// const swaggerUI = require('swagger-ui-express');
// const swaggerOptions = require('./docs/swagger'); 
// const swaggerSpec = require('./docs/swagger'); 

console.log("public dir", dir);
app.use(cors());
app.use(express.static(dir));
app.use(express.json({limit: '50mb'}));
app.use(
  bodyParser.urlencoded({
    extended: true,
  })
);
app.use(api);
// app.use('/api/docs', swaggerUI.serve, swaggerUI.setup(swaggerSpec)); 

app.get("/", (req, res) => {
  res.send({ message: "HELLO" });
});

app.all("*", function (req, res, next) {
  const allowedOrigins = [
    "https://bot-designer-server-2.azurewebsites.net",
    "https://chatbot.infotointell.com",
    "https://dev.searchat.com",
    "https://dev.infotointell.com",
    "https://very-secret-app.azurewebsites.net",
    "https://incubator-stage.azurewebsites.net",
    "https://mock-bot-stage.azurewebsites.net",
    "https://mock-bot-a083.azurewebsites.net",
    "https://performit-bot.azurewebsites.net",
    "https://sa3i-bot-2.azurewebsites.net",
    "https://searchat-builder-staging-eyxob.ondigitalocean.app",
    "https://sanad-builder-t5btt.ondigitalocean.app"
  ];
  // var origin = req.get("Origin") || "*";
  const origin = req.headers.origin;
  console.log("origin", origin);
  if (allowedOrigins.includes(origin)) {
    res.setHeader("Access-Control-Allow-Origin", origin);
  }
  // res.set("Access-Control-Allow-Origin", origin);
  res.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  res.set("Access-Control-Expose-Headers", "Content-Length");
  res.set("Access-Control-Allow-Credentials", "true");
  res.set("Access-Control-Allow-Headers", "X-Requested-With, Content-Type"); // add the list of headers your site allows.
  if ("OPTIONS" == req.method) return res.send(200);
  next();
});

setupAssociations();

module.exports = app;
