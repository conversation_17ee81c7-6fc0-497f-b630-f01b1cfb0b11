const { Sequelize } = require("sequelize");
const { database } = require("./config/appConfig");

console.log("database",database)
const sequelize = new Sequelize(
  database.dbname,
  database.username,
  database.password,
  {
    logging: true,
    host: database.host,
    dialect: "mssql",
    pool: {
      max: 5,
      min: 0,
      idle: 10000,
      acquire: 60000,
    },
    dialectOptions: {
      trustedConnection: true,
      options: {
        encrypt: true,
      }
    },
  }
);

try {
  sequelize.authenticate().then( () => {
    console.log("Connection has been established successfully.");
    // sequelize.sync();
  });
} catch (error) {
  console.error("Unable to connect to the database:", error);
}
module.exports = sequelize;
