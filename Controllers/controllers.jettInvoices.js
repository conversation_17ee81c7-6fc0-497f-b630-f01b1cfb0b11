const { getFile } = require("../calls/file.call");
const JettInvoice = require("../Models/Jett.Invoice");
const JettBookingTicket = require("../Models/Jett.BookingTicket");
const JettCardRecharge = require("../Models/Jett.CardRecharge");
const JettKHBMasterTicket = require("../Models/Jett.KHBMasterTicket");
const JettKHBSlaveTicket = require("../Models/Jett.KHBSlaveTicket");
const JettTracks = require("../Models/Jett.Tracks");

const createInvoice = async (req, res, next) => {
  const data = { ...req.body };
  JettInvoice.create(data)
    .then(async (invoice) => {
      req.invoice = invoice;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const updateInvoice = async (req, res, next) => {
  const data = { ...req.body };
  JettInvoice.findOne({ where: { invoice_id: data.invoice_id } })
    .then(async (invoice) => {
      if (!invoice) {
        return res.status(404).send("Invoice not found");
      }
      invoice.updateInfo(data);
      await invoice.save();
      req.invoice = invoice;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const payBookingInvoice = async (req, res, next) => {
  const invoice_id = req.body.invoice_id;
  const payment = req.body.payment;
  const ticket_numbers = req.body.ticket_numbers;
  // change invoice status to paid
  // create Tickets
  JettInvoice.findOne({ where: { invoice_id } })
    .then(async (invoice) => {
      if (!invoice) {
        return res.status(404).send("Invoice not found");
      }

      const ticketsData = await getFile(invoice.collected_data_url);
      const tickets = JSON.parse(ticketsData.file_data);

      let isIncomplete = false
      for (const ticket of tickets) {
        const ticket_number = ticket_numbers?.find(
          (t) => t.reservationID === ticket.reservation.ReservationID.toString()
        )?.TicketNo
        if(!ticket_number){
          isIncomplete = true
        }
        await JettBookingTicket.create({
          jett_invoice_id: invoice.jett_invoice_id,
          name: ticket.name,
          phone: ticket.phone,
          email: ticket.email,
          national_id: ticket.nationalID,
          track: ticket.chosenTrack.path,
          track_id: ticket.chosenTrack.track_id,
          pickup_point: ticket.pickupPoint.PickupPointName,
          pickup_point_id: ticket.pickupPoint.PickupPointID,
          reservation_id: ticket.reservation.reservationID,
          ticket_number: ticket_number, 
          ticket_price: ticket.amount,
          ticket_price_name: ticket.price.TicketPriceName,
          price_type_name: ticket.price.PriceTypeName,
          trip_date: ticket.chosenTrip.DEPARTURE_DATE,
          trip_time: ticket.chosenTrip.DEPARTURE_TIME,
          seat_no: ticket.seat,
          is_return_trip: ticket?.is_return_trip
        });
      }

      if(isIncomplete){
        invoice.updateInfo({ status: "incomplete", payment });
      } else {
        invoice.updateInfo({ status: "paid", payment });
      }   
      await invoice.save();

      req.invoice = invoice;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const createMSRKHBTickets = async (req, res, next) => {
  const data = { ...req.body };
  console.log(data, "----------------------------")
  JettKHBMasterTicket.create(data).then((ticket) => {
    req.ticket = ticket;
    return next();
  }).catch((e) =>
    console.log(e)
  );
};

const getTicket = async (req, res) => {
  return res.send(req.ticket);
}

const createSLVKHBTickets = async (req, res, next) => {
  const data = { ...req.body };
  JettKHBSlaveTicket.create(data).then((ticket) => {
    req.ticket = ticket;
    return next();
  });

}

const payCardRecharge = async (req, res, next) => {
  const invoice_id = req.body.invoice_id;
  const payment = req.body.payment;
  // change invoice status to paid
  JettInvoice.findOne({ where: { invoice_id } })
    .then(async (invoice) => {
      if (!invoice) {
        return res.status(404).send("Invoice not found");
      }
      invoice.updateInfo({ status: "paid", payment });
      await invoice.save();

      JettCardRecharge.create({
        jett_invoice_id: invoice.jett_invoice_id,
        card_number: req.body.card_number,
        subscription_number: req.body.subscription_number,
        amount: req.body.amount,
      });
      req.invoice = invoice;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const createTrack = async (req, res, next) => {
  const data = { ...req.body };
  JettTracks.create(data)
    .then((track) => {
      req.track = track;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const updateTrack = async (req, res, next) => {
  const data = { ...req.body
  };

  JettTracks.findOne({ where: { jett_track_id: data.jett_track_id } })
    .then(async (track) => {
      if (!track) {
        return res.status(404).send("Track not found");
      }
      track.updateInfo(data);
      await track.save();
      req.track = track;
      return next();
    })
    .catch((e) => res.status(422).send(e));
}

const deleteTrack = async (req, res, next) => {
  const data = { ...req.body };
  JettTracks.findOne({ where: { jett_track_id: data.jett_track_id } })
    .then(async (track) => {
      if (!track) {
        return res.status(404).send("Track not found");
      }
      await track.destroy();
      return res.send("Track deleted successfully");
    })
    .catch((e) => res.status(422).send(e));
}

const getTrack = async (req, res) => {
  return res.send(req.track);
};

const getTracks = async (req, res) => {
  return res.send(req.tracks);
};

const getInvoice = async (req, res) => {
  return res.send(req.invoice);
};

const getAllInvoices = async (req, res) => {
  return res.send(req.invoices);
};

module.exports = {
  createInvoice,
  getInvoice,
  updateInvoice,
  payBookingInvoice,
  payCardRecharge,
  createMSRKHBTickets,
  createSLVKHBTickets,
  getTicket,
  getAllInvoices,
  createTrack,
  getTrack,
  updateTrack,
  deleteTrack,
  getTracks,
};
