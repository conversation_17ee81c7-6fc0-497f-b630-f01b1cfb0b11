const db = require("../db");

const get = (req, res) => {
  res.send(req.botdomain);
};

const set = async (req, res, next) => {
  const bot_id = req.body.bot_id || req.bot.bot_id;
  const host = req.body.host || req.query.host;
  req.botdomain = await db.query("set_bot_domain :bot_id , :host", {
    replacements: {
        host,
        bot_id
    }
  });
  return next();
};

const validate = async (req, res, next) => {
  const bot_id = req.body.bot_id || req.bot.bot_id;
  const host = req.body.host || req.query.host;
  const botdomain = await db.query("get_bot_domain :bot_id , :host", {
    replacements: {
        host,
        bot_id
    }
  });  
  if (botdomain[0][0]) {
    res.send({
      success: true,
    });
  } else if (!botdomain[0][0]) {
    res.send({
      success: false,
    });
  }
};

const find = (req, res, next) => {
  const bot_id = req.body.bot_id || req.bot.bot_id;
  const host = req.body.host || req.query.host;

  BotDomain.findOne({
    where: {
      host,
      domain,
    },
  }).then((botdomain) => {
    req.botdomain = botdomain;
    return next();
  });
};

const update = async (req, res, next) => {
  const botdomain = req.botdomain;
  const data = { ...req.body };
  botdomain.updateInfo(data);
  req.botdomain = botdomain;
  await botdomain.save();
  return next();
};

module.exports = {
  get,
  update,
  set,
  find,
  validate,
};
