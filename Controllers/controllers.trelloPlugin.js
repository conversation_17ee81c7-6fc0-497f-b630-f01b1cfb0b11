const TrelloPlugin = require("../Models/TrelloPlugin");
const get = (req, res) => {
  return res.send(req.trelloplugin);
};
const find = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  TrelloPlugin.findOne({ where: { bot_id } }).then((trelloplugin) => {
    if (!trelloplugin) {
      return res.status(401).send({ message: "plugin trello not found" });
    } else {
      console.log(trelloplugin, "trelloplugin");

      req.trelloplugin = trelloplugin;
      return next();
    }
  });
};
const set = (req, res, next) => {
  const data = req.body;
  TrelloPlugin.create(data).then((trelloplugin) => {
    req.trelloplugin = trelloplugin;
    return next();
  });
};
const update = async (req, res, next) => {
  const data = req.body;
  const trelloplugin = req.trelloplugin;
  trelloplugin.updateInfo(data);
  await trelloplugin.save();
  return next();
};

const purge = (req, res, next) => {
  const bot_id = req.body.bot_id;
  TrelloPlugin.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    res.send({ message: "trello integration data deleted" });
  });
};
module.exports = { update, find, set, get, purge };
