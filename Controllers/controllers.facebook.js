const Facebook = require("../Models/Facebook");
const fetch = require("node-fetch");
const sequelize = require("../db");
const { createDELETEJSON } = require("../helper/header");

const get = (req, res) => {
  res.send(req.facebook);
};

const getAll = (req, res) => {
  res.send(req.facebookPages);
};

const set = (req, res, next) => {
  const data = { ...req.body };

  Facebook.create({
    bot_id: data.bot_id,
    pageId: data.pageId,
    page_token: data.page_token,
    channel: data.channel,
    name: data.name,
  })
    .then((facebook) => {
      req.facebook = facebook;
      return next();
    })
    .catch((e) => {
      console.log("error", e);
      res.status(409).send({ message: "facebook is already exists" });
    });
};

const setSubscription = async (req, res, next) => {
  const facebook = req.facebook;
  const get_started_trigger = req.body.get_started_trigger;
  const pageId =
    facebook.channel === "instagram"
      ? req.body.connected_facebook_page_id
      : facebook.pageId;
  const page_token =
    facebook.channel === "instagram"
      ? req.body.connected_facebook_page_token
      : facebook.page_token;
  const data = await fetch(
    `https://graph.facebook.com/v20.0/${pageId}/subscribed_apps?subscribed_fields=messaging_postbacks,messages,messaging_optouts,
    messaging_optins,message_reads,
    inbox_labels,message_reactions,messaging_feedback,messaging_handovers
    &access_token=${page_token}`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    }
  ).then((resp) => resp.json());
  fetch(
    `https://graph.facebook.com/v20.0/me/messenger_profile?access_token=${page_token}`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        get_started: {
          payload: get_started_trigger || "menu",
        },
        greeting: [
          {
            locale: "default",
            text: "Hello {{user_first_name}}!",
          },
        ],
      }),
    }
  ).then((resp) => resp.json());
  //res.send(data);
  return next();
};

const purge = (req, res, next) => {
  const bot_id = req.body.bot_id;
  Facebook.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    res.send({ message: "facebook connection deleted" });
  });
};

const update = async (req, res, next) => {
  const facebook = req.facebook;
  const facebookData = req.body;

  console.log("req.body");
  console.log(req.body);
  console.log("req.body");
  if (facebookData && facebook) {
    facebook.updateInfo({ ...facebookData });
    await facebook.save();
    return next();
  } else {
    return next();
  }
};

const setGetStartedTrigger = async (req, res, next) => {
  const get_started_trigger = req.body.get_started_trigger;

  const page_token = req.facebook.page_token;

  const data = await fetch(
    `https://graph.facebook.com/v20.0/me/messenger_profile?access_token=${page_token}`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        get_started: {
          payload: get_started_trigger,
        },
        greeting: [
          {
            locale: "default",
            text: "Hello {{user_first_name}}!",
          },
        ],
      }),
    }
  ).then((resp) => resp.json());

  res.status(200).send(data);
};

const getPagesFromToken = async (req, res) => {
  const accessToken = req.body.access_token;
  const data_1 = await fetch(
    "https://graph.facebook.com/v20.0/oauth/access_token?grant_type=fb_exchange_token&client_id=****************&client_secret=69e545ae15436cd487e0deb6ff9b9191&fb_exchange_token=" +
      accessToken,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  ).then((res) => res.json());
  const long_lived_user_token = data_1.access_token;
  const data_2 = await fetch(
    "https://graph.facebook.com/v20.0/me?access_token=" + long_lived_user_token,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  ).then((res) => res.json());
  const user_id = data_2.id;
  const data_3 = await fetch(
    "https://graph.facebook.com/v20.0/" +
      user_id +
      "/accounts?access_token=" +
      long_lived_user_token,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  ).then((res) => res.json());

  const pagesToSend =data_3?.data?.length ? [...data_3.data] : []

  // const insta_pages = [];
  for (var indf = 0; indf < data_3?.data?.length; indf++) {
    var data_4 = await fetch(
      "https://graph.facebook.com/v20.0/" +
        data_3.data[indf].id +
        "?access_token=" +
        long_lived_user_token +
        "&fields=access_token,instagram_business_account{id,name,username}",
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }
    ).then((res) => res.json());

    if (data_4.instagram_business_account) {
      pagesToSend[indf] = {
        ...data_3.data[indf],
        instagram_business_account: {
          ...data_4.instagram_business_account,
          access_token: data_4.access_token,
        },
      };
    }
    //console.log("LBC", data_4);
    // if (data_4.instagram_business_account) {
    //   insta_pages.push({
    //     ...data_4.instagram_business_account,
    //     access_token: data_4.access_token,
    //     connected_facebook_page_id: data_3.data[indf].id,
    //     connected_facebook_page_token: data_3.data[indf].access_token,
    //   });
    // }
  }


  res.send({ long_lived_user_token, user_id, pages: pagesToSend });
};

const getInstagramPages = async (req, res) => {
  const accessToken = req.body.access_token;
  const data_1 = await fetch(
    "https://graph.facebook.com/v20.0/oauth/access_token?grant_type=fb_exchange_token&client_id=****************&client_secret=69e545ae15436cd487e0deb6ff9b9191&fb_exchange_token=" +
      accessToken,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  ).then((res) => res.json());
  const long_lived_user_token = data_1.access_token;
  const data_2 = await fetch(
    "https://graph.facebook.com/v20.0/me?access_token=" + long_lived_user_token,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  ).then((res) => res.json());
  const user_id = data_2.id;
  const data_3 = await fetch(
    "https://graph.facebook.com/v20.0/" +
      user_id +
      "/accounts?access_token=" +
      long_lived_user_token,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }
  ).then((res) => res.json());

  if(!data_3?.data?.length) {
    return res.send({ message: "No pages"});
  }
  const insta_pages = [];
  for (var indf = 0; indf < data_3.data.length; indf++) {
    var data_4 = await fetch(
      "https://graph.facebook.com/v20.0/" +
        data_3.data[indf].id +
        "?access_token=" +
        long_lived_user_token +
        "&fields=access_token,instagram_business_account{id,name,username}",
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }
    ).then((res) => res.json());
    //console.log("LBC", data_4);
    if (data_4.instagram_business_account) {
      insta_pages.push({
        ...data_4.instagram_business_account,
        access_token: data_4.access_token,
        connected_facebook_page_id: data_3.data[indf].id,
        connected_facebook_page_token: data_3.data[indf].access_token,
      });
    }
  }

  res.send({
    long_lived_user_token,
    user_id,
    pages: insta_pages,
  });
};

const getMessagesBySession = (req, res) => {
  const session_id = req.query.session_id;
  //get_transactions_by_session;

  sequelize
    .query("get_transactions_by_session :session_id", {
      replacements: {
        session_id,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const disconnectPage = async (req, res, next) => {
  const bot_id = req.body.bot_id;
  const facebook_channel_id = req.body.facebook_channel_id;

  const page = await Facebook.findOne({
    where: {
      bot_id,
      facebook_channel_id,
    },
  });

  console.log("page", page);

  if(!page) {
    return res.status(200).send({ error: "facebook page not found" });
  }

try {
  console.log("here", page.pageId)
  if(page.channel === "facebook") {
    const resp = await fetch(
      `https://graph.facebook.com/v20.0/${page?.pageId}/subscribed_apps?access_token=${page.page_token}`,
      createDELETEJSON({})
    );

    console.log("res", resp);
  }
    await page.destroy();
    res.send({ message: "facebook page disconnected" });
} catch (error) {
  console.log("error", error);
    res.status(500).send({ error: "facebook page not disconnected" });
}

};

const purgePage = (req, res, next) => {
  const bot_id = req.body.bot_id;
  const facebook_channel_id = req.body.facebook_channel_id;

  Facebook.destroy({
    where: {
      bot_id,
      facebook_channel_id,
    },
  }).then(() => {
    res.send({ message: "facebook page disconnected" });
  });
};

module.exports = {
  get,
  getAll,
  set,
  update,
  purge,
  getPagesFromToken,
  setSubscription,
  getInstagramPages,
  setGetStartedTrigger,
  getMessagesBySession,
  purgePage,
  disconnectPage
};
