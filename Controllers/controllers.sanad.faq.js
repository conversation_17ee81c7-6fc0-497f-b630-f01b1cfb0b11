const SanadFAQ = require("../Models/Sanad.Faq");
const sequelize = require("../db");
const { lemmatization, lemmtaizationLocal } = require("../helper/helper")

const get = (req, res) => {
  res.send(req.sanadFaq);
};

const getAll = (req, res, next) => {
  res.send(req.sanadFaqs);
};

const set = async (req, res, next) => {
  const data = { ...req.body };
  SanadFAQ.create({
    ...data,
  })
    .then((sanadFaq) => {
      req.sanadFaq = sanadFaq;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const setAll = (req, res, next) => {
    console.log("req.body", req.body)
    SanadFAQ.bulkCreate(req.body)
    .then((sanadFaqs) => {
      req.sanadFaqs = sanadFaqs;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const updateMany = async (req, res, next) => {
  const sanadFaqs = req.sanadFaqs;
  const data = [...req.body];
  // faqs.map( (faq) => {
  for (var i = 0; i < sanadFaqs.length; i++) {
    var sanadFaq = sanadFaqs[i];
    var faqData = data.find((a) => a.sanad_faq_id === sanadFaq.sanad_faq_id);
    sanadFaq.updateInfo(faqData);
    await sanadFaq.save();
  }
  //});
  return next();
};

const update = async (req, res, next) => {
  const sanadFaq = req.sanadFaq;
  const data = { ...req.body };
  sanadFaq.updateInfo(data);
  req.sanadFaq = sanadFaq;
  await sanadFaq.save();
  return next();
};

const purge = (req, res, next) => {
  const data = { ...req.body };
  SanadFAQ.destroy({
    where: {
        sanad_faq_id: data.sanad_faq_id,
    },
  }).then(() => {
    res.send({ message: "sanad faq deleted successfully" });
  });
};


const getPool =  (req,res) => {
    sequelize.query("designer_get_sanad_pool").then(data =>{
        res.send(data[0])
    })
}

const getAllFaqsPerSer = async (req, res) => {
  const bot_id= req.query.bot_id;
 const data= await sequelize.query("designer_get_faqs_related_service :bot_id ", {
      replacements: {
        bot_id: bot_id,
      },
    });
    return res.send(data[0]);
  };

const purgeAllQna = (req, res, next) => {
  const bot_id = req.body.bot_id;
  SanadFAQ.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    return res.send({ message: "many where deleted" });
  });
};

const purgeMany = (req, res, next) => {
  const faq_ids = [...req.body].map((a) => a.sanad_faq_id);
  const bot_ids = [...req.body].map((a) => a.bot_id);

  SanadFAQ.destroy({
    where: {
      bot_id: bot_ids,
      sanad_faq_id: faq_ids,
    },
  }).then(() => {
    return next();
  });
};


const lemmtaizationMany = async (req, res, next) => {
  try {
    const data = [...req.body];
    const newData = await Promise.all(
      data.map(async (ent) => {
        return {
          ...ent,
          lemmatized_question: ent?.lemmatized_question
            ? (await lemmtaizationLocal(ent?.lemmatized_question))?.data?.answer ||
              ent.question
            : "",
        };
      })
    );
    req.body = newData;
    next();
  } catch (error) {
    console.log(error)
  }
};
const lemmtaizationOne = async (req, res, next) => {
  try {
    const data = { ...req.body };

    const newData = {
      ...data,
      lemmatized_question: data?.lemmatized_question
        ? (await lemmtaizationLocal(data?.lemmatized_question))?.data?.answer ||
          data.question
        : "",
    };

    req.body = newData;

    next();
  } catch (error) {
    console.log(error);
  }
};

module.exports = {
  get,
  set,
  update,
  purge,
  getAll,
  setAll,
  getPool,
  purgeAllQna,
  purgeMany,
  updateMany,
  getAllFaqsPerSer,
  lemmtaizationMany,
  lemmtaizationOne,
};
