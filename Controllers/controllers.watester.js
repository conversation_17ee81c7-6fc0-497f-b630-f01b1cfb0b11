const WATester = require("../Models/WaTester");

const get = (req, res) => {
  res.send(req.watester);
};

const getAll = (req, res, next) => {
  res.send(req.watesters);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  WATester.create(data)
    .then((watester) => {
      req.watester = watester;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const update = async (req, res, next) => {
  const watester = req.watester;
  const data = { ...req.body };
  watester.updateInfo(data);
  req.watester = watester;
  await watester.save();
  return next();
};

const purge = (req, res, next) => {
  const data = { ...req.body };
  WATester.destroy({
    where: {
      whatsapp_tester_id: data.whatsapp_tester_id,
    },
  }).then(() => {
    res.send({ message: "Room deleted successfully" });
  });
};
const purgeOne = (req, res, next) => {
  const watester = req.watester;
  const data = { ...req.body };
  console.log(data, "data");
  WATester.destroy({
    where: {
      bot_id: data.bot_id,
      phone: data.phone,
    },
  }).then(() => {
    return next();
  });
};

module.exports = {
  get,
  set,
  update,
  purge,
  getAll,
  purgeOne,
};
