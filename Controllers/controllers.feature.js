const Feature = require("../Models/Feature");

const get = (req, res) => {
  res.send(req.feature);
};

const getAll = (req, res) => {
  res.send(req.features);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  Feature.create({
    bot_id: data.bot_id,
    feature_name: data.feature_name,
  }).then((feature) => {
    req.feature = feature;
    return next();
  });
};

const purge = (req, res, next) => {
  const feature_id = req.body.feature_id;
  Feature.destroy({
    where: {
      feature_id,
    },
  }).then(() => {
    res.send({ message: "feature deleted" });
  });
};

const update = async (req, res, next) => {
  const feature = req.feature;
  const data = req.body;
  if (data && feature) {
    feature.updateInfo({ ...data });
    await feature.save();
    return next();
  } else {
    return next();
  }
};

const purgeAll = (req, res, next) => {
  const bot_id = req.body.bot_id;
  Feature.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    return next();
  });
};

const setAll = (req, res, next) => {
  const data = req.body.data;
  const bot_id = req.body.bot_id;
  const featuresToAdd = [];
  data.map((a) => {
    if (
      featuresToAdd.findIndex((b) => a.feature_name === b.feature_name) === -1
    ) {
      featuresToAdd.push({
        bot_id,
        feature_name: a.feature_name,
      });
    }
  });
  Feature.bulkCreate(featuresToAdd).then((features) => {
    req.features = features;
    console.log("PICTURE ON WALL");
    return next();
  });
};

module.exports = { get, getAll, set, update, purgeAll, purge, setAll };
