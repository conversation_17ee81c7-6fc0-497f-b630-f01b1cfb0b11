const Intent = require("../Models/Intent");

const get = (req, res) => {
  res.send(req.intent.toJSON());
};

const getAll = (req, res) => {
  res.send(req.intents);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  Intent.create(data).then((intent) => {
    req.intent = intent;
    return next();
  });
};

const purge = (req, res, next) => {
  const intent_id = req.body.intent_id;
  Intent
    .destroy({
      where: {
        intent_id,
      },
    })
    .then(() => {
      res.send({ message: "intent deleted" });
    });
};

const update = async (req, res, next) => {
  const intent = req.intent;
  const intentData = req.body;
  if (intentData && intent) {
    intent.updateInfo({ ...intentData });
    await intent.save();
    return next();
  } else {
    return next();
  }
};

module.exports = { get, getAll, set, update, purge };
