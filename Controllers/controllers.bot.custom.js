const Bot_Custom = require("../Models/Bot.Custom");
const Custom_Class = require("../Models/Custom.Class");
const Custom_Payment = require("../Models/Custom.Payment")
const Custom_Attendance = require("../Models/Custom.Attendance")
const Trainee_Class = require("../Models/Custom.Class.Trainee")


const sequelize = require("../db");
const Custom_Discount = require("../Models/Custom.Discount");

const get = (req, res) => {
  res.send(req.custom);
};

const set = (req, res, next) => {
    const data = {
      ...req.body,
      form_details: JSON.stringify(req.body.form_details)
    };
    console.log("data",data)
    Bot_Custom.create(data).then((custom) => {
      req.custom= custom;
      return next();
    }).catch(err => {
      console.log("err",err)
    })
  };

const update = async (req, res) => {
  const custom = req.custom;
  const data = { 
    ...req.body ,
    form_details: JSON.stringify(req.body.form_details)
  };
  custom.updateInfo(data);
  req.custom = custom;
  await custom.save();
  res.send({
    ...req.custom.dataValues,
    form_details: JSON.parse(req.custom.form_details)
  });
};

const purge = async (req, res, next) => {
  const bot_id = req.body.bot_id;
  Bot_Custom.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    res.send({ message: "bot custom deleted" });
  });
};


const getDataByTableBotId = async (req, res, next) => {
  const bot_id = req.body.bot_id;
  const table_name = req.body.table_name;
  sequelize.query(`
    SELECT * FROM ${table_name} WHERE bot_id = ${bot_id}
  `).then((data) => {
    res.send(data[0])
  })
};

const deleteFromCustomTable = async(req,res) => {
  const pk_name = req.body.pk_name;
  const pk = req.body.pk;
  const table_name = req.body.table_name;
  sequelize.query(`
    DELETE FROM ${table_name} WHERE ${pk_name} = ${pk}
  `).then(() => {
    res.send({
      message: "deleted successfully"
    })
  })
}


const updateDataTableFromCustTble = async(req,res,next) => {
  const table_name = req.body.table_name;
  const data = {...req.body};
  const class_id = req.body.class_id;
  const trainee_id = req.body.trainee_id
  const paid_balance = req.body.paid_balance;
  const payment_id = req.body.payment_id;
  const discount_id = req.body.discount_id

  const pkname = Object.keys(data)[0];
  const keys = Object.keys(data).filter(a => a!== "bot_id" && a !== "table_name" && a !==pkname);
  var sql = "UPDATE " + table_name +  " ";
  sql = sql + "SET" +  " ";
  for( var i =0; i< keys.length; i++){
    sql = sql + keys[i] + "=" +  "'" + data[keys[i]] + "'"
    
    if(i+1 !== keys.length ){
     sql = sql + ","
    }
    sql = sql + " "
  }
  sql = sql + "WHERE " + pkname + "=" + data[pkname]
  var unpaid_balance = 0;     
  if(table_name.includes("Payments")||table_name.includes("payments")){
    Custom_Discount.findOne({where:{discount_id:discount_id}}).then(async(custom_discount)=>{ 
      if(Boolean(custom_discount?.discount_percentage)){
       unpaid_balance =await parseFloat((85 - (85 * custom_discount?.discount_percentage)) - paid_balance)
      }
      else if(Boolean(custom_discount?.discount_value)){
      unpaid_balance= await parseFloat( (85 - custom_discount?.discount_value) - paid_balance)
      }else{
      unpaid_balance=await parseFloat(85  - paid_balance)
      } 
      const data = {...req.body};
      var sql = "UPDATE " + table_name +  " ";
      sql = sql + "SET" +  " ";
      for( var i =0; i< keys.length; i++){
        if(keys[i] === "unpaid_balance"){
          data[keys[i]] =  await parseFloat(unpaid_balance)
          sql = sql + keys[i] + "=" +  "'" + data[keys[i]] + "'"
          }else{
          sql = sql + keys[i] + "=" +  "'" + data[keys[i]] + "'"
      }        
        if(i+1 !== keys.length ){
         sql = sql + ","
        }
        sql = sql + " "
      }
      sql = sql + "WHERE " + pkname + "=" + data[pkname]
      await sequelize.query(sql)
        return next()
          })
    }else{
        sequelize.query(sql).then(() => {
         res.send({
          message: "updated successfully"
    })
  })
}

}

const addDatatoCustTble = async(req,res,next) => {
  const class_id = req.body.class_id;
  const trainee_id = req.body.trainee_id
  const table_name = req.body.table_name;
  const paid_balance = req.body.paid_balance;
  const trainee_class_id = req.body.trainee_class_id
  const attendance_day = req.body.attendance_day;
  const attendance_year = req.body.attendance_year
  const attendance_month = req.body.attendance_month
  const discount_id = req.body.discount_id
  const reset_number = req.body.reset_number;
  const payment_date= req.body.payment_date
  const data = {...req.body};
  // const pkname = Object.keys(data)[0];
  const keys = Object.keys(data).filter(a => a !== "table_name" );
  
  var sql = "INSERT INTO " + table_name + "" + `(${keys})` + "VALUES" + "(";
  for( var i =0; i< keys.length; i++){
    sql = sql +  "'" + data[keys[i]] + "'"
    if(i+1 !== keys.length ){
    sql = sql + ","
    }
  }
  sql ="SET IDENTITY_INSERT" + " " + table_name + " " + "OFF" + ";" + "  " + sql + ")"

  var unpaid_balance = 0;     
  if(table_name.includes("Payments")||table_name.includes("payments")){
      Custom_Discount.findOne({where:{discount_id:discount_id}}).then(async(custom_discount)=>{ 
        if(Boolean(custom_discount?.discount_percentage)){
         unpaid_balance =await parseFloat((85 - (85 * custom_discount?.discount_percentage)) - paid_balance)
     
        }
        else if(Boolean(custom_discount?.discount_value)){
        unpaid_balance= await parseFloat( (85 - custom_discount?.discount_value) - paid_balance)
        }else{
        unpaid_balance=await parseFloat(85  - paid_balance)
        } 
        const data = {...req.body};
        // const pkname = Object.keys(data)[0];
        var sql = "INSERT INTO " + table_name + "" + `(${keys})` + "VALUES" + "(";
        for( var i =0; i< keys.length; i++){
          console.log(keys[i] , "eertt")
          if(keys[i] === "unpaid_balance"){
            data[keys[i]] =  await parseFloat(unpaid_balance)
            sql = sql +  "'" + data[keys[i]] + "'"
          }else{
            sql = sql +  "'" + data[keys[i]] + "'"
          }
          if(i+1 !== keys.length ){
          sql = sql + ","
          }
        }
        sql ="SET IDENTITY_INSERT" + " " + table_name + " " + "OFF" + ";" + "  " + sql + ")"
        await sequelize.query(sql).then(()=>{  
          Trainee_Class.findOne({where:{trainee_id:trainee_id,class_id:class_id}}).then(async(trainee_class)=>{
            trainee_class.updateInfo({
              ...trainee_class,
              is_paid: true
          })
          await trainee_class.save();
          })
          return next()
        })
    })
  }else{
    sequelize.query(sql).then(() => {
      if(table_name.includes("Attendances")){
        Custom_Attendance.findOne({where: { trainee_class_id:trainee_class_id , attendance_day:attendance_day , attendance_month:attendance_month , attendance_year:attendance_year}}).then((custom_attendance)=>{
          Trainee_Class.findOne({where: { trainee_class_id:trainee_class_id}}).then ((trainee_class)=>{
            if(trainee_class){
              trainee_class.updateInfo({
                ...trainee_class,
                attendance_number: ++trainee_class.attendance_number
            });
            trainee_class.save()
            }
      })
    })
      }
      res.send({
        message: "added successfully"
      })
    })
  }
}

    const getPaymentInfo = async(req,res)=>{
       sequelize
      .query("designer_custom_get_info_payments :bot_id", {
        replacements: {
          bot_id: req.body.bot_id,
        },
      }).then((data)=>{
        console.log(data[0],"paymentss dataa")
       res.send(data[0]);
      })     
    }
  

module.exports = {
  get,
  update,
  set,
  purge,
  getDataByTableBotId,
  deleteFromCustomTable,
  updateDataTableFromCustTble,
  addDatatoCustTble,
  getPaymentInfo
};
