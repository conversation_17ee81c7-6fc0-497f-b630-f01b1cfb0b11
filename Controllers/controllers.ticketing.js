const { v4: uuidv4 } = require("uuid");
const TicketingSupportTicket = require("../Models/TicketingSupportTicket");
const TicketingLog = require("../Models/TicketingLog");
const TicketingComment = require("../Models/TicketingComment");
const TicketingCommentAttachment = require("../Models/TicketingCommentAttachment");
const sequelize = require("../db");
const { sendToSignalR } = require("../calls/messaging.call");
const {
  createTicketUpdatesEmail,
  createTicketNewCommentEmail,
  createTicketBotOwnerEmail,
  ticketCreatedCustomerEmail,
} = require("../Constants/Emails");
const sgEmail = require("../Services/Email");
const createMessage = require("../Constants/message");
const TicketingSupportAgent = require("../Models/TicketingSupportAgent");
const Editor = require("../Models/Editor");
const SendGrid = require("../Models/SendGrid");
const Smtp = require("../Models/Smtp");
const nodemailer = require("nodemailer");
const Bot = require("../Models/Bot");
const User = require("../Models/User");
const TicketingCategory = require("../Models/TicketingCategory");
const TicketingAgentDepartment = require("../Models/TicketingAgentDepartment");
const TicketingRating = require("../Models/TicketingRating");

const getTicket = (req, res) => {
  return res.send(req.ticket);
};

const getIntegration = (req, res) => {
  return res.send(req.integration);
};

const createTicket = async (req, res, next) => {
  const {
    bot_id,
    ticketing_integration_id,
    channel,
    conversation_id,
    category_id,
    ...rest
  } = req.body;

  const ticket_uuid = uuidv4();

  try {
    const ticket = await TicketingSupportTicket.create({
      bot_id,
      channel,
      conversation_id,
      category_id,
      ticket_uuid,
      status: "open",
      ticketing_integration_id,
      ...rest,
    });

    const category = await TicketingCategory.findOne({
      where: {
        department_category_id: category_id,
      },
    });

    sendToSignalR({
      userId: `ticketing__${bot_id}`,
      message: {
        type: 'new_ticket',
        ticket: {
          ...ticket.get(),
          category: category,
        },
      },
    })

    req.ticket = ticket;
    req.emailTicketCustomer = true;

    return next();
  } catch (error) {
    console.log(error);
    return res.status(500).send({ message: "Error creating ticket" });
  }
};

const updateTicket = async (req, res, next) => {
  const { ticket } = req;
  const { support_agent_id, ...rest } = req.body;

  // const transaction = await sequelize.transaction();
  try {
    const keyToUpdate = Object.keys(rest)[0];

    if (ticket[keyToUpdate] === undefined) {
      return res.status(400).send({ message: "Invalid key to update" });
    }

    const old_value = ticket[keyToUpdate];
    const new_value = req.body[keyToUpdate];

    ticket.update({
      ...req.body,
    });

    TicketingLog.create({
      ticket_id: ticket.ticket_id,
      action_type: keyToUpdate,
      old_value: old_value,
      new_value: new_value,
      changed_by: support_agent_id ? "agent" : "customer",
      support_agent_id: support_agent_id || null,
    });

    sendToSignalR({
      userId: `ticketing__${ticket.ticket_uuid}`,
      message: {
        ticket: ticket.get(),
      },
    });

    req.ticket = ticket;

    if (keyToUpdate === "status") {
      req.status = new_value;
      req.emailStatusCustomer = true;
    }

    // await transaction.commit();
    return next();
  } catch (error) {
    console.log(error);

    // await transaction.rollback();
    return res.status(500).send({ message: "Error updating ticket" });
  }
};

const getTicketingOverview = (req, res, next) => {
  const { bot_id, day, month, year, endDay, endMonth, endYear } = req.query;

  if(req.query.endDay === 'undefined' || req.query.endMonth === 'NaN' || req.query.endYear === 'undefined' || !req.query.endDay || !req.query.endMonth || !req.query.endYear ) {
    sequelize
      .query("Get_Ticketing_Overview :BotID, :day, :month, :year", {
        replacements: {
          BotID: bot_id,
          day,
          month,
          year,
        }
      })
      .then((data) => {
        res.send(data[0]);
      })
  } else {
    sequelize
      .query("Get_Ticketing_Overview :BotID, :day, :month, :year, :endDay, :endMonth, :endYear", {
        replacements: {
          BotID: bot_id,
          day,
          month,
          year,
          endDay,
          endMonth,
          endYear
        }
      })
      .then((data) => {
        res.send(data[0]);
      })
  }
};

const createComment = async (req, res, next) => {
  const { ticket_id, comment, attachments, support_agent_id } = req.body;
  const transaction = await sequelize.transaction();
  try {
    const newComment = await TicketingComment.create(
      {
        ticket_id,
        comment,
        sender: support_agent_id ? "agent" : "customer",
        support_agent_id: support_agent_id || null,
        has_attachments: attachments ? true : false,
      },
      { transaction }
    );

    if (attachments) {
      attachments.forEach(
        async (attachment) => {
          await TicketingCommentAttachment.create({
            comment_id: newComment.comment_id,
            url: attachment.url,
            name: attachment.name,
            type: attachment.type,
          });
        },
        { transaction }
      );
    }

    const ticket = await TicketingSupportTicket.findOne({
      where: {
        ticket_id,
      },
    });

    const support_agent = await TicketingSupportAgent.findOne({
      where: {
        support_agent_id,
      },
      include: [
        {
          model: Editor,
          as: "editor",
        },
      ],
    });

    if(support_agent_id){
      sendToSignalR({
        userId: `ticketing__${ticket.ticket_uuid}`,
        message: {
          ticket: ticket.get(),
        },
      });
    } else if (ticket.support_agent_id) {
      sendToSignalR({
        userId: `ticketing__${ticket.bot_id}_${ticket.support_agent_id}`,
        message: {
          type: 'new_comment',
          ticket: ticket.get(),
          comment: {
            ...newComment.get(),
            attachments: attachments ?? [],
          },
        },
      });
    } else {
      sendToSignalR({
        userId: `ticketing__${ticket.bot_id}`,
        message: {
          type: 'new_comment_unassigned',
          ticket: ticket.get(),
          comment: {
            ...newComment.get(),
            attachments: attachments ?? [],
          },
        },
      });
    }

    req.ticket = ticket;
    req.comment = newComment;
    req.supportAgent = support_agent;
    req.emailCommentCustomer = true;

    await transaction.commit();
    return next();
  } catch (error) {
    console.log(error);
    await transaction.rollback();
    return res.status(500).send({ message: "Error creating comment" });
  }
};

const sendTicketingEmail = async (req, res, next) => {

  const customerName = req.ticket.customer_name ?? "Valued Customer";
  let emailBody = "";
  let title = "";

  const sendgridInteg = await SendGrid.findOne({
    where: {
      bot_id: req.ticket.bot_id,
    },
  });

  const smtpInteg = await Smtp.findOne({
    where: {
      bot_id: req.ticket.bot_id,
    },
  });

  const bot = await Bot.findOne({
    where: {
      bot_id: req.ticket.bot_id,
    }
  })

  if (!sendgridInteg && !smtpInteg) {
    return next();
  }

  if (req.emailCommentCustomer) {
    title = 'New Comment on Ticket';
    emailBody = createTicketNewCommentEmail(customerName, {
      ticket: req.ticket,
      comment: req.comment,
      supportAgent: req.supportAgent,
    }, bot);
  } else if (req.emailStatusCustomer) {
    title = 'Ticket Updated';
    emailBody = createTicketUpdatesEmail(customerName, {
      ticket: req.ticket,
    }, bot);
  } else if (req.emailTicketCustomer) {
    title = 'Ticket Created';
    emailBody = ticketCreatedCustomerEmail(customerName, {
      ticket: req.ticket,
    }, bot);
  } else {
    return next();
  }

  if (sendgridInteg && sendgridInteg.apiKey) {
    sgEmail.setApiKey(sendgridInteg.apiKey);
    sgEmail.send({
      to: req.ticket.customer_email,
      subject: title,
      from: sendgridInteg.email,
      html: emailBody,
    });
  } else if (smtpInteg) {
    nodemailer
      .createTransport({
        host: smtpInteg.host,
        port: smtpInteg.port ?? 587,
        secure: Boolean(smtpInteg.secure),
        auth: {
          user: smtpInteg.username,
          pass: smtpInteg.password,
        },
      })
      .sendMail({
        from: smtpInteg.email,
        to: req.ticket.customer_email,
        subject: title,
        html: emailBody,
      })
      .then((info) => {
        console.log(info);
      })
      .catch((error) => {
        console.log("Error sending smtp email: ", error);
      });
  }

  return next();
};
const sendTicketingNotificationToBot = async (req, res, next) => {
  const ticket = req.ticket;
  const  bot_id = ticket.bot_id;
  if(bot_id){
  sendToSignalR({
    userId: `ticketing__bot_${bot_id}`,
    message: {
      text:"tickets have been updated",
      ticket: ticket,
    },
  });
  }
  return next();
};

const sendTicketingEmailToBotCreator = async (req, res, next) => {
  const ticket = req.ticket;

  console.log(ticket, 'ticket');
  
  // get bot creator
  const bot = await Bot.findOne({
    where: {
      bot_id: ticket.bot_id,
    },
    include: [
      {
        model: User,
        as: "user",
      }
    ]
  });
  console.log(bot);
  // get bot creator email
  const email = bot.user.email;
  console.log(email, 'email');
  // send email to bot creator
  let emailBody = "";

  const sendgridInteg = await SendGrid.findOne({
    where: {
      bot_id: ticket.bot_id,
    },
  });

  const smtpInteg = await Smtp.findOne({
    where: {
      bot_id: ticket.bot_id,
    },
  });

  if (!sendgridInteg && !smtpInteg) {
    return next();
  }

  emailBody = createTicketBotOwnerEmail(bot.user.user_name, {
    ticket: ticket,
    bot: bot,
  });

  if (sendgridInteg && sendgridInteg.apiKey) {
    sgEmail.setApiKey(sendgridInteg.apiKey);
    sgEmail.send({
      to: email,
      subject: `Ticket Created`,
      from: sendgridInteg.email,
      html: emailBody,
    });
  } else if (smtpInteg) {
    nodemailer
      .createTransport({
        host: smtpInteg.host,
        port: smtpInteg.port ?? 587,
        secure: Boolean(smtpInteg.secure),
        auth: {
          user: smtpInteg.username,
          pass: smtpInteg.password,
        },
      })
      .sendMail({
        from: smtpInteg.email,
        to: email,
        subject: `Ticket Created`,
        html: emailBody,
      })
      .then((info) => {
        console.log(info);
      })
      .catch((error) => {
        console.log("Error sending smtp email: ", error);
      });
  }

  return next();
}

const assignTicket = async (req, res, next) => {
  const { ticket } = req;
  const { support_agent_id, assigned_by } = req.body;

  try {

    if(ticket.support_agent_id){
      return res.status(200).send({ message: "Ticket already assigned" });
    }

    const supportAgent = await TicketingSupportAgent.findOne({
      where: {
        support_agent_id,
      },
    });

    if (!supportAgent) {
      return res.status(404).send({ message: "Support agent not found" });
    }

    await ticket.update({
      support_agent_id:support_agent_id,
    });

    TicketingLog.create({
      ticket_id: ticket.ticket_id,
      action_type: "support_agent_id",
      old_value: null,
      new_value: support_agent_id,
      changed_by: "agent",
      support_agent_id: assigned_by,
    });

    sendToSignalR({
      userId: `ticketing__${ticket.bot_id}`,
      message: {
        type: 'ticket_assigned',
        ticket: ticket.get(),
      },
    });

    return next();
  } catch (error) {
    return res.status(500).send({ message: "Error assigning ticket" });
  }
};

const createRating = async (req, res, next) => {
  const { ticket_id, rating, rating_msg } = req.body;

  try {
    const ticket = await TicketingSupportTicket.findOne({
      where: {
        ticket_id,
      },
    });

    if (!ticket) {
      return res.status(404).send({ message: "Ticket not found" });
    }

    const ratingRes = await TicketingRating.create({
      ticket_id,
      rating,
      rating_msg,
      support_agent_id: ticket.support_agent_id,
    });

    req.rating = ratingRes;

    return next();
  } catch (error) {
    return res.status(500).send({ message: "Error creating rating" });
  }
}

const getComments = (req, res) => {
  return res.send(req.comments);
};

const getSupportAgent = (req, res) => {
  return res.send(req.supportAgent);
};

const getTickets = (req, res) => {
  return res.send({
    tickets: req.tickets,
    categoryIds: req.categoryIds,
  })
};

const getCategories = (req, res) =>{
  return res.send(req.categories)
}

const getDepartments = (req, res) =>{
  return res.send(req.departments)
}

const getAllEditorsAgents = async (req, res) => {
  res.send(req.editorsAgents);
}
const getRating = (req, res) => {
  return res.send(req.rating);
};

module.exports = {
  getTicket,
  createTicket,
  getTicketingOverview,
  updateTicket,
  createComment,
  getComments,
  getSupportAgent,
  getTickets,
  assignTicket,
  sendTicketingEmail,
  getIntegration,
  sendTicketingEmailToBotCreator,
  getCategories,
  getDepartments,
  getAllEditorsAgents,
  createRating,
  getRating,
  sendTicketingNotificationToBot,
}
