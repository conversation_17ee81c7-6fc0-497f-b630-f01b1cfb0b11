const Dialog = require("../Models/Dialogs");
const Trigger = require("../Models/Trigger");
const controllersDVC = require("./controllers.dialog.version.control");
const { getFile, generateStorageId, uploadFile } = require("../calls/file.call");
const DialogVersionControl = require("../Models/DialogVersionControl");
const Dialogs = require("../Models/Dialogs");
const Bot = require("../Models/Bot");

const get = async (req, res) => {
  const dialog = req.dialog.dataValues;
  const configuration = await getFile(dialog.url);
  res.send({
    ...dialog,
    configuration: JSON.parse(configuration?.file_data || "{}"),
  });
};

const getAll = (req, res, next) => {
  res.send(req.dialogs);
};

const getTriggers = (req, res, next) => {
  res.send(req.triggers);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  Dialog.create(data)
    .then(async (dialog) => {
      req.dialog = dialog;
      controllersDVC.setDVC(req, dialog);
      next();
    })
    .catch((error) => {
      res.status(500).send({ message: "Internal server error" + error });
    });
};

const update = async (req, res, next) => {
  const data = req.body;
  const dialog = req.dialog;
  console.log("update");
  if (req.body && dialog) {
    dialog.updateInfo(data);
    await dialog.save();
    return next();
  } else {
    return next();
  }
};

const purge = async (req, res, next) => {
  const dialog_id = req.body.dialog_id;
  const triggers = req.triggers;

  controllersDVC.purgeDVC(dialog_id);
  if (triggers.length > 0) {
    try {
      for (const trigger of triggers) {
        await trigger.destroy(); // Ensure triggers are deleted before proceeding
      }
    } catch (error) {
      return res.status(500).send({ message: "Failed to delete triggers", error: error.message });
    }
  }

  // Proceed to delete the Dialog after all related triggers have been deleted
  Dialog.destroy({
    where: {
      dialog_id,
    },
  }).then(() => {
    res.send({ message: "Dialog deleted" });
  }).catch(error => {
    // Handle potential errors, such as failing to delete the Dialog
    res.status(500).send({ message: "Failed to delete dialog", error: error.message });
  });
};
  // Dialog.destroy({
  //   where: {
  //     dialog_id,
  //   },
  // }).then(() => {
  //   if (triggers.length > 0) {
  //     triggers.forEach((trigger) => {
  //       trigger.destroy();
  //     });
  //   }
  //   res.send({ message: "Dialog deleted" });
  // });
// };


const getIsLiveURL = async(req, res, next) => {
  const dialog_id = req.query.dialog_id ;
  const dialog = await Dialogs.findOne({
    where: {dialog_id}
  });
  const dialogVersionControl = await DialogVersionControl.findOne({
    where: {dialog_id, is_live: true}
  });
  if(!dialogVersionControl){
    res.send({
      url: dialog.url,
      dialog_name: dialog.dialog_name
    });
    return;
  }
  res.send({
    url: dialogVersionControl.url,
    dialog_name: dialog.dialog_name
  });
};

const getIsLiveLocally = async(dialog_id)=> {
  const dialog = await Dialogs.findOne({
    where: {dialog_id}
  });
  const dialogVersionControl = await DialogVersionControl.findOne({
    where: {dialog_id, is_live: true}
  });
  //NOTE: removed so we dont get a fallback when the wlcm dialog is not published
  // if(!dialogVersionControl){
  //   return({
  //     url: dialog.url,
  //   });
  // }
  console.log(dialogVersionControl);
  return({
    url: dialogVersionControl?.url,
  });

}

const setFallback = async (req, res, next) => {
  try {
    const dialog_id = req.query.dialog_id;
    const dialog = await Dialogs.findOne({ where: { dialog_id } });
    if (!dialog) {
      return res.status(404).send({ message: "This dialog does not exist" });
    }
    const bot_id = dialog.bot_id;
    const dialogs = await Dialogs.findAll({ where: { bot_id } });
    dialogs.map(dialog => {
      dialog.update({ fallBack: 0 });
    });
    await dialog.update({ fallBack: 1 });
    res.send(dialog);
  } catch (error) {
    next(error);
  }
};


const setWelcome = async(req, res, next)=>{
  const dialog_id = req.query.dialog_id;

  const dialog = await Dialogs.findOne({
    where: {dialog_id}
  });
  const bot_id = dialog.bot_id;
  if(!dialog){
    res.send({message: "This dialog Does not Exist"});
  }
  const dialogs = await Dialogs.findAll({
    where: {bot_id}
  });
  dialogs.map(dialog => {
    dialog.update({ welcome: 0 });
  });
  dialog.update({ welcome: 1 });
  res.send(dialog);
};

const getFallback = async(req, res, next) =>{
  const bot_id = req.query.bot_id;
  const dialog = await Dialogs.findOne({
    where: {bot_id, fallBack: true}
  });
  if(!dialog){
    res.send({});
    return;
  }
  else{
    const url = await getIsLiveLocally(dialog.dialog_id);
    dialog.url=url.url;
    res.send(dialog);
  }
};

const getWelcome = async(req, res, next) =>{
  const bot_id = req.query.bot_id;
  const dialog = await Dialogs.findOne({
    where: {bot_id, welcome: true}
  });
  console.log(dialog, 'dialog');
  if(!dialog){
    res.send({});
    
  }
  else {
    const url = await getIsLiveLocally(dialog.dialog_id);
    dialog.url=url.url;
    res.send(dialog);
  }
};

const removeFallback = async(req, res, next) =>{
  const bot_id = req.query.bot_id;
  const dialog = await Dialogs.findOne({
    where: {bot_id, fallBack: true}
  });
  if(!dialog){
    next();
  }
  await dialog.update({ fallBack: 0 });
  next();
};

const removeWelcome = async(req, res, next) =>{
  const bot_id = req.query.bot_id;
  const dialog = await Dialogs.findOne({
    where: {bot_id, welcome: 1}
  });
  if(!dialog){
    next();
  }
  await dialog.update({welcome: 0});
  next();
};

const duplicate = async(req, res, next) =>{
  const {dialog_id, bot_id, dialogName} = req.query;
  const bot = await Bot.findOne({
    where:{bot_id: bot_id},
  })
  const url = `Bots/${bot.file_name}/Dialogs/${generateStorageId(12)}.json`;
  const urlData = await getIsLiveLocally(dialog_id)
  const data = await getFile(urlData.url);
  const dialog = await  Dialogs.create({
    url,
    bot_id,
    dialog_name: dialogName || `copy of ${req.body.dialog_name}`
  });
  controllersDVC.setDVC(req, dialog);
  await uploadFile({
    path: url,
    file_body: data.file_data,
  });
  res.send({
    ...dialog.dataValues,
    url: url,
    configuration: JSON.parse(data?.file_data || "{}")
  });
}



module.exports = {
  get,
  getAll,
  set,
  update,
  purge,
  getTriggers,
  getIsLiveURL,
  setFallback,
  setWelcome,
  getFallback,
  getWelcome,
  removeFallback,
  removeWelcome,
  duplicate,
};
