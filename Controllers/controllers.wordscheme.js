const WordScheme = require("../Models/WordScheme");
const similarity = require("../NLP/SenteceSmilarity");

const get = (req, res) => {
  res.send(req.wordscheme);
};

const getAll = (req, res) => {
  res.send(req.wordschemes);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  WordScheme.create({
    main_word: data.main_word,
    alternative_word: data.alternative_word,
    dialect: data.dialect,
    language: data.language,
    similarity_pct: similarity(
      data.alternative_word.split(" "),
      data.main_word.split(" ")
    ).score_pct,
  }).then((wordscheme) => {
    req.wordscheme = wordscheme;
    return next();
  });
};

const purge = (req, res, next) => {
  const word_id = req.body.word_id;
  WordScheme.destroy({
    where: {
      word_id,
    },
  }).then(() => {
    res.send({ message: "Word Scheme deleted" });
  });
};

const update = async (req, res, next) => {
  const wordscheme = req.wordscheme;
  const wordschemeData = req.body;
  if (wordschemeData && wordscheme) {
    wordscheme.updateInfo({ ...wordschemeData });
    await wordscheme.save();
    return next();
  } else {
    return next();
  }
};
const findWordSchemeByQueryId = (req, res, next) => {
  const word_id = req.query.word_id;
  req.word_id = word_id;
  WordScheme.findOne({ where: { word_id } }).then((word) => {
    if (word) {
      req.word = word;
      return next();
    } else {
      return res.sendStatus(404);
    }
  });
};


module.exports = {
  get,
  getAll,
  set,
  update,
  purge,
  findWordSchemeByQueryId
};
