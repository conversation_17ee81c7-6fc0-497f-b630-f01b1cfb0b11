const ReportPlugin = require("../Models/ReportPlugin");

const get = (req, res) => {
  res.send(req.reportplugin);
};

const set = (req, res, next) => {
  const data = req.body;
  ReportPlugin.create({
    bot_id: data.bot_id,
  })
    .then((reportplugin) => {
      req.reportplugin = reportplugin;
      return next();
    })
    .catch((e) => res.send({ e, message: "an error occured" }));
};

const update = async (req, res, next) => {
  const reportplugin = req.reportplugin;
  const data = { ...req.body };
  reportplugin.updateInfo(data);
  req.reportplugin = reportplugin;
  await reportplugin.save();
  return next();
};

module.exports = { get, set, update };
