const BotTemplate = require("../Models/BotTemplate");

const get = (req, res) => {
  res.send(req.botTemplate);
};

const getAll = (req, res) => {
    res.send(req.botTemplates);
};

const set = (req, res, next) => {
  const data = req.body;
  BotTemplate.create({
    ...data,
    bot_template_slug: data.bot_template_title.toLowerCase().replace(/ /g, "-")
  })
    .then((botTemplate) => {
      req.botTemplate = botTemplate;
      return next();
    })
    .catch((err) => res.send({ message: err }));
};

const addRate = async (req,res,next) => { 
  const { rate } = req.body;
  const botTemplate = req.botTemplate;
  const average = parseFloat((((botTemplate.rate) * (botTemplate.raters_number)) + rate)  /  (botTemplate.raters_number + 1))
     botTemplate.updateInfo({
      ...botTemplate,
      rate:average.toFixed(2),
      raters_number: ++botTemplate.raters_number
  })
  await botTemplate.save();
  return next();
}


const update = async (req, res, next) => {
  const botTemplate = req.botTemplate;
  const data = { ...req.body };
  botTemplate.updateInfo(data);
  await botTemplate.save();
  return next();
};

const purge = async (req, res, next) => {
  const bot_template_id = req.body.bot_template_id;
  BotTemplate.destroy({
    where: {
      bot_template_id,
    },
  }).then(() => {
    res.send({ message: "bot template data deleted" });
  });
};


module.exports = { 
    getAll,
    get, 
    set, 
    update,
    purge,
    addRate
};
