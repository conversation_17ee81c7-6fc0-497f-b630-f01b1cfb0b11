const PlanFunction = require("../Models/Plan.Function");

const create = (req, res, next) => {
  const data = req.body;
  PlanFunction.create(data)
    .then((planfunction) => {
      req.planfunction = planfunction;
      return next();
    })
    .catch((e) => {
      res.send({ e, message: "Plan id is already used" });
    });
};

const get = (req, res) => {
  return res.send(req.planfunction);
};

const getAll = (req, res) => {
  return res.send(req.planfunctions);
};

const update = async (req, res, next) => {
  const planfunction = req.planfunction;
  const data = req.body;
  planfunction.updateInfo(data);
  await planfunction.save();
  return next();
};

module.exports = { create, get, update, getAll };
