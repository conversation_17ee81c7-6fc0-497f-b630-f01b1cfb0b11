const WhatsApp = require("../Models/WhatsApp");
const fetch = require("node-fetch");
const { waAdminAuthToken } = require("../config/appConfig");
const ********_base = "https://********-api-6564.clare.ai";
const waba_360_base = "https://waba.360dialog.io";
const waba_vonage_base = "https://api.nexmo.com/v2/********-manager/wabas";
const waba_vonage_media = "https://api.nexmo.com/v2/********-manager/media/uploads";
const { storageServerUrl } = require("../constants");
const {
  loginToHub,
  generteKey,
  getPhoneNumber,
  setWebhookFun,
  setWABAfun,
} = require("../Utils/utiles.********360");
const config = require("../config/appConfig");
const FormData = require('form-data');


const get = (req, res) => {
  res.send(req.********);
};

const set = async (req, res, next) => {
  const data = { ...req.body };
  console.log("data",data)
  const accessToken = await loginToHub();
  console.log("accessToken",accessToken)
  const generateKey = await generteKey(accessToken, data.channel_id, config.partner360Id);
  const phoneNumber = await getPhoneNumber(
    accessToken,
    data.channel_id,
    config.partner360Id
  );
  await setWebhookFun(accessToken, phoneNumber, config.partner360Id);
  await setWABAfun(generateKey.api_key, phoneNumber);
  const dataFrom360 = {
    bot_id: data.bot_id,
    phone: phoneNumber,
    bsb: "360",
    api_key: generateKey.api_key,
    uri: generateKey.address,
    hub_url: "https://hub.360dialog.io",
    hub_token: "",
    username: "",
    password: ""
  };

  WhatsApp.create(dataFrom360)
    .then((********) => {
      req.******** = ********;
      return next();
    })
    .catch((error) => {
      console.log("error",error)
      res.status(409).send({ message: "Something went wrong" });
  });
};

const purge = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  WhatsApp.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    res.send({ message: "WhatsApp data deleted" });
  });
};

const update = async (req, res, next) => {
  const ******** = req.********;
  const bodyData = req.body;
  if (bodyData && ********) {
    ********.updateInfo({ ...bodyData });
    await ********.save();
    return next();
  } else {
    return next();
  }
};

const uploadMedia = async (req, res) => {
  const apiKey = req.********.apiKey || req.********.hub_token;
  const bsb = req.********.bsb;

  const data = await fetch(
    storageServerUrl.concat(`/getFile?path=${req.body.path}`),
    {
      method: "GET",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      withCredentials: true,
      crossdomain: true,
      timeout: 5000,
    }
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));



    console.log("data", typeof data?.file_data_buff, data?.file_data_buff);
  fetch(********_base.concat("/v1/media"), {
    method: "POST",
    headers: {
      "Content-Type": req.body.type,
      Authorization: `Bearer ${waAdminAuthToken}`,
    },
    withCredentials: true,
    crossdomain: true,
    body: data?.file_data,
  })
    .then((response) => {
      return response.json();
    })
    .then((data) => res.send(data))
    .catch((err) =>
      res.status(400).send({ message: "something wrong happedned" })
    );
  

};

const uploadMediaVonage = async (req, res) => {
  const apiKey = req.********.apiKey || req.********.hub_token;
  const bsb = req.********.bsb;

  const data = await fetch(
    storageServerUrl.concat(`/getFile?path=${req.body.path}`),
    {
      method: "GET",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      withCredentials: true,
      crossdomain: true,
      timeout: 5000,
    }
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));

 const apiUrl = `${waba_vonage_media}?file_type=${req.body.file_type}`;

 const filePath2 = req.body.url;

fetch(filePath2)
  .then(response => response.ok ? response.text() : Promise.reject(`HTTP error! Status: ${response.status}`))
  .then(fileContent => {
    const form = new FormData();
    form.append('mediafile', Buffer.from(fileContent), { filename: req.body.name, contentType: req.body.file_type });

    const options = {
      method: 'POST',
      headers: {
        'Content-Type': `multipart/form-data; boundary=${form.getBoundary()}`,
        'Authorization': `Bearer ${apiKey}`,
      },
      body: form,
    };

    return fetch(apiUrl, options);
  })
  .then(response => response.json())
  .then(response => res.send(response))
  .catch(error => console.error('Error:', error));


};

const getTemplates = async (req, res) => {
  const apiKey = req.********.apiKey || req.********.hub_token;
  const waba = req.********.waba;
  const bsb = req.********.bsb;
  let data;
  if(bsb!=="vonage"){
     data = await fetch(
      waba_360_base.concat(`/v1/configs/templates`),
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "D360-API-KEY": `${apiKey}`,
        },
        crossdomain: true,
      }
    )

  }else if(bsb==="vonage"){
     data= await fetch(`${waba_vonage_base}/${waba}/templates`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
    })


  }


  const dataJson = await data.json()
  res.send(dataJson)
}

const createTemplate = async (req, res) => {
  const apiKey = req.********.apiKey || req.********.hub_token;
  const template = req.body.template;
  const waba = req.********.waba;
  const bsb = req.********.bsb;
  let data;
  if(bsb!=="vonage"){
    data = await fetch(
      waba_360_base.concat(`/v1/configs/templates`),
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "D360-API-KEY": `${apiKey}`,
        },
        crossdomain: true,
        body: JSON.stringify(template)
      }
    )
  }else if(bsb==="vonage"){

    data= await fetch(`${waba_vonage_base}/${waba}/templates`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify(template)
    })

  }

  const dataJson = await data.json()
  res.send(dataJson)
}

module.exports = { get, set, update, purge, uploadMedia, getTemplates, createTemplate,uploadMediaVonage };
