const sgEmail = require("../Services/Email");
const Email = require("../Models/Email");

function makeCode(length) {
  var result = "";
  var characters = "0123456789";
  var charactersLength = characters.length;
  for (var i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

const set = (req, res, next) => {
  const email_address = req.body.email;
  const bot_id = req.body.bot_id;
  const code = makeCode(3);
  Email.create({
    code,
    bot_id,
    email: email_address,
  }).then((email) => {
    req.email = email;
    return next();
  });
};

const sendCode = (req, res, next) => {
  const { email, code } = req.email;
  //FIXME sendSMS(email, code);
  sgEmail
    .send({
      to: email,
      subject: "Verify",
      from: "<EMAIL>",
      html: `
      <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="max-width:360px;background-color:#ffffff;border:1px solid #eee;border-radius:5px;box-shadow:0 5px 10px rgba(20,50,70,.2);margin-top:20px;margin:0 auto;padding:68px 0 130px">
        <tbody>
          <tr style="width:100%">
            <td><img alt="Logo" height="88" width="88" src="https://infotointell.fra1.digitaloceanspaces.com/assets/searchatLogo.png" style="display:block;outline:none;border:none;text-decoration:none;margin:0 auto" />
              <h1 style="font-size:20px;line-height:16px;margin:16px 8px 8px 8px;color:#1F9CA1;font-weight:700;font-family:HelveticaNeue,Helvetica,Arial,sans-serif;height:16px;letter-spacing:0;text-transform:uppercase;text-align:center">Verification Code</h1>
              <p style="color:#000;display:inline-block;font-family:HelveticaNeue-Medium,Helvetica,Arial,sans-serif;font-size:15px;font-weight:500;line-height:24px;margin-bottom:0;margin-top:0;text-align:center">Please use the following code to complete the verification process:</p>
              <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="background:rgba(0,0,0,.05);border-radius:4px;margin:16px auto 14px;vertical-align:middle;width:280px">
                <tbody>
                  <tr>
                    <td>
                      <p style="font-size:32px;line-height:40px;margin:0 auto;color:#000;display:inline-block;font-family:HelveticaNeue-Bold;font-weight:700;letter-spacing:6px;padding-bottom:8px;padding-top:8px;width:100%;text-align:center">${code}</p>
                    </td>
                  </tr>
                </tbody>
              </table>
              </td>
              </tr>
              <p style="font-size:11px;line-height:23px;margin:0;font-family:HelveticaNeue,Helvetica,Arial,sans-serif;letter-spacing:0;padding:0 40px;text-align:center">If you didn't request this code, please ignore this email.</p>
        </tbody>
      </table>`
    })
    .then(() => {
      res.send({ message: "code was sent to your email please check" });
    })
    .catch((e) => res.send(e));
};

const sendHtml = (req, res, next) => {
  const { email, body, title } = req.body;
  sgEmail
    .send({
      to: email,
      subject: title,
      from: "<EMAIL>",
      html: body,
    })
    .then(() => {
      res.send({ message: "Email has been sent successfully" });
    })
    .catch((e) => res.send(e));
};

const checkCode = (req, res, next) => {
  const email = req.email;
  const userCode = req.body.code;
  if (email.code === userCode) {
    return next();
  } else {
    res.send({ message: "The submitted code is incorrect" });
  }
};

const checkVerified = (req, res, next) => {
  const email = req.email;
  if (!email.verified) {
    return next();
  } else {
    res.send({ message: "Already verified" });
  }
};

const verifyEmail = (req, res, next) => {
  const email = req.email;
  email.verify();
  email.save();
  return next();
};

const sendVerifyMessage = (req, res) => {
  res.send({
    success: true,
    message: "Verified successfully",
    email: req.email,
  });
};

const purge = (req, res, next) => {
  const email_address = req.body.email;
  const bot_id = req.body.bot_id;

  Email.destroy({
    where: {
      verified: false,
      email: email_address,
      bot_id,
    },
  }).then(() => next());
};

const getAll = (req, res, next) => {
  res.send(req.emails);
};

module.exports = {
  sendCode,
  set,
  checkCode,
  checkVerified,
  verifyEmail,
  sendVerifyMessage,
  purge,
  getAll,
  sendHtml,
};