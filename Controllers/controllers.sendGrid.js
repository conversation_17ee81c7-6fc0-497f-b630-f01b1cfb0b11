const sgEmail = require("../Services/Email");
const SendGrid = require("../Models/SendGrid");
const { urlToBase64 } = require("../Utils/utils.base64");
const { MEDIA_STORAGE_URL } = require("../constants");

const get = (req, res) => {
  return res.send(req.sgPlugin);
};

const find = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  SendGrid.findOne({ where: { bot_id } }).then((sgPlugin) => {
    if (!sgPlugin) {
      return res.status(401).send({ message: "plugin email not found" });
    } else {
      req.sgPlugin = sgPlugin;
      return next();
    }
  });
};

const update = async (req, res, next) => {
  const data = req.body;
  const email_address = req.sgPlugin;
  email_address.updateInfo(data);
  await email_address.save();
  return next();
};

const set = (req, res, next) => {
  const data = req.body;
  const botId = data.bot_id;

  SendGrid.findOne({ where: { bot_id: botId } }).then((existingSendGrid) => {
    if (existingSendGrid) {
      existingSendGrid.updateInfo(data);
      existingSendGrid.save();
      req.sgPlugin = existingSendGrid;
      return next();
    } else {
      SendGrid.create(data)
        .then((sgPlugin) => {
          req.sgPlugin = sgPlugin;
          return next();
        })
        .catch((error) => {
          res.status(500).send({ message: "Internal server error" + error });
        });
    }
  });
};


const sendEmail = async (req, res, next) => {
  const { email, body, title, from, attachments, bcc, cc } = req.body;

  let attachmentsArray = [];

  try {
    if (attachments.length > 0) {
      const attachmentPromises = attachments?.map((attachment) => {
        return urlToBase64(`${MEDIA_STORAGE_URL}/${attachment.path}`);
      });

      const base64DataArray = await Promise.all(attachmentPromises);

      attachmentsArray = base64DataArray?.map((base64Data, index) => {
        return {
          content: base64Data,
          filename: attachments[index].filename,
          type: attachments[index].type,
          disposition: attachments[index].disposition,
        };
      });
    }

    const sgPlugin = req.sgPlugin;
    if (sgPlugin.apiKey) {
      sgEmail.setApiKey(sgPlugin.apiKey);
      sgEmail
        .send({
          to: email,
          cc,
          bcc,
          subject: title,
          from: sgPlugin.email,
          html: body,
          attachments: attachmentsArray,
        })
        .then(() => {
          res.send({ message: "sent successfully!" });
        })
        .catch((e) => {
          res.send(e);
        });
      return;
    }
    sgEmail.setApiKey(process.env.SENDGRID_API_KEY);
    sgEmail
      .send({
        to: email,
        cc,
        bcc,
        subject: title,
        from: "<EMAIL>",
        html: body,
        attachments: attachmentsArray,
      })
      .then(() => {
        res.send({ message: "Email has been sent successfully" });
      })
      .catch((e) => {
        res.send(e);
      });
  } catch (error) {
    console.error("Error fetching the files:", error);
  }
};

const purge = (req, res, next) => {
  const bot_id = req.body.bot_id;
  SendGrid.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    res.send({ message: "SendGrid integration data deleted" });
  });
};

const getAll = (req, res, next) => {
  res.send(req.emails);
};

module.exports = {
  set,
  getAll,
  get,
  update,
  find,
  sendEmail,
  purge,
};
