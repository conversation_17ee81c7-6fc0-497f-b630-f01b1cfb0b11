const Intent = require("../Models/Intent");
const sequelize = require("../db");

const isDev = global.process.env.NODE_ENV === "production" ? false : true;

const checkIfYear = (strValue) => +strValue >= 1900 && +strValue <= 2025;

var intents = [];
var totalsIntent = {};
var yearsIntent = {};
var salesIntent = {};
var profitIntent = {};
var categoryIntent = {};
var percentageIntent = {};

var emailIntent = {};
var sendIntent = {};

var allIntent = {};
var returnsIntent = {};

var growthIntent = {};
var monthlyIntent = {};
var topIntent = {};
var itemIntent = {};

var expectationIntent = {};

var monthIntent = {};
var yearIntent = {};
var compareIntent = {};
var salesmanIntent = {};
var trendIntent = {};

var explainIntent = {};

var customerIntent = {};

var janIntent = {};
var febIntent = {};
var octIntent = {};

const approximate = (v) => {
  if (typeof v === "number") {
    return Math.floor(v * 1000) / 1000;
  } else {
    return v;
  }
};

const replaceBracketsWithEntity = (param, entities) => {
  var matchingKey = [...param.matchAll(/\${(.*?)\}/g)];
  if (matchingKey.length) {
    var lbc = param;
    const heaven = matchingKey.map((match) => {
      lbc = setStringKey(lbc, match[1], entities); //NOTE match[1] == key
      return lbc;
    });
    return heaven[heaven.length - 1];
  } else {
    return param;
  }
};

const setStringKey = (str, key, entities) => {
  const data = entities[key];
  if (key.includes(".")) {
    const ky1 = key.split(".")[0];
    const ky2 = key.split(".")[1];
    return str.replace("${" + key + "}", this[ky1][ky2]);
  } else {
    return str.replace("${" + key + "}", data);
  }
};

Intent.findAll({}).then((data) => {
  intents = data;
  if (isDev) {
    return;
  }

  salesIntent = intents.find((a) => a.intent_type === "sales");
  profitIntent = intents.find((a) => a.intent_type === "profit");

  emailIntent = intents.find((a) => a.intent_type === "email");

  explainIntent = intents.find((a) => a.intent_type === "explain");

  sendIntent = intents.find((a) => a.intent_type === "send");
  trendIntent = intents.find((a) => a.intent_type === "trend");

  percentageIntent = intents.find((a) => a.intent_type === "percentage");

  customerIntent = intents.find((a) => a.intent_type === "customer");
  salesmanIntent = intents.find((a) => a.intent_type === "salesman");
  expectationIntent = intents.find((a) => a.intent_type === "expectation");

  returnsIntent = intents.find((a) => a.intent_type === "return");

  totalsIntent = intents.find((a) => a.intent_type === "total");
  yearsIntent = intents.find((a) => a.intent_type === "years");
  allIntent = intents.find((a) => a.intent_type === "all");
  groupIntent = intents.find((a) => a.intent_type === "grouped");
  compareIntent = intents.find((a) => a.intent_type === "compare");

  monthIntent = intents.find((a) => a.intent_type === "month");
  yearIntent = intents.find((a) => a.intent_type === "year");

  growthIntent = intents.find((a) => a.intent_type === "growth");
  monthlyIntent = intents.find((a) => a.intent_type === "monthly");
  topIntent = intents.find((a) => a.intent_type === "top");
  itemIntent = intents.find((a) => a.intent_type === "item");

  graphIntent = intents.find((a) => a.intent_type === "graph");
  targetIntent = intents.find((a) => a.intent_type === "target");

  categoryIntent = intents.find((a) => a.intent_type === "category");

  barIntent = intents.find((a) => a.intent_type === "bar");
  columnIntent = intents.find((a) => a.intent_type === "column");
  lineIntent = intents.find((a) => a.intent_type === "line");
  pieIntent = intents.find((a) => a.intent_type === "pie");

  janIntent = intents.find(
    (a) => a.intent_type === "date" && a.intent_subType === "Januray"
  );
  febIntent = intents.find(
    (a) => a.intent_type === "date" && a.intent_subType === "February"
  );
  octIntent = intents.find(
    (a) => a.intent_type === "date" && a.intent_subType === "October"
  );

  compareIntent.intent_props = compareIntent.intent_props.split(" ");

  janIntent.intent_props = janIntent.intent_props.split(" ");
  febIntent.intent_props = febIntent.intent_props.split(" ");
  octIntent.intent_props = octIntent.intent_props.split(" ");

  totalsIntent.intent_props = totalsIntent.intent_props.split(" ");
  yearsIntent.intent_props = yearsIntent.intent_props.split(" ");

  returnsIntent.intent_props = returnsIntent.intent_props.split(" ");

  salesIntent.intent_props = salesIntent.intent_props.split(" ");
  profitIntent.intent_props = profitIntent.intent_props.split(" ");
  percentageIntent.intent_props = percentageIntent.intent_props.split(" ");

  expectationIntent.intent_props = expectationIntent.intent_props.split(" ");

  sendIntent.intent_props = sendIntent.intent_props.split(" ");
  emailIntent.intent_props = emailIntent.intent_props.split(" ");

  customerIntent.intent_props = customerIntent.intent_props.split(" ");
  salesmanIntent.intent_props = salesmanIntent.intent_props.split(" ");

  explainIntent.intent_props = explainIntent.intent_props.split(" ");
  targetIntent.intent_props = targetIntent.intent_props.split(" ");
  graphIntent.intent_props = graphIntent.intent_props.split(" ");

  allIntent.intent_props = allIntent.intent_props.split(" ");
  groupIntent.intent_props = groupIntent.intent_props.split(" ");
  monthIntent.intent_props = monthIntent.intent_props.split(" ");
  yearIntent.intent_props = yearIntent.intent_props.split(" ");
  topIntent.intent_props = topIntent.intent_props.split(" ");
  itemIntent.intent_props = itemIntent.intent_props.split(" ");
  growthIntent.intent_props = growthIntent.intent_props.split(" ");
  monthlyIntent.intent_props = monthlyIntent.intent_props.split(" ");

  categoryIntent.intent_props = categoryIntent.intent_props.split(" ");

  trendIntent.intent_props = trendIntent.intent_props.split(" ");

  barIntent.intent_props = barIntent.intent_props.split(" ");
  columnIntent.intent_props = columnIntent.intent_props.split(" ");
  lineIntent.intent_props = lineIntent.intent_props.split(" ");
  pieIntent.intent_props = pieIntent.intent_props.split(" ");
});

var today = new Date();

const user_NER = [
  {
    username: "ibrahim",
    email: "<EMAIL>",
    lang: "en",
  },
  {
    username: "ابراهيم",
    email: "<EMAIL>",
    lang: "ar",
  },
  {
    username: "moayad",
    email: "<EMAIL>",
    lang: "en",
  },
  {
    username: "مؤيد",
    email: "<EMAIL>",
    lang: "ar",
  },
  {
    username: "zaid",
    email: "<EMAIL>",
    lang: "en",
  },
  {
    username: "raed",
    email: "<EMAIL>",
    lang: "en",
  },
];

const user_NER_names = [];
user_NER.forEach((usrObj) => user_NER_names.push(usrObj.username));

var date =
  today.getFullYear() + "-" + (today.getMonth() + 1) + "-" + today.getDate();

const FunctionsMain = () => {
  var today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth() + 1;
  const day = today.getDate();

  return [
    {
      intents: ["send", "email", "username"],
      no_sp: true,
      entities: { user_name: "", user_email: "" },
      email: true,
      message: "Email will be sent to ${user_name} with address ${user_email}",
      message_ar: "البريد الالكتروني سيذهب الى ${user_email}",
    },
    {
      sp: "total_sales :year_param , :month_param",
      intents: ["total", "sales"],
      entities: { year_param: 2021, month_param: 9, month_name: "Spetember" },
      message:
        "The total sales for ${month_name} ${year_param} is: ${total_sales} JOD",
      graph: null,
      x: "total_sales",
      y: null,
    },
    {
      sp: "total_sales :year_param , :month_param",
      intents: ["sales"],
      entities: { year_param: 2021, month_param: 10, month_name: "October" },
      message:
        "The total sales for ${month_name} ${year_param} is: ${total_sales} JOD",
      graph: null,
      x: "total_sales",
      y: null,
    },
    {
      sp: "total_sales :year_param , :month_param",
      intents: ["sales", "month", "year"],
      entities: { year_param: 2021, month_param: 9 },
      message:
        "The total sales for ${month_name} ${year_param} is: ${total_sales} JOD",
      graph: null,
      x: "total_sales",
      y: null,
    },
    {
      sp: "total_sales :year_param , :month_param",
      intents: ["sales", "month"],
      entities: { year_param: 2021, month_param: 9 },
      message:
        "The total sales for ${month_name} ${year_param} is: ${total_sales} JOD",
      graph: null,
      x: "total_sales",
      y: null,
    },
    {
      sp: "total_sales :year_param , :month_param",
      intents: ["sales", "year"],
      entities: { year_param: 2021, month_param: null },
      message: "The total sales for year ${year_param} is: ${total_sales}",
      graph: null,
      x: "total_sales",
      y: null,
    },
    {
      sp: "total_sales :year_param , :month_param",
      intents: ["profit"],
      entities: { year_param: 2021, month_param: 9, total_profit: null },
      message:
        "The total profit for the year ${year_param} month ${month_param} is: ${total_profit}",
      graph: null,
      x: "total_profit",
      y: null,
    },
    {
      sp: "total_sales :year_param , :month_param",
      intents: ["profit", "year"],
      entities: { year_param: 2021, month_param: null, total_profit: null },
      message:
        "The total profit for year ${year_param} is: ${total_profit} JOD",
      graph: null,
      x: "total_profit",
      y: null,
    },
    {
      sp: "total_sales :year_param , :month_param",
      intents: ["profit", "month"],
      entities: { year_param: 2021, month_param: 9, total_profit: null },
      message:
        "The total profit for the year ${year_param} month ${month_param} is: ${total_profit}",
      graph: null,
      x: "total_profit",
      y: null,
    },
    {
      sp: "total_sales :year_param , :month_param",
      intents: ["profit", "year", "month"],
      entities: { year_param: 2021, month_param: 9, total_profit: null },
      message:
        "The total profit for the year ${year_param} month ${month_param} is: ${total_profit}",
      graph: null,
      x: "total_profit",
      y: null,
    },

    {
      sp: "total_sales :year_param , :month_param",
      intents: ["profit", "percentage"],
      entities: { year_param: 2021, month_param: 9 },
      message:
        "The total profit percentage for the year ${year_param} month ${month_param} is: ${total_profit_pct}",
      graph: null,
      x: "total_profit_pct",
      y: null,
    },
    {
      sp: "total_sales :year_param , :month_param",
      intents: ["profit", "percentage", "year"],
      entities: { year_param: 2021, month_param: null },
      message:
        "The total profit percentage for year ${year_param} is: ${total_profit_pct}",
      graph: null,
      x: "total_profit_pct",
      y: null,
    },
    {
      sp: "total_sales :year_param , :month_param",
      intents: ["profit", "percentage", "month"],
      entities: { year_param: 2021, month_param: 9, month_name: "Septemper" },
      message:
        "The total profit percentage for ${month_name} ${year_param} is: ${total_profit_pct}",
      graph: null,
      x: "total_profit_pct",
      y: null,
    },
    {
      sp: "total_sales :year_param , :month_param",
      intents: ["profit", "year", "percentage", "month"],
      entities: { year_param: 2021, month_param: 9 },
      message:
        "The total profit percentage for the year ${year_param} month ${month_param} is: ${total_profit_pct}",
      graph: null,
      x: "total_profit_pct",
      y: null,
    },
    {
      sp: "Get_Perform_Sales_ALLYears",
      intents: ["sales", "all", "years"],
      entities: {},
      message: "the total sales since launch ${average_sales}",
      graph: null,
      x: "average_sales",
      y: "total_sales",
    },
    {
      sp: "Get_Perform_Sales_ALLYears_Grouped",
      entities: {},
      intents: ["sales", "all", "years", "grouped"],
      message: "the total sales since launch",
      drilldown: true,
      graph: "bar",
      x: "cYear",
      y: "total_sales",
    },
    {
      sp: "Get_Perform_Sales_ALLYears_MonthlyGrowth",
      intents: ["sales", "monthly", "growth"],
      entities: { year_param: 2021 },
      message: "the total monthly growth",
      graph: "line",
      x: "cmonth",
      y: "sales_diff",
    },
    {
      sp: "Get_Perform_Sales_ALLYears_MonthlyGrowth",
      intents: ["sales", "monthly", "growth", "year"],
      entities: { year_param: 2021 },
      message: "the total monthly growth",
      graph: "line",
      x: "cmonth",
      y: "sales_diff",
    },
    {
      sp: "Get_Perform_Sales_ALLYears_MonthlyGrowth",
      intents: ["monthly", "sales"],
      entities: {},
      message: "the trend of total monthly sales",
      graph: "bar",
      x: "cmonth",
      y: "sell",
    },
    {
      sp: "Get_perform_top_N_sold_items_month :top_param, :month_param, :year_param",
      intents: ["top", "sales", "item", "year", "month"],
      message: "top ${top_param} for ${month_param}/${year_param}",
      entities: { top_param: 5, month_param: 10, year_param: 2021 },
      graph: "bar",
      x: "ItemDesc",
      y: "total_sell",
    },
    {
      sp: "top_N_customers_sales :top_param, :year_param,:month_param, :module_name",
      intents: ["top", "sales", "customer", "year", "month"],
      message: "top ${top_param} for ${month_param}/${year_param}",
      entities: {
        top_param: 5,
        month_param: 10,
        year_param: 2021,
        module_name: "sales",
      },
      graph: "bar",
      x: "customer_name",
      y: "total_sales",
    },
    {
      sp: "top_N_customers_sales :top_param, :year_param,:month_param, :module_name",
      intents: ["top", "sales", "customer"],
      message: "top ${top_param} for ${month_param}/${year_param}",
      entities: {
        top_param: 5,
        month_param: 10,
        year_param: 2021,
        module_name: "sales",
      },
      graph: "bar",
      x: "customer_name",
      y: "total_sales",
    },
    {
      sp: "top_N_customers_sales :top_param, :year_param,:month_param, :module_name",
      intents: ["top", "profit", "customer"],
      message: "top ${top_param} for ${month_param}/${year_param}",
      entities: {
        top_param: 5,
        month_param: 10,
        year_param: 2021,
        module_name: "sales",
      },
      graph: "bar",
      x: "customer_name",
      y: "total_profit",
    },

    {
      sp: "top_N_salesperson :top_param, :year_param,:month_param, :module_name",
      intents: ["top", "profit", "salesman"],
      message: "top ${top_param} for ${month_param}/${year_param}",
      entities: {
        top_param: 5,
        month_param: 10,
        year_param: 2021,
        module_name: "profit",
      },
      graph: "bar",
      x: "salesman",
      y: "total_profit",
    },

    {
      sp: "top_N_salesperson :top_param, :year_param,:month_param, :module_name",
      intents: ["top", "sales", "salesman"],
      message: "top ${top_param} for ${month_param}/${year_param}",
      entities: {
        top_param: 5,
        month_param: 10,
        year_param: 2021,
        module_name: "sales",
      },
      graph: "bar",
      x: "salesman",
      y: "total_sales",
    },

    {
      sp: "Get_perform_top_N_sold_items_month :top_param, :month_param, :year_param",
      intents: ["top", "sales", "item", "year"],
      message: "top ${top_param} for ${month_param}/${year_param}",
      entities: { top_param: 5, month_param: 10, year_param: 2021 },
      graph: "bar",
      x: "ItemDesc",
      y: "total_sell",
    },
    {
      sp: "compare_sales_profit_pct :year_param",
      intents: ["compare", "sales", "year"],
      message: "comparing sales for ${year_param}",
      two_way_auth: true,
      entities: { year_param: 2021 },
      graph: "bar",
      x: "month",
      y: "total_sales",
    },
    {
      sp: "compare_sales_profit_pct :year_param",
      intents: ["compare", "profit", "year"],
      message: "comparing profit for ${year_param}",
      title: "Profit Comparison by Year",
      entities: { year_param: 2021 },
      two_way_auth: true,
      graph: "bar",
      x: "month",
      y: "total_profit",
    },
    {
      sp: "compare_sales_profit_pct :year_param",
      intents: ["compare", "profit", "percentage", "year"],
      message: "comparing profit percentage for ${year_param}",
      entities: { year_param: 2021 },
      two_way_auth: true,
      graph: "bar",
      x: "month",
      y: "total_profit_pct",
    },
    {
      sp: "trend_sales_pct :year_param",
      drilldown: true,
      intents: ["trend", "sales", "percentage"],
      message: "Comparing sales percentage for ${year_param}",
      percentage_graph: true,
      entities: { year_param: 2021 },
      graph: "bar",
      x: "month",
      y: "total_sales_pct",
    },
    {
      sp: "trend_sales_pct :year_param",
      intents: ["sales", "percentage", "year"],
      message: "Comparing sales percentage for ${year_param}",
      percentage_graph: true,
      entities: { year_param: 2021 },
      graph: "line",
      x: "month",
      y: "total_sales_pct",
    },

    {
      sp: "compare_customers_sales :year_param, :month_param, :cust1, :cust2",
      intents: ["compare", "mall1", "mall2", "sales", "year", "month"],
      message: "comparing profit percentage for ${year_param}",
      entities: {
        year_param: 2021,
        month_param: 8,
        cust1: "mall1",
        cust2: "mall2",
      },
      graph: "bar",
      x: "customer_name",
      y: "total_sales",
    },
    {
      sp: "compare_customers_sales :year_param, :month_param, :cust1, :cust2",
      intents: ["compare", "mall1", "mall2", "sales"],
      message: "comparing sales percentage for ${year_param}",
      entities: {
        year_param: 2021,
        month_param: 8,
        cust1: "mall1",
        cust2: "mall2",
      },
      graph: "bar",
      x: "customer_name",
      y: "total_sales",
    },
    {
      sp: "compare_customers_sales :year_param, :month_param, :cust1, :cust2",
      no_sp: true,
      intents: ["expectations"],
      message:
        "The Expected sales for next month (November) is: ${custom_value}",
      entities: {
        year_param: 2021,
        month_param: 8,
        cust1: "mall1",
        cust2: "mall2",
      },
    },

    {
      sp: "total_sales_by_category :year_param, :month_param",
      intents: ["category", "sales", "year", "month"],
      message:
        "The Expected sales for next month (November) is: ${custom_value}",
      entities: {
        year_param: 2021,
        month_param: 8,
      },
      graph: "bar",
      x: "category",
      y: "total_sales",
    },
    {
      sp: "total_sales_by_category :year_param, :month_param",
      intents: ["category", "sales"],
      title: "sales by category",
      message:
        "The Expected sales for next month (November) is: ${custom_value}",
      entities: {
        year_param: 2021,
        month_param: 8,
      },
      graph: "bar",
      x: "category",
      y: "total_sales",
    },

    {
      sp: "total_profit_by_category :year_param, :month_param",
      intents: ["category", "profit", "year", "month"],
      message:
        "The Expected sales for next month (November) is: ${custom_value}",
      entities: {
        year_param: 2021,
        month_param: 8,
      },
      graph: "bar",
      x: "category",
      y: "total_profit",
    },
    {
      sp: "total_profit_by_category :year_param, :month_param",
      intents: ["category", "profit"],
      message:
        "The Expected profit for next month (November) is: ${custom_value}",
      entities: {
        year_param: 2021,
        month_param: 8,
      },
      graph: "bar",
      x: "category",
      y: "total_profit",
    },

    {
      sp: "top_N_customers_returns :top_param, :year_param, :month_param",
      intents: ["top", "customer", "returns"],
      message:
        "The Expected profit for next month (November) is: ${custom_value}",
      entities: {
        year_param: 2021,
        month_param: 8,
        top_param: 5,
      },
      graph: "bar",
      x: "customer_name",
      y: "total_returns",
    },

    {
      sp: "top_N_customers_returns :top_param, :year_param, :month_param",
      intents: ["target", "sales", "month"],
      message:
        "Total sales ${month_name} ${year_param} is: 563009 \n Target is: 612045 \n You are away of target by: 8%",
      message_ar:
        "إجمالي مبيعات أكتوبر 2021 هو: 563009  n الهدف هو: 612045  n أنت بعيد عن الهدف بنسبة: 8٪",
      entities: {
        year_param: 2021,
        month_param: 8,
        top_param: 5,
      },
      graph: null,
      x: "customer_name",
      y: "total_returns",
    },
    {
      sp: "top_N_customers_returns :top_param, :year_param, :month_param",
      intents: ["target", "sales"],
      message:
        "Total sales ${month_name} ${year_param} is: 563009 \n Target is: 612045 \n You are away of target by: 8%",
      message_ar:
        "إجمالي مبيعات أكتوبر 2021 هو: 563009 الهدف هو: 612045 أنت بعيد عن الهدف بنسبة: 8٪",
      entities: {
        year_param: 2021,
        month_param: 8,
        top_param: 5,
      },
      graph: null,
      x: "customer_name",
      y: "total_returns",
    },
    {
      sp: "target_sales :year_param, :month_param",
      intents: ["target", "sales", "month", "graph"],
      message:
        "Total sales ${month_name} ${year_param} is: 563009 \n Target is: 612045 \n You are away of target by: 8%",
      entities: {
        year_param: 2021,
        month_param: 8,
        top_param: 5,
      },
      graph: "gun",
      x: "customer_name",
      y: "total_returns",
    },
    {
      sp: "target_sales :year_param, :month_param",
      intents: ["customer", "salesman"],
      message:
        "Total sales ${month_name} ${year_param} is: 563009 \n Target is: 612045 \n You are away of target by: 8%",
      entities: {
        year_param: 2021,
        month_param: 8,
        top_param: 5,
      },
      graph: "multi-line",
      x: ["customers", "salesmen"],
      y: "total_returns",
    },
  ];
};

let arrayChecker = (arr, target) =>
  target.every((v) => arr.includes(v)) && arr.length === target.length;

const v1Performit = async (req, res, next) => {
  const question = req.question_full;
  const bot_id = req.body.bot_id;
  var success = false;
  const questionNER = [];
  const normalizedFunctions = [...FunctionsMain()];
  // const utilsFunctions = [...utilsFunctionsMain()];

  const questionEntities = {};
  const questionProps = {};
  question.map((a, qindex) => {
    //NOTE falsafeh part
    if (user_NER_names.indexOf(a) !== -1) {
      var user_index = user_NER_names.indexOf(a);
      var user = user_NER[user_index];
      questionEntities.user_name = user.username;
      questionEntities.user_email = user.email;
      questionNER.push("username");
    }

    if (["mall1"].indexOf(a) !== -1) {
      questionNER.push("mall1");
    }
    if (["mall2"].indexOf(a) !== -1) {
      questionNER.push("mall2");
    }

    if (barIntent.intent_props.indexOf(a) !== -1) {
      questionProps.graph = "bar";
    }
    if (lineIntent.intent_props.indexOf(a) !== -1) {
      questionProps.graph = "line";
    }
    if (columnIntent.intent_props.indexOf(a) !== -1) {
      questionProps.graph = "column";
    }
    if (compareIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("compare");
    }

    if (returnsIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("returns");
    }

    if (sendIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("send");
    }
    if (emailIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("email");
    }
    if (trendIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("trend");
    }
    if (categoryIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("category");
    }

    if (expectationIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("expectations");
      questionEntities.custom_value = 462681.7;
    }

    if (janIntent.intent_props.indexOf(a) !== -1) {
      questionEntities.month_param = 1;
      questionEntities.month_name = "January";
      if (questionEntities.month_param1) {
        questionEntities.month_param2 = 1;
      } else {
        questionEntities.month_param1 = 1;
      }
      questionNER.push("month");
    }
    if (febIntent.intent_props.indexOf(a) !== -1) {
      questionEntities.month_param = 2;
      questionEntities.month_name = "February";
      if (questionEntities.month_param1) {
        questionEntities.month_param2 = 2;
      } else {
        questionEntities.month_param1 = 2;
      }
      questionNER.push("month");
    }
    if (octIntent.intent_props.indexOf(a) !== -1) {
      questionEntities.month_param = 10;
      questionEntities.month_name = "October";
      if (questionEntities.month_param1) {
        questionEntities.month_param2 = 10;
      } else {
        questionEntities.month_param1 = 10;
      }
      questionNER.push("month");
    }
    if (topIntent.intent_props.indexOf(a) !== -1) {
      var postValue = question[qindex + 1];
      questionEntities.top_param = postValue;
      questionNER.push("top");
    }
    if (customerIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("customer");
    }
    if (salesmanIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("salesman");
    }
    if (targetIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("target");
    }
    if (monthIntent.intent_props.indexOf(a) !== -1) {
      var postValue = question[qindex + 1];
      questionEntities.month_param = postValue;
      if (+postValue === 2) {
        questionEntities.month_name = "February";
      }
      if (questionEntities.month_param1) {
        questionEntities.month_param2 = postValue;
      } else {
        questionEntities.month_param1 = postValue;
      }
      if (!isNaN(questionEntities.month_param)) {
        questionNER.push("month");
      }
    }
    if (checkIfYear(a)) {
      var postValue = a;
      questionEntities.year_param = postValue;
      if (questionEntities.year_param1) {
        questionEntities.year_param2 = postValue;
      } else {
        questionEntities.year_param1 = postValue;
      }

      !questionNER.includes("year") ? questionNER.push("year") : null;
    }
    if (yearIntent.intent_props.indexOf(a) !== -1) {
      var postValue = question[qindex + 1];
      questionEntities.year_param = postValue;
      if (questionEntities.year_param1) {
        questionEntities.year_param2 = postValue;
      } else {
        questionEntities.year_param1 = postValue;
      }
      !questionNER.includes("year") ? questionNER.push("year") : null;
    }

    if (profitIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("profit");
    }
    if (explainIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("explain");
    }
    if (graphIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("graph");
    }
    if (percentageIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("percentage");
    }

    // if (totalsIntent.intent_props.indexOf(a) !== -1) {
    //   questionNER.push("total");
    // }
    if (salesIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("sales");
    }
    if (allIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("all");
    }
    if (yearsIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("years");
    }
    if (groupIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("grouped");
    }
    if (itemIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("item");
    }
    if (growthIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("growth");
    }
    if (monthlyIntent.intent_props.indexOf(a) !== -1) {
      questionNER.push("monthly");
    }
  });
  if (!questionNER.length) {
    //NOTE NOTHING FOUND
    return res.send({ question, success, questionProps, questionNER });
  }
  normalizedFunctions.map((func) => {
    const entities = {
      ...func.entities,
      ...questionEntities,
    };

    if (arrayChecker(questionNER, func.intents) && !success) {
      success = true;
      if (func.no_sp) {
        res.send({
          entities,
          function_type: func.email ? "email" : "unknown",
          message: func.message
            ? replaceBracketsWithEntity(func.message, entities)
            : "",
          message_ar: func.message_ar
            ? replaceBracketsWithEntity(func.message_ar, entities)
            : undefined,
          bot_id,
          success,
        });
      } else {
        sequelize
          .query(func.sp, {
            replacements: {
              ...func.entities,
              ...questionEntities,
            },
          })
          .then((data) => {
            if (!func.graph) {
              //NOTE TEXT ANSWER
              entities[func.x] = approximate(data[0][0][func.x]);
            }
            res.send({
              entities,
              message: func.message
                ? replaceBracketsWithEntity(func.message, entities)
                : undefined,
              message_ar: func.message_ar
                ? replaceBracketsWithEntity(func.message_ar, entities)
                : undefined,
              bot_id,
              success,
              title: func.title
                ? replaceBracketsWithEntity(func.title, entities)
                : func.intents.join(" "),
              data: func.graph
                ? data[0].map((a) => {
                    return {
                      ...a,
                      [func.y]: approximate(a[func.y]),
                      [func.x]: approximate(a[func.x]),
                    };
                  })
                : [],
              answered: true,
              graph: func.graph,
              drilldown: func.drilldown ? true : false,
              x: func.x,
              y: func.y,
              ...questionProps,
              percentage_graph: func.percentage_graph ? true : false,
            });
          });
      }
    }
  });
  if (!success) {
    return res.send({ question, success, questionNER, questionProps });
  }
};

module.exports = { v1Performit };
