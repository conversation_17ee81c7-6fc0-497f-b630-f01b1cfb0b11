const { Op } = require("sequelize");
const sequelize = require("../db");
const InternalLiveChatAgents = require("../Models/InternalLiveChatAgents");
const Editor = require("../Models/Editor");
const InternalLiveChatIntegration = require("../Models/InternalLiveChatIntegration");
const InternalLiveChatPendingMessages = require("../Models/InternalLiveChatPendingMessages");
const InternalLiveChatQueue = require("../Models/InternalLiveChatQueue");
const InternalLiveChatSession = require("../Models/InternalLiveChatSession");
const InternalLiveChatTransactions = require("../Models/InternalLiveChatTransactions");
const User = require("../Models/User");
const InternalLiveChatAgentsLog = require("../Models/InternalLiveChatAgentsLog");
const InternalLiveChatRatings = require("../Models/InternalLiveChatRatings")

const createAgent = async (req, res, next) => {
  try {
    const data = req.body;

    const { editor_id, bot_id } = data;

    const editor = await Editor.findOne({
      where: {
        editor_id: editor_id,
      },
    });

    if (!editor) {
      return res.status(404).json({ error: "Editor not found" });
    }
    console.log(editor.bot_id, bot_id, editor.agent_privilege);
    if (editor.bot_id !== bot_id || !editor.agent_privilege) {
      return res.status(403).json({ error: "Unauthorized" });
    }

    const user = User.findOne({
      where: {
        email: editor.email,
      },
    });

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    const integration = await InternalLiveChatIntegration.findOne({
      where: {
        bot_id,
      },
    });

    if (!integration) {
      await InternalLiveChatIntegration.create({
        bot_id,
        status: "active",
        max_sessions_per_agent: 5,
        time_zone: data?.time_zone ?? "Asia/Amman",
      });
    }

    // prevent duplicate agent creation
    
    InternalLiveChatAgents.create({
      editor_id,
      bot_id,
      current_active_chats: 0,
      status: "inactive",
      time_zone: data?.time_zone ?? integration?.time_zone ?? "Asia/Amman",
    }).then((agent) => {
      req.agent = agent;
      return next();
    });
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

const bulkCreateAgents = async (req, res, next) => {
  try {
    const { editors, bot_id } = req.body;

    const integration = await InternalLiveChatIntegration.findOne({
      where: {
        bot_id,
      },
    });

    if (!integration) {
      await InternalLiveChatIntegration.create({
        bot_id,
        status: "active",
        max_sessions_per_agent: 5,
        time_zone: req.body?.time_zone ?? "Asia/Amman",
      });
    }


    const agentsCreated = await InternalLiveChatAgents.bulkCreate(
      editors.map((ed) => ({
        editor_id: ed.editor_id,
        bot_id,
        current_active_chats: 0,
        status: "inactive",
        time_zone: req.body?.time_zone ?? integration?.time_zone ?? "Asia/Amman",
      }))
    );

    res.send(agentsCreated);
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

const updateAgent = async (req, res, next) => {
  try {
    const { agent_id, ...data } = req.body;

    const agent = await InternalLiveChatAgents.findOne({
      where: {
        agent_id,
      },
    });

    if (!agent) {
      return res.status(404).json({ error: "Agent not found" });
    }

    await agent.update(data);

    req.agent = agent;
    return next();
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

const requestChat = async (req, res, next) => {
  try {
    const data = req.body;
    InternalLiveChatQueue.create(data).then((queue) => {
      req.queue = queue;
      return next();
    });
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

const assignChat = async (req, res, next) => {
  const transaction = await sequelize.transaction();
  try {
    const { queue_id, agent_id } = req.body;

    const agent = await InternalLiveChatAgents.findOne(
      {
        where: {
          agent_id,
        },
      },
      { transaction }
    );

    if (!agent) {
      await transaction.rollback();
      return res.status(404).json({ error: "Agent not found" });
    }

    const session = await InternalLiveChatSession.create(
      {
        queue_id,
        agent_id,
        start_time: new Date(),
        status: "pending",
        time_zone: agent.time_zone,
      },
      { transaction }
    );

    const queue = await InternalLiveChatQueue.findOne(
      {
        where: {
          queue_id,
        },
      },
      { transaction }
    );
    if (!queue) {
      await transaction.rollback();
      return res.status(404).json({ error: "Queue not found" });
    }
    await queue.update({ status: "assigned" }, { transaction });

    await agent.update(
      { current_active_chats: agent.current_active_chats + 1 },
      { transaction }
    );

    await transaction.commit();

    req.agent = agent;
    req.queue = queue;
    req.session = session;
    return next();
  } catch (error) {
    if (transaction) await transaction.rollback();
    return res.status(500).json({ error: error.message });
  }
};

const acceptChat = async (req, res, next) => {
  const transaction = await sequelize.transaction();
  try {
    const { session_id, agent_id } = req.body;

    const session = await InternalLiveChatSession.findOne(
      {
        where: {
          session_id,
        },
      },
      { transaction }
    );
    if (!session) {
      await transaction.rollback();
      return res.status(404).json({ error: "Session not found" });
    }

    if (session.agent_id !== agent_id) {
      await transaction.rollback();
      return res.status(403).json({ error: "Unauthorized" });
    }

    await session.update({ status: "active" }, { transaction });
    await transaction.commit();

    req.session = session;
    return next();
  } catch (error) {
    if (transaction) await transaction.rollback();
    return res.status(500).json({ error: error.message });
  }
};

const rejectChat = async (req, res, next) => {
  const transaction = await sequelize.transaction();
  try {
    const { session_id, agent_id } = req.body;

    const session = await InternalLiveChatSession.findOne(
      {
        where: {
          session_id,
        },
      },
      { transaction }
    );

    console.log(session);
    if (!session) {
      await transaction.rollback();
      return res.status(404).json({ error: "Session not found" });
    }

    if (session.agent_id !== agent_id) {
      await transaction.rollback();
      return res.status(403).json({ error: "Unauthorized" });
    }

    const queue = await InternalLiveChatQueue.findOne(
      {
        where: {
          queue_id: session.queue_id,
        },
      },
      { transaction }
    );
    if (!queue) {
      await transaction.rollback();
      return res.status(404).json({ error: "Queue not found" });
    }

    await queue.update({ status: "waiting" }, { transaction });

    const transactions = await InternalLiveChatTransactions.findAll(
      {
        where: {
          session_id,
          sender: "customer",
        },
      },
      { transaction }
    );

    for (const tr of transactions) {
      await InternalLiveChatPendingMessages.create(
        {
          queue_id: session.queue_id,
          message: tr.message,
        },
        { transaction }
      );
    }

    await session.update(
      { status: "closed due to agent rejection", end_time: new Date() },
      { transaction }
    );

    const agent = await InternalLiveChatAgents.findOne(
      {
        where: {
          agent_id,
        },
      },
      { transaction }
    );
    if (!agent) {
      await transaction.rollback();
      return res.status(404).json({ error: "Agent not found" });
    }

    await agent.update(
      { current_active_chats: agent.current_active_chats - 1 },
      { transaction }
    );

    await transaction.commit();

    // res.send({ message: "Chat Rejected" });
    return next();
  } catch (error) {
    if (transaction) await transaction.rollback();
    return res.status(500).json({ error: error.message });
  }
};

const closeChat = async (req, res, next) => {
  const transaction = await sequelize.transaction();
  try {
    const { session_id, agent_id, statusToSet } = req.body;

    const session = await InternalLiveChatSession.findOne(
      {
        where: {
          session_id,
        },
      },
      { transaction }
    );

    if (!session) {
      await transaction.rollback();
      return res.status(404).json({ error: "Session not found" });
    }

    if (session.agent_id !== agent_id) {
      await transaction.rollback();
      return res.status(403).json({ error: "Unauthorized" });
    }

    if (session.status.includes("closed")) {
      await transaction.rollback();
      return res.status(403).json({ error: "Chat already closed" });
    }

    await session.update(
      { status: statusToSet || "closed", end_time: new Date() },
      { transaction }
    );

    const agent = await InternalLiveChatAgents.findOne(
      {
        where: {
          agent_id,
        },
      },
      { transaction }
    );

    if (!agent) {
      await transaction.rollback();
      return res.status(404).json({ error: "Agent not found" });
    }

    await agent.update(
      { current_active_chats: agent.current_active_chats - 1 },
      { transaction }
    );

    await transaction.commit();

    // res.send({ message: "Chat closed" });
    return next();
  } catch (error) {
    console.log(error);
    if (transaction) await transaction.rollback();
    return res.status(500).json({ error: error.message });
  }
};

const createIntegration = async (req, res, next) => {
  try {
    const data = req.body;

    const integration = await InternalLiveChatIntegration.create({
      bot_id: data.bot_id,
      status: "active",
      max_sessions_per_agent: data.max_sessions_per_agent
        ? data.max_sessions_per_agent
        : 5,
      time_zone: data.time_zone ? data.time_zone : "Asia/Amman",
    });

    req.integration = integration;
    return next();
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

const createTransaction = async (req, res, next) => {
  try {
    const data = req.body;

    const transaction = await InternalLiveChatTransactions.create(data);

    req.transaction = transaction;
    return next();
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

const createPendingMessage = async (req, res, next) => {
  try {
    const data = req.body;

    const message = await InternalLiveChatPendingMessages.create(data);

    req.message = message;
    return next();
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

const transferPendingMessages = async (req, res, next) => {
  const transaction = await sequelize.transaction();
  try {
    const { queue_id, session_id } = req.body;

    const messages = await InternalLiveChatPendingMessages.findAll(
      {
        where: {
          queue_id,
        },
      },
      { transaction }
    );

    if (!messages.length) {
      await transaction.rollback();
      return res.status(404).json({ error: "No pending messages" });
    }

    for (const message of messages) {
      await InternalLiveChatTransactions.create(
        {
          session_id: session_id,
          sender: "customer",
          message: message.message,
          sentAt: message.createdAt,
        },
        { transaction }
      );

      await message.destroy({ transaction });
    }

    await transaction.commit();
    return next();
    // res.send({ message: "Pending messages transferred" });
  } catch (error) {
    if (transaction) await transaction.rollback();
    return res.status(500).json({ error: error.message });
  }
};

const dropQueue = async (req, res, next) => {

  const queue_id = req.body.queue_id || req.query.bot_id;
  const queue = await InternalLiveChatQueue.findOne({
    where: {
      queue_id,
    },
  });

  if (!queue) {
    return res.status(404).json({ error: "Queue not found" });
  }

  await queue.update({ status: "dropped" });

  // check if session exists
  const session = await InternalLiveChatSession.findOne({
    where: {
      queue_id,
    },
  });

  if (session) {
    await session.update({
      status: "closed due to customer",
      end_time: new Date(),
    });
    // decrement agent current_active_chats
    const agent = await InternalLiveChatAgents.findOne({
      where: {
        agent_id: session.agent_id,
      },
    });

    if (agent) {
      await agent.update({
        current_active_chats: agent.current_active_chats - 1,
      });
    }
  }

  return next();
  // res.send({ message: "Queue dropped" });
};

const getAgent = (req, res) => {
  res.send(req.agent);
};

const getQueue = (req, res) => {
  res.send(req.queue);
};

const getSessionData = (req, res) => {
  const response = {
    agent: req.agent,
    queue: req.queue,
    session: req.session,
  };
  res.send(response);
};

const getSession = (req, res) => {
  res.send(req.session);
};

const getSessions = (req, res) => {
  res.send(req.sessions);
};

const getIntegration = (req, res) => {
  res.send(req.integration);
};

const getTransaction = (req, res) => {
  res.send(req.transaction);
};

const getTransactions = (req, res) => {
  res.send(req.transactions);
};

const getPendingMessage = (req, res) => {
  res.send(req.message);
};



const updateAgentStatus = async (req, res, next) => {
  const transaction = await sequelize.transaction();
  try {
    const { agent_id } = req.query;

    const agent = await InternalLiveChatAgents.findOne({
      where: { agent_id },
      transaction,
    });

    if (!agent) {
      await transaction.rollback();
      return res.status(404).json({ error: "Agent not found" });
    }

    const newStatus = agent.status === "active" ? "inactive" : "active";
    await agent.update({ status: newStatus }, { transaction });

    await InternalLiveChatAgentsLog.create({
      agent_id,
      status: newStatus,
      time_zone: agent.time_zone
    }, { transaction });
    req.agent = agent;

    await transaction.commit();
    return next();
    // return res
    //   .status(200)
    //   .json({ message: "Status updated successfully", agent });
  } catch (error) {
    if (transaction) await transaction.rollback();
    return res.status(500).json({ error: error.message });
  }
};

const updateSessionStatus = async (req, res, next) => {
  const transaction = await sequelize.transaction();
  try {
    const { session_id, status } = req.body;

    const validStatuses = [
      "pending",
      "active",
      "closed",
      "closed due to agent inactivity",
      "closed due to customer inactivity",
      "closed due to inactivity",
      "closed due to agent rejection",
      "closed due to agent unavailability",
    ];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: "Invalid status value" });
    }

    const session = await InternalLiveChatSession.findOne({
      where: { session_id },
      transaction,
    });

    if (!session) {
      await transaction.rollback();
      return res.status(404).json({ error: "Session not found" });
    }
    if(session.status === 'pending'){
      // await session.update({activatedAt: new Date()});
      await session.update({ status, activatedAt: new Date() }, { transaction });
    } else {
      await session.update({ status }, { transaction });
    }
    

    await transaction.commit();

    req.session = session;
    return next();
    // return res
    //   .status(200)
    //   .json({ message: "Session status updated successfully", session });
  } catch (error) {
    if (transaction) await transaction.rollback();
    return res.status(500).json({ error: error.message });
  }
};

const updateIntegration = async (req, res, next) => {
  const transaction = await sequelize.transaction();
  try {
    const { livechat_integration_id, ...data } = req.body;

    const integration = await InternalLiveChatIntegration.findOne({
      where: { livechat_integration_id },
      transaction,
    });

    if (!integration) {
      await transaction.rollback();
      return res.status(404).json({ error: "Integration not found" });
    }

    await integration.update(data, { transaction });

    await transaction.commit();

    req.integration = integration;
    return next();
  } catch (error) {
    if (transaction) await transaction.rollback();
    return res.status(500).json({ error: error.message });
  }
};

const getAllEditorsAgents = async (req, res) => {
  res.send(req.editorsAgents);
};

const createAgentLog = async (req, res, next) => {
  try {
    const data = req.body;

    const agent = await InternalLiveChatAgents.findOne({
      where: {
        agent_id: data.agent_id,
      },
    });

    if (!agent) {
      return res.status(404).json({ error: "Agent not found" });
    }

    const agentLog = await InternalLiveChatAgentsLog.create({
      ...data,
      time_zone: agent.time_zone,
    });

    req.agentLog = agentLog;
    return next();
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
}

const getAgentLog = (req, res) => {
  res.send(req.agentLog);
}

const getLiveChatCalculated = (req, res, next) => {
  if(req.query.endDay === 'undefined' || req.query.endMonth === 'NaN' || req.query.endYear === 'undefined' || !req.query.endDay || !req.query.endMonth || !req.query.endYear ){
    const {bot_id, day, month, year} = req.query;
    sequelize
    .query("Get_LiveChat_Calculated :ID , :day , :month , :year", {
      replacements: {
        ID: bot_id,
        day,
        month,
        year,
      }
    })
    .then((data) => {
      res.send(data[0]);
    });  
  } else {
  const {bot_id, day, month, year, endDay, endMonth, endYear} = req.query;
    sequelize
      .query("Get_LiveChat_Calculated :ID , :day , :month , :year, :endDay, :endMonth, :endYear", {
        replacements: {
          ID: bot_id,
          day,
          month,
          year,
          endDay,
          endMonth,
          endYear
        }
      })
      .then((data) => {
        res.send(data[0]);
      });
  }
}

const createRating = async (req, res, next) => {
  try {
    const { session_id, agent_id, rating, rating_msg } = req.body;

    const session = await InternalLiveChatSession.findOne({
      where: { session_id },
    });

    if (!session) {
      return res.status(404).json({ error: "Session not found" });
    }

    if (session.agent_id !== agent_id) {
      return res.status(403).json({ error: "Unauthorized" });
    }

    const newRating = await InternalLiveChatRatings.create({
      session_id,
      agent_id,
      rating,
      rating_msg,
    });

    req.rating = newRating;
    return res.send(req.rating);
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};

const getAgentRatings = async (req, res) => {
  try {
    const { agent_id } = req.query;

    const ratings = await InternalLiveChatRatings.findAll({
      where: { agent_id },
    });

    if (!ratings.length) {
      return res.status(404).json({ error: "No ratings found for this agent" });
    }

    res.send(ratings);
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
};


module.exports = {
  createAgent,
  requestChat,
  assignChat,
  acceptChat,
  rejectChat,
  closeChat,
  getAgent,
  createIntegration,
  createTransaction,
  createPendingMessage,
  transferPendingMessages,
  dropQueue,
  getQueue,
  getSessionData,
  getSessions,
  getIntegration,
  getTransaction,
  getPendingMessage,
  getTransactions,
  getSession,
  getTransactions,
  getLiveChatCalculated,
  updateAgentStatus,
  updateSessionStatus,
  updateIntegration,
  getAllEditorsAgents,
  bulkCreateAgents,
  createAgentLog,
  getAgentLog,
  createRating,
  getAgentRatings,
  updateAgent
};
