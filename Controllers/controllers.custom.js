const fetch = require("node-fetch");
var parseString = require("xml2js").parseString;
const sequelize = require("../db");
const Bot = require("../Models/Bot");
const { performance } = require("perf_hooks");

const getSa3iTracking = async (req, res, next) => {
  var bodyString =
    "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:jor='http://www.jordanPost.com.jo/'>" +
    "<soapenv:Header/>" +
    "<soapenv:Body>" +
    "<jor:TrackItemXML>" +
    "<jor:itemNo>" +
    req.body.id +
    "</jor:itemNo>" +
    "</jor:TrackItemXML>" +
    "</soapenv:Body>" +
    "</soapenv:Envelope>";
  const url = "http://212.35.69.89/JoPostWebInterface/JoPostAPI.asmx";

  const data = await fetch(url, {
    method: "POST", // *GET, POST, PUT, DELETE, etc.
    mode: "cors", // no-cors, *cors, same-origin
    cache: "no-cache", // *default, no-cache, reload, force-cache, only-if-cached
    credentials: "same-origin", // include, *same-origin, omit
    headers: {
      "Content-Type": 'text/xml;charset="UTF-8"',
      // 'Content-Type': 'application/x-www-form-urlencoded',
    },
    redirect: "follow", // manual, *follow, error
    referrerPolicy: "no-referrer", // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
    body: bodyString,
    // body data type must match "Content-Type" header
  }).then((resp) => resp.text());
  var TrackerResponseObjects;

  parseString(data, function (err, result) {
    TrackerResponseObjects =
      result["soap:Envelope"]["soap:Body"][0]["TrackItemXMLResponse"][0][
        "TrackItemXMLResult"
      ][0]["WebTrackResponse"];
  });

  res.send({ TrackerResponseObjects });
};

const getGoogleVoice = async (req, res, next) => {
  const text = req.query.text;
  const language = req.query.language;
  const url = encodeURI(
    `https://translate.google.com/translate_tts?ie=UTF-8&q=${text}&tl=${language}&tk=995126.592330&client=t`
  );
  const headers = {
    referer: "https://translate.google.com/",
    "user-agent": "stagefright/1.2 (Linux;Android 5.0)",
  };
  console.log(url);
  const file = await fetch(url, {
    method: "GET",
    headers,
  });
  res.send({ file });
};

const getGoogleSheet = async (req, res, next) => {
  const { google } = require("googleapis");

  const auth = await google.auth.getClient({
    scopes: ["https://www.googleapis.com/auth/spreadsheets.readonly"],
  });
  const sheets = google.sheets({ version: "v4", auth });
  const response = await sheets.spreadsheets.values.get({
    spreadsheetId: req.body.sheetID,
    range: req.body.range,
  });
  // const posts = response.data.values;
  res.send(response.data.values);
};

const insertReport = async (req, res, next) => {
  const info = req.body;
  sequelize.query(
    `ssc_report_set_withdatetime 
      :report_id,
      :recipient,
      :national_id,
      :nationality,
      :including,
      :municapility,
      :building_address,
      :building_long,
      :building_lat,
      :building_name,
      :building_id_sec,
      :building_id,
      :accepted,
      :report_date,
      :comment`,
    {
      replacements: {
        report_id: info.report_id,
        recipient: info.recipient,
        nationality: info.nationality,
        national_id: info.national_id,
        including: info.including,
        municapility: info.municapility,
        building_address: info.building_address,
        building_long: info.building_long ? info.building_long : null,
        building_lat: info.building_lat ? info.building_lat : null,
        building_name: info.building_name,
        building_id_sec: info.building_id_sec,
        building_id: info.building_id,
        accepted: info.accepted,
        comment: info.comment,
        report_date: new Date().toLocaleString("en-US", {
          timeZone: "Asia/Amman",
        }),
      },
    }
  );
  res.send({ success: true });
};

const createOutboundCall = async (req, res, next) => {
  function _0x5a8e() {
    var _0x10b05c = [
      "stringify",
      "204215GOamBQ",
      "raedfesesi",
      "json",
      "fc7adb477d193530702ea76dba97066914824e9f1d0e1eeaa18b84f3a3c10099",
      "1535208ElatWB",
      "push",
      "28421",
      "then",
      "1196430qldNMA",
      "41026mRsMiN",
      "body",
      "join",
      "1PhunHa",
      "472535HqCsIb",
      "3836UNfibr",
      "https://kitapi-us.voximplant.com/api/v3/scenario/runScenario",
      "application/x-www-form-urlencoded;charset=UTF-8",
      "505062XaSEps",
      "28433",
      "96264296088",
      "log",
    ];
    _0x5a8e = function () {
      return _0x10b05c;
    };
    return _0x5a8e();
  }
  function _0x58b6(_0x590977, _0x5c2ee8) {
    var _0x5a8eb1 = _0x5a8e();
    return (
      (_0x58b6 = function (_0x58b611, _0x2d19d5) {
        _0x58b611 = _0x58b611 - 0xb6;
        var _0x32537f = _0x5a8eb1[_0x58b611];
        return _0x32537f;
      }),
      _0x58b6(_0x590977, _0x5c2ee8)
    );
  }
  var _0x4a6df8 = _0x58b6;
  (function (_0x439643, _0x579acb) {
    var _0x28aa7f = _0x58b6,
      _0x498da4 = _0x439643();
    while (!![]) {
      try {
        var _0x5263ad =
          (parseInt(_0x28aa7f(0xba)) / 0x1) *
            (-parseInt(_0x28aa7f(0xb7)) / 0x2) +
          -parseInt(_0x28aa7f(0xbf)) / 0x3 +
          parseInt(_0x28aa7f(0xbc)) / 0x4 +
          parseInt(_0x28aa7f(0xc4)) / 0x5 +
          parseInt(_0x28aa7f(0xb6)) / 0x6 +
          -parseInt(_0x28aa7f(0xbb)) / 0x7 +
          parseInt(_0x28aa7f(0xc8)) / 0x8;
        if (_0x5263ad === _0x579acb) break;
        else _0x498da4["push"](_0x498da4["shift"]());
      } catch (_0x414d4a) {
        _0x498da4["push"](_0x498da4["shift"]());
      }
    }
  })(_0x5a8e, 0x2b260);
  const { phone, text, lang, bot_id, senario_id } = req[_0x4a6df8(0xb8)];

  var details = {
      scenario_id: senario_id
        ? senario_id
        : lang === "ar"
        ? _0x4a6df8(0xc0)
        : _0x4a6df8(0xca),
      phone: phone,
      variables: JSON[_0x4a6df8(0xc3)]({ text: text }),
      caller_id: _0x4a6df8(0xc1),
      domain: _0x4a6df8(0xc5),
      access_token: _0x4a6df8(0xc7),
    },
    formBody = [];
  for (var property in details) {
    var encodedKey = encodeURIComponent(property),
      encodedValue = encodeURIComponent(details[property]);
    formBody[_0x4a6df8(0xc9)](encodedKey + "=" + encodedValue);
  }
  (formBody = formBody[_0x4a6df8(0xb9)]("&")),
    fetch(_0x4a6df8(0xbd), {
      method: "POST",
      headers: { "Content-Type": _0x4a6df8(0xbe) },
      body: formBody,
    })
      ["then"]((_0x44eb5f) => _0x44eb5f[_0x4a6df8(0xc6)]())
      [_0x4a6df8(0xcb)]((_0x35be6a) => console[_0x4a6df8(0xc2)](_0x35be6a));
  res.send({ success: true });
};

const checkHealth = async (req, res) => {
  var startTime = performance.now();

  const bot = await Bot.findAll({
    benchmark: true,
    logging: console.log,
    where: { bot_id: 25 },
  });
  var endTime = performance.now();

  res.send({
    query_time: endTime - startTime,
    worth_investigate: endTime - startTime > 1500,
  });
};

module.exports = {
  getSa3iTracking,
  getGoogleVoice,
  getGoogleSheet,
  createOutboundCall,
  checkHealth,
  insertReport,
};
