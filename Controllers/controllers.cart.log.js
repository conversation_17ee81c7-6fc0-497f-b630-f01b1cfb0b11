const CartLog = require("../Models/Cart.Log");
const CartLogItem = require("../Models/Cart.Log.Item");

const approximate = (value) => Math.floor(value * 100) / 100;

const getAll = (req, res) => {
  return res.send(req.cartlogs);
};

const get = (req, res) => {
  return res.send(req.cartlog);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  CartLog.create({
    //items: JSON.stringify(data.cart),
    bot_id: data.bot_id,
    price_total: data.total_price,
    sale_total: data.total_sale,
    bot_user_id: data.bot_user_id,
    username: data.name,
    address: data.address,
    phone: data.phone,

    continent: data.continent,
    country: data.country,
    city: data.city,
  }).then((cartlog) => {
    req.cartlog = cartlog;
    return next();
  });
};

const setItems = (req, res, next) => {
  const data = { ...req.body };
  const vat = req.body.vat_ammount ? req.body.vat_ammount : 0;
  CartLogItem.bulkCreate(
    data.cart.map((a) => {
      return {
        ...a,
        bot_user_id: req.cartlog.bot_user_id,
        cart_log_id: req.cartlog.cart_log_id,
        continent: req.cartlog.continent,
        country: req.cartlog.country,
        city: req.cartlog.city,
        vat_ammount_pct: vat,
        vat_ammount: approximate(a.price * vat),
      };
    })
  ).then((cartItems) => {
    req.cartlog.bot_designer_cart_log_items = cartItems;
    return next();
  });
};

const update = async (req, res, next) => {
  const cartlog = req.cartlog;
  const dbData = { ...req.body };
  cartlog.updateInfo(dbData);
  await cartlog.save();
  return next();
};

module.exports = { getAll, set, setItems, get, update };
