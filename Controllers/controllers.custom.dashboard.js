const CustomDashboard = require("../Models/Custom.Dashboard");

const set = (req, res, next) => {
  const data = {
    ...req.body,
    bot_id: req.body.bot_id || req.query.bot_id || req.bot.bot_id,
  };
  CustomDashboard.create(data)
    .then((customDashboard) => {
      req.customDashboard = customDashboard;
      return next();
    })
    .catch((error) => {
      res.status(500).send({ message: "Internal server error" + error });
    });
};

const getAll = (req, res) => {
  res.send(req.customDashboard);
};

const update = async (req, res, next) => {
  const data = req.body;
  const customDashboard = req.customDashboard;
  if (req.body && customDashboard) {
    customDashboard.updateInfo(data);
    await customDashboard.save();
    return next();
  } else {
    return next();
  }
};

const purge = async (req, res, next) => {
  const custom_dashboard_id = req.body.custom_dashboard_id;
  CustomDashboard.destroy({
    where: {
      custom_dashboard_id,
    },
  }).then(() => {
    res.send({ message: "Custom Dashboard deleted" });
  });
};
module.exports = {
  set,
  getAll,
  update,
  purge
};
