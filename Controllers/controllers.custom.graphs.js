// const Custom_graphs = require("../Models/Custom.graphs");
// // const blob_users =
// // const sequelize = require("../bot_messages");

// const getAllGraph = (req, res, next) => {
//   const bot_id = req.query.bot_id;
//   Custom_graphs.findAll({
//     where: { bot_id: bot_id },
//   }).then((customGraphs) => {
//     if (customGraphs) {
//       return res.send(customGraphs);
//     }
//     return res.status(401).send({ massage: "this bot don't have graphs yet " });
//   });
// };

// const getOnline_users = async (req, res, next) => {
//   const bot_id = req.query.bot_id;
//   const data = await sequelize.query(`select * from count_online (${bot_id})`);
//   return res.send(data[0][0]);
// };
// const get_avg_per_hour = async (req, res, next) => {
//   const bot_id = req.query.bot_id;
//   const data = await sequelize.query(`select * from perhour(${bot_id})`);
//   console.log(data[0], "ssssssssssssssssssssssss");

//   return res.send(data[0]);
// };
// const get_avg_per_day = async (req, res, next) => {
//   const bot_id = req.query.bot_id;
//   const data = await sequelize.query(`select * from per_day(${bot_id})`);
//   console.log(data, "ssssssssssssssssssssssss");
//   return res.send(data[0]);
// };

// const createGraphForBot = (req, res, next) => {
//   const bot_id = req.body.bot_id;
//   const graph_name = req.body.graph_name;
//   Custom_graphs.findOne({
//     where: { bot_id: bot_id, graph_name: graph_name },
//   }).then((custom) => {
//     if (custom) {
//       return res.status(401).send({ massage: "this graph is created before" });
//     }
//     Custom_graphs.create({
//       bot_id: bot_id,
//       graph_name: graph_name,
//     }).then((data) => {
//       return res.send({ massage: "created sucessfully" });
//     });
//   });
// };
// module.exports = {
//   getAllGraph,
//   getOnline_users,
//   createGraphForBot,
//   get_avg_per_hour,
//   get_avg_per_day,
// };
