const DialogExpiryLog = require("../Models/DialogExpiryLog");

const get = (req, res) => {
  res.send(req.dialogExpiryLog);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  DialogExpiryLog.create(data).then((dialogExpiryLog) => {
    req.dialogExpiryLog = dialogExpiryLog;
    next();
  });
};

const update = async (req, res, next) => {
  const dialogExpiryLog = req.dialogExpiryLog;
  const data = { ...req.body };
  dialogExpiryLog.updateInfo(data);
  req.dialogExpiryLog = dialogExpiryLog;
  await dialogExpiryLog.save();
  return next();
};

const purge = (req, res, next) => {
  const dialog_expiry_log_id =
    req.body.dialog_expiry_log_id || req.dialogExpiryLog.dialog_expiry_log_id;
  DialogExpiryLog.destroy({ where: { dialog_expiry_log_id } }).then(() => {
    res.send({ message: "Dialog expiry log deleted" });
  });
};

module.exports = {
  get,
  set,
  update,
  purge,
};
