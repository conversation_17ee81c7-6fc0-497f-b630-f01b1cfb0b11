const ItemFeature = require("../Models/Item.Feature");

const get = (req, res) => {
  res.send({
    feature_name: req.feature ? req.feature.feature_name : null,
    feature_type: req.feature ? req.feature.feature_type : null,
    ...req.itemfeature.toJSON(),
  });
};

const getAll = (req, res) => {
  res.send(req.itemfeatures);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  ItemFeature.create(data).then((itemfeature) => {
    req.itemfeature = itemfeature;
    return next();
  });
};

const purge = async (req, res, next) => {
  const item_feature_id = req.body.item_feature_id;

  const itemFeature = await ItemFeature.findOne({
    where: {
      item_feature_id,
    },
  });
  const bot_id = itemFeature.bot_id; 

  ItemFeature.destroy({
    where: {
      item_feature_id,
    },
  }).then(() => {
    req.bot_id = bot_id;
    return next();
    // res.send({ message: "Item feature deleted" });
  });
};

const update = async (req, res, next) => {
  const itemfeature = req.itemfeature;
  const data = req.body;
  if (data && itemfeature) {
    itemfeature.updateInfo({ ...data });
    await itemfeature.save();
    return next();
  } else {
    return next();
  }
};

const purgeAll = (req, res, next) => {
  const bot_id = req.body.bot_id;
  ItemFeature.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    return next();
  });
};

const setAllMap = (req, res, next) => {
  const data = req.body.data;
  const features = req.features;
  ItemFeature.bulkCreate(
    data.map((a) => {
      return {
        bot_id: a.bot_id,
        feature_value: `${a.feature_value}`,
        item_id: a.item_id,
        feature_id: features.find((b) => b.feature_name === a.feature_name)
          .feature_id,
      };
    })
  ).then((itemfeatures) => {
    req.itemfeatures = itemfeatures;
    return next();
  });
};

module.exports = { get, getAll, set, update, purge, purgeAll, setAllMap };
