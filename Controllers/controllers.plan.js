const Plan = require("../Models/Plan");

const create = (req, res, next) => {
  const data = req.body;
  Plan.create({
    plan_id: data.plan_id,
    plan_name: data.plan_name,
    plan_description: data.plan_description,
    currency: data.currency,
    plan_price: data.plan_price,
  })
    .then((plan) => {
      req.plan = plan;
      return next();
    })
    .catch(() => {
      res.send({ message: "Plan id is already used" });
    });
};

const get = (req, res) => {
  return res.send(req.plan);
};

const getAll = (req, res) => {
  return res.send(req.plans);
};

const update = async (req, res, next) => {
  const plan = req.plan;
  const data = req.body;
  plan.updateInfo(data);
  await plan.save();
  return next();
};

module.exports = { create, get, update, getAll };
