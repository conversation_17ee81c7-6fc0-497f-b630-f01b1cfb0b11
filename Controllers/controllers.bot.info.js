const BotInfo = require("../Models/Bot.Info");

const get = (req, res) => {
  res.send(req.botinfo);
};

const set = (req, res, next) => {
  const bot_id = req.body.bot_id || req.bot.bot_id;
  BotInfo.create({ bot_id }).then((botinfo) => {
    req.botinfo = botinfo;
    return next();
  });
};

const update = async (req, res, next) => {
  const botinfo = req.botinfo;
  const data = { ...req.body };
  botinfo.updateInfo(data);
  req.botinfo = botinfo;
  await botinfo.save();
  return next();
};

module.exports = {
  get,
  update,
  set,
};
