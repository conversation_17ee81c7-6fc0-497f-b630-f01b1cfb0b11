const Customs_Class = require("../Models/Custom.Class");

const get = (req, res) => {
  res.send(req.custom_class);
};

const set = (req, res, next) => {
    const data = {...req.body};
    Customs_Class.create(data).then((custom_class) => {
      req.custom_class = custom_class;
      return next();
    });
  };

const update = async (req, res, next) => {
  const custom_class = req.custom_class;
  const data = { ...req.body };
  custom_class.updateInfo(data);
  req.custom_class = custom_class;
  await custom_class.save();
  return next();
};

const purge = (req, res, next) => {
    const class_id = req.body.class_id;
    const bot_id = req.body.bot_id
    Customs_Class.destroy({
      where: {
        class_id,bot_id
      },
    }).then(() => {
      res.send({ message: "Class is deleted" });
    });
  };


module.exports = {
  get,
  update,
  set,
  purge
};
