const SanadFeedback = require("../Models/Sanad.Feedback");

const set = (req, res, next) => {
  const data = { ...req.body };
  SanadFeedback.create(data).then((feedback) => {
    req.feedback = feedback;
    return next();
  });
};

const get = (req, res) => {
  res.send(req.feedback);
};

const getAll = (req, res, next) => {
  res.send(req.feedbacks);
};

const update = (req, res, next) => {
  const data = { ...req.body };
  req.feedback.update(data).then((feedback) => {
    req.feedback = feedback;
    return next();
  });
};

module.exports = {
  get,
  getAll,
  set,
  update,
};
