const OfferItem = require("../Models/Offer.Item");

const get = (req, res) => {
  res.send(req.offeritem);
};

const getAll = (req, res, next) => {
  res.send(req.offeritems);
};

const set = async (req, res, next) => {
  const data = { ...req.body };
  OfferItem.create(data)
    .then((offeritem) => {
      req.offeritem = offeritem;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const setAll = async (req, res, next) => {
  OfferItem.bulkCreate(req.body.offer_items)
    .then((offeritems) => {
      req.offeritems = offeritems;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const update = async (req, res, next) => {
  const offer = req.offer;
  const data = { ...req.body };
  offer.updateInfo(data);
  req.offer = offer;
  await offer.save();
  return next();
};

const purge = (req, res, next) => {
  const data = { ...req.body };
  OfferItem.destroy({
    where: {
      offer_id: data.offer_id,
    },
  }).then(() => {
    return next();
  });
};

const purgeOne = (req, res, next) => {
  const data = { ...req.body };
  OfferItem.destroy({
    where: {
      item_id: data.item_id,
    },
  }).then(() => {
    return next();
  });
};

const purgeAll = (req, res, next) => {
  const offer_id = req.body.offer_id;
  OfferItem.destroy({
    where: {
      offer_id: offer_id,
    },
  }).then(() => {
    return next();
  });
};

module.exports = {
  get,
  set,
  update,
  purge,
  purgeAll,
  getAll,
  setAll,
  purgeOne,
};
