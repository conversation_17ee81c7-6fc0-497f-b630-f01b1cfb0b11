const Weather = require("../Models/Weather");

const get = (req, res) => {
  res.send(req.weather);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  Weather.create(data)
    .then((weather) => {
      req.weather = weather;
      return next();
    })
    .catch(() => {
      res.status(409).send({ message: "Weather is already exists" });
    });
};

const purge = (req, res, next) => {
  const bot_id = req.body.bot_id;
  Weather.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    res.send({ message: "weather data deleted" });
  });
};

const update = async (req, res, next) => {
  const weather = req.weather;
  const weatherData = req.body;
  if (weatherData && weather) {
    weather.updateInfo({ ...weatherData });
    await weather.save();
    return next();
  } else {
    return next();
  }
};

module.exports = { get, set, update, purge };
