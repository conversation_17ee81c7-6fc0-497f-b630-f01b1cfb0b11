const sequelize = require("../db");

const getTopNItemsSales = (req, res, next) => { 
  if(req.query.endDay === 'undefined' || req.query.endMonth === 'NaN' || req.query.endYear === 'undefined' || !req.query.endDay || !req.query.endMonth || !req.query.endYear ){
    sequelize
      .query("designer_cart_log_items_get_top_n_sale :bot_id, :N, :day , :month , :year", {
        replacements: {
          bot_id: req.query.bot_id,
          day: req.query.day,
          month: req.query.month,
          year: req.query.year,
          // period: req.query.period,
          N: req.query.N,
        },
      })
      .then((data) => {
        res.send(data[0]);
      });
  } else {
    sequelize
      .query("designer_cart_log_items_get_top_n_sale :bot_id, :N, :day, :month, :year, :endDay, :endMonth, :endYear", {
        replacements: {
          bot_id: req.query.bot_id,
          day: req.query.day,
          month: req.query.month,
          year: req.query.year,
          endDay: req.query.endDay,
          endMonth: req.query.endMonth,
          endYear: req.query.endYear,
          // period: req.query.period,
          N: req.query.N,
        },
      })
      .then((data) => {
        res.send(data[0]);
      });
  }
}

const getTopSalesByCategory = (req, res, next) => {
  if(req.query.endDay === 'undefined' || req.query.endMonth === 'NaN' || req.query.endYear === 'undefined' || !req.query.endDay || !req.query.endMonth || !req.query.endYear ){
    sequelize
      .query("designer_cart_log_items_get_top_sales_cat :bot_id, :day , :month , :year", {
        replacements: {
          bot_id: req.query.bot_id,
          // period: req.query.period,
          day: req.query.day,
          month: req.query.month,
          year: req.query.year,
        },
      })
      .then((data) => {
        res.send(data[0]);
      });
  } else {
    sequelize
      .query("designer_cart_log_items_get_top_sales_cat :bot_id, :day , :month , :year , :endDay, :endMonth, :endYear", {
        replacements: {
          bot_id: req.query.bot_id,
          // period: req.query.period,
          day: req.query.day,
          month: req.query.month,
          year: req.query.year,
          endDay: req.query.endDay,
          endMonth: req.query.endMonth,
          endYear: req.query.endYear,
        },
      })
      .then((data) => {
        res.send(data[0]);
      });
  }
};

const getTopSalesByOffer = (req, res, next) => {
  if(req.query.endDay === 'undefined' || req.query.endMonth === 'NaN' || req.query.endYear === 'undefined' || !req.query.endDay || !req.query.endMonth || !req.query.endYear ){
    sequelize
      .query("designer_cart_log_items_get_top_sales_offer :bot_id, :day , :month , :year", {
        replacements: {
          bot_id: req.query.bot_id,
          period: req.query.period,
          day: req.query.day,
          month: req.query.month,
          year: req.query.year,
        },
      })
      .then((data) => {
        res.send(data[0]);
      });
    } else {
      sequelize
        .query("designer_cart_log_items_get_top_sales_offer :bot_id, :day , :month , :year, :endDay, :endMonth, :endYear", {
          replacements: {
            bot_id: req.query.bot_id,
            period: req.query.period,
            day: req.query.day,
            month: req.query.month,
            year: req.query.year,
            endDay: req.query.endDay,
            endMonth: req.query.endMonth,
            endYear: req.query.endYear,
          },
        })
        .then((data) => {
          res.send(data[0]);
        });
    }
};

const getSalesByMonth = (req, res, next) => {
  sequelize
    .query("designer_cart_log_items_get_top_sales_month :bot_id, :year", {
      replacements: {
        bot_id: req.query.bot_id,
        year: req.query.year,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getSalesByDay = (req, res, next) => {
  sequelize
    .query("designer_cart_log_items_get_sales_day :bot_id", {
      replacements: {
        bot_id: req.query.bot_id,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getSalesByCity = (req, res, next) => {
  if(req.query.endDay === 'undefined' || req.query.endMonth === 'NaN' || req.query.endYear === 'undefined' || !req.query.endDay || !req.query.endMonth || !req.query.endYear ){
    sequelize
      .query("designer_cart_log_items_get_top_sales_city :bot_id ,:day , :month , :year", {
        replacements: {
          bot_id: req.query.bot_id,
          // period: req.query.period,
          day: req.query.day,
          month: req.query.month,
          year: req.query.year,
        },
      })
      .then((data) => {
        res.send(data[0]);
      });
    } else {
      sequelize
        .query("designer_cart_log_items_get_top_sales_city :bot_id ,:day , :month , :year, :endDay, :endMonth, :endYear", {
          replacements: {
            bot_id: req.query.bot_id,
            // period: req.query.period,
            day: req.query.day,
            month: req.query.month,
            year: req.query.year,
            endDay: req.query.endDay,
            endMonth: req.query.endMonth,
            endYear: req.query.endYear,
          },
        })
        .then((data) => {
          res.send(data[0]);
        });
    }
};

const getSalesByCountry = (req, res, next) => {
  if(req.query.endDay === 'undefined' || req.query.endMonth === 'NaN' || req.query.endYear === 'undefined' || !req.query.endDay || !req.query.endMonth || !req.query.endYear ){
    sequelize
      .query("designer_cart_log_items_get_top_sales_country :bot_id ,:day , :month , :year", {
        replacements: {
          bot_id: req.query.bot_id,
          // period: req.query.period,
          day: req.query.day,
          month: req.query.month,
          year: req.query.year,
        },
      })
      .then((data) => {
        res.send(data[0]);
      });
    } else {
    sequelize
      .query("designer_cart_log_items_get_top_sales_country :bot_id ,:day , :month , :year, :endDay, :endMonth, :endYear", {
        replacements: {
          bot_id: req.query.bot_id,
          // period: req.query.period,
          day: req.query.day,
          month: req.query.month,
          year: req.query.year,
          endDay: req.query.endDay,
          endMonth: req.query.endMonth,
          endYear: req.query.endYear,
        },
      })
      .then((data) => {
        res.send(data[0]);
      });
  }
};

const getSalesByContinent = (req, res, next) => {
  if(req.query.endDay === 'undefined' || req.query.endMonth === 'NaN' || req.query.endYear === 'undefined' || !req.query.endDay || !req.query.endMonth || !req.query.endYear ){
    sequelize
      .query("designer_cart_log_items_get_top_sales_continent :bot_id ,:day , :month , :year", {
        replacements: {
          bot_id: req.query.bot_id,
          // period: req.query.period,
          day: req.query.day,
          month: req.query.month,
          year: req.query.year,
        },
      })
      .then((data) => {
        res.send(data[0]);
      });
    } else {
    sequelize
      .query("designer_cart_log_items_get_top_sales_continent :bot_id ,:day , :month , :year, :endDay, :endMonth, :endYear", {
        replacements: {
          bot_id: req.query.bot_id,
          // period: req.query.period,
          day: req.query.day,
          month: req.query.month,
          year: req.query.year,
          endDay: req.query.endDay,
          endMonth: req.query.endMonth,
          endYear: req.query.endYear,
        },
      })
      .then((data) => {
        res.send(data[0]);
      });
  }
};

module.exports = {
  getTopNItemsSales,
  getTopSalesByCategory,
  getTopSalesByOffer,
  getSalesByMonth,
  getSalesByDay,
  getSalesByCity,
  getSalesByCountry,
  getSalesByContinent,
};
