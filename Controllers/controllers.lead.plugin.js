const LeadPlugin = require("../Models/LeadPlugin");

const get = (req, res) => {
  res.send(req.leadplugin);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  LeadPlugin.create(data)
    .then((leadplugin) => {
      req.leadplugin = leadplugin;
      return next();
    })
    .catch(() => {
      res.status(409).send({ message: "Lead plugin is already exists" });
    });
};

const purge = (req, res, next) => {
  const bot_id = req.body.bot_id;
  LeadPlugin.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    res.send({ message: "Lead Plugin data deleted" });
  });
};

const update = async (req, res, next) => {
  const leadplugin = req.leadplugin;
  if (req.body && leadplugin) {
    leadplugin.updateInfo({ ...req.body });
    await leadplugin.save();
    return next();
  } else {
    return next();
  }
};

module.exports = { get, set, update, purge };
