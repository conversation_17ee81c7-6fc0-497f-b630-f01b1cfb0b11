const Conversion = require("../Models/Conversion");

const create = (req, res, next) => {
  const data = { ...req.body };
  Conversion.create({
    bot_id: data.bot_id,
    item_id: data.item_id,
    url: data.url,
  }).then((conversion) => {
    req.conversion = conversion;
    return next();
  });
};

const get = (req, res) => {
  res.send(req.conversion);
};

module.exports = {
  create,
  get,
};
