const Seat = require("../Models/Seat");
const SeatMessage = require("../Models/Seat.Message");
const SeatConversation = require("../Models/Seat.Conversation");
const SeatHours = require("../Models/Seat.Hours");
const { Op } = require("sequelize");
const sequelize = require("../db");
const SeatBot = require("../Models/Seat.Bot");
const LiveChatPlugin = require("../Models/LiveChatPlugin");
function convertTZ(date, tzString) {
  return new Date(
    (typeof date === "string" ? new Date(date) : date).toLocaleString("en-US", {
      timeZone: tzString,
    })
  );
}

const updateConvSeat = function (seatbot_id, updValue) {
  sequelize.query(
    "designer_update_current_conversation :seatbot_id ,:updValue",
    {
      replacements: {
        seatbot_id,
        updValue,
      },
    }
  );
};

const updateConvSeatbyLId = function (bot_id, liveperson_id, updValue) {
  sequelize
    .query(
      "designer_update_current_conversation_bylivepersonID :bot_id , :liveperson_id ,:updValue",
      {
        replacements: {
          bot_id,
          liveperson_id,
          updValue,
        },
      }
    )
    .then((data) => {
      return data;
      // return next();
    });
};

const findSeatBot = async (req, res, next) => {
  const seatBot = await SeatBot.findOne({
    where: {
      email: req.body.email || req.query.email,
      bot_id: req.body.bot_id || req.query.bot_id,
    },
  });
  if (!seatBot) {
    return res
      .status(401)
      .send({ message: "seat bot not found with this email and bot_id" });
  }
  req.seatBot = seatBot;
  return next();
};
const getAllSeatHours = (req, res) => {
  res.send(req.workinghours);
};

const setAllSeatHours = (req, res, next) => {
  const data = req.body;
  const workinghoursData = req.body.workinghours;
  const bot_id = req.body.bot_id;
  SeatHours.bulkCreate(
    workinghoursData.map((a) => {
      return { ...a, bot_id };
    })
  ).then((workinghours) => {
    req.workinghours = workinghours;
    return next();
  });
};

const createWorkingHourForSeat = (req, res, next) => {
  const workinghoursData = req.body.workinghours;
  const liveperson_id = req.seatBot.liveperson_id;
  const seatbot_id = req.body.seatbot_id;
  const bot_id = req.body.bot_id;

  SeatHours.bulkCreate(
    workinghoursData.map((a) => {
      return { ...a, bot_id, liveperson_id, seatbot_id };
    })
  ).then((seathour) => {
    req.seathour = seathour;
    return next();
  });
};

const purgeAllSeatHours = (req, res, next) => {
  const bot_id = req.body.bot_id;
  const email = req.body.email;
  const seatbot_id = req.body.seatbot_id;
  // SeatBot.findOne({
  //   where: {
  //     bot_id,
  //     email,
  //   },
  // }).then((seatbot) => {
  //   if (!seatbot) {
  //     return res
  //       .status(401)
  //       .send({ message: "seat bot not found with this email and bot_id" });
  //   }
  // });
  SeatHours.destroy({
    where: {
      seatbot_id: seatbot_id,
    },
  });
  return next();
};

const findworkingHour = (req, res, next) => {
  SeatHours.findAll({
    where: {
      seatbot_id: req.seatBot.seatbot_id,
    },
  }).then((seathour) => {
    if (!seathour) {
      return res.status(401).send({ message: "seathour not found" });
    }
    req.seathour = seathour;
    return next();
  });
};
const getseatBothour = (req, res, next) => {
  res.send(req.seathour);
};
const getseatBot = (req, res, next) => {
  res.send(req.seatBot);
};

const getSeatHours = (req, res, next) => {
  res.send(req.seathour);
};

const findSeat = (req, res, next) => {
  Seat.findOne({ where: { email: req.body.email } }).then((seat) => {
    if (!seat) {
      return res.status(401).send({ message: "seat not found" });
    }
    req.seat = seat;
    return next();
  });
};

const checkSeatBotExistance = (req, res, next) => {
  SeatBot.findOne({
    where: { email: req.body.email, bot_id: req.body.bot_id },
  }).then((seatbot) => {
    if (!seatbot) {
      return next();
    }
    return res.status(409).send({ message: "Seat Already Exists" });
  });
};

const findAll = (req, res, next) => {
  SeatBot.findAll({
    where: { bot_id: req.query.bot_id || req.body.bot_id },
  }).then((seatbot) => {
    if (seatbot) {
      req.seatbot = seatbot;
      return next();
    }
    // return res.status(409).send({ message: "Seat Already Exists" });
  });
};
const checkSeatExistance = (req, res, next) => {
  Seat.findOne({
    where: { email: req.body.email },
  }).then((seat) => {
    if (!seat) {
      return next();
    }
    return res.status(409).send({ message: "Seat in use" });
  });
};
const findLiveChatPlugin = (req, res, next) => {
  const bot_id = req.query.bot_id || req.body.bot_id;
  LiveChatPlugin.findOne({ where: { bot_id } }).then((livechatplugin) => {
    if (livechatplugin) {
      console.log(livechatplugin, "livechatpluginlivechatplugin");
      req.livechatplugin = livechatplugin;
      return next();
    } else {
      return res.status(401).send({ message: "live chat plugin not found " });
    }
  });
};

const findAvailableSeat = async (req, res, next) => {
  console.log(
    req.livechatplugin.time_zone,
    req.livechatplugin,
    " req.livechatplugin.time_zone req.livechatplugin.time_zone"
  );
  const bot_id = req.query.bot_id || req.body.bot_id;
  const time_zone = req.livechatplugin.time_zone;
  console.log(time_zone, "time_zonetime_zonetime_zone");
  const day = new Date()
    .toLocaleDateString("EN-JO", { weekday: "long" })
    .toLowerCase();
  const hours = convertTZ(new Date(), time_zone.replace(/\s/g, "")).getHours();
  const minutes =
    (10 / 6) * convertTZ(new Date(), time_zone.replace(/\s/g, "")).getMinutes();
  const time = Math.floor((hours + minutes / 100) * 100) / 100;
  console.log(hours, "hours");
  console.log(minutes, "minutes");
  console.log(time, "time");

  const data = await sequelize.query("get_avaliable_seat :bot_id ,:time,:day", {
    replacements: {
      bot_id,
      time,
      day,
    },
  });

  req.seat = data[0][0];
  if (!req.seat) {
    return res.send({ message: "Not Available" });
  }
  updateConvSeat(req.seat.seatbot_id, 1);
  return next();
};

const findSeats = (req, res, next) => {};

const findMessages = (req, res, next) => {
  const conversation_id = req.body.conversation_id || req.query.conversation_id;
  SeatMessage.findAll({ where: { conversation_id } }).then((messages) => {
    req.messages = messages;
    return next();
  });
};

const findConversations = (req, res, next) => {
  //{bot_id , liveperson_id}
  const liveperson_id = req.body.liveperson_id || req.query.liveperson_id;
  const ended = req.body.ended || req.query.ended || null;
  sequelize
    .query("get_livechat_conversations :liveperson_id,:ended", {
      replacements: {
        liveperson_id,
        ended,
      },
    })
    .then((data) => {
      req.conversations = data[0];
      return next();
    });
};

const findConversation = (req, res, next) => {
  const conversation_id = req.body.conversation_id || req.query.conversation_id;
  SeatConversation.findOne({ where: { conversation_id } }).then(
    (conversation) => {
      req.conversation = conversation;
      return next();
    }
  );
};

const endConversation = (req, res, next) => {
  const conversation = req.conversation;
  updateConvSeatbyLId(conversation.bot_id, conversation.liveperson_id, -1);
  conversation.endConv();
  conversation.save();
  return next();
};

const findHours = (req, res, next) => {};

const seatLogin = async (req, res, next) => {
  const seat = req.seat;
  const { email, password } = req.body;
  //FIXME THIS IS BAD Practice
  if (seat.authenticate(password)) {
    if (seat.online) {
      return res
        .status(401)
        .send({ message: "Seat Already online on another device" });
    }
    seat.setOnline();
    await seat.save();
    return res.send({
      user: seat.toJSON(),
    });
  } else {
    return res.status(401).send({ message: "incorrect password" });
  }
};

const updateSeatPassword = async (req, res, next) => {
  const seat = req.seat;

  seat.setPassword(req.body.password);
  await seat.save();
  res.send({ message: "password updated successfully" });
};

const seatLogout = async (req, res) => {
  const seat = req.seat;
  seat.setOffline();
  await seat.save();
  return res.status(200).send({ message: "logout successfully" });
};

const createSeat = (req, res, next) => {
  const data = req.body;
  Seat.create({
    // bot_id: data.bot_id,
    email: data.email,
    //password: data.password, FIXME REMOVE COL
    online: false,
  })
    .then(async (seat) => {
      req.seat = seat;
      seat.setPassword(req.body.password);
      await seat.save();

      sequelize
        .query("set_livechat_bots :livepersonemail , :livepersonId", {
          replacements: {
            livepersonemail: seat.email,
            livepersonId: seat.liveperson_id,
          },
        })
        .then((data) => console.log(data));
      sequelize
        .query("set_livechat_hours :livepersonemail , :livepersonId", {
          replacements: {
            livepersonemail: seat.email,
            livepersonId: seat.liveperson_id,
          },
        })
        .then((data) => console.log(data));
      return next();
    })
    .catch((e) => {
      console.log(e);
      res.status(409).send({ message: "email is already in use" });
    });
};

const createSeatBot = (req, res, next) => {
  const data = req.body;
  SeatBot.create({
    bot_id: data.bot_id,
    email: data.email,
    // liveperson_id: req.seat.liveperson_id,
  })
    .then((seatbot) => {
      req.seatbot = seatbot;

      sequelize
        .query("set_livechat_seats :livepersonemail , :livepersonId", {
          replacements: {
            livepersonemail: seatbot.email,
            livepersonId: seatbot.liveperson_id,
          },
        })
        .then((data) => console.log("data", data[0][0], data));

      return next();
    })
    .catch((e) => {
      console.log(e, " next e");

      res.status(409).send({ message: "email is already in use" });
    });
};

const createConversation = (req, res, next) => {
  const data = req.body;
  SeatConversation.create({
    bot_id: data.bot_id,
    conversation_id: data.conversation_id,
    liveperson_id: data.liveperson_id,
    channel: data.channel,
  }).then((conversation) => {
    req.conversation = conversation;
    return next();
  });
};

const checkConversationExistance = (req, res, next) => {
  const data = req.body;

  SeatConversation.findOne({
    where: { conversation_id: data.conversation_id },
  }).then((conversation) => {
    if (conversation) {
      if (conversation.ended) {
        conversation.startConv();
        conversation.save();
      }
      return res.send(conversation);
    }
    return next();
  });
};

const getConversation = (req, res, next) => {
  res.send(req.conversation);
};

const getConversations = (req, res, next) => {
  res.send(req.conversations);
};

const getSeat = (req, res, next) => {
  res.send(req.seat);
};

const getSeatAuth = (req, res, next) => {
  res.send({ user: req.seat });
};

const getSeatBot = (req, res, next) => {
  res.send(req.seatbot);
};

const updateSeat = (req, res, next) => {
  const seat = req.seat;
  const data = req.body;
  seat.updateInfo(data);
  return next();
};

const addMessages = (req, res, next) => {
  const _messages = req.body;
  for (var i = 0; i < _messages.length; i++) {
    if (
      !_messages[i].conversation_id ||
      !_messages[i].bot_id ||
      !_messages[i].liveperson_id ||
      !_messages[i].from ||
      !_messages[i].type
    ) {
      return res.send({ message: "invalid data in row Number: " + i });
    }
  }
  SeatMessage.bulkCreate(_messages).then((messages) => {
    req.messages = messages;
    return next();
  });
};

const getMessages = (req, res, next) => {
  res.send(req.messages);
};

module.exports = {
  findSeats,
  findSeat,
  findAvailableSeat,
  findHours,
  findMessages,
  findConversations,
  seatLogin,
  seatLogout,
  addMessages,
  createSeat,
  createConversation,
  updateSeat,
  getSeat,
  getConversation,
  getConversations,
  findConversation,
  getMessages,
  endConversation,
  checkConversationExistance,
  createSeatBot,
  getSeatBot,
  checkSeatBotExistance,
  checkSeatExistance,
  getSeatAuth,
  findAll,
  updateSeatPassword,
  findSeatBot,
  createWorkingHourForSeat,
  getSeatHours,
  getseatBot,
  findworkingHour,
  getseatBothour,
  getAllSeatHours,
  setAllSeatHours,
  purgeAllSeatHours,
  findLiveChatPlugin,
};
