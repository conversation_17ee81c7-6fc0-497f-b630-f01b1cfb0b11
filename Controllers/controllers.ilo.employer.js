const Employer = require("../Models/Employer.ILO");

const set = (req, res, next) => {
    const data = { ...req.body };
    Employer.create({
        ...data
    }).then((employer) => {
        req.employer = employer;
        return next();
    });
};
const getAll = (req, res) => {
    res.send(req.employers);
};
const get = (req, res) => {
    res.send(req.employer);
};


const purge = (req, res, next) => {
    const employer_id = req.body.employer_id;
    Employer.destroy({
        where: {
            employer_id
        },
    }).then(() => {
        res.send({ message: "employer data deleted " });
    });
};

const update = async (req, res, next) => {
    const employer = req.employer;
    const employerData = req.body;
    if (employerData && employer) {
        employer.updateInfo({ ...employerData });
        await employer.save();
        return next();
    } else {
        return next();
    }
};
module.exports = {
    set,
    get,
    purge,
    update,
    getAll
};
