const { default: axios } = require("axios");
const TelegramIntegration = require("../Models/Telegram.integration");

const create = (req, res) => {
  const { bot_id, token } = req.body;
  TelegramIntegration.findAll({
    where: {
      bot_id,
      token,
    },
  }).then((telInts) => {
    if (telInts?.length === 0)
      TelegramIntegration.create({
        bot_id,
        token,
      })
        .then((telInts) => {
          axios
            .post(
              `https://api.telegram.org/bot${token}/setWebhook?url=https://mock-engine.azurewebsites.net/telegram/messages?bot_id=${bot_id}`
            )
            .then((response) => {
              return res.send(telInts);
            })
            .catch((error) => {
              console.log("error", error);
              return res.status(500).send({ message: "Webhook register failed" });
            });
        })
        .catch((error) => {
          console.log(error);
          return res.status(500).send({ message: "Something went wrong" });
        });
    else
      return res.status(409).send({ message: "Telegram Integration exists" });
  });
};

const get = (req, res) => {
  const { bot_id } = req.params;
  TelegramIntegration.findOne({
    where: {
      bot_id,
    },
  })
    .then((telInt) => {
      if (!telInt)
        return res
          .status(404)
          .send({ message: "Telegram Integration not found" });
      return res.send(telInt);
    })
    .catch((error) => {
      console.log(error);
      return res.status(500).send({ message: "Something went wrong" });
    });
};

const deleteConnection = async (req, res) => {
  const { telegram_integration_id } = req.body;
  const telInt = await TelegramIntegration.findOne({
    where: {
      telegram_integration_id,
    },
  });
  if (!telInt)
    return res.status(404).send({ message: "Telegram Integration not found" });

  TelegramIntegration.destroy({
    where: {
      telegram_integration_id,
    },
  });
  return res.status(202).send({ ...telInt });
};
module.exports = { create, get, deleteConnection };
