const MonitorBot = require("../Models/Monitor.bot");

const get = (req, res) => {
  res.send(req.monitorbot);
};

const getAll = (req, res) => {
  res.send(req.monitorbots);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  MonitorBot.create({
    email: data.email,
    bot_id: data.bot_id,
    active: data.active,
    dashboard: data.dashboard,
    order: data.order,
  }).then((monitorbot) => {
    req.monitorbot = monitorbot;
    return next();
  });
};

const purge = (req, res, next) => {
  const data = req.body;
  MonitorBot.destroy({
    where: {
      bot_id: data.bot_id,
      email: data.email,
    },
  }).then(() => {
    res.send({ message: "Monitor Bot data deleted" });
  });
};

const update = async (req, res, next) => {
  const monitorbot = req.monitorbot;
  if (req.body && monitorbot) {
    monitorbot.updateInfo({ ...req.body });
    await monitorbot.save();
    return next();
  } else {
    return next();
  }
};

module.exports = { get, set, update, purge, getAll };
