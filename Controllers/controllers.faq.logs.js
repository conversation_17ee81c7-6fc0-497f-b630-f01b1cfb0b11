const FAQ_Logs = require("../Models/Faq.Logs");
const User = require("../Models/User");

const get = (req, res) => {
  res.send(req.faq_logs);
};


const set = (req, res, next) => {
  const data = { ...req.body };
  FAQ_Logs.create(data)
    .then((faq_logs) => {
      req.faq_logs = faq_logs;
      return next();
    })
    .catch((e) => res.send({ e, message: "an error occured" }));
};

const update = async (req, res, next) => {
  const faq_logs = req.faq_logs;
  const data = { ...req.body };
  faq_logs.updateInfo(data);
  req.faq_logs = faq_logs;
  await faq_logs.save();
  return next();
};

module.exports = { get, set, update };
