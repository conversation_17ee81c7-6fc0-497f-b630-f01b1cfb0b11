const Item = require("../Models/Item");
const { createOrderEmail, createCustomerEmail } = require("../Constants/Emails");
const sgEmail = require("../Services/Email");
const createMessage = require("../Constants/message");
const fs = require("fs");
const { sendSMS } = require("../Services/SMS");
const get = (req, res) => {
  res.send(req.item);
};

const getDetails = (req, res) => {
  if(!req.cart)
   { res.send(req.item);}
  else{
    req.item.is_cart = req.cart.cart_active && req.cart.phone_verified && req.cart.email_verified ? true : false;
    req.item.currency = req.cart.cart_currency ? req.cart.cart_currency : req.item.currency;
    var offeritem = req.offeritems
    ? req.offeritems.find((a) => a.item_id === req.item.item_id)
    : undefined;
  var offer = offeritem
    ? req.offers.find((a) => a.offer_id === offeritem.offer_id)
    : undefined;
  req.item.offer = offer;
    res.send(req.item);
  }
}

const getAll = (req, res, next) => {
  if (!req.cart) {
    res.send(req.items);
  } else {
    res.send(
      req.items.map((itm) => {
        itm.is_cart =
          req.cart.cart_active &&
          req.cart.phone_verified &&
          req.cart.email_verified
            ? true
            : false;
        itm.currency = req.cart.cart_currency
          ? req.cart.cart_currency
          : itm.currency;
        var offeritem = req.offeritems
          ? req.offeritems.find((a) => a.item_id === itm.item_id)
          : undefined;
        var offer = offeritem
          ? req.offers.find((a) => a.offer_id === offeritem.offer_id)
          : undefined;
        itm.offer = offer;
        return itm;
      })
    );
  }
};

const set = async (req, res, next) => {
  const data = { ...req.body };
  Item.create(data)
    .then(async (item) => {
      req.item = item;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const setAll = async (req, res, next) => {
  const itemsData = req.body.items;
  const bot_id = req.body.bot_id;
  const categories = req.categories;
  Item.bulkCreate(
    itemsData.map((item) => {
      return {
        ...item,
        bot_id,
        category_name: null,
        category_id: categories.find(
          (a) => a.category_name === item.category_name.toLowerCase()
        ).category_id,
        lemmatized_item_title: item.lemmatized_item_title
      };
    })
  )
    .then((items) => {
      req.items = items;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const update = async (req, res, next) => {
  const item = req.item;
  const data = { ...req.body };
  item.updateInfo(data);
  req.item = item;
  await item.save();
  return next();
};

const purge = async (req, res, next) => {
  const data = { ...req.body };
  const item = await Item.findOne({
    where: {
      item_id: data.item_id,
    },
  });
  const bot_id = item.bot_id; 
  Item.destroy({
    where: {
      item_id: data.item_id,
    },
  }).then(() => {
    req.bot_id = bot_id;
    return next();
    // res.send({ message: "Item deleted successfully" });
  });
};

const purgeAll = (req, res, next) => {
  Item.destroy({
    where: {
      bot_id: req.body.bot_id,
    },
  }).then(() => {
    return next();
  });
};


const sendOrderByEmail = (req, res, next) => {
  const items = req.items;
  const cart = req.cart;
  const customer_email = req.body?.customer_email;

  const storeEmailPromise = sgEmail.send(
    createMessage(
      "YOUR HAVE AN ORDER",
      cart.email,
      createOrderEmail(items, req.body.cart, {
        name: req.body.name,
        phone: req.body.phone,
        address: req.body.address,
        total_price: req.body.total_price,
      })
    )
  );
  if(customer_email){
    const customerEmailPromise = sgEmail.send(
      createMessage(
        "YOUR ORDER",
        customer_email,
        createCustomerEmail(items, req.body.cart, {
          name: req.body.name,
          phone: req.body.phone,
          address: req.body.address,
          total_price: req.body.total_price,
        })
      )
    );

    Promise.all([storeEmailPromise, customerEmailPromise])
      .then(() => {
        res.send({
          email: customer_email,
          cartlog: req.cartlog,
          message: "Both store and customer emails have been sent",
          success: true,
        });
      })
      .catch(() => {
        res.send({
          email: customer_email,
          cartlog: req.cartlog,
          message: "There was an issue with sending one or both emails",
          success: false,
        });
      });
  } else {
    storeEmailPromise
    .then(() =>
      res.send({
        email: cart.email,
        cartlog: req.cartlog,
        message: "Your order has been sent the store",
        success: true,
      })
    )
    .catch(() =>
      res.send({
        email: cart.email,
        cartlog: req.cartlog,
        message: "There is an issue with the email",
        success: false,
      })
    );
  }

};

function indexOfMax(arr) {
  if (arr.length === 0) {
    return -1;
  }

  var max = arr[0];
  var maxIndex = 0;

  for (var i = 1; i < arr.length; i++) {
    if (arr[i] > max) {
      maxIndex = i;
      max = arr[i];
    }
  }

  return maxIndex;
}

const updateItemsQuantities = async (req, res, next) => {
  const cartItems = req.body.cart;

  try {
    for (const cartItem of cartItems) {

      const { item_id, qty } = cartItem;
      const item = await Item.findOne({ where: { item_id: item_id } });
      if (item) {
        await item.update({ item_qty: item.item_qty - qty });
      }
    }
    return next();
  } catch (error) {
    console.error('Error updating items:', error);
    return res.status(500).json({ message: 'Failed to update items.' });
  }
};


module.exports = {
  get,
  set,
  update,
  purge,
  getAll,
  setAll,
  purgeAll,
  sendOrderByEmail,
  getDetails,
  updateItemsQuantities,
};
