const Logs = require("../Models/Bot.Logs");
const Bot = require("../Models/Bot");
const User = require("../Models/User");
const sequelize = require("../db");
const FAQ = require("../Models/FAQ");
const botLogHelper = require("../helper/log.helper");
const SmallTalk = require("../Models/SmallTalk");
const BadWord = require("../Models/BadWord");

const createBotFaqLogs = async (req, res, next) => {
  const requestType = req?.method;
  const action_type = botLogHelper.getActionType(requestType);

  const botId =
  req?.faq?.bot_id ?  req?.faq?.bot_id  :  req?.faqs?.length > 0 ? req?.faqs[0]?.bot_id : null;
  const { bot, user } = await botLogHelper.getBot(req, botId);
  const bot_id = bot?.bot_id;

  let category = "FAQ";

  if (requestType?.toLowerCase() === "delete") {
    const faqIds = req?.body?.faqs;
    await FAQ.findAll({
      where: { bot_id, faq_id: faqIds },
    }).then(async (faqs) => {
      const deletedFaqs = faqs?.dataValues || faqs;
      await Logs.bulkCreate(
        deletedFaqs?.map((faq) => {
          return {
            ...faq,
            bot_id: bot.bot_id,
            user_id: user?.user_id,
            log: faq.question,
            category: category,
            action_type: action_type,
            id: faq.faq_id,
          };
        })
      );
    });
  } else if (requestType?.toLowerCase() === "post") {
    const createdFaqs = req?.faqs || req?.body;
    await Logs.bulkCreate(
      createdFaqs?.map((faq) => {
        return {
          ...faq,
          bot_id: bot.bot_id,
          user_id: user?.user_id,
          log: faq.question,
          category: category,
          action_type: action_type,
          id: faq?.faq_id,
        };
      })
    );
  } else if (
    requestType?.toLowerCase() === "put" ||
    requestType?.toLowerCase() === "patch"
  ) {
    const updatedFaqs = req?.body;
    if(req?.body?.length > 0){
    await Logs.bulkCreate(
      updatedFaqs?.map((faq) => {
        return {
          ...faq,
          bot_id: bot.bot_id,
          user_id: user?.user_id,
          log: faq.question,
          category: category,
          action_type: action_type,
          id: faq?.faq_id,
        };
      })
    );
  }else{
  await Logs.create(
    {
      bot_id: bot.bot_id,
      user_id: user?.user_id,
      log: updatedFaqs?.question || 'details',
      category: category,
      action_type: action_type,
      id: updatedFaqs?.faq_id,
    }
  )
  }
  }
  return next();
};
const createBotDialogLogs = async (req, res, next) => {
  const requestType = req?.method;
  const action_type = botLogHelper.getActionType(requestType);

  const botId = req?.body?.bot_id || req?.dialog?.bot_id;
  const { bot, user } = await botLogHelper.getBot(req, botId);
  const bot_id = bot?.bot_id;

  const category = "DIALOG";

  if (requestType?.toLowerCase() === "delete") {
    const deletedDialog = req?.dialog;
    Logs.create({
      bot_id: bot.bot_id,
      user_id: +user?.user_id || null,
      log: deletedDialog.dialog_name,
      category: category,
      action_type: action_type,
      id: deletedDialog?.dialog_id,
    });
  } else if (requestType?.toLowerCase() === "post") {
    const createdDialog = req?.dialog;
    Logs.create({
      bot_id: bot.bot_id,
      user_id: +user?.user_id || null,
      log: createdDialog.dialog_name,
      category: category,
      action_type: action_type,
      id: createdDialog?.dialog_id,
    });
  } else if (
    requestType?.toLowerCase() === "put" ||
    requestType?.toLowerCase() === "patch"
  ) {
    const updatedDialog = req?.body;
    Logs.create({
      bot_id: bot.bot_id,
      user_id: +user?.user_id || null,
      log: updatedDialog.dialog_name,
      category: category,
      action_type: action_type,
      id: updatedDialog?.dialog_id,
    });
  }
  return next();
};
const createBotSmallTalkLogs = async (req, res, next) => {
  const requestType = req?.method;
  const action_type = botLogHelper.getActionType(requestType);

  const botId =
    req?.smallTalk?.bot_id ||
    (req?.smallTalks?.length > 0 ? req?.smallTalks[0]?.bot_id : null);
  const { bot, user } = await botLogHelper.getBot(req, botId);
  const bot_id = bot?.bot_id;

  let category = "SMALLTALK";

  if (requestType?.toLowerCase() === "delete") {
    console.log(req.body);
    const smallTalksIds = req?.body?.smalltalks || req?.body?.small_talk_id;
    await SmallTalk.findAll({
      where: { bot_id, small_talk_id: smallTalksIds },
    }).then(async (smallTalks) => {
      const deletedSmallTalks = smallTalks?.dataValues || smallTalks;
      await Logs.bulkCreate(
        deletedSmallTalks?.map((smallTalk) => {
          return {
            ...smallTalk,
            bot_id: bot.bot_id,
            user_id: user?.user_id,
            log: smallTalk.question,
            category: category,
            action_type: action_type,
            id: smallTalk.small_talk_id,
          };
        })
      );
    });
  } else if (requestType?.toLowerCase() === "post") {
    const smallTalks = req?.smallTalks?.dataValues || req?.smallTalks;
    const smallTalk = req?.smallTalk?.dataValues || req?.smallTalk;
    if (smallTalks) {
      const createdSmallTalks = smallTalks?.map((t) => t.dataValues);

      await Logs.bulkCreate(
        createdSmallTalks?.map((smallTalk) => {
          return {
            ...smallTalk,
            bot_id: bot.bot_id,
            user_id: user?.user_id,
            log: smallTalk?.question,
            category: category,
            action_type: action_type,
            id: smallTalk?.small_talk_id,
          };
        })
      );
    } else {
      await Logs.create({
        ...smallTalk,
        bot_id: bot.bot_id,
        user_id: user?.user_id,
        log: smallTalk?.question,
        category: category,
        action_type: action_type,
        id: smallTalk?.small_talk_id,
      });
    }
  } else if (
    requestType?.toLowerCase() === "put" ||
    requestType?.toLowerCase() === "patch"
  ) {
    const updatedSmallTalk = req?.body;

    await Logs.create({
      ...updatedSmallTalk,
      bot_id: bot.bot_id,
      user_id: user?.user_id,
      log: updatedSmallTalk?.question,
      category: category,
      action_type: action_type,
      id: updatedSmallTalk?.small_talk_id,
    });
  }
  return next();
};
const createBotBadWordLogs = async (req, res, next) => {
  const requestType = req?.method;
  const action_type = botLogHelper.getActionType(requestType);

  const botId =
    req?.badWord?.bot_id ||
    (req?.badWords?.length > 0 ? req?.badWords[0]?.bot_id : null);

  const { bot, user } = await botLogHelper.getBot(req, botId);
  const bot_id = bot?.bot_id;

  let category = "BADWORD";
  if (requestType?.toLowerCase() === "delete") {
    const badWordsIds = req?.body?.badwords || req?.body?.bad_word_id;
    await BadWord.findAll({
      where: { bot_id, bad_word_id: badWordsIds },
    }).then(async (badWords) => {
      const deletedBadwords = badWords?.dataValues || badWords;
      await Logs.bulkCreate(
        deletedBadwords?.map((badWord) => {
          return {
            ...badWord,
            bot_id: bot.bot_id,
            user_id: user?.user_id,
            log: badWord?.bad_word || '',
            category: category,
            action_type: action_type,
            id: badWord.bad_word_id,
          };
        })
      );
    });
  } else if (requestType?.toLowerCase() === "post") {
    const badWords = req?.badWords?.dataValues || req?.badWords;
    const badWord = req?.badWord?.dataValues || req?.badWord;
    if (badWords) {
      const createdBadWords = badWords?.map((t) => t.dataValues);
      await Logs.bulkCreate(
        createdBadWords?.map((badWord) => {
          return {
            ...badWord,
            bot_id: bot.bot_id,
            user_id: user?.user_id,
            log: badWord?.bad_word,
            category: category,
            action_type: action_type,
            id: badWord?.bad_word_id,
          };
        })
      );
    } else {
      await Logs.create({
        ...badWord,
        bot_id: bot.bot_id,
        user_id: user?.user_id,
        log: badWord?.bad_word,
        category: category,
        action_type: action_type,
        id: badWord?.bad_word_id,
      });
    }
  } else if (
    requestType?.toLowerCase() === "put" ||
    requestType?.toLowerCase() === "patch"
  ) {
    const updatedbadWord = req?.body;

    await Logs.create({
      ...updatedbadWord,
      bot_id: bot.bot_id,
      user_id: user?.user_id,
      log: updatedbadWord?.bad_word,
      category: category,
      action_type: action_type,
      id: updatedbadWord?.bad_word_id,
    });
  }
  return next();
};

const createBotChannelLogs = async (req, res, next) => {
  let id = null;

  const requestType = req?.method;
  const category = req?.body?.whatsapp_id
    ? "whatsapp"
    : req?.body?.channel || req?.query?.channel || "CHANNEL";
  if (category === "facebook") {
    const facebook = req?.facebook?.dataValues || req?.facebook;
    id = facebook?.facebook_channel_id;
  }
  if (category === "whatsapp") {
    const whatsapp = req?.whatsapp?.dataValues || req?.whatsapp;
    id = whatsapp?.whatsapp_id;
  }
  let names = "connection";

  const { bot, user } = await botLogHelper.getBot(req, req?.body?.bot_id);
  const action_type = botLogHelper.getActionType(requestType);

  const bot_id = bot.bot_id;

  const botLog = botLogHelper.getBotLog(
    user,
    action_type,
    category,
    names,
    bot
  );

  

  Logs.create({
    bot_id: bot.bot_id,
    user_id: user?.user_id,
    log: botLog.log,
    category: botLog.category,
    action_type: botLog.action_type,
    id,
  });

  return next();
};


const getAll = (req, res) => {
  res.send(req.logs);
};
module.exports = {
  createBotFaqLogs,
  createBotDialogLogs,
  createBotSmallTalkLogs,
  createBotChannelLogs,
  getAll,
  createBotBadWordLogs
};
