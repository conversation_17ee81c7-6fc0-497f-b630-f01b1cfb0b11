const Message = require("../Models/Message");
const sequelize = require("../db");

const get = (req, res, next) => {
  res.send(req.message);
};

const set = async (req, res, next) => {
  const data = { ...req.body };
  Message.create(data)
    .then((message) => {
      req.message = message;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const update = async (req, res, next) => {
  const message = req.message;
  if (req.body && message) {
    message.updateInfo({ ...req.body });
    await message.save();
    return next();
  } else {
    return next();
  }
};

const find = async (req, res, next) => {
  const language = req.body.language || req.query.language;
  Message.findOne({ where: { language } })
    .then((message) => {
      req.message = message;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const getSessions = (req, res, next) => {
  const bot_id = req.query.bot_id;
  sequelize
    .query("designer_get_sessions :botID", {
      replacements: {
        botID: bot_id,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};


const getMessagesPerSession = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const tr_session = req.query.tr_session;
  sequelize
    .query("bot_designer_messages_per_session :botID , :tr_session", {
      replacements: {
        botID: bot_id,
        tr_session : tr_session
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

module.exports = { set, get, update, find,getSessions,getMessagesPerSession };
