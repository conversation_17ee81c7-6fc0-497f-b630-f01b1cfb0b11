const Customs_Trainee = require("../Models/Customs.Trainee");

const get = (req, res) => {
  res.send(req.customs_trainee);
};

const set = (req, res, next) => {
    const data = {...req.body};
    Customs_Trainee.create(data).then((customs_trainee) => {
      req.customs_trainee = customs_trainee;
      return next();
    });

  };

const update = async (req, res, next) => {
  const customs_trainee = req.customs_trainee;
  const data = { ...req.body };
  customs_trainee.updateInfo(data);
  req.customs_trainee = customs_trainee;
  await customs_trainee.save();
  return next();
};



const purge = (req, res, next) => {
    const trainee_id = req.body.trainee_id;
    const bot_id = req.body.bot_id
    Customs_Trainee.destroy({
      where: {
        trainee_id,bot_id
      },
    })
    .then(() => {
      res.send({ message: "Student is deleted" });
    });

  }


module.exports = {
  get,
  update,
  set,
  purge
};
