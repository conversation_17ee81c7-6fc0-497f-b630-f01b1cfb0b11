const Preprocessor = require("../NLP/QnA");
const FAQ = require("../Models/FAQ");
const similarity = require("../NLP/SenteceSmilarity");
const Op = require("sequelize").Op;
const FuzzySearch = require("fuzzy-search");
const Trigger = require("../Models/Trigger");
const Intent = require("../Models/Intent");
const TagMatch = require("../Models/TagMatch");
const FallbackTransaction = require("../Models/FallbackTransaction");
const FaqContext = require("../Models/FAQ.Context");
const sequelize = require("../db");
const toNotinclude = require("../Dictionary/Not.Include.json");
const WhQuestions = require("../Dictionary/Wh.json");
const isProd = global.process.env.NODE_ENV === "production" ? true : false;
const { serverUrl } = require("../constants");
const fetch = require("node-fetch");
// const cachedDB = require("../db_cache");

var intents = [];
var operationIntent = [];

Intent.findAll({}).then((data) => {
  intents = data;
  operationIntent = intents.filter((a) => a.intent_type === "Operation");
});

function toEnglishTokenize(str) {
  const persianNumbers = ["۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹", "۰"];
  const arabicNumbers = ["١", "٢", "٣", "٤", "٥", "٦", "٧", "٨", "٩", "٠"];
  const englishNumbers = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "0"];

  return str
    .split("")
    .map(
      (c) =>
        englishNumbers[persianNumbers.indexOf(c)] ||
        englishNumbers[arabicNumbers.indexOf(c)] ||
        c
    )
    .join("")
    .replace(/[^a-zA-Z0-9,.${}_\u0621-\u064A ]/g, "");
}

const approximate = (value) => Math.floor(value * 100) / 100;

const checkOperationalIntent = (question) => {
  const operation = {};
  var splicedQuestion = question;
  const belowIntent = operationIntent
    .find((a) => a.intent_subType === "below")
    .intent_props.split(" ");
  const betweenIntent = operationIntent
    .find((a) => a.intent_subType === "between")
    .intent_props.split(" ");
  const aboveIntent = operationIntent
    .find((a) => a.intent_subType === "above")
    .intent_props.split(" ");
  //.map((a) => a.replace("--", " ").replace("--", " ").replace("--", " "));
  question.map((q, i) => {
    if (belowIntent.indexOf(q) !== -1) {
      splicedQuestion = question.slice(0, i);
      var subOperationValues = +question.slice(i).find((a) => !isNaN(+a));
      operation.value = subOperationValues;
      operation.type = "below";
    }
    if (aboveIntent.indexOf(q) !== -1) {
      splicedQuestion = question.slice(0, i);
      var subOperationValues = +question.slice(i).find((a) => !isNaN(+a));
      operation.value = subOperationValues;
      operation.type = "above";
    }
    if (betweenIntent.indexOf(q) !== -1) {
      splicedQuestion = question.slice(0, i);
      var subOperationValues = question
        .slice(i)
        .map((a) => +a)
        .filter((a) => !isNaN(+a));
      operation.values = subOperationValues;
      //console.log("subOperationValues", subOperationValues);
      operation.type = "between";
    }
  })
  return { operation, splicedQuestion };
};

const insertTransactionDB = (info) => {
  // NOTE The End is near
  console.log("is_voice_tr", info.is_voice_tr);
  // console.log("info", info);
  if (true /*isProd*/) {
    sequelize
      .query(
        `bot_designer_transaction_set :bot_id,
          :country,
          :city,
          :ip,
          :function,
          :Q,
          :A,
          :category,
          :answered,
          :continent,
          :session,
          :channel,
          :score,
          :owner,
          :is_voice_tr
          `,
        {
          replacements: {
            bot_id: info.bot_id === "JOPOST_Sa3i" ? 63 : info.bot_id,
            country: info.country ? info.country : null,
            city: info.city ? info.city : null,
            ip: info.ip ? info.ip : null,
            is_voice_tr: info.is_voice_tr ? info.is_voice_tr : false,
            continent: info.continent ? info.continent : null,
            session: info.session ? info.session : null,
            channel: info.channel ? info.channel : "Dev",

            function: info.function,
            Q: info.Q,
            A: info.A,
            category: info.category,
            answered: info.answered,
            score: info.score.toFixed(1),
            owner: info.owner,
          },
        }
      )
      .then((data) => {});
  }
};

const generateTransactionAnswer = (tag) => {
  if (tag.trigger_type === "qna" || tag.trigger_type === "faq") {
    return {
      A: tag.answer,
      function: "QNA",
      category: "QNA",
    };
  } else if (
    tag.trigger_type.includes("suggestion") ||
    tag.trigger_type === "card" ||
    tag.trigger_type === "dialog" ||
    tag.trigger_type === "whatsapp_list" ||
    tag.trigger_type === "whatsapp_buttons" ||
    tag.trigger_type === "whatsapp_template"
  ) {
    return {
      A: tag.question,
      category: tag.trigger_type,
      function: tag.trigger_name || "",
    };
  } else if (tag.trigger_type === "item") {
    return {
      A: tag.item_title,
      function: "item",
      category: "item",
    };
  } else if (tag.trigger_type === "category") {
    return {
      A: tag.category_name,
      category: "category",
      function: "category",
    };
  } else if (tag.trigger_type === "offer") {
    return {
      A: tag.question, //NOTE offer_description
      category: "offer",
      function: "offer",
    };
  }
};

const generateTransactionObj = (body_info, req, tag, scoreObj) => {
  const info = {
    ...body_info,
    owner: req.bot.user_id,
    bot_id: req.bot.bot_id,
    Q: req.question_full.join(" "),
    ...generateTransactionAnswer(tag),
    answered: 1,
    score: scoreObj.score,
  };

  return info;
};

// const cartQuestions = Array.from(
//   new Set(cartQuestionsArray.join(" ").split(" "))
// ).join(" ");

const preProcessText = (req, res, next) => {
  req.q1 = new Preprocessor(req.body.q1);
  req.q2 = new Preprocessor(req.body.q2);
  return next();
};

const findSimilarity = (req, res, next) => {
  req.similarityScore = similarity(req.q1.splittedArr, req.q2.splittedArr);

  return next();
};

const getAnswer = (req, res, next) => {
  res.send({
    ...req.similarityScore,
  });
};

function indexOfMax(arr) {
  if (arr.length === 0) {
    return -1;
  }

  var max = arr[0];
  var maxIndex = 0;

  for (var i = 1; i < arr.length; i++) {
    if (arr[i] > max) {
      maxIndex = i;
      max = arr[i];
    }
  }

  return maxIndex;
}

function getLiteralMatch(literals) {
  var max = literals[0];
  var maxIndex = 0;
  for (var i = 0; i < literals.length; i++) {
    if (literals[i] > max) {
      maxIndex = i;
      max = literals[i];
    }
  }
  if (max) {
    return { success: true, maxIndex };
  } else {
    return { success: false, maxIndex };
  }
}

function getAdaptiveMax(simScores) {
  const literals = simScores.map((a) => a.literal);
  const literalMatch = getLiteralMatch(literals);
  const arr = simScores.map((a) => a.score);
  const adaArr = [];

  if (literalMatch.success) {
    return { maxIndex: literalMatch.maxIndex, adaArr };
  }
  if (arr.length === 0) {
    return -1;
  }
  var max = arr[0];
  var maxIndex = 0;

  for (var i = 0; i < arr.length; i++) {
    if (arr[i] > 0.91) {
      adaArr.push(i);
    }
    if (arr[i] > max) {
      maxIndex = i;
      max = arr[i];
    }
  }
  return { maxIndex, adaArr };
}

function checkConflicts(adaArr, tags, isComingFromConf) {
  if (isComingFromConf) {
    return {
      isConflict: false,
      conflictedTags: [],
    };
  }
  const numberOfCats = adaArr
    .map((a) => tags[a])
    .filter((a) => a.trigger_type === "category").length;
  const conflictedTags = Array.from(
    adaArr
      .map((a) => {
        return tags[a];
      })
      .filter((a) => a.trigger_type === "category" || a.trigger_type === "item")
      .map((a) => {
        return {
          question: a.question,
          item_id: a.item_id,
          category_id: a.category_id,
          trigger_type: a.trigger_type,
        };
      })
  );
  if (numberOfCats > 1) {
    return {
      isConflict: true,
      conflictedTags,
    };
  } else {
    return {
      isConflict: false,
      conflictedTags: [],
    };
  }
}

const lookupQuestion = (req, res, next) => {
  const question = `%${req.body.question}%`;
  FAQ.findAll({
    where: {
      question: {
        [Op.like]: question,
      },
    },
  }).then((results) => res.send(results));
};

const fuzzyQuestion = (req, res, next) => {
  const question = `%${req.body.question}%`;
  FAQ.findAll({}).then((faqs) => {
    const searcher = new FuzzySearch(faqs, ["question"]);
    const search = searcher.search(req.body.question);
    res.send(search);
  });
};

const findFAQSimilarity = (req, res, next) => {
  const faq_threshold = req.global_threthold - 0.15;
  const question = req.question_full;
  const question_length = req.question_full.length;
  const bot_id = req.body.bot_id;

  sequelize
    .query(`designer_faqs_get :botID ${/**:chitChat */ ""}`, {
      replacements: {
        botID: bot_id,
        //chitChat: false
      },
    })
    .then((data) => {
      const faqs = data[0];
      const faqs_length = faqs.length;
      req.searched.faqs = faqs_length;
      req.searched.total += faqs_length;
      const simScore = [];
      faqs.map((a) => {
        var sim = similarity(question, a.question.split(" ")).score;
        if (sim >= question_length * faq_threshold) {
          simScore.push(approximate(sim));
        } else {
          simScore.push(0);
        }
      });
      const maxIndex = indexOfMax(simScore);
      if (!simScore.filter((a) => a).length) {
        //NOTE FALLBACK
        return next();
      }
      const scoreObj = similarity(question, faqs[maxIndex].question.split(" "));
      insertTransactionDB({
        owner: req.bot.user_id,

        answered: true,
        Q: req.question_full.join(" "),
        A: faqs[maxIndex].answer,
        function: "QNA",
        bot_id,
        category: "QNA",

        continent: req.body.continent,
        channel: req.body.channel,
        session: req.body.session,
        is_voice_tr: req.body.is_voice_tr,
        country: req.body.country,
        city: req.body.city,
        ip: req.body.ip,
        score: simScore[maxIndex] / req.question_full.join(" ").length,
      });
      res.send({
        ...faqs[maxIndex],
        searched: faqs.length,
        trigger_type: "faq",
        question_length,
        question_proc: req.question,
        question_full: req.question_full,
        answered: true,
        ...scoreObj,
      });
    });
};

const findCaterogiesSimilarity = (req, res, next) => {
  const category_threshold = req.global_threthold + 0.05;
  const question = req.question;
  const question_length = question.length;
  const bot_id = req.body.bot_id;
  const searchIntent = intents.find(
    (a) => a.intent_type === "Products"
  ).intent_props;
  // Trigger.findAll({
  //   where: { bot_id },
  // }).then((triggers) => {

  sequelize
    .query("designer_categories_get :botID", {
      replacements: {
        botID: bot_id,
      },
    })
    .then((data) => {
      const categories = data[0];
      const cats_length = categories.length;
      req.searched.total += cats_length;
      req.searched.categories = cats_length;
      if (!cats_length) {
        return next();
      }
      const simScore = [];
      categories.map((a) => {
        var sim = similarity(question, a.category_name.split(" ")).score;
        if (sim >= question_length * category_threshold) {
          simScore.push(approximate(sim));
        } else {
          simScore.push(0);
        }
      });
      const maxIndex = indexOfMax(simScore);
      if (!simScore.filter((a) => a).length) {
        //NOTE FALLBACK
        return next();
      }
      insertTransactionDB({
        answered: true,
        owner: req.bot.user_id,
        Q: req.question_full.join(" "),
        A: categories[maxIndex].category_name,
        function: "category",
        bot_id,
        category: "category",
        continent: req.body.continent,
        channel: req.body.channel,
        session: req.body.session,
        is_voice_tr: req.body.is_voice_tr,
        country: req.body.country,
        city: req.body.city,
        ip: req.body.ip,
        score: simScore[maxIndex] / req.question.length,
      });

      res.send({
        ...categories[maxIndex],
        trigger_type: "category",
        score: simScore[maxIndex],
        // searched: req.searched,
        proc_question: req.question,
        full_question: req.question_full,
        question: generateSearchTerm(
          categories[maxIndex].category_name.split(" "),
          question
        ),
        incoming: generateSearchTerm(
          question,
          categories[maxIndex].category_name.split(" ")
        ),
        question_length,
        answered: true,
      });
    });
};

const fallbackMessage = (req, res, next) => {
  const bot_id = req.body.bot_id;
  const fallback_message = req.bot.fallback_message;
  const fallback_dialog = req.bot.fallback_dialog;
  if (!req.body.question_inspect) {
    FallbackTransaction.create({
      question: req.question_full.join(" "),
      resolved: false,
      bot_id,
    });
    insertTransactionDB({
      bot_id,
      owner: req.bot.user_id,
      answered: false,
      Q: req.question_full.join(" "),
      A: fallback_message !== "" ? fallback_message : fallback_dialog,
      function: "fallback",
      continent: req.body.continent,
      channel: req.body.channel,
      session: req.body.session,
      is_voice_tr: req.body.is_voice_tr,
      country: req.body.country,
      city: req.body.city,
      ip: req.body.ip,
      category: "fallback",
      score: 0,
    });
  }
  if(fallback_message  && !fallback_dialog){
    return  res.send({
      bot_id,
      trigger_type: "fallback",
      proc_question: req.question,
      full_question: req.question_full,
      answer: fallback_message,
      answered: false,
    })
  }else{
    return  res.send({
      bot_id,
      trigger_type: "fallback_dialog",
      proc_question: req.question,
      full_question: req.question_full,
      answer: fallback_dialog,
      answered: false,
    })
};
}

const validateIntent = (q1, q2, threshold) => {
  //NOTE TWO WAY SIMILARITY SCORE
  const simScore = similarity(q1, q2).score;
  const senLen = Math.min(q1.length, q2.length);
  return simScore > senLen * threshold;
};

const generateSearchTerm = (item_name, question) => {
  const similarityObj = similarity(item_name, question);
  const index = indexOfMax(similarityObj.matchScore);
  return item_name[index];
};

const generateSearchTermArray = (question, item_name, threshold) => {
  const similarityObj = similarity(item_name, question);
  const indexes = similarityObj.matchScore
    .map((a, ind) => {
      //console.log(a, ind);
      if (a >= threshold) {
        return ind;
      } else {
        return false;
      }
    })
    .map((a) => a);

  // console.log("item_name", item_name);
  // console.log("threshold", threshold);

  // console.log("question", question);
  // console.log("similarityObj", similarityObj);
  // console.log("indexes", indexes);

  return item_name.filter((a, i) => indexes.indexOf(i) !== -1);
};

const findItemsSimilarity = (req, res, next) => {
  var delly = false; //NOTE (13/6/2021) NICKNAME FOR STOPPING THE PROCESS PROPS TO Matthew Dellavedova FOR STOPPING STEPH 2016
  const item_threshold = req.global_threthold;
  const question = req.question;
  const question_length = question.length;
  const bot_id = req.body.bot_id;
  const searchIntent = intents.find(
    (a) => a.intent_type === "Products"
  ).intent_props;
  const priceIntent = intents.find(
    (a) => a.intent_type === "Prices"
  ).intent_props;
  const optionsIntent = intents.find(
    (a) => a.intent_type === "Options"
  ).intent_props;

  // Trigger.findAll({
  //   where: { bot_id },
  // }).then((triggers) => {

  sequelize
    .query("designer_items_get :botID", {
      replacements: {
        botID: bot_id,
      },
    })
    .then((data) => {
      const items = data[0];
      const items_length = items.length;
      req.searched.total += items_length;
      req.searched.items = items_length;

      if (!items_length) {
        return next();
      }

      const simScore = [];
      items.map((a) => {
        // if (!delly) {
        //   if (
        //     validateIntent(
        //       question,
        //       (optionsIntent + " " + a.item_title.toLowerCase()).split(" "),
        //       0.95,
        //       true
        //     )
        //   ) {
        //     //SECTION OPTION INTENT
        //     delly = true;
        //     res.send({
        //       //score: simScore[maxIndex],
        //       ...a,
        //       // item_title: "DELLY",
        //       // item_description: "DELLY",
        //       trigger_type: "items",
        //       searched: items.length,
        //       question_length,
        //       answered: true,
        //     });
        //   }
        // }

        var sim = Math.max(
          similarity(question, a.item_title.split(" ")).score,
          similarity(question, a.item_description.split(" ")).score,

          // ...(validateIntent(a.item_title.split(" "), question, item_threshold)
          //   ? [
          similarity(question, (searchIntent + " " + a.item_title).split(" "))
            .score,
          similarity(question, (priceIntent + " " + a.item_title).split(" "))
            .score,
          similarity(question, (optionsIntent + " " + a.item_title).split(" "))
            .score
          //   ]
          // : [0])
        );

        if (sim >= question_length * item_threshold) {
          simScore.push(approximate(sim));
        } else {
          simScore.push(0);
        }
      });
      if (delly) {
        return;
      }
      const maxIndex = indexOfMax(simScore);
      if (!simScore.filter((a) => a).length) {
        //NOTE FALLBACK
        return next();
      }

      insertTransactionDB({
        owner: req.bot.user_id,
        answered: true,
        Q: req.question_full.join(" "),
        A: items[maxIndex].item_title,
        function: "item",
        bot_id,
        category: "item",
        country: req.body.country,
        is_voice_tr: req.body.is_voice_tr,
        continent: req.body.continent,
        channel: req.body.channel,
        session: req.body.session,

        city: req.body.city,
        ip: req.body.ip,
        score: simScore[maxIndex] / req.question.length,
      });

      res.send({
        score: simScore[maxIndex],
        ...items[maxIndex],
        trigger_type: "item",
        question: items[maxIndex].item_title,
        // generateSearchTerm(
        //   items[maxIndex].item_title.split(" "),
        //   question_full
        // ),
        incoming: generateSearchTerm(
          question_full,
          items[maxIndex].item_title.split(" ")
        ),
        searched: req.searched,
        question_length,
        answered: true,
      });
    });
};

const findTriggerSimilarity = (req, res, next) => {
  const trigger_threshold = req.global_threthold;
  const question = req.question;
  const question_length = question.length;
  const bot_id = req.body.bot_id;
  // Trigger.findAll({
  //   where: { bot_id },
  // }).then((triggers) => {

  sequelize
    .query("designer_triggers_get :botID", {
      replacements: {
        botID: bot_id,
      },
    })
    .then((data) => {
      const triggers = data[0];
      const triggers_length = triggers.length;
      req.searched.triggers = triggers_length;
      req.searched.total += triggers_length;
      if (!triggers_length) {
        return next();
      }
      const simScore = [];
      triggers.map((a) => {
        var sim = similarity(question, a.trigger.split(" ")).score;
        if (sim >= question_length * trigger_threshold) {
          simScore.push(approximate(sim));
        } else {
          simScore.push(0);
        }
      });
      const maxIndex = indexOfMax(simScore);
      if (!simScore.filter((a) => a).length) {
        //NOTE FALLBACK
        return next();
      }
      insertTransactionDB({
        owner: req.bot.user_id,
        answered: true,
        Q: req.question_full.join(" "),
        A: triggers[maxIndex].trigger,
        function: triggers[maxIndex].trigger_name
          ? triggers[maxIndex].trigger_name
          : "",
        bot_id,
        continent: req.body.continent,
        is_voice_tr: req.body.is_voice_tr,
        channel: req.body.channel,
        session: req.body.session,
        country: req.body.country,
        city: req.body.city,
        ip: req.body.ip,
        category: triggers[maxIndex].trigger_type,
        score: simScore[maxIndex] / req.question_full.length,
      });
      res.send({
        ...triggers[maxIndex],
        searched: req.searched,
        question_length,
        answered: true,
      });
    });
};

const findFAQContainerSimilarity = (req, res, next) => {
  const faq_threshold = 0.8;
  const question = req.body.question.split(" ");
  const question_length = question.length;
  const bot_id = req.body.bot_id;
  const fallback_message = req.bot.fallback_message;
  sequelize
    .query("designer_faq_contexts_get :botID", {
      replacements: {
        botID: bot_id,
      },
    })
    .then((data) => {
      const contexts = data[0];
      const simScore = [];
      contexts.map((a) => {
        var sim = similarity(question, a.context.split(" ")).score;
        if (sim >= faq_threshold * question_length) {
          simScore.push(sim);
        } else {
          simScore.push(0);
        }
      });
      const maxIndex = indexOfMax(simScore);
      if (!simScore.filter((a) => a).length) {
        return res.send({
          answer: fallback_message,
          searched: contexts.length,
          answered: false,
        });
      }

      res.send({
        ...contexts[maxIndex],
        searched: contexts.length,
        trigger_type: "faq",
        question_length,
        answered: true,
        ...similarity(question, contexts[maxIndex].context.split(" ")),
      });
    });
};

const findTriggerContainerSimilarity = (req, res, next) => {
  const trigger_threshold = 0.8;
  const question = req.body.question.split(" ");
  const question_length = question.length;
  const bot_id = req.body.bot_id;

  sequelize
    .query("designer_trigger_contexts_get :botID", {
      replacements: {
        botID: bot_id,
      },
    })
    .then((data) => {
      const triggers = data[0];
      const simScore = [];
      triggers.map((a) => {
        var sim = similarity(question, a.context.split(" ")).score;
        if (sim >= question_length * trigger_threshold) {
          simScore.push(sim);
        } else {
          simScore.push(0);
        }
      });
      const maxIndex = indexOfMax(simScore);
      if (!simScore.filter((a) => a).length) {
        //NOTE FALLBACK
        return next();
      }

      res.send({
        ...triggers[maxIndex],
        searched: triggers.length,
        question_length,
        answered: true,
      });
    });
};

const preProcessPerformit = (req, res, next) => {
  const global_threthold = 0.8;
  req.global_threthold = global_threthold;
  var bodyQuestion = toEnglishTokenize(req.body.question);
  var question = bodyQuestion.trim().toLowerCase().split(" ");
  question = question.filter((qw) => toNotinclude.indexOf(qw) === -1);
  req.question = question;
  req.question_full = bodyQuestion.trim().toLowerCase().split(" ");
  return next();
};

const preProcessQuestion = (req, res, next) => {
  const global_threthold = 0.8;
  //console.log("preproccessing started");
  req.global_threthold = global_threthold;
  req.conflict = false;
  req.item_set = false;
  req.offer_set = false;
  req.category_set = false;

  console.log("req.body.question", req.body.question);

  if (req.body.question.includes("__conflict")) {
    //NOTE ignore conflicts
    req.conflict = true;
  }
  if (req.body.question.includes("__item_set")) {
    //NOTE Show N items
    req.item_set = true;
  }
  if (req.body.question.includes("__category_set")) {
    //NOTE Show N categories
    req.category_set = true;
  }
  if (req.body.question.includes("__feature_set")) {
    //NOTE Show All items with feature requested
    req.feature_set = true;
  }
  if (req.body.question.includes("__cart_set")) {
    //NOTE Category pagination
    req.cart_set = true;
    if (req.body.question.includes("category_ids")) {
      const spliitedText = req.body.question
        .replace("__cart_set", "")
        .split("category_ids");
      // console.log(spliitedText);
      req.slice = +spliitedText[0];
      req.category_ids = spliitedText[1].split(",").map((a) => +a);
    } else {
      req.slice = +req.body.question.replace("__cart_set", "");
    }
  }
  if (req.body.question.includes("__offer_set")) {
    req.offer_set = true;
    req.offer_description = req.body.question.replace("__offer_set", "");
  }
  if (req.body.question.includes("__item_carousel")) {
    //NOTE Item pagination
    req.item_carousel = true;
    // "bot_id=54&item_title=Trinket{AND}__item_carousel2"
    req.slice = +req.body.question
      .replace("__item_carousel", "")
      .split("{SPLIT}")[1];
    const queryString = req.body.question
      .replace("__item_carousel", "")
      .split("{SPLIT}")[0];
    const queryArr = queryString.split("&").map((a) => a.split("="));
    queryArr.map((a) => {
      req.query[a[0]] = a[1];
    });
  }
  if (req.body.question.includes("__item_qna")) {
    req.item_qna = true;
    req.body.question = req.body.question.replace("__item_qna", "");
    req.item_id = +req.body.question.replace("__item_qna", "").split(" ")[0];
    req.body.question = req.body.question.replace(`${req.item_id}`, "");
  }
  if (req.body.question.includes("__item_spec")) {
    req.item_spec = true;
    req.item_id = +req.body.question.replace("__item_spec", "");
  }
  var bodyQuestion = toEnglishTokenize(req.body.question)
    .replace("__conflict", "")
    .replace("__item_set", "")
    .replace("__cart_set", "")
    .replace("__item_spec", "")
    .replace("__category_set", "")
    .replace("__item_carousel", "")
    .replace("__feature_set", "");

  if (req.item_set) {
    return res.send({
      bot_id: req.body.bot_id,
      trigger_type: "item",
      item_ids: bodyQuestion.split(","),
    });
  }
  if (req.offer_set) {
    return res.send({
      bot_id: req.body.bot_id,
      trigger_type: "offer",
      tag: {
        question: req.offer_description,
      },
      offer_description: req.offer_description,
    });
  }
  if (req.category_set) {
    return res.send({
      bot_id: req.body.bot_id,
      trigger_type: "item",
      category_ids: bodyQuestion.split(","),
    });
  }
  if (req.item_spec) {
    return res.send({
      bot_id: req.body.bot_id,
      item_id: req.item_id,
      trigger_type: "item_specs",
    });
  }
  if (req.cart_set) {
    return res.send({
      bot_id: req.body.bot_id,
      trigger_type: "cart_carousel",
      category_ids: req.category_ids,
      slice: req.slice + 1,
    });
  }
  if (req.feature_set) {
    return res.send({
      bot_id: req.body.bot_id,
      isConflict: false,
      trigger_type: "item",
      features: [
        {
          feature_id: bodyQuestion.split("${SPLIT}")[0],
          feature_value: bodyQuestion.split("${SPLIT}")[1],
        },
      ],
    });
  }
  if (req.item_carousel) {
    if (req.query.category_name) {
      return res.send({
        features: [],
        question: req.query.item_title,
        ...req.query,
        isConflict: false,
        slice: req.slice,
        bot_id: req.body.bot_id,
        trigger_type: "category",
      });
    } else {
      return res.send({
        question: req.query.item_title,
        ...req.query,
        isConflict: false,
        slice: req.slice,
        bot_id: req.body.bot_id,
        trigger_type: "item",
      });
    }
  }

  var question = bodyQuestion.trim().toLowerCase().split(" ");
  question = question.filter((qw) => toNotinclude.indexOf(qw) === -1);
  req.question = question;
  req.question_full = bodyQuestion.trim().toLowerCase().split(" ");
  var wh_question = req.question_full.filter(
    (qw) => WhQuestions.indexOf(qw) !== -1
  )[0];
  req.wh_question = wh_question;

  req.searched = {
    faqs: 0,
    triggers: 0,
    items: 0,
    categories: 0,
    total: 0,
  };
  const { operation, splicedQuestion } = checkOperationalIntent(req.question);
  req.question = splicedQuestion;
  req.operation = operation;
  // console.log("preproccessing ended");
  return next();
};

const checkLossyIntent = (req, res, next) => {
  const searchIntent = intents
    .find((a) => a.intent_type === "Products")
    .intent_props.split(" ");
  const priceIntent = intents
    .find((a) => a.intent_type === "Prices")
    .intent_props.split(" ");
  const optionsIntent = intents
    .find((a) => a.intent_type === "Options")
    .intent_props.split(" ");

  req.searchIntent = searchIntent;
  req.priceIntent = priceIntent;
  req.optionsIntent = optionsIntent;

  if (
    validateIntent(req.question, searchIntent, 0.8) ||
    validateIntent(req.question, priceIntent, 0.8) ||
    validateIntent(req.question, optionsIntent, 0.8)
  ) {
    const bot_id = req.body.bot_id;
    return res.send({
      bot_id,
      proc_question: req.question,
      full_question: req.question_full,
      searched: req.searched,
      trigger_type: "internals",
      answer: "what are you searching for",
      answered: false,
    });
  }
  // if (validateIntent(searchIntent, req.question, 0.9)){}
  return next();
};

const generateFallbackTagScore = (tag) => {
  return {
    literal: 0,
    score: 0,
    trigger_type: tag.trigger_type,
  };
};

const generateSimScoreEl = (tag, sim, tag_threshold) => {
  return {
    score: sim.score_pct,
    tag_match: {
      //NOTE FOR TagMatch Recording
      tag: tag.question,
      total_score: sim.score,
      score_pct: sim.score_pct,
      match_score: sim.matchScore.map((a) => approximate(a)).join(","),
      literal_score: checkLiteralScore(sim.literal, tag_threshold),
      tag_type: tag.trigger_type,
      respond: false,
      //NOTE remaining updateRespond, question & bot_id
    },
    literal: checkLiteralScore(sim.literal, tag_threshold),
    trigger_type: tag.trigger_type,
  };
};

const checkLiteralScore = (literal, threshold) => {
  return literal >= threshold ? literal : 0;
};

const findTaggedSimilarity = (req, res, next) => {
  const bot_id = req.body.bot_id;
  const body_info = {
    continent: req.body.continent,
    country: req.body.country,
    city: req.body.city,
    ip: req.body.ip,
    channel: req.body.channel,
    session: req.body.session,
    is_voice_tr: req.body.is_voice_tr,
  };
  const tag_threshold = req.global_threthold - 0.01;

  const question = req.question;
  const question_length = req.question.length;

  const question_full = req.question_full;
  const question_full_length = req.question_full.length;
  var end_item_qna = true;

  // const searchIntent = intents.find(
  //   (a) => a.intent_type === "Products"
  // ).intent_props;
  // const priceIntent = intents.find(
  //   (a) => a.intent_type === "Prices"
  // ).intent_props;
  // const optionsIntent = intents.find(
  //   (a) => a.intent_type === "Options"
  // ).intent_props;

  sequelize
    .query("designer_get_all_tags :botID", {
      replacements: {
        botID: bot_id,
      },
    })
    .then((data) => {
      const tags = data[0];
      const tags_legnth = tags.length;
      if (!tags_legnth) {
        return next();
      }
      const simScoreArr = [];
      const featureScoreArr = [];
      tags.map((tag) => {
        tag.question = toEnglishTokenize(tag.question.trim().toLowerCase());
        if (
          tag.trigger_type === "item" ||
          tag.trigger_type === "category" ||
          tag.trigger_type === "offer"
        ) {
          var sim = similarity(question_full, tag.question.split(" "));
          // var simScore = approximate(sim.score / question_full_length);
          if (
            sim.score_pct >= tag_threshold ||
            checkWordThreshold(sim.matchScore, tag_threshold) ||
            checkIncludes(sim.matched, question_full, tag.question.split(" "))
          ) {
            simScoreArr.push(generateSimScoreEl(tag, sim, tag_threshold));
          } else {
            simScoreArr.push(generateFallbackTagScore(tag));
          }
        } else if (
          tag.trigger_type === "card" ||
          tag.trigger_type === "suggestion" ||
          tag.trigger_type === "dialog" ||
          tag.trigger_type === "whatsapp_buttons" ||
          tag.trigger_type === "whatsapp_list" ||
          tag.trigger_type === "whatsapp_template"
        ) {
          var sim = similarity(question_full, tag.question.split(" "));
          // var simScore = approximate(sim.score / question_full_length);
          if (
            sim.score_pct >= tag_threshold ||
            checkWordThreshold(sim.matchScore, tag_threshold) ||
            checkIncludes(sim.matched, question_full, tag.question.split(" "))
          ) {
            simScoreArr.push(generateSimScoreEl(tag, sim, tag_threshold));
          } else {
            simScoreArr.push(generateFallbackTagScore(tag));
          }
        } else if (tag.trigger_type === "qna") {
          var sim = similarity(question_full, tag.question.split(" "));
          // var simScore = approximate(sim.score) / question_full_length;
          if (
            sim.score_pct >= tag_threshold ||
            (checkWordThreshold(sim.matchScore, tag_threshold) &&
              checkWhMatch(tag.question.split(" "), req.wh_question))
          ) {
            simScoreArr.push(generateSimScoreEl(tag, sim, tag_threshold));
          } else {
            simScoreArr.push(generateFallbackTagScore(tag));
          }
        } else if (tag.trigger_type === "feature") {
          if (req.item_qna && tag.item_id === req.item_id) {
            var sim = similarity(question_full, tag.question.split(" "));
            var simScore = approximate(sim.score / question_full_length);
            if (
              checkWordThreshold(sim.matchScore, tag_threshold) ||
              checkIncludes(sim.matched, question_full, tag.question.split(" "))
            ) {
              end_item_qna = false;
              simScoreArr.push({
                ...tag,
                score: approximate(simScore) * 100,
                trigger_type: "qna",
                question: question_full.join(" "),
                answered: true,
                answer: tag.feature_value,
                bot_id: tag.bot_id,
              });
              tag.answer = tag.feature_value;
              tag.trigger_type = "qna";
              return tag;
            } else {
              simScoreArr.push(generateFallbackTagScore(tag));
            }
          } else {
            //NOTE Here is where typical feature search will be
            const featureText =
              tag.question.split(" "); /*+ " " + tag.feature_value)*/

            var sim = similarity(question_full, featureText);
            simScoreArr.push(generateFallbackTagScore(tag));
            var featureScore = approximate(sim.score / featureText.length);
            if (
              featureScore >= tag_threshold
              // || checkIncludes(sim.matched, question_full, featureText)
            ) {
              const existingFeatureIndex = featureScoreArr.findIndex(
                (a) => a.feature_name === tag.question
              );
              if (existingFeatureIndex !== -1) {
                if (
                  featureScoreArr[existingFeatureIndex].score < featureScore
                ) {
                  featureScoreArr[existingFeatureIndex].score = featureScore;
                  featureScoreArr[existingFeatureIndex].feature_value =
                    tag.feature_value;
                }
              } else {
                featureScoreArr.push({
                  score: approximate(featureScore),
                  feature_id: tag.feature_id,
                  item_id: tag.item_id,
                  feature_name: tag.question,
                  feature_value: tag.feature_value,
                  feature_type: tag.feature_type,
                });
              }
            }
          }
        }
      });
      const { maxIndex, adaArr } = getAdaptiveMax(simScoreArr);
      const { isConflict, conflictedTags } = checkConflicts(
        adaArr,
        tags,
        req.conflict
      );

      if (!simScoreArr.filter((a) => a.score).length || !tags[maxIndex]) {
        //NOTE FALLBACK
        return next();
      }
      if (tags[maxIndex].trigger_type !== "feature") {
        //FIXME
        var tgmth = { ...simScoreArr[maxIndex].tag_match };
        tgmth.respond = true;
        simScoreArr[maxIndex].tag_match = tgmth;
        // console.log("insertTagMatches start");
        insertTagMatches(simScoreArr, question_full, bot_id, maxIndex);
        // console.log("insertTagMatches end");

        const transaction = generateTransactionObj(
          body_info,
          req,
          tags[maxIndex],
          simScoreArr[maxIndex]
        );
        insertTransactionDB(transaction);
      }
      res.send({
        end_item_qna,
        features: featureScoreArr,
        wh_question: req.wh_question,
        score: simScoreArr[maxIndex].score,
        literal: simScoreArr[maxIndex].literal,
        operation: req.operation,
        isConflict,
        conflictedTags,
        tag: tags[maxIndex],
        ...tags[maxIndex],
        question_length,
        question_proc: req.question,
        question_full: req.question_full,
        answered: true,
        search_terms: question.length
          ? generateSearchTermArray(
              question,
              tags[maxIndex].question.split(" "),
              req.global_threthold
            )
          : [],
        question: question.length
          ? generateSearchTerm(tags[maxIndex].question.split(" "), question)
          : "",
        incoming: question.length
          ? generateSearchTerm(question, tags[maxIndex].question.split(" "))
          : "",
      });
    });
};

const findTaggedPool = async (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  const data = await sequelize.query("designer_get_all_tags :botID", {
    replacements: {
      botID: bot_id,
    },
  });
  return res.send(data[0]);
};

const findTaggedPoolCached = async (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  // const data = await cachedDB.get(`pool_${bot_id}`);
  return res.send(JSON.parse(data));
};

const insertTagMatches = (simScoreArr, question, bot_id, maxIndex) => {
  const isoDate = new Date().toISOString();
  const question_id = `${question.join("-")}_${bot_id}_${isoDate}`;
  // console.log("question_id", question_id);
  const match_tags = simScoreArr
    .map((a, mthindex) => {
      return {
        ...a.tag_match,
        respond: mthindex === maxIndex ? true : false,
        question: question.join(" "),
        bot_id,
        question_id,
      };
    })
    .filter((a) => a.total_score);
  // console.log("match_tags", match_tags);
  match_tags.map((mthtg) => {
    TagMatch.create(mthtg)
      .then((a) => {
        // console.log("created");
      })
      .catch((e) => {
        // console.log("nah");
      });
  });
};

const checkWordThreshold = (arr, threshold) =>
  arr.filter((a) => a >= threshold).length;

const checkWhMatch = (tag, whQuestion) => {
  //NOTE tag: FAQ question
  //NOTE whQuestion: Wh from user question
  if (!whQuestion) {
    //SECTION No WH Found in user question
    return true;
  } else if (tag.indexOf(whQuestion) === -1) {
    //SECTION WH Found in user question but not found in tag
    var otherWhMatch = true;
    tag.map((tg) => {
      if (WhQuestions.indexOf(tg) !== -1) {
        otherWhMatch = false;
      }
    });
    return otherWhMatch;
  } else {
    //SECTION WH Found in user question and found in tag
    return true;
  }
};

const checkIncludes = (matchedArray, q, tag_name) => {
  var isThereIncluding = false;
  matchedArray.map((m, i) => {
    if (m !== -1) {
      tag_name.map((tg) => {
        if (Boolean(tg) && q[i]) {
          //NOTE to remove white spaces from the item
          if (tg.includes(q[i]) || q[i].includes(tg)) {
            isThereIncluding = true;
          }
        }
      });
    }
  });
  return isThereIncluding;
};

const generateTips = async (req, res, next) => {
  const bot_id = req.body.bot_id;
  const question = req.body.question;
  const type = req.body.type;
  const results = await fetch(serverUrl.concat("/api/faq/similarity"), {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      question,
      bot_id,
    }),
  }).then((res) => res.json());
  const tips = {
    warnings: [],
    conflict: false,
  };
  switch (results.trigger_type) {
    case "item":
      tips.warnings.push(
        "This is linking into the " + results.tag.item_title + " product"
      );
      tips.warnings.push(...generateErros(question, type, "product"));
      break;
    case "offer":
      tips.warnings.push("This is linking into the " + results.tag.question);
      tips.warnings.push(...generateErros(question, type, "offer"));
      break;
    case "category":
      tips.warnings.push(
        "This is linking into the " + results.category_name ||
          results.tag.category_name + " product"
      );
      tips.warnings.push(...generateErros(question, type, "category"));
      break;
    case "card":
    case "suggestion":
    case "whatsapp_list":
    case "whatsapp_buttons":
    case "dialog":
      tips.warnings.push(
        `The question ${question} already links to "${results.tag.question}" (${results.trigger_type}) trigger `
      );
      tips.conflict = true;
      break;
    case "qna":
      tips.warnings.push(
        `The question (${question}) already links to (${results.tag.question}) in the qna`
      );
      tips.conflict = true;
      break;
  }
  return res.send({ ...tips, tag: results.tag });
};

const generateErros = (q, type, tag) => {
  const warnings = [];
  const question = q.split(" ");
  if (question.length === 1) {
    warnings.push(
      `This keyword is linked directly into the (${tag}) this question maybe masked out`
    );
    warnings.push("Please add more context to it to differentiate it");
    if (type === "qna") {
      warnings.push(`this text will be masked out duo to the (${tag})`);
    }
  }
  return warnings;
};

const getTags = (req, res) => {
  sequelize.query("designer_get_searchat", {}).then((data) => {
    res.send(data[0][0]);
  });
};

const getAllBotsTags = (req, res) => {
  // const tag_threshold = req.global_threthold - 0.01;

  // const question = req.question;
  // req.question = question;
  // req.question_full = question;
  // const question_length = req.question.length;

  // const question_full = req.question_full;
  // const question_full_length = req.question_full.length;

  sequelize
    .query("designer_get_searchat_contains :question", {
      replacements: {
        question: `${req.question
          .map((a) => "FORMSOF(INFLECTIONAL," + "*" + a + "*" + ")")
          .join(" OR ")}`,
      },
    })
    .then((data) => {
      //console.log("COUNTRY CLUB");
      var tags = data[0];

      if (Object.keys(req.operation).length > 0) {
        tags = genOperations(req.operation, tags);
      }
      return res.send({
        operation: req.operation,
        ...groupCompanies(tags),
      });
    });
};

function compare(a, b) {
  if (a.score > b.score) {
    return -1;
  }
  if (a.score < b.score) {
    return 1;
  }
  return 0;
}
const groupCompanies = (items_old) => {
  const companies = [];
  const locationMock = [
    {
      distance: "12km",
      duration: "6 minutes",
    },
    {
      distance: "10km",
      duration: "9 minutes",
    },
    {
      distance: "15km",
      duration: "13 minutes",
    },
  ];
  const indexes = [];
  const items = items_old.map((item, i) => {
    if (companies.findIndex((a) => a.bot_id === item.bot_id) === -1) {
      companies.push({
        bot_id: item.bot_id,
        file_name: item.file_name,
        company_name: item.company_name,
        bot_icon: item.bot_icon,
        city: item.city,
        language: item.language,
        items_num: items_old.filter((a) => a.bot_id === item.bot_id).length,
        ...locationMock[indexes.length],
      });
      indexes.push("ghali");
    }
    return {
      ...item,
      item_icon: item.item_icons.split(",")[0],
      // bot_id: undefined,
      // file_name: undefined,
      // company_name: undefined,
      // bot_icon: undefined,
      // city: undefined,
      // language: undefined,
      bot_id: item.bot_id,
    };
  });
  return { companies, items };
};

const genOperations = (opts, data) => {
  if (opts.type) {
    if (opts.type === "below") {
      return data.filter((a) => a.item_price <= opts.value);
    } else if (opts.type === "between") {
      const price_from = Math.min(...opts.values);
      const price_to = Math.max(...opts.values);
      return data.filter(
        (a) => a.item_price >= price_from && a.item_price <= price_to
      );
    } else if (opts.type === "above") {
      return data.filter((a) => a.item_price >= opts.value);
    } else {
      return "";
    }
  } else {
    return "";
  }
};

const getPotentialMatches = (req, res, next) => {
  const bot_id = req.body.bot_id;

  const faq_id = req.body.faq_id;
  const trigger_id = req.body.trigger_id;

  const question_full = req.question_full;
  const question_full_length = req.question_full.length;

  const threshold = req.global_threthold;

  sequelize
    .query("designer_get_all_tags :botID", {
      replacements: {
        botID: bot_id,
      },
    })
    .then((data) => {
      const tags = data[0].filter(
        (a) => a.trigger_id !== trigger_id && a.faq_id !== faq_id
      );
      const questionTagNER = [];
      const tags_legnth = tags.length;
      if (!tags_legnth) {
        return next();
      }
      const simScoreArr = [];
      const featureScoreArr = [];
      question_full.map((a) => {
        questionTagNER.push([]);
      });
      tags.map((tag) => {
        tag.question = toEnglishTokenize(tag.question.trim().toLowerCase());
        var sim = similarity(question_full, tag.question.split(" "));
        var simScore = approximate(sim.score / question_full_length);

        if (
          checkIncludes(sim.matched, question_full, tag.question.split(" "))
        ) {
          sim.matchScore.map((scor, matchIndex) => {
            if (scor >= threshold) {
              var wordIndex = sim.matched[matchIndex];
              questionTagNER[matchIndex].push({
                question: tag.question,
                score: approximate(simScore),
                trigger_type: tag.trigger_type,
                faq_id: tag.faq_id,
                trigger_id: tag.trigger_id,
                item_id: tag.item_id,
                category_id: tag.category_id,
              });
            }
          });
          // simScoreArr.push({
          //   matched_word: "",
          //   question: tag.question,
          //   score: approximate(simScore),
          //   trigger_type: tag.trigger_type,
          // });
        }
      });
      res.send({ simScoreArr, questionTagNER });
    });
};

module.exports = {
  findFAQSimilarity,
  findTriggerSimilarity,
  findCaterogiesSimilarity,
  getAnswer,
  preProcessText,
  findSimilarity,
  fallbackMessage,
  lookupQuestion,
  fuzzyQuestion,
  findFAQContainerSimilarity,
  findTriggerContainerSimilarity,
  findItemsSimilarity,
  preProcessQuestion,
  checkLossyIntent,
  findTaggedSimilarity,
  generateTips,
  getAllBotsTags,
  toEnglishTokenize,
  getTags,
  preProcessPerformit,
  getPotentialMatches,
  findTaggedPool,
  findTaggedPoolCached,
};
