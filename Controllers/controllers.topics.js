const Dialogs = require("../Models/Dialogs");
const FAQ = require("../Models/FAQ");
const Topic = require("../Models/Topic");
const Trigger = require("../Models/Trigger");

const get = (req, res) => {
    return res.send(req.topic);
}

const set = (req, res, next) => {
    const { display_name, bot_id } = req.body;
    let keywords = JSON.stringify(req.body.keywords);
    let lemmatized_keywords = JSON.stringify(req.body.lemmatized_keywords);
    Topic.create({ display_name, keywords, bot_id, lemmatized_keywords }).then((topic) => {
        req.topic = topic;
        return next();
    });
};

const update = (req, res, next) => {
    const data = {...req.body, lemmatized_keywords: req.body.lemmatized_keywords};

    req.topic.updateInfo(data);
    req.topic.save().then((topic) => {
        req.topic = topic;
        return next();
    });
}

const getMany = (req, res) => {
    return res.send(req.topics);
}

const purge = async (req, res, next) => {
    const data = { ...req.body };

    const [hasFAQs, hasTriggers, hasDialogs] = await Promise.all([
        FAQ.findOne({ where: { topic_id: data.topic_id } }),
        Trigger.findOne({ where: { topic_id: data.topic_id } }),
        Dialogs.findOne({ where: { topic_id: data.topic_id } }),
    ]);

    if (hasFAQs || hasTriggers || hasDialogs) {
        return res.status(401).send({ message: "topic has dependencies" });
    }

    Topic
        .destroy({
            where: {
                topic_id: data.topic_id,
            },
        })
        .then(() => {
            return res.send({ message: "topic deleted successfully" });
        });
}


const getTopicKB = (req, res) => {
    const dataToSend = {
        topic: req.topic,
        faqs: req.kb.faqs,
        triggers: req.kb.triggers,
        dialogs: req.kb.dialogs,
    };
    return res.send(dataToSend);
}

module.exports = {
    get,
    set,
    update,
    getMany,
    purge,
    getTopicKB
}