const Offer = require("../Models/Offer");

const get = (req, res) => {
  res.send(req.offer);
};

const getAll = (req, res, next) => {
  res.send(req.offers);
};

const set = async (req, res, next) => {
  const data = { ...req.body };
  Offer.create(data)
    .then(async (offer) => {
      req.offer = offer;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const setAll = async (req, res, next) => {
  Offer.bulkCreate(req.body)
    .then((offers) => {
      req.offers = offers;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const update = async (req, res, next) => {
  const offer = req.offer;
  const data = { ...req.body };
  offer.updateInfo(data);
  req.offer = offer;
  await offer.save();
  return next();
};

const purge = async (req, res, next) => {
  const data = { ...req.body };
  const offer = await Offer.findOne({
    where: {
      offer_id: data.offer_id,
    },
  });
  const bot_id = offer.bot_id; 
  Offer.destroy({
    where: {
      offer_id: data.offer_id,
    },
  }).then(() => {
    req.bot_id = bot_id;
    return next();
    // res.send({ message: "Item Offer deleted successfully" });
  });
};
// const purgeAll = (req, res, next) => {
//   Offer.destroy({
//     where: {
//       bot_id: req.body.bot_id,
//     },
//   }).then(() => {
//     return next();
//   });
// };

module.exports = {
  get,
  set,
  update,
  purge,
  getAll,
  setAll,
  // purgeAll,
};
