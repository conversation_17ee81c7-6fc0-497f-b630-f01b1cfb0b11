const UserFollow = require("../Models/User.Follow");

const set = (req, res, next) => {
  const data = { ...req.body };
  UserFollow.create({
    bot_id: data.bot_id,
    user_id: data.user_id,
  }).then((userfollow) => {
    req.userfollow = userfollow;
    return next();
  });
};

const get = (req, res, next) => {
  res.send(req.userfollow);
};

const getAll = (req, res, next) => {
  res.send(req.userfollows);
};

const purge = (req, res, next) => {
  const data = { ...req.body };
  UserFollow.destroy({
    where: {
      follow_id: data.follow_id,
    },
  }).then(() => {
    res.send({ message: "Follow deleted successfully" });
  });
};

module.exports = {
  set,
  get,
  getAll,
  purge,
};
