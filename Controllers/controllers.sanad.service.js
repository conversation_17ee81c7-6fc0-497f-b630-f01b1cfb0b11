const SanadService = require("../Models/Sanad.Service");
const sequelize = require("../db");
const { lemmatization, distinct } = require("../helper/helper");


const get = (req, res) => {
  res.send(req.sanadService);
};

const getAll = (req, res, next) => {
  res.send(req.sanadServices);
};

const set = async (req, res, next) => {
  const data = { ...req.body };
  // const lemmatizationData = await lemmatization(data.title);
  SanadService.create({
    ... data,
    // lemmatized_title:  lemmatizationData.data.text
  })
    .then((sanadService) => {
      req.sanadService = sanadService;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const setAll = (req, res, next) => {
    console.log("req.body", req.body)
  SanadService.bulkCreate(req.body)
    .then((sanadServices) => {
      req.sanadServices = sanadServices;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const update = async (req, res, next) => {
  const sanadService = req.sanadService;
  const data = { ...req.body };
  sanadService.updateInfo(data);
  req.sanadService = sanadService;
  await sanadService.save();
  return next();
};

const purge = (req, res, next) => {
  const data = { ...req.body };
  SanadService.destroy({
    where: {
      sanad_service_id: data.sanad_service_id,
    },
  }).then(() => {
    res.send({ message: "sanad service deleted successfully" });
  });
};
const getNotApprovedServices = async (req, res) => {
  const bot_id= req.query.bot_id;
 const data= await sequelize.query("designer_not_approved_sanad_service :bot_id ", {
      replacements: {
        bot_id: bot_id,
      },
    });
    return res.send(data[0]);
  };


const getApprovedServices = async (req, res) => {
  const bot_id= req.query.bot_id;
 const data= await sequelize.query("designer_approved_sanad_service :bot_id ", {
      replacements: {
        bot_id: bot_id,
      },
    });
    return res.send(data[0]);
  };

const purgeMany = (req, res, next) => {
  const services_ids = [...req.body].map((a) => a.sanad_service_id);
  const bot_ids = [...req.body].map((a) => a.bot_id);

  SanadService.destroy({
    where: {
      bot_id: bot_ids,
      sanad_service_id: services_ids,
    },
  }).then(() => {
    return next();
  });
};
const updateMany = async (req, res, next) => {
  const sanadServices = req.sanadServices;
  const data = [...req.body];
  // faqs.map( (faq) => {
  for (var i = 0; i < sanadServices.length; i++) {
    var sanadService = sanadServices[i];
    var faqData = data.find((a) => a.sanad_service_id === sanadService.sanad_service_id);
    sanadService.updateInfo(faqData);
    await sanadService.save();
  }
  //});
  return next();
};


const getBroadcastingDetails = async (req, res) => {
  Promise.all([ 
    sequelize.query("get_sanad_conversations"),
    sequelize.query("get_sanad_consumed_services")
  ]).then((data) => {
    return res.send({
      conversations: data[0][0],
      consumed_services: data[1][0]
    });
  });

}

const getNotReviewedList = async (req,res) => {
  const data = await sequelize.query("bot_designer_get_not_reviewed_list");
  const list = [];
  const services = distinct(data[0],"sanad_service_id");
  console.log("data",data[0])
  for(var i =0 ; i < services.length; i++){
    const service = services[i];
    const bot = {
      bot_id: service.bot_id,
      bot_name: service.bot_name
    } 
    const faqs = data[0].filter(a => a.sanad_service_id === service.sanad_service_id).map(a => {
      return {
        sanad_faq_id: a.faq_id,
        question: a.question,
        answer: a.answer,
        sanad_approved: a.faq_sanad_approved,
        entity_approved: a.faq_entity_approved,
      }
    });

    list.push({
      bot,
      faqs,
      service: {
        sanad_service_id: service.sanad_service_id,
        title: service.service_title,
        sanad_approved: service.service_sanad_approved,
        entity_approved: service.service_entity_approved
      }
    })

  }
  return res.send(list);
}


const getSanadKb = async (req,res) => {
  const data = await sequelize.query("bot_designer_get_sanad_kb");
  const list = [];
  const services = distinct(data[0],"sanad_service_id");
  // console.log("data",data[0])
  for(var i =0 ; i < services.length; i++){
    const service = services[i];
    const bot = {
      bot_id: service.bot_id,
      bot_name: service.bot_name
    } 
    const faqs = data[0].filter(a => a.sanad_service_id === service.sanad_service_id).map(a => {
      return {
        sanad_faq_id: a.faq_id,
        question: a.question,
        answer: a.answer,
        sanad_approved: a.faq_sanad_approved,
        entity_approved: a.faq_entity_approved,
        createdAt: a.faq_createdAt,
        reviewed: a.faq_reviewed
      }
    });

    list.push({
      bot,
      faqs,
      service: {
        sanad_service_id: service.sanad_service_id,
        title: service.service_title,
        sanad_approved: service.service_sanad_approved,
        entity_approved: service.service_entity_approved,
        createdAt: service.service_createdAt,
        reviewed: service.service_reviewed
      }
    })

  }
  return res.send(list);
}

module.exports = {
  get,
  set,
  update,
  purge,
  getAll,
  setAll,
  updateMany,
  purgeMany,
  getNotApprovedServices,
  getApprovedServices,
  getBroadcastingDetails,
  getNotReviewedList,
  getSanadKb
};
