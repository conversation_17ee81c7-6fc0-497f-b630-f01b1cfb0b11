const GenesysIntegration = require("../Models/GenesysIntegration");
const GenesysIntegrationSession = require("../Models/GenesysIntegrationSession");
const GenesysIntegrationSessionTransaction = require("../Models/GenesysIntegrationTransaction");

const get = (req, res) => {
  res.send(req.genesysIntegration);
};

const getAll = (req, res) => {
  res.send(req.genesysIntegrations);
};

const getOneSession = (req, res) => {
  res.send(req.genesysIntegrationSession);
}

const getOneSessionTr = (req, res) => {
res.send(req.genesysIntegrationSessionTransaction);
}

const getAllSessions = (req, res) => {
  res.send(req.genesysIntegrationSessions);
}

const set = (req, res, next) => {
  const data = { ...req.body };
  GenesysIntegration.create(data)
    .then((genesysIntegration) => {
      req.genesysIntegration = genesysIntegration;
      return next();
    })
    .catch(() => {
      res.status(409).send({ message: "Genesys integration is already exists" });
    });
};


const setSession = (req, res, next) => {
  const data = { ...req.body };
  GenesysIntegrationSession.create(data)
    .then((genesysIntegrationSession) => {
      req.genesysIntegrationSession = genesysIntegrationSession;
      return next();
    })
    .catch(() => {
      res.status(409).send({ message: "livechat integration session is already exists" });
    });
}; 

const setSessionTr = (req, res, next) => {
const data = { ...req.body };
GenesysIntegrationSessionTransaction.create(data)
  .then((genesysIntegrationSessionTransaction) => {
    req.genesysIntegrationSessionTransaction = genesysIntegrationSessionTransaction;
    return next();
  })
  .catch(() => {
    res.status(409).send({ message: "livechat integration session transaction is already exists" });
  });
}; 

const purge = (req, res, next) => {
  const bot_id = req.body.bot_id;
  GenesysIntegration.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    res.send({ message: "genesys integration data deleted" });
  });
};

const update = async (req, res, next) => {
  const GenesysIntegration = req.genesysIntegration;
  if (req.body && GenesysIntegration) {
    GenesysIntegration.updateInfo({ ...req.body });
    await GenesysIntegration.save();
    return next();
  } else {
    return next();
  }
};

module.exports = { 
    get, 
    set, 
    update, 
    purge,
    getAll,
    getOneSession,
    getOneSessionTr,
    getAllSessions,
    setSession,
    setSessionTr,
};
