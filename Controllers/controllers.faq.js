const FAQ = require("../Models/FAQ");
const FAQ_Logs = require("../Models/Faq.Logs");
const sequelize = require("../db");
const { toEnglishTokenize } = require("./controllers.engine");
var JSSoup = require('jssoup').default;
// const request = require('request');
const htmlTags = require("../html_tags.json")
const fs = require("fs");
const {lemmtaizationLocal } = require("../helper/helper");
const PDFExtract = require('pdf.js-extract').PDFExtract;


const distinct = (arr, by) => arr.reduce((acc, current) => {
  const x = acc.find(item => item[by] === current[by]);
  if (!x) {
    return acc.concat([current]);
  } else {
    return acc;
  }
}, []);


const get = (req, res) => {
  res.send(req.faq);
};
const getAll = (req, res) => {
  res.send(req.faqs);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  const type = "add_faq";
  sequelize
    .query("designer_faq_set :botID, :Q,:A,:KeyNum, :topic_id", {
      replacements: {
        botID: data.bot_id,
        Q: toEnglishTokenize(data.question),
        hidden: data.hidden || false,
        A: data.answer,
        KeyNum: data.question.split(" ").length,
        topic_id: data.topic_id || null,
      },
    })
    .then((data) => {
      const faq = data[0][0];
      req.faq = faq;
      req.type= type;
      return next();
    });
};

const createLogs = (req, res, next) => {
  const type = req.type;
  const faq = req.faq;
  const data = req.body;
  FAQ_Logs.create({
    faq_id: (type !== "update_faq" && type !== "add_faq") ? data.faq_id : faq.faq_id,
    bot_id:data.bot_id,
    faq_type:(type !== "update_faq" && type !== "add_faq") ? "delete_faq" : type,
    user_id:data.user_id
  })
  return next()
};
const lemmtaizationMany = async (req, res, next) => {
  try {
    const data = [...req.body];
    const newData = await Promise.all(
      data.map(async (ent) => {
        return {
          ...ent,
          lemmatized_question: ent?.lemmatized_question
            ? (await lemmtaizationLocal(ent?.lemmatized_question))?.data?.answer ||
              ent.question
            : "",
        };
      })
    );
    req.body = newData;
    next();
  } catch (error) {
console.log(error)
next()
  }
};
const setMany = (req, res, next) => {
  const data = [...req.body];
  FAQ.bulkCreate(
    data.map((ent) => {
      return {
        ...ent,
        question: toEnglishTokenize(ent.question),
      };
    })
  ).then((faqs) => {
    req.faqs = faqs;
    return next();
    // res.send(faqs);
  });
};

const purge = (req, res, next) => {
  const data = { ...req.body };
  FAQ.destroy({
    where: {
      faq_id: data.faq_id,
    },
  }).then(() => {
    return next();
    // return res.send({ message: "faq deleted successfully" });
  });
};



const update = async (req, res, next) => {
  const faq = req.faq;
  const type = "update_faq";
  const data = { ...req.body };
  faq.updateInfo(data);
  req.faq = faq;
  req.type=type;
  await faq.save();
  return next();
};


const createLogsForDELETE = (req, res, next) => {
  const data = req.body;
  FAQ_Logs.create({
    faq_id:data.faq_id,
    bot_id:data.bot_id,
    faq_type:"delete_faq",
    user_id:data.user_id
  })
  .then(() => {
    return next();
  })
};


const updateMany = async (req, res, next) => {
  const faqs = req.faqs;
  const data = [...req.body];
  // faqs.map( (faq) => {
  for (var i = 0; i < faqs.length; i++) {
    var faq = faqs[i];
    var faqData = data.find((a) => a.faq_id === faq.faq_id);
    faq.updateInfo(faqData);
    await faq.save();
  }
  //});
  return next();
};

const purgeAllQna = (req, res, next) => {
  const bot_id = req.body.bot_id;
  FAQ.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    return next();
    // return res.send({ message: "many where deleted" });
  });
};

const purgeMany = (req, res, next) => {
  const faq_ids = [...req.body].map((a) => a.faq_id);
  const bot_ids = [...req.body].map((a) => a.bot_id);

  FAQ.destroy({
    where: {
      bot_id: bot_ids,
      faq_id: faq_ids,
    },
  }).then(() => {
    return next();
  });
};

const purgeManyV2 = (req, res, next) => {
  const faq_ids = req.body.faqs;
  const bot_id = req.body.bot_id

  FAQ.destroy({
    where: {
      bot_id: bot_id,
      faq_id: faq_ids,
    },
  }).then(() => {
    return next();
  });
};

const convertFAQsRecursively = (req, res, next) => {
  const faqs = req.faqs;
  const children = faqs
    .filter((a) => a.parent_id)
    .map((a) => {
      var subchildren = faqs.filter((b) => b.parent_id === a.faq_id);
      if (subchildren.length) {
        return {
          ...a.toJSON(),
          children: subchildren,
        };
      } else {
        return undefined;
      }
    })
    .filter((a) => a);
  const roots = faqs
    .filter((a) => !a.parent_id)
    .map((a) => {
      var child = children.filter((ch) => ch.parent_id === a.faq_id);
      return { ...a.toJSON(), children: child };
    });
  res.send(roots);
  //return next();
};



const jsKeyWords = [
  '.click',
  ".show",
  ".find",
  ".trim","$(","http"
]
// const faqsScrapper = (req,res,next) => {
//   const bot_id = req.body.bot_id;
//   const page_url = req.body.page_url;

//   request(page_url, function (
//     error,
//     response,
//     body
//   ) {  
//       var soup = new JSSoup(body);
//       var faqs =[];
//       var content = [];
//       for( var i =0 ; i< htmlTags.length ;i++){
//         const htmlTag = htmlTags[i];
//         soup.findAll(htmlTag).map(a => {
//           content.push(a);
//         })
//       }
//      var content = content.filter(
//        a => 
//         Boolean(a.text) && 
//         (a.text.includes("?")||a.text.includes("؟")) && 
//         a.text.trim().length < 200
//       );
  
//     content.forEach(a => {
//       var question = a.text;
//       var nextEl = a.nextElement;
//       while(!Boolean(nextEl.text)  || nextEl.text?.length < 10){
//         nextEl = nextEl.nextElement; 
//       }
//       faqs.push({
//         question: question ,
//         answer: nextEl.text.replace(/&#?[a-z0-9]+;/i,"")
//       });
//     })
//     faqs = faqs.filter(a => {
//       var isJs = false;
//       jsKeyWords.forEach(b => {
//         if(a.answer.includes(b)){
//           isJs = true;
//         }
//       })
//         return !isJs;
//       }
//      )
//      faqs = faqs.filter(a => a.question !== a.answer).filter(a => !a.answer?.includes("?") && !a.answer?.includes("؟"))
//      faqs = distinct(faqs, "question")

  
//       req.body = faqs.map(a => {
//         return {
//           bot_id,
//           answer: a.answer?.substring(0, 999),
//           question: toEnglishTokenize(a.question?.substring(0, 999))
//         }
//       })
//       return next();
//   })
// }


const extractFaqsPdf = (req,res, next) => {

  const bot_id = req.query.bot_id;


  const pdfExtract = new PDFExtract();
  const options = {}; /* see below */

  pdfExtract.extract('blob/test.pdf', options, (err, data) => {
    if (err) return console.log(err);
    const faqs = [];
    const questions = [];
    const contents = [];
    data.pages.map(page => {
  
      
      const qs = page.content.filter(a => a.str?.includes("?") || a.str?.includes("؟"));
      qs.forEach(a => {
        questions.push(a.str)
      });
      contents.push(
        ...page.content
      );
  
    })
  
    questions.forEach(question => {
      const idx = contents.findIndex(a => a.str === question);
      var isScrapped = false;
      if(idx !== -1){
        var currentIdx = idx;
        var answer = "";
        while(!isScrapped && currentIdx !== (contents.length -1)){
          currentIdx += 1;
          if(contents[currentIdx]?.str?.includes("?") || contents[currentIdx]?.str?.includes("؟")){
            isScrapped = true;
          }
          else{
            answer = answer.concat(` ${contents[currentIdx]?.str}`)
          }
        }
        faqs.push({
          bot_id,
          answer: answer?.substring(0, 999),
          question: toEnglishTokenize(question?.substring(0, 999))
        });
      }
     
    })
    

    req.body = faqs;
    return next();
  });
  
}

module.exports = {
  get,
  getAll,
  set,
  purge,
  update,
  setMany,
  purgeAllQna,
  purgeMany,
  purgeManyV2,
  convertFAQsRecursively,
  updateMany,
  // faqsScrapper,
  extractFaqsPdf,
  createLogs,
  createLogsForDELETE,
  lemmtaizationMany
};
