const { default: axios } = require("axios");
const SlackIntegration = require("../Models/Slack.integration");

const getByChannel = (req, res) => {
  const { channel_id } = req.params;
  SlackIntegration.findOne({
    where: {
      channel_id,
    },
  })
    .then((slack) => {
      if (!slack)
        return res.status(404).send({ message: "Slack Integration not found" });

      return res.send(slack);
    })
    .catch((error) => {
      console.log(error);
      return res.status(500).send({ message: "Something went wrong" });
    });
};
const getByBot = (req, res) => {
  const { bot_id } = req.params;
  SlackIntegration.findAll({
    where: {
      bot_id,
    },
  })
    .then((slack) => {
      if (!slack)
        return res.status(404).send({ message: "Slack Integration not found" });

      return res.send(slack);
    })
    .catch((error) => {
      console.log(error);
      return res.status(500).send({ message: "Something went wrong" });
    });
};

const create = (req, res) => {
  const { channel_id, channel, api_token } = req.body;
  SlackIntegration.findAll({
    where: {
      channel_id,
      channel,
      api_token,
    },
  }).then((slackInts) => {
    if (slackInts?.length === 0)
      SlackIntegration.create({
        channel_id,
        channel,
        api_token,
      })
        .then((slack) => {
          return res.send(slack);
        })
        .catch((error) => {
          console.log(error);
          return res.status(500).send({ message: "Something went wrong" });
        });
    else return res.status(409).send({ message: "Slack Integration exists" });
  });
};

const update = (req, res) => {
  const { bot_id, channel_id } = req.body;
  SlackIntegration.findOne({
    where: {
      channel_id,
    },
  })
    .then((slack) => {
      if (!slack)
        return res.status(404).send({ message: "Slack Integration not found" });

      slack.updateInfo({ bot_id });
      slack
        .save()
        .then((slack) => {
          return res.send(slack);
        })
        .catch((error) => {
          console.log(error);
          return res.status(500).send({ message: "Something went wrong" });
        });
    })
    .catch((error) => {
      console.log(error);
      return res.status(500).send({ message: "Something went wrong" });
    });
};

const deleteConnection = async (req, res) => {
  const { bot_id, channel } = req.body;
  const slackInt = await SlackIntegration.findOne({
    where: {
      bot_id,
      channel,
    },
  });
  if (!slackInt)
    return res.status(404).send({ message: "Slack Integration not found" });
  axios.post(
    "https://slack.com/api/chat.postMessage",
    {
      channel: slackInt.channel_id,
      text: "Bot has been deleted from this channel",
    },
    {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${slackInt.api_token}`,
      },
    }
  );
  SlackIntegration.destroy({
    where: {
      slack_integration_id: slackInt.slack_integration_id,
    },
  });
  return res.status(202).send({ ...slackInt });
};
module.exports = { getByChannel, create, update, getByBot, deleteConnection };
