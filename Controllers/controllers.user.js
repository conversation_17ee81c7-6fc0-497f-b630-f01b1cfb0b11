const User = require("../Models/User");
const sgEmail = require("../Services/Email");
const {
 builderUrl
} = require("../constants")

const get = (req, res) => {
  const user = req.user;
  res.send({ user });
};

const getResetPwdAccess = (req, res) => {
  res.send({ access: "access granted" });
};

const register = async (req, res) => {
  const data = req.body;
  User.create({
    company_name: data.company_name,
    email: data.email,
    user_name: data.user_name,
    email_verification: false,
    country: data.country,
    city: data.city,
  })
    .then(async (user) => {
      user.setPassword(req.body.password);
      await user.save();
      res
        .status(201)
        .send({ message: "registeration successful verify you email", user: user.toAuthJSON()});
    })
    .catch((error) => {
      console.log(error);
      res.status(400).send(error);
    });
};

const login = async (req, res) => {
  const user = req.user;
  user.updateRecord();
  await user.save();
  return res.send({ user: user.toAuthJSON() });
};

const forgetPassword = async (req, res) => {
  const user = req.user;
  if (user.reset) {
    return res.status(401).send({ message: "you have already set the email" });
  }
  user.reset = true;
  user.sendResetPassword();
  await user.save();
  return res.send({ message: "Password reset email has been sent successfully" });
};

const sendResetPassword = async (req, res) => {
  const user = req.user;
  const emailBody =  req.emailBody
try {
    await sgEmail
    .send({
      from: "<EMAIL>",
      to: user.email,
      subject: 'Searchat Password Reset 🔒',
      html: emailBody ,
    }, (error, result) => {
      if (error) {
        console.log(error)
        return res.status(500).send({ message: "something went wrong" });
      }
    })
  
    user.reset = true;
    await user.save();

    return res.send({ message: "Password reset email has been sent successfully" });
} catch (error) {
  return res.status(500).send({ message: "something went wrong" });
}

}

const setNewPassword = async (req, res) => {
  const user = req.user;
  const password = req.body.password;
  user.reset = false;
  user.setPassword(password);
  await user.save();
  return res.send({ message: "setting new password went successfully" });
};

const finalizeEmailVerification = async (req, res) => {
  const user = req.user;
  await user.save();
  res
    .status(301)
    .redirect(
      builderUrl.concat(`/login?auto=true&user_id=${user.user_id}`)
    );
};

const logout = (req, res) => {
  //const user = req.user; # NOTE in case of tracking
  res.send({ message: "logout successful" });
};

const updateProfile = async (req, res) => {
  const user = req.user;
  user.update({ ...req.body });
  await user.save();
  res.send({ ...user.toAuthJSON() });
};

const generateToken = async (req, res) => {
  const user = req.user;
  res.send({ 
    token : user.generateToken()
  });
};

module.exports = {
  register,
  login,
  forgetPassword,
  setNewPassword,
  finalizeEmailVerification,
  get,
  logout,
  updateProfile,
  generateToken,
  sendResetPassword,
  getResetPwdAccess
};
