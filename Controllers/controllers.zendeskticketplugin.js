const ZendeskTicketPlugin = require("../Models/ZendeskTicketPlugin");
const fetch = require("node-fetch");
const get = (req, res) => {
  res.send(req.ZendeskTicketPlugin);
};

const find = (req, res, next) => {
  const bot_id = req.query.bot_id || req.body.bot_id;
  ZendeskTicketPlugin.findOne({ where: { bot_id } }).then(
    (ZendeskTicketPlugin) => {
      if (!ZendeskTicketPlugin) {
        return res.status(401).send({ message: "Plugin ticket not found" });
      }
      req.ZendeskTicketPlugin = ZendeskTicketPlugin;
      return next();
    }
  );
};

const set = (req, res, next) => {
  const bot_id = req.body.bot_id;
  console.log(req.body, "req.body set");
  ZendeskTicketPlugin.create({
    bot_id,
  })
    .then((ZendeskTicketPlugin) => {
      req.ZendeskTicketPlugin = ZendeskTicketPlugin;
      return next();
    })
    .catch((error) => {
      res.send(error);
    });
};

const update = async (req, res, next) => {
  const ZendeskTicketPlugin = req.ZendeskTicketPlugin; //from db
  const ZendeskTicketPluginData = req.body; //from body
  console.log(ZendeskTicketPlugin, "ZendeskTicketPlugin");
  console.log(ZendeskTicketPluginData, "ZendeskTicketPluginData");

  if (Boolean(ZendeskTicketPluginData.code_from_url)) {
    let URL = `https://${ZendeskTicketPluginData.sub_domain}.zendesk.com/oauth/tokens?grant_type=authorization_code&code=${ZendeskTicketPluginData.code_from_url}&client_id=${ZendeskTicketPluginData.client_id}&client_secret=${ZendeskTicketPluginData.client_secret}&redirect_uri=http%3A%2F%2Flocalhost%3A3000&scope=read`;
    console.log(URL);
    const data = await fetch(URL, {
      method: "POST",
      url: URL,
      headers: {
        "Content-Type": "application/json; charset=UTF-8",
      },
    }).then((response) => response.json());
    console.log(data, "datadata");
    await ZendeskTicketPlugin.updateInfo({
      ...ZendeskTicketPluginData,
      access_token: data.access_token,
    });
    await ZendeskTicketPlugin.save();

    return next();
  } else if (ZendeskTicketPlugin && ZendeskTicketPluginData) {
    ZendeskTicketPlugin.updateInfo({ ...ZendeskTicketPluginData });
    await ZendeskTicketPlugin.save();
    return next();
  } else {
    return next();
  }
};

const sendTicketToZendesk = async (req, res, next) => {
  const ticketZendesk = req.ZendeskTicketPlugin;
  const ZendeskTicketDataTicket = req.body; //from body

  let URL = `https://${ticketZendesk.sub_domain}.zendesk.com/api/v2/tickets.json`;
  console.log(URL);
  console.log(ZendeskTicketDataTicket, "ZendeskTicketDataTicket");
  console.log(ticketZendesk, "ticketZendesk");
  console.log(ticketZendesk.access_token, "ticketZendesk.access_token");
  const data = await fetch(URL, {
    method: "POST",
    url: URL,
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${ticketZendesk.access_token}`,
    },
    body: JSON.stringify({
      ticket: {
        subject: ZendeskTicketDataTicket.subject,
        comment: { body: ZendeskTicketDataTicket.description },
        requester: {
          name: ZendeskTicketDataTicket.email,
          email: ZendeskTicketDataTicket.email,
        },
      },
    }),
  })
    .then((response) => {
      console.log("response", response);
      return response.json();
    })
    .catch((err) => console.log("ERR", err));
  return res.send(data);
};

module.exports = {
  get,
  set,
  update,
  find,
  sendTicketToZendesk,
};
