const Smtp = require("../Models/Smtp");

const get = (req, res) => {
  return res.send(req.SmtpPlugin);
};

const find = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  Smtp.findOne({ where: { bot_id } }).then((SmtpPlugin) => {
    if (!SmtpPlugin) {
      return res.status(401).send({ message: "plugin email not found" });
    } else {
      req.SmtpPlugin = SmtpPlugin;
      return next();
    }
  });
};

const update = async (req, res, next) => {
  const data = req.body;
  const smtpPlugin = req.SmtpPlugin;
  smtpPlugin.updateInfo(data);
  await smtpPlugin.save();
  return next();
};

const set = (req, res, next) => {
  const data = req.body;

  const botId = data.bot_id;

  Smtp.findOne({ where: { bot_id: botId } }).then((existingSmtp) => {
    if (existingSmtp) {
      existingSmtp.updateInfo(data);
      existingSmtp.save();
      req.SmtpPlugin = existingSmtp;
      return next();
    } else {
      Smtp.create(data)
        .then((SmtpPlugin) => {
          req.SmtpPlugin = SmtpPlugin;
          return next();
        })
        .catch((error) => {
          res.status(500).send({ message: "Internal server error" + error });
        });
    }
  });
};



const purge = (req, res, next) => {
  const bot_id = req.body.bot_id;
  Smtp.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    res.send({ message: "Smtp integration data deleted" });
  });
};

const getAll = (req, res, next) => {
  res.send(req.emails);
};

module.exports = {
  set,
  getAll,
  get,
  update,
  find,
  purge,
};
