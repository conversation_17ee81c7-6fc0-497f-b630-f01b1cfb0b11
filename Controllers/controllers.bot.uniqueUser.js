const BotUniqueUser = require("../Models/Bot.UniqueUser");


const set = (req, res, next) => {
  const data = req.body;
    BotUniqueUser.create({
      bot_id: data.bot_id,
    }).then((botUniqueUser) => {
      req.botUniqueUser = botUniqueUser;
      return next();
    });
};


const getAll = (req, res) => {
    res.send(req.botUniqueUsers);
};

const get = (req, res) => {
    res.send(req.botUniqueUser);
};


module.exports = { set , getAll, get};
