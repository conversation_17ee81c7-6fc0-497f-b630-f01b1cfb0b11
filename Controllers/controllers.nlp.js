const fetch = require("node-fetch");
const utf = require("utf8");

const replaceDiactricArrWithSentence = async (req, res, next) => {
  var arr = [
    "ذَهَب",
    "+نا",
    "إِلَى",
    "حَدِيق",
    "+َة",
    "+ِ",
    "ال+",
    "حَيَوان",
    "+ات",
    "+ِ",
    "فِي",
    "مِصْر",
  ];
  var arr = ["عَلَي", "+كَ", "شَرِب", "+َ", "ال+", "خَمْر"];

  var arr = [
    "هَلْ",
    "تَ+",
    "عْلَم",
    "+ِينَ",
    "كَمْ",
    "كَلَّف",
    "+َ",
    "+نِي",
    "هٰذا",
    "ال+",
    "صَبْر",
    "+ِ",
  ];

  var arr = [
    "وَ+",
    "لَمّا",
    "بَدا",
    "لِيَ",
    "أَنَّ",
    "+ها",
    "لا",
    "تُ+",
    "حِبّ",
    "+نِي",
    "وَ+",
    "أَنَّ",
    "هَوا",
    "+ها",
    "لَيِس",
    "+َ",
    "عَن",
    "+ِّي",
    "بِ+",
    "مِنْجَلِيّ",
  ];

  arr = [
    "شَعَر",
    "+َ",
    "ال+",
    "شَرِيف",
    "+ِ",
    "ال+",
    "رِضَى",
    "–",
    "إِذا",
    "أَنْتَ",
    "فَتَّش",
    "+تُ",
    "ال+",
    "قُلُوب",
    "+ِ",
    "وَجَد",
    "+تُ",
    "+ها",
  ];
  const url = "https://api.searchat.com:5000/api/tokenize_bwtok_split_diac";

  var arr = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ txt: req.body.text }),
  }).then((res) => res.json());

  // arr = arr_res
  //   .slice(1, arr_res.length)
  //   .split(",")
  //   .map((a) => a.slice(1, a.length - 1));
  //   console.log(arr_res);

  for (var i = 1; i < arr.length; i++) {
    var sec = arr[i];
    var pre = sec[0];
    var post = sec[sec.length - 1];

    //NOTE POSTFIX
    if (post.includes("+")) {
      arr[i + 1] = arr[i].concat(arr[i + 1]);
      arr = arr.filter((a, k) => k !== i);
      i -= 1;
    } //NOTE PREFIX
    else if (pre.includes("+")) {
      arr[i - 1] = arr[i - 1].concat(arr[i]);
      arr = arr.filter((a, k) => k !== i);
      i -= 1;
    }
  }

  res.send({
    sentence: arr
      .map((a) => a.replace("+", "").replace("+", "").replace("+", ""))
      .join(" "),
  });
};

module.exports = { replaceDiactricArrWithSentence };
