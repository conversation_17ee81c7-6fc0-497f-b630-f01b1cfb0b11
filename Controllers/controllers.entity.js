const Entity = require("../Models/NamedEntity");

const get = (req, res) => {
  res.send(req.entity.toJSON());
};

const getAll = (req, res) => {
  res.send(req.entities);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  Entity.create({
    entity_name: data.entity_name,
    entity_type: data.entity_type,
    entity_subType: data.entity_subType,
  })
    .then((entity) => {
      req.entity = entity;
      return next();
    })
    .catch(() => {
      res.status(409).send({ message: "entity name is already exists" });
    });
};

const purgeAll = (req, res, next) => {
  const bot_id = req.body.bot_id;
  Trigger.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    res.send({ message: "triggers deleted" });
  });
};

const purge = (req, res, next) => {
  const entity_id = req.body.entity_id;
  Entity.destroy({
    where: {
      entity_id,
    },
  }).then(() => {
    res.send({ message: "entity deleted" });
  });
};

const update = async (req, res, next) => {
  const entity = req.entity;
  const entityData = req.body;
  if (entityData && entity) {
    entity.updateInfo({ ...entityData });
    await entity.save();
    return next();
  } else {
    //FIXME I don't like this سمردحة
    //STATUS Code should be descriptive well
    //And this issue case would return 200 with an hidden issue
    return next();
  }
};

module.exports = { get, getAll, set, update, purge };
