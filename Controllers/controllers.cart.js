const Cart = require("../Models/Cart");

const get = (req, res) => {
  res.send(req.cart);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  Cart.create(data)
    .then((cart) => {
      req.cart = cart;
      return next();
    })
    .catch(() => {
      res.status(409).send({ message: "Cart is already exists" });
    });
};

const purge = (req, res, next) => {
  const bot_id = req.body.bot_id;
  Cart.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    res.send({ message: "cart data deleted" });
  });
};

const update = async (req, res, next) => {
  const cart = req.cart;
  const cartData = req.body;
  if (cartData && cart) {
    cart.updateInfo({ ...cartData });
    await cart.save();
    return next();
  } else {
    return next();
  }
};

module.exports = { get, set, update, purge };
