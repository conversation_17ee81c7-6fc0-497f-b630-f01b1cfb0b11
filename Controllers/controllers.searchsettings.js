const SearchSettings = require("../Models/SearchSettings");

const get = (req, res) => {
  return res.send(req.searchSettings);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  const botId = data.bot_id;
  SearchSettings.findOne({ where: { bot_id: botId } }).then(
    (existingSettings) => {
      if (existingSettings) {
        // res.status(409).send({ message: "This bot settings already exists " });
        existingSettings.updateInfo(data);
        existingSettings.save();
        req.searchSettings = existingSettings;
        return next();
      } else {
        SearchSettings.create(data)
          .then((searchSettings) => {
            req.searchSettings = searchSettings;
            return next();
          })
          .catch((error) => {
            res.status(500).send({ message: "Internal server error" });
          });
      }
    }
  );
};

const purge = (req, res, next) => {
  const bot_id = req.body.bot_id;
  SearchSettings.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    res.send({ message: "bot search settings data deleted" });
  });
};

const update = async (req, res, next) => {
  const data = req.body;
  const searchSettings = req.searchSettings;
  if (req.body && searchSettings) {
    searchSettings.updateInfo(data);
    await searchSettings.save();
    return next();
  } else {
    return next();
  }
};
module.exports = { update, purge, set, get };
