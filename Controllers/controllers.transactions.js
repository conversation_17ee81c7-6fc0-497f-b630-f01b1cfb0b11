const TRUserInteraction = require("../Models/TRUserInteractions");
const TRCube = require("../Models/TRCubes");
const TRBotResponse = require("../Models/TRBotResponse");
const TRBotResponseSource = require("../Models/TRBotResponseSources");
const TRMapping = require("../Models/TRMapping");
const sequelize = require("../db");

// create one user interactions
const createUserInteraction = async (req, res, next) => {
  try {
    const data = req.body;
    const response = await sequelize.query(
      `bot_designer_tr_user_interaction_set 
        :bot_id ,
        :channel ,
        :conversation_id ,
        :message ,
        :is_voice ,
        :session_uuid ,
        :message_uuid ,
        :button_payload ,
        :continent ,
        :country ,
        :city ,
        :ip_address ,
        :type,
        :user_contact_id,
        :name
        `,
      {
        replacements: {
          bot_id: data.bot_id,
          channel: data.channel ? data.channel : "Dev",
          conversation_id: data.conversation_id ? data.conversation_id : null,
          message: data.message,
          is_voice: data.is_voice,
          session_uuid: data?.session_uuid ? data.session_uuid : null,
          message_uuid: data?.message_uuid ? data.message_uuid : null,
          button_payload: data.button_payload,
          continent: data.continent ? data.continent : null,
          country: data.country ? data.country : null,
          city: data.city ? data.city : null,
          ip_address: data.ip_address ? data.ip_address : null,
          type: data.type ? data.type : null,
          user_contact_id: data.user_contact_id ? data.user_contact_id : null,
          name: data.name ? data.name : null,
        },
      }
    );

    req.res = response[0][0];
  } catch (error) {
    console.log(error);

    req.res = null;
  } finally {
    next();
  }
};

// create one cube
const createTrCube = async (req, res, next) => {
  try {
    const payload = req.body;
    const cc = await TRCube.create({ ...payload });
    req.res = cc.dataValues;
  } catch (error) {
    req.res = null;
  } finally {
    next();
  }
};

const createFinalTr = async (req, res, next) => {
  const transaction = await sequelize.transaction();
  try {
    const { bot_id, result_status, engine, user_interaction_id, bot_answers } =
      req.body;

      console.dir(bot_answers, {depth: null})

    const tr_cube = (
      await TRCube.create(
        { engine, result_status, user_interaction_id, bot_id },
        { transaction }
      )
    ).dataValues;

    let dialogToReturn = { bot_response_source_id: null };

    for (const ba of bot_answers) {
      const trBotRes = (
        await TRBotResponse.create(
          {
            tr_cube_id: tr_cube.tr_cube_id,
            response_type: ba.response_type,
            response: ba.response,
          },
          { transaction }
        )
      ).dataValues;

      // Update dialogToReturn if `bot_response_source_id` exists
      if (ba.sources?.[0]?.bot_response_source_id) {
        dialogToReturn.bot_response_source_id =
          ba.sources[0].bot_response_source_id;
      }

      // Handle mappings
      if (dialogToReturn.bot_response_source_id) {
        await TRMapping.create(
          {
            bot_response_source_id: dialogToReturn.bot_response_source_id,
            bot_response_id: trBotRes.bot_response_id,
          },
          { transaction }
        );
      } else {
        for (const item of ba.sources) {
          const src = await TRBotResponseSource.create(
            {
              tr_cube_id: tr_cube.tr_cube_id,
              source_id: item.source_id,
              source_type: item.source_type,
              source_title: item.source_title,
              source_entity: item.source_entity,
            },
            { transaction }
          );

          // Update dialogToReturn only if conditions are met
          if (
            result_status !== "conflict" &&
            (item.source_type.includes("dialog"))
          ) {
            dialogToReturn = {
              bot_response_source_id: src.dataValues.bot_response_source_id,
            };
          }

          await TRMapping.create(
            {
              bot_response_source_id: src.dataValues.bot_response_source_id,
              bot_response_id: trBotRes.bot_response_id,
            },
            { transaction }
          );
        }
      }
    }

    // Commit transaction and assign the final dialogToReturn
    await transaction.commit();
    req.res = dialogToReturn;
  } catch (error) {
    if (transaction) await transaction.rollback();
    console.error(error); // Log error for debugging
    req.res = null;
  } finally {
    next();
  }
};

// create many bot responses
const createManyBotResponses = async (req, res, next) => {
  try {
    const payload = req.body;
    const cbr = await TRBotResponse.bulkCreate(payload);

    req.res = cbr;
  } catch (error) {
    req.res = null;
  } finally {
    next();
  }
};

const deleteUserInteraction = async (req, res, next) => {
  try {
    const { message_uuid } = req.body;
    const cbr = await TRUserInteraction.update(
      {
        is_deleted: true,
      },
      {
        where: { message_uuid },
      }
    );

    req.res = cbr;
  } catch (error) {
    req.res = null;
  } finally {
    next();
  }
};
module.exports = {
  createUserInteraction,
  createTrCube,
  createManyBotResponses,
  createFinalTr,
  deleteUserInteraction,
};
