const Biller = require("../Models/Biller");

const get = (req, res) => {
  res.send(req.biller);
};

const getAll = (req, res) => {
  res.send(req.billers);
};

const set = (req, res, next) => {
  const data = req.body;
  Biller.create(data).then((biller) => {
    req.biller = biller;
    return next();
  });
};

const update = async (req, res, next) => {
  const biller = req.biller;
  const data = { ...req.body };
  biller.updateInfo(data);
  req.biller = biller;
  await biller.save();
  return next();
};

const generatePrice = (req, res, next) => {
  const plan = req.plan;
  const promotion = req.promotion;
  const countryPlan = req.countryPlan;
  const months = req.body.months;
  var price = months * plan.plan_price * countryPlan.conversion_ration;
  if(Boolean(promotion?.promotion_percentage)){
    price = months * (plan.plan_price - (plan.plan_price * promotion?.promotion_percentage)) * countryPlan.conversion_ration ;
  }
  else if(Boolean(promotion?.promotion_value)){
    price = months * (plan.plan_price -  promotion?.promotion_value) * countryPlan.conversion_ration ;
  }

  res.send({
    currency: countryPlan.currency,
     price
  });
};

module.exports = {
  get,
  getAll,
  update,
  set,
  generatePrice,
};
