const ItemOption = require("../Models/Item.Option");

const get = (req, res) => {
  res.send(req.itemoption);
};

const getAll = (req, res, next) => {
  res.send(req.itemoptions);
};

const set = async (req, res, next) => {
  const data = { ...req.body };
  ItemOption.create(data)
    .then((itemoption) => {
      req.itemoption = itemoption;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const update = async (req, res, next) => {
  const itemoption = req.itemoption;
  const data = { ...req.body };
  itemoption.updateInfo(data);
  await itemoption.save();
  return next();
};

const purge = (req, res, next) => {
  const data = { ...req.body };
  ItemOption.destroy({
    where: {
      item_id: data.item_id,
    },
  }).then(() => {
    return next();
  });
};

const purgeOne = (req, res, next) => {
  const data = { ...req.body };
  ItemOption.destroy({
    where: {
      item_option_id: data.item_option_id,
    },
  }).then(() => {
    return res.send({ message: "item option was deleted" });
  });
};

module.exports = {
  get,
  getAll,
  set,
  update,
  purge,
  purgeOne,
};
