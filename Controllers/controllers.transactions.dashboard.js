const sequelize = require("../db");
const TRCube = require("../Models/TRCubes");
const TRUserInteraction = require("../Models/TRUserInteractions");
const TRBotResponse = require("../Models/TRBotResponse");
const TRMapping = require("../Models/TRMapping");
const TRBotResponseSource = require("../Models/TRBotResponseSources");
const { Op } = require("sequelize");
const DialogCheckpoint = require("../Models/DialogCheckpoint");

// create one user interactions
const getStats = async (req, res, next) => {
  try {
    const {
      bot_id,
      start_day,
      start_month,
      start_year,
      end_day,
      end_month,
      end_year,
    } = req.body;

    const response = await TRUserInteraction.sequelize.query(
      `
        DECLARE @startDate DATE = DATEFROMPARTS(:year, :month, :day);
        DECLARE @endDate DATE = CASE 
            WHEN :endYear IS NOT NULL AND :endMonth IS NOT NULL AND :endDay IS NOT NULL 
            THEN DATEFROMPARTS(:endYear, :endMonth, :endDay)
            ELSE GETDATE() 
        END;


        select 'graph' as type, 'msg_per_date' as card , DATEFROMPARTS(year,month,day) as date, year,month,day, count(user_interaction_id) as count from bot_designer_tr_user_interactions where bot_id = :bot_id 
         AND DATEFROMPARTS(year, month, day) BETWEEN @startDate AND @endDate
         group by year,month,day order by date asc;

        select 'number' as type,'msg_per_date' as card , count(user_interaction_id) as count from bot_designer_tr_user_interactions where bot_id = :bot_id 
         AND DATEFROMPARTS(year, month, day) BETWEEN @startDate AND @endDate;


         select 'number' as type,'voice_per_date' as card , count(user_interaction_id) as count from bot_designer_tr_user_interactions where bot_id = :bot_id 
        AND is_voice = 1 AND DATEFROMPARTS(year, month, day) BETWEEN @startDate AND @endDate;


          select 'graph' as type, 'users_per_date' as card , DATEFROMPARTS(year,month,day) as date, year, month,day, count(distinct conversation_id) as count from bot_designer_tr_user_interactions where bot_id = :bot_id 
         AND DATEFROMPARTS(year, month, day) BETWEEN @startDate AND @endDate
         group by year,month,day  order by date asc;

         select 'number' as type, 'users_per_date' as card , count(distinct conversation_id) as count from bot_designer_tr_user_interactions where bot_id = :bot_id 
         AND DATEFROMPARTS(year, month, day) BETWEEN @startDate AND @endDate;

         select 'number' as type, 'date_since_launch' as card, createdAt as date from bot_designer_bots where bot_id = :bot_id;

         select 'number' as type, 'sessions_count' as card , count(distinct session_uuid) as count ,channel from bot_designer_tr_user_interactions where bot_id = :bot_id 
         AND DATEFROMPARTS(year, month, day) BETWEEN @startDate AND @endDate group by channel;

        `,
      {
        replacements: {
          bot_id,
          year: start_year,
          month: start_month,
          day: start_day,
          endYear: end_year,
          endMonth: end_month,
          endDay: end_day,
        },
      }
    );

    req.res = response[0];
  } catch (error) {
    console.log(error);
    req.res = [];
  } finally {
    next();
  }
};

const getHeatmap = async (req, res, next) => {
  try {
    const {
      bot_id,
      start_day,
      start_month,
      start_year,
      end_day,
      end_month,
      end_year,
    } = req.body;

    const response = await TRUserInteraction.sequelize.query(
      `
        DECLARE @startDate DATE = DATEFROMPARTS(:year, :month, :day);
        DECLARE @endDate DATE = CASE 
            WHEN :endYear IS NOT NULL AND :endMonth IS NOT NULL AND :endDay IS NOT NULL 
            THEN DATEFROMPARTS(:endYear, :endMonth, :endDay)
            ELSE GETDATE() 
        END;

       select weekday,hour, count(user_interaction_id) as count from bot_designer_tr_user_interactions where bot_id = :bot_id 
         AND DATEFROMPARTS(year, month, day) BETWEEN @startDate AND @endDate
         group by weekday, hour;
        `,
      {
        replacements: {
          bot_id,
          year: start_year,
          month: start_month,
          day: start_day,
          endYear: end_year,
          endMonth: end_month,
          endDay: end_day,
        },
      }
    );

    req.res = response[0];
  } catch (error) {
    console.log(error);
    req.res = [];
  } finally {
    next();
  }
};

const getMessagesUserDate = async (req, res, next) => {
  try {
    const {
      bot_id,
      start_day,
      start_month,
      start_year,
      end_day,
      end_month,
      end_year,
    } = req.body;

    const response = await TRUserInteraction.sequelize.query(
      `
        DECLARE @startDate DATE = DATEFROMPARTS(:year, :month, :day);
        DECLARE @endDate DATE = CASE 
            WHEN :endYear IS NOT NULL AND :endMonth IS NOT NULL AND :endDay IS NOT NULL 
            THEN DATEFROMPARTS(:endYear, :endMonth, :endDay)
            ELSE GETDATE() 
        END;

       select 
        DATEFROMPARTS(year,month,day) as date,
        count(user_interaction_id) as messages, 
        count(distinct conversation_id) as users,
        (count(user_interaction_id) / count(distinct conversation_id)) as avg
        from bot_designer_tr_user_interactions 
       where bot_id = :bot_id 
         AND DATEFROMPARTS(year, month, day) BETWEEN @startDate AND @endDate
         group by year,month,day;

         
        `,
      {
        replacements: {
          bot_id,
          year: start_year,
          month: start_month,
          day: start_day,
          endYear: end_year,
          endMonth: end_month,
          endDay: end_day,
        },
      }
    );

    req.res = response[0];
  } catch (error) {
    console.log(error);
    req.res = [];
  } finally {
    next();
  }
};

const getAnsweredDate = async (req, res, next) => {
  try {
    const {
      bot_id,
      start_day,
      start_month,
      start_year,
      end_day,
      end_month,
      end_year,
    } = req.body;

    const response = await TRUserInteraction.sequelize.query(
      `
        DECLARE @startDate DATE = DATEFROMPARTS(:year, :month, :day);
        DECLARE @endDate DATE = CASE 
            WHEN :endYear IS NOT NULL AND :endMonth IS NOT NULL AND :endDay IS NOT NULL 
            THEN DATEFROMPARTS(:endYear, :endMonth, :endDay)
            ELSE GETDATE() 
        END;






        
        WITH interaction_counts AS (
        SELECT 
            ui.year,
            ui.month,
            ui.day,
            SUM(CASE WHEN c.result_status = 'fallback' THEN 1 ELSE 0 END) AS fallback_count,
            SUM(CASE WHEN c.result_status = 'conflict' THEN 1 ELSE 0 END) AS conflict_count,
            SUM(CASE WHEN c.result_status = 'answered' THEN 1 ELSE 0 END) AS answered_count
        FROM 
            bot_designer_tr_user_interactions ui
        INNER JOIN 
            bot_designer_tr_cubes c 
        ON 
            ui.user_interaction_id = c.user_interaction_id
        WHERE 
            ui.bot_id = :bot_id
            AND c.engine NOT IN ('dialog')
            AND DATEFROMPARTS(year, month, day) BETWEEN @startDate AND @endDate
        GROUP BY 
            ui.year, ui.month, ui.day
    )
    SELECT 

        'line' as graph,
        YEAR || '-' || MONTH || '-' || DAY AS date,
        fallback_count,
        conflict_count,
        answered_count,
        answered_count * 1.0 / (fallback_count + conflict_count + answered_count) AS answered_pct
    FROM 
        interaction_counts;


    WITH interaction_counts AS (
    SELECT 
        SUM(CASE WHEN c.result_status = 'fallback' THEN 1 ELSE 0 END) AS fallback_count,
        SUM(CASE WHEN c.result_status = 'conflict' THEN 1 ELSE 0 END) AS conflict_count,
        SUM(CASE WHEN c.result_status = 'answered' THEN 1 ELSE 0 END) AS answered_count,
        COUNT(*) AS total_count
    FROM 
        bot_designer_tr_user_interactions ui
    INNER JOIN 
        bot_designer_tr_cubes c 
    ON 
        ui.user_interaction_id = c.user_interaction_id
    WHERE 
        ui.bot_id = :bot_id
        AND c.engine NOT IN ('dialog')
        AND DATEFROMPARTS(year, month, day) BETWEEN @startDate AND @endDate
)
SELECT 
    'pie' as graph,
    fallback_count * 1.0 / total_count * 100 AS fallback_pct,
    conflict_count * 1.0 / total_count * 100 AS conflict_pct,
    answered_count * 1.0 / total_count * 100 AS answered_pct
FROM 
    interaction_counts;

        `,
      {
        replacements: {
          bot_id,
          year: start_year,
          month: start_month,
          day: start_day,
          endYear: end_year,
          endMonth: end_month,
          endDay: end_day,
        },
      }
    );

    req.res = response[0];
  } catch (error) {
    console.log(error);
    req.res = [];
  } finally {
    next();
  }
};

const getMessagesUserChannel = async (req, res, next) => {
  try {
    const {
      bot_id,
      start_day,
      start_month,
      start_year,
      end_day,
      end_month,
      end_year,
    } = req.body;

    const response = await TRUserInteraction.sequelize.query(
      `
        DECLARE @startDate DATE = DATEFROMPARTS(:year, :month, :day);
        DECLARE @endDate DATE = CASE 
            WHEN :endYear IS NOT NULL AND :endMonth IS NOT NULL AND :endDay IS NOT NULL 
            THEN DATEFROMPARTS(:endYear, :endMonth, :endDay)
            ELSE GETDATE() 
        END;

        select 
        channel,
        count(user_interaction_id) as messages, 
        count(distinct conversation_id) as users
        from bot_designer_tr_user_interactions 
       where bot_id = :bot_id 
         AND DATEFROMPARTS(year, month, day) BETWEEN @startDate AND @endDate
         group by channel;




        
       
  

        `,
      {
        replacements: {
          bot_id,
          year: start_year,
          month: start_month,
          day: start_day,
          endYear: end_year,
          endMonth: end_month,
          endDay: end_day,
        },
      }
    );

    req.res = response[0];
  } catch (error) {
    console.log(error);
    req.res = [];
  } finally {
    next();
  }
};

const getFunctionsRequestsCount = async (req, res, next) => {
  try {
    const {
      bot_id,
      start_day,
      start_month,
      start_year,
      end_day,
      end_month,
      end_year,
    } = req.body;

    const response = await TRUserInteraction.sequelize.query(
      `
        DECLARE @startDate DATE = DATEFROMPARTS(:year, :month, :day);
        DECLARE @endDate DATE = CASE 
            WHEN :endYear IS NOT NULL AND :endMonth IS NOT NULL AND :endDay IS NOT NULL 
            THEN DATEFROMPARTS(:endYear, :endMonth, :endDay)
            ELSE GETDATE() 
        END;

       select 
       Top(10)
    (CASE when bs.source_type = 'dialog' then bs.source_title else bs.source_type end) as function_requested,
      count(*) as count

    from bot_designer_tr_user_interactions ui join bot_designer_tr_cubes c on ui.user_interaction_id = c.user_interaction_id
    inner join bot_designer_tr_bot_response_sources bs on c.tr_cube_id = bs.tr_cube_id
    where ui.bot_id = :bot_id 
         AND DATEFROMPARTS(year, month, day) BETWEEN @startDate AND @endDate 
         group by bs.source_type ,bs.source_title
         order by count desc
        `,
      {
        replacements: {
          bot_id,
          year: start_year,
          month: start_month,
          day: start_day,
          endYear: end_year,
          endMonth: end_month,
          endDay: end_day,
        },
      }
    );

    req.res = response[0];
  } catch (error) {
    console.log(error);
    req.res = [];
  } finally {
    next();
  }
};

const getUserPerYear = async (req, res, next) => {
  try {
    const { bot_id } = req.body;

    const response = await TRUserInteraction.sequelize.query(
      `
        select 
          count(distinct conversation_id) as users,
          year
        from bot_designer_tr_user_interactions
          where bot_id = :bot_id
          group by year;
        `,
      {
        replacements: {
          bot_id,
        },
      }
    );

    req.res = response[0];
  } catch (error) {
    console.log(error);
    req.res = [];
  } finally {
    next();
  }
};

const getLastMessages = async (req, res, next) => {
  try {
    const { bot_id } = req.body;

    const response = await TRUserInteraction.sequelize.query(
      `
        select 
           TOP(15)
          message,
          conversation_id,
          createdAt
        from bot_designer_tr_user_interactions
          where bot_id = :bot_id and type = 'text'
          order by createdAt desc
        `,
      {
        replacements: {
          bot_id,
        },
      }
    );

    req.res = response[0];
  } catch (error) {
    console.log(error);
    req.res = [];
  } finally {
    next();
  }
};

const getMessagesUserCountry = async (req, res, next) => {
  try {
    const {
      bot_id,
      start_day,
      start_month,
      start_year,
      end_day,
      end_month,
      end_year,
    } = req.body;

    const response = await TRUserInteraction.sequelize.query(
      `
        DECLARE @startDate DATE = DATEFROMPARTS(:year, :month, :day);
        DECLARE @endDate DATE = CASE 
            WHEN :endYear IS NOT NULL AND :endMonth IS NOT NULL AND :endDay IS NOT NULL 
            THEN DATEFROMPARTS(:endYear, :endMonth, :endDay)
            ELSE GETDATE() 
        END;

       select 
        count(user_interaction_id) as messages, 
        count(distinct conversation_id) as users,
        country
        from bot_designer_tr_user_interactions 
       where bot_id = :bot_id 
         AND DATEFROMPARTS(year, month, day) BETWEEN @startDate AND @endDate
         and country is not null
         group by country;

         
        `,
      {
        replacements: {
          bot_id,
          year: start_year,
          month: start_month,
          day: start_day,
          endYear: end_year,
          endMonth: end_month,
          endDay: end_day,
        },
      }
    );

    req.res = response[0];
  } catch (error) {
    console.log(error);
    req.res = [];
  } finally {
    next();
  }
};
const getTopNotAnswered = async (req, res, next) => {
  try {
    const {
      bot_id,
      start_day,
      start_month,
      start_year,
      end_day,
      end_month,
      end_year,
    } = req.body;

    const response = await TRUserInteraction.sequelize.query(
      `
        DECLARE @startDate DATE = DATEFROMPARTS(:year, :month, :day);
        DECLARE @endDate DATE = CASE 
            WHEN :endYear IS NOT NULL AND :endMonth IS NOT NULL AND :endDay IS NOT NULL 
            THEN DATEFROMPARTS(:endYear, :endMonth, :endDay)
            ELSE GETDATE() 
        END;

       select 
        TOP(15)
        count(ui.conversation_id) as count,
        c.result_status,
        ui.message
        from bot_designer_tr_user_interactions ui join bot_designer_tr_cubes c on ui.user_interaction_id = c.user_interaction_id

       where ui.bot_id = :bot_id 
         AND DATEFROMPARTS(year, month, day) BETWEEN @startDate AND @endDate
         and c.result_status in ('fallback', 'conflict')
         and ui.type = 'text'
         group by message, c.result_status
         order by count desc;
         
        `,
      {
        replacements: {
          bot_id,
          year: start_year,
          month: start_month,
          day: start_day,
          endYear: end_year,
          endMonth: end_month,
          endDay: end_day,
        },
      }
    );

    req.res = response[0];
  } catch (error) {
    console.log(error);
    req.res = [];
  } finally {
    next();
  }
};

const getNotAnsweredInfo = async (req, res, next) => {
  try {
    const { bot_id, message } = req.body;

    const response = await TRUserInteraction.sequelize.query(
      `
     

       select 
        TOP(15)
         ui.conversation_id,
        channel
        from bot_designer_tr_user_interactions ui join bot_designer_tr_cubes c on ui.user_interaction_id = c.user_interaction_id
         where ui.bot_id = :bot_id 
         and c.result_status in ('fallback', 'conflict')
         and ui.message = :message
         group by ui.conversation_id,channel;
         
        `,
      {
        replacements: {
          bot_id,
          message,
        },
      }
    );

    req.res = response[0];
  } catch (error) {
    console.log(error);
    req.res = [];
  } finally {
    next();
  }
};

const getChatHistory = async (req, res, next) => {
  try {
    const { bot_id, conversation_id } = req.body;

    const response = await TRUserInteraction.findAll({
      where: {
        bot_id,
        conversation_id,
      },
      include: [
        {
          model: TRCube,
          attributes: ["tr_cube_id"],

          include: [
            {
              model: TRBotResponse,
              as: "bot_responses",
              include: [
                {
                  model: TRBotResponseSource,
                  through: {
                    attributes: [],
                  },
                  as: "sources",
                },
              ],
            },
          ],
        },
      ],
      order: [["createdAt", "ASC"]],
    });

    req.res = response;
  } catch (error) {
    console.log(error);
    req.res = [];
  } finally {
    next();
  }
};

const getConversations = async (req, res, next) => {
  try {
    const { bot_id } = req.body;
    const page = +req.query.page || 1;
    const pageSize = +req.query.pageSize || 5;

    const response = await TRUserInteraction.findAll({
      attributes: [
        "conversation_id",
        [sequelize.fn("MAX", sequelize.col("createdAt")), "last_interaction"],
        "channel",
        "country",
        "name",
        "user_contact_id",
      ],
      where: {
        bot_id,
      },
      group: [
        "conversation_id",
        "channel",
        "country",
        "name",
        "user_contact_id",
      ],
      order: [[sequelize.fn("MAX", sequelize.col("createdAt")), "DESC"]],
      limit: pageSize,
      offset: (page - 1) * pageSize,
    });

    const total = await TRUserInteraction.count({
      distinct: true,
      col: "conversation_id",
      where: {
        bot_id,
      },
    });

    req.res = {
      data: response,
      pagination: {
        totalItems: total,
        totalPages: Math.ceil(total / pageSize),
        currentPage: page,
        pageSize: pageSize,
        hasNextPage: page < Math.ceil(total / pageSize),
        hasPrevPage: page > 1,
      },
    };
  } catch (error) {
    console.log(error);
    req.res = [];
  } finally {
    next();
  }
};

const getConversationsSearched = async (req, res, next) => {
  try {
    const { bot_id, searchQuery } = req.body;
    const response = await TRUserInteraction.findAll({
      attributes: [
        "conversation_id",
        [sequelize.fn("MAX", sequelize.col("createdAt")), "last_interaction"],
        "channel",
        "country",
        "name",
        "user_contact_id",
      ],
      where: {
        bot_id,

        [Op.or]: [
          {
            name: {
              [Op.like]: `%${searchQuery}%`,
            },
          },
          {
            conversation_id: {
              [Op.like]: `%${searchQuery}%`,
            },
          },
        ],
        // conversation_id: {
        //   [Op.like]: `%${conversation_id}%`,
        // },
      },
      group: [
        "conversation_id",
        "channel",
        "country",
        "name",
        "user_contact_id",
      ],
      order: [[sequelize.fn("MAX", sequelize.col("createdAt")), "DESC"]],
      limit: 10,
    });

    req.res = response;
  } catch (error) {
    console.log(error);
    req.res = [];
  } finally {
    next();
  }
};

const getCheckpointData = async (req, res, next) => {
  try {
    const {
      bot_id,
      start_day,
      start_month,
      start_year,
      end_day,
      end_month,
      end_year,
    } = req.body;

    
    const response = await DialogCheckpoint.sequelize.query(
      `
        DECLARE @startDate DATE = DATEFROMPARTS(:year, :month, :day);
        DECLARE @endDate DATE = CASE 
            WHEN :endYear IS NOT NULL AND :endMonth IS NOT NULL AND :endDay IS NOT NULL 
            THEN DATEFROMPARTS(:endYear, :endMonth, :endDay)
            ELSE GETDATE() 
        END;
    SELECT 
    a.tag,
    a.dialog_id,
    a.channel,
    COUNT(DISTINCT a.conversation_id) as unique_users,
    COUNT(a.conversation_id) as users,
    b.dialog_name
FROM [dbo].[bot_designer_dialog_checkpoints] as a
left join [dbo].[bot_designer_dialogs] as b 
on a.dialog_id = b.dialog_id
WHERE a.bot_id = :bot_id  
AND a.createdAt BETWEEN @startDate AND @endDate
GROUP BY a.channel, a.tag, a.dialog_id, b.dialog_name
         
        `,
      {
        replacements: {
          bot_id,
          year: start_year,
          month: start_month,
          day: start_day,
          endYear: end_year,
          endMonth: end_month,
          endDay: end_day,
        },
      }
    );

    req.res = response[0];
  } catch (error) {
    console.log(error);
    req.res = [];
  } finally {
    next();
  }
};
module.exports = {
  getStats,
  getHeatmap,
  getMessagesUserDate,
  getAnsweredDate,
  getMessagesUserChannel,
  getFunctionsRequestsCount,
  getUserPerYear,
  getLastMessages,
  getMessagesUserCountry,
  getTopNotAnswered,
  getNotAnsweredInfo,
  getChatHistory,
  getConversations,
  getConversationsSearched,
  getCheckpointData,
};
