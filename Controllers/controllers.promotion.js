const Promotion = require("../Models/Promotion");
const jwt = require("jsonwebtoken");
const secret = require("../config/appConfig").secret;
const {
 builderUrl
} = require("../constants");

const get = (req, res) => {
  res.send(req.promotion);
};

const set = (req, res, next) => {
  const data = req.body;
  Promotion.create({
    promotion_percentage: data.promotion_percentage,
    promotion_value: data.promotion_value
  }).then((promotion) => {
    req.promotion = promotion;
    return next();
  });
};

const update = async (req, res, next) => {
  const promotion = req.promotion;
  const data = { ...req.body };
  promotion.updateInfo(data);
  await promotion.save().then(() => next());
};

const purge = (req, res, next) => {
  const promotion_id = req.body.promotion_id;
  Promotion.destroy({
    where: {
        promotion_id,
    },
  }).then(() => {
    res.send({ message: "Promotion was deleted" });
  });
};

const generatePromotionURL = (req, res, next) => {
    const promotion_id = req.body.promotion_id;
    const promotion_token = jwt.sign(
        {
          promotion_id
        },
        secret
    );
    return res.send({
        promotion_url: builderUrl + "?promotion=" + promotion_token 
    });
};

module.exports = {
  get,
  set,
  update,
  purge,
  generatePromotionURL
};
