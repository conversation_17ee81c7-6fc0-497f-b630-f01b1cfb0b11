const DialogCheckpoint = require("../Models/DialogCheckpoint");

const get = (req, res) => {
  res.send(req.dialogCheckpoint);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  DialogCheckpoint.create(data).then((dialogCheckpoint) => {
    req.dialogCheckpoint = dialogCheckpoint;
    next();
  });
};

const update = async (req, res, next) => {
  const dialogCheckpoint = req.dialogCheckpoint;
  const data = { ...req.body };
  dialogCheckpoint.updateInfo(data);
  req.dialogCheckpoint = dialogCheckpoint;
  await dialogCheckpoint.save();
  return next();
};

const purge = (req, res, next) => {
  const dialog_checkpoint_id =
    req.body.dialog_checkpoint_id || req.dialogCheckpoint.dialog_checkpoint_id;
  DialogCheckpoint.destroy({ where: { dialog_checkpoint_id } }).then(() => {
    res.send({ message: "Dialog checkpoint deleted" });
  });
};

const getMany = (req, res) => {
  res.send(req.dialogCheckpoints);
};

module.exports = {
  get,
  update,
  set,
  purge,
  getMany,
};
