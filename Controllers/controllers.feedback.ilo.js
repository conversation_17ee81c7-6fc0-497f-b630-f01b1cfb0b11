const FeedbackQuestionsILO = require("../Models/FeedbackQuestionsIlo");
const FeedbackLogILO = require("../Models/FeedbackLogIlo");
const Feedback = require("../Models/FeedbackIlo")


const set = (req, res, next) => {
    const data = { ...req.body };
    Feedback.create({
        ...data
    }).then((feedback) => {
        req.feedback = feedback;
        return next();
    });
};

const setQuestion = (req, res, next) => {
    const data = { ...req.body };
    FeedbackQuestionsILO.create({
        ...data
    }).then((feedbackQuestion) => {
        req.feedbackQuestion = feedbackQuestion;
        return next();
    });
};

const setLog = (req, res, next) => {
    const data = { ...req.body };
    FeedbackLogILO.create({
        ...data
    }).then((feedbackLog) => {
        req.feedbackLog = feedbackLog;
        return next();
    });
};

const get = (req, res) => {
    res.send(req.feedback);
};

const getAll = (req, res) => {
    res.send(req.feedbacks);
};

const getLog = (req, res) => {
    res.send(req.feedbackLog);
};

const getLogAll = (req, res) => {
    res.send(req.feedbackLogs);
};

const getQuestion = (req, res) => {
    res.send(req.feedbackQuestion);
};

const getQuestionAll = (req, res) => {
    res.send(req.feedbackQuestions);
};

const purge = (req, res, next) => {
    const feedback_id = req.body.feedback_id;
    Feedback.destroy({
        where: {
            feedback_id
        },
    }).then(() => {
        res.send({ message: "Feedback data deleted " });
    });
};


const purgeLog = (req, res, next) => {
    const feedback_log_id = req.body.feedback_log_id;
    FeedbackLogILO.destroy({
        where: {
            feedback_log_id
        },
    }).then(() => {
        res.send({ message: "Feedback Log data deleted " });
    });
};

const purgeQuestion = (req, res, next) => {
    const feedback_question_id = req.body.feedback_question_id;
    FeedbackQuestionsILO.destroy({
        where: {
            feedback_question_id
        },
    }).then(() => {
        res.send({ message: "Feedback Question data deleted " });
    });
};

module.exports = {
    get,
    getAll,
    set,
    setLog,
    setQuestion,
    getLog,
    getLogAll,
    getQuestion,
    getQuestionAll,
    purge,
    purgeLog,
    purgeQuestion
};
