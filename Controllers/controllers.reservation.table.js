const ReservationTable = require("../Models//Reservation.Table");
const ReservationOrder = require("../Models//Reservation.Order");

const get = (req, res) => {
  res.send(req.table);
};

const getAll = (req, res, next) => {
  res.send(req.tables);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  ReservationTable.create(data)
    .then((table) => {
      req.table = table;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const setAll = (req, res, next) => {
  Offer.bulkCreate(req.body)
    .then((tables) => {
      req.tables = tables;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const update = async (req, res, next) => {
  const table = req.table;
  const data = { ...req.body };
  table.updateInfo(data);
  req.table = table;
  await table.save();
  return next();
};

const purge = (req, res, next) => {
  const data = { ...req.body };
  ReservationTable.destroy({
    where: {
      table_id: data.table_id,
    },
  }).then(() => {
    res.send({ message: "table deleted successfully" });
  });
};

const setOrder = (req, res, next) => {
  const data = { ...req.body };

  ReservationOrder.create({
    bot_id: data.bot_id,
    room_id: data.room_id,
    table_id: data.table_id,
    bot_user_id: data.bot_user_id,
    guests: data.guests,
    to: data.to,
    from: data.from,
  }).then((order) => {
    res.send(order);
  });
};

module.exports = {
  get,
  set,
  update,
  purge,
  getAll,
  setAll,
  setOrder,
};
