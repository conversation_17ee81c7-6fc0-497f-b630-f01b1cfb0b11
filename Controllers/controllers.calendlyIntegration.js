const CalendlyIntegration = require("../Models/CalendlyIntegration");

const get = (req, res) => {
  return res.send(req.calendlyIntegration);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  const botId = data.bot_id;

  CalendlyIntegration.findOne({ where: { bot_id: botId } }).then(
    (existingCalendlyIntegration) => {
      if (existingCalendlyIntegration) {
        existingCalendlyIntegration.updateInfo(data);
        existingCalendlyIntegration.save();
        req.calendlyIntegration = existingCalendlyIntegration;
        return next();
      } else {
        CalendlyIntegration.create(data)
          .then((calendlyIntegration) => {
            req.calendlyIntegration = calendlyIntegration;
            return next();
          })
          .catch((error) => {
            res.status(500).send({ message: "Internal server error" + error });
          });
      }
    }
  );
};

const purge = (req, res, next) => {
  const bot_id = req.body.bot_id;
  CalendlyIntegration.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    res.send({ message: "Calendly integration data deleted" });
  });
};

const update = async (req, res, next) => {
  const data = req.body;
  const calendlyIntegration = req.calendlyIntegration;
  if (req.body && calendlyIntegration) {
    calendlyIntegration.updateInfo(data);
    await calendlyIntegration.save();
    return next();
  } else {
    return next();
  }
};
module.exports = { update, purge, set, get };
