const { Op } = require("sequelize");
const PayoneRecord = require("../Models/PayoneRecord");
const WaTemplateLog = require("../Models/Wa.Template.Log");

const get = (req, res) => {
  return res.send(req.payoneRecord);
};

const getTLog = (req, res) => {
  return res.send(req.tLog);
};

const getAll = (req, res) => {
  PayoneRecord.findAll({
    where: {
      payment: {
        [Op.not]: null,
        [Op.not]: "deleted",
      },
    },
  }).then((payoneRecords) => {
    return res.send(payoneRecords);
  });
}

const set = (req, res, next) => {
  const data = { ...req.body };
  PayoneRecord.create(data)
    .then((payoneRecord) => {
      req.payoneRecord = payoneRecord;
      return next();
    })
    .catch(() => {
      res.status(409).send({ message: "Payone record already exists" });
    });
};

const purge = (req, res, next) => {
  const invoice_id = req.body.invoice_id;
  PayoneRecord.destroy({
    where: {
      invoice_id,
    },
  }).then(() => {
    res.send({ message: "payone record data deleted" });
  });
};

const update = async (req, res, next) => {
  const payoneRecord = req.payoneRecord;
  if (req.body && payoneRecord) {
    payoneRecord.updateInfo({ ...req.body });
    await payoneRecord.save();
    return next();
  } else {
    return next();
  }
};

const insertLog = (req, res, next) => {
  const data = { ...req.body };
  WaTemplateLog.create(data).then((tLog) => {
    req.tLog = tLog;
    return next();
  }).catch(()=>{
    res.status(409).send({ message: "An error occurred" });
  });
};

module.exports = { get, set, update, purge, getAll, getTLog, insertLog };
