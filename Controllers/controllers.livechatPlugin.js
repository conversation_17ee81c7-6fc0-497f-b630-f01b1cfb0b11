const LiveChatPlugin = require("../Models/LiveChatPlugin");
const get = (req, res) => {
  res.send(req.Livechatplugin);
};

const find = (req, res, next) => {
  const bot_id = req.query.bot_id || req.body.bot_id;
  LiveChatPlugin.findOne({ where: { bot_id } }).then((Livechatplugin) => {
    if (!Livechatplugin) {
      return res
        .status(401)
        .send({ message: `Live Chat not found for bot_id = ${bot_id}` });
    }
    req.Livechatplugin = Livechatplugin;
    return next();
  });
};
const set = (req, res, next) => {
  const bot_id = req.body.bot_id;
  const seat_num = req.body.seat_num;
  LiveChatPlugin.findOne({ where: { bot_id } }).then((Livechatplugin) => {
    if (!Livechatplugin) {
      LiveChatPlugin.create({
        bot_id,
        seat_num,
      })
        .then((Livechatplugin) => {
          req.Livechatplugin = Livechatplugin;
          return next();
        })
        .catch((error) => {
          res.send(error);
        });
    } else {
      return res.status(401).send({
        message: `Live Chat plugin already exisit for bot_id = ${bot_id}`,
      });
    }
  });
};

const update = async (req, res, next) => {
  const LiveChatData = req.body;
  LiveChatPlugin.findOne({
    where: {
      bot_id: LiveChatData.bot_id,
    },
  }).then(async (livechat) => {
    livechat.updateInfo({ ...LiveChatData });
    await livechat.save();
    return next();
  });
};

module.exports = {
  get,
  set,
  update,
  find,
};
