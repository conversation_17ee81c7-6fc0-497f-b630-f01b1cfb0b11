const ReservationRoom = require("../Models//Reservation.Room");

const get = (req, res) => {
  res.send(req.room);
};

const getAll = (req, res, next) => {
  res.send(req.rooms);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  ReservationRoom.create(data)
    .then((room) => {
      req.room = room;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const setAll = (req, res, next) => {
  Offer.bulkCreate(req.body)
    .then((rooms) => {
      req.rooms = rooms;
      return next();
    })
    .catch((e) => res.status(422).send(e));
};

const update = async (req, res, next) => {
  const room = req.room;
  const data = { ...req.body };
  room.updateInfo(data);
  req.room = room;
  await room.save();
  return next();
};

const purge = (req, res, next) => {
  const data = { ...req.body };
  ReservationRoom.destroy({
    where: {
      room_id: data.room_id,
    },
  }).then(() => {
    res.send({ message: "Room deleted successfully" });
  });
};

module.exports = {
  get,
  set,
  update,
  purge,
  getAll,
  setAll,
};
