const { uploadRag, deleteRag } = require("../calls/rag.calls");
const LLMIntegration = require("../Models/LLMIntegration");
const UnstructuredResources = require("../Models/UnstructuredResources");

const get = (req, res) => {
  return res.send(req.unstructuredResource);
};

const getAll = (req, res) => {
  res.send(req.unstructuredResources);
};

const set = (req, res, next) => {
  const data = { ...req.body };

  UnstructuredResources.create(data)
    .then((unstructuredResource) => {
      req.unstructuredResource = unstructuredResource;
      return next();
    })
    .catch((error) => {
      res.status(500).send({ message: "Internal server error" });
    });
};

const setMany = async (req, res, next) => {
  const data = [...req.body];
  const llm = await LLMIntegration.findOne({
    where: { bot_id: data[0].bot_id },
  });
  const separatorsText = data[0]?.chunkConfig?.separators;
  let separatorsArr = [];
  if (separatorsText && typeof separatorsText === "string") {
    let tempSeparators = separatorsText.trim().split("~~");
    for (let i = 0; i < tempSeparators.length; i++) {
      if (tempSeparators[i] === "\\n") {
        separatorsArr.push("\n");
      } else if (tempSeparators[i] === "\\t") {
        separatorsArr.push("\t");
      }
      separatorsArr.push(tempSeparators[i]);
    }
  }

  const separator = data[0]?.chunkConfig?.separator;
  if (separator) {
    for (let i = 0; i < separator.length; i++) {
      if (separator[i] === "\\n") {
        separator[i] = "\n";
      } else if (separator[i] === "\\t") {
        separator[i] = "\t";
      }
    }
  }


      for(let i = 0; i< data.length; i++){
        try {
          const ragRes = await uploadRag({
            llm_config: {
              llm_key: llm.dataValues.llm_key,
              llm_type: llm.dataValues.llm_type,
              llm_model: llm.dataValues.llm_model,
              llm_temperature: llm.dataValues.llm_temperature,
            },
            store_config: {
              store_url: llm.dataValues.store_url,
              collection_name: llm?.dataValues?.collection_name,
              store_type: llm?.dataValues?.store_type,
            },
            config: {
              chunk_overlap:
                data[i]?.chunkConfig?.chunk_overlap ||
                llm?.dataValues?.chunk_overlap ||
                null,
              chunk_size:
                data[i]?.chunkConfig?.chunk_size ||
                llm?.dataValues?.chunk_size ||
                null,
              chunking_strategy:
                data[i]?.chunkConfig?.chunking_strategy ||
                llm?.dataValues?.chunk_methodology ||
                null,
              breakpoint_threshold_type:
                data[i]?.chunkConfig?.breakpoint_threshold_type || null,
              separator: separator || null,
              separators: separatorsArr.length ? separatorsArr : null,
              keep_separator: data[i]?.chunkConfig?.keep_separator || null,
              tokens_per_chunk: data[i]?.chunkConfig?.tokens_per_chunk || null,
              model_name: data[i]?.chunkConfig?.model_name || null,
  
              persona: llm?.dataValues.persona || null,
            },
            source: {
              source_type:
                data[i].resource_type === "pdf" || "text" || "docx" || "xlsx"
                  ? "document"
                  : data[i].resource_type,
              document: {
                source_type: data[i].resource_type,
                document_url: data[i].resource_url,
              },
            },
          });
          if ("status_code" in ragRes && ragRes?.status_code === 200) {
            UnstructuredResources.create(data[i]);
          }  
        } catch (error) {
        }
      }
  return next();
};

const purge = async (req, res, next) => {
  const resource_id = req.body.resource_id;
  const data = await UnstructuredResources.findOne({
    where: { resource_id },
  });
  const llm = await LLMIntegration.findOne({
    where: { bot_id: data.bot_id },
  });
  const rag = await deleteRag({
    llm_config: {
      llm_key: llm.dataValues.llm_key,
      llm_type: llm.dataValues.llm_type,
      llm_model: llm.dataValues.llm_model,
      llm_temperature: llm.dataValues.llm_temperature || 0,
    },
    store_config: {
      store_url: llm?.dataValues?.store_url,
      collection_name: llm?.dataValues?.collection_name,
      store_type: llm?.dataValues?.store_type,
    },
    config: {
      chunk_overlap: llm?.dataValues?.chunk_overlap || null,
      chunk_size: llm?.dataValues?.chunk_size || null,
      persona: llm?.dataValues.persona,
    },
    file_name: data.dataValues.resource_name,
  });
  console.log("😲😲😲 ", rag);
  UnstructuredResources.destroy({
    where: {
      resource_id,
    },
  }).then(() => {
    res.send({ message: "resource data deleted" });
  });
};

const purgeMany = async (req, res, next) => {
  const resource_ids = req.body.unstructuredResources;
  const bot_id = req.body.bot_id;

  const llm = await LLMIntegration.findOne({
    where: { bot_id },
  });
  const resources = await UnstructuredResources.findAll({
    where: {
      resource_id: resource_ids,
      bot_id: bot_id,
    },
  });
  await Promise.all(
    resources.map(async (resource) => {
      const rag = await deleteRag({
        llm_config: {
          llm_key: llm.dataValues.llm_key,
          llm_type: llm.dataValues.llm_type,
          llm_model: llm.dataValues.llm_model,
          llm_temperature: llm.dataValues.llm_temperature || 0,
        },
        store_config: {
          store_url: llm.dataValues.store_url,
          collection_name: llm?.dataValues?.collection_name,
          store_type: llm?.dataValues?.store_type
        },
        config: {
          chunk_overlap: llm?.dataValues?.chunk_overlap || 50,
          chunk_size: llm?.dataValues?.chunk_size || 300,
          persona: llm?.dataValues.persona,
        },
        file_name: resource.dataValues.resource_name,
      });
      console.log("😲😲😲 ", rag);
    })
  );
  UnstructuredResources.destroy({
    where: {
      bot_id: bot_id,
      resource_id: resource_ids,
    },
  }).then(() => {
    return next();
  });
};

const update = async (req, res, next) => {
  const data = req.body;
  const unstructuredResource = req.unstructuredResource;
  if (req.body && unstructuredResource) {
    unstructuredResource.updateInfo(data);
    await unstructuredResource.save();
    return next();
  } else {
    return next();
  }
};

const updateMany = async (req, res, next) => {
  const data = [...req.body];
  const unstructuredResources = req.unstructuredResources;

  for (var i = 0; i < unstructuredResources.length; i++) {
    var resource = unstructuredResources[i];
    var resourceData = data.find((a) => a.resource_id === resource.resource_id);
    resource.updateInfo(resourceData);
    await resource.save();
  }

  return next();
};

module.exports = {
  get,
  getAll,
  set,
  setMany,
  purge,
  purgeMany,
  update,
  updateMany,
};
