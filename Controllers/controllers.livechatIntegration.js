const LiveChatIntegration = require("../Models/LiveChatIntegration");
const LiveChatIntegrationSession = require("../Models/LiveChatIntegrationSession");
const LiveChatIntegrationSessionTransaction = require("../Models/LiveChatIntegrationTransaction");

const get = (req, res) => {
  res.send(req.livechatIntegration);
};

const getAll = (req, res) => {
  res.send(req.livechatIntegrations);
};

const getOneSession = (req, res) => {
    res.send(req.livechatIntegrationSession);
}

const getOneSessionTr = (req, res) => {
  res.send(req.livechatIntegrationSessionTransaction);
}

const getAllSessions = (req, res) => {
    res.send(req.livechatIntegrationSessions);
}

const updateSession = async (req, res, next) => {
  const livechatIntegrationSession = req.livechatIntegrationSession;
  if (req.body && livechatIntegrationSession) {
    livechatIntegrationSession.updateInfo({ ...req.body });
    await livechatIntegrationSession.save();
    return next();
  } else {
    res.send(livechatIntegrationSession);
    return next();
  }
}

const set = (req, res, next) => {
  const data = { ...req.body };
  LiveChatIntegration.create(data)
    .then((livechatIntegration) => {
      req.livechatIntegration = livechatIntegration;
      return next();
    })
    .catch(() => {
      res.status(409).send({ message: "livechat integration is already exists" });
    });
};

const setSession = (req, res, next) => {
    const data = { ...req.body };
    LiveChatIntegrationSession.create(data)
      .then((livechatIntegrationSession) => {
        req.livechatIntegrationSession = livechatIntegrationSession;
        return next();
      })
      .catch(() => {
        res.status(409).send({ message: "livechat integration session is already exists" });
      });
}; 

const setSessionTr = (req, res, next) => {
  const data = { ...req.body };
  LiveChatIntegrationSessionTransaction.create(data)
    .then((livechatIntegrationSessionTransaction) => {
      req.livechatIntegrationSessionTransaction = livechatIntegrationSessionTransaction;
      return next();
    })
    .catch(() => {
      res.status(409).send({ message: "livechat integration session transaction is already exists" });
    });
}; 

const purge = (req, res, next) => {
  const bot_id = req.body.bot_id;
  LiveChatIntegration.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    res.send({ message: "livechat integration data deleted" });
  });
};

const update = async (req, res, next) => {
  const livechatIntegration = req.livechatIntegration;
  if (req.body && livechatIntegration) {
    livechatIntegration.updateInfo({ ...req.body });
    await livechatIntegration.save();
    return next();
  } else {
    return next();
  }
};

module.exports = { 
    get, 
    set, 
    update, 
    purge,
    getOneSession,
    getAllSessions,
    setSession,
    setSessionTr,
    getOneSessionTr,
    getAll,
    updateSession
};
