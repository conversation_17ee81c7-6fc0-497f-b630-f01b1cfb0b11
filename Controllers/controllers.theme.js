const Theme = require("../Models/Theme");

const get = (req, res) => {
  return res.send(req.theme); // theme record but Theme model
};
const getAll = (req, res) => {
  return res.send(req.themes);
};

const create = (req, res, next) => {
  const data = req.body;
  console.log(data, "dataaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa");

  Theme.create(data).then((theme) => {
    req.theme = theme;
    return next();
  });
  // .catch((e) => {
  //   res.send({ e, message: "theme id is already used" });
  // });
};

const update = async (req, res, next) => {
  const theme = req.theme;
  const data = req.body;

  theme.updateInfo(data);
  await theme.save();
  return next();
};

module.exports = { create, get, update, getAll };
