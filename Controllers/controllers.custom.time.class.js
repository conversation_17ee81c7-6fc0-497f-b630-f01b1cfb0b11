const Custom_WorkingHours = require("../Models/Custom.WorkingHours");
const Customs_Class = require("../Models/Custom.Class");
const sequelize = require("../db");



const createWorkingHourForClass = (req, res, next) => {
    const workinghoursData = req.body.workinghours;
    const class_id = req.body.class_id;
    const bot_id = req.body.bot_id;
  
    Custom_WorkingHours.bulkCreate(
      workinghoursData.map((a) => {
        return { ...a, bot_id, class_id };
      })
    ).then((custom_workingHours) => {
      req.custom_workingHours = custom_workingHours;
      return next();
    });
  };

  const findClassById = async (req, res, next) => {
    const class_desc = await Customs_Class.findOne({
      where: {
        class_id: req.body.class_id || req.query.class_id
      },
    });
    if (!class_desc) {
      return res
        .status(401)
        .send({ message: "not found" });
    }
    req.class_desc = class_desc;
    return next();
  };
  const findworkingHourClass = (req, res, next) => {
    Custom_WorkingHours.findAll({
      where: {
        class_id: req.class_desc.class_id,
      },
    }).then((custom_workingHours) => {
      if (!custom_workingHours) {
        return res.status(401).send({ message: "classhour not found" });
      }
      req.custom_workingHours = custom_workingHours;
      return next();
    });
  };

  const getClassTime = async (req, res) => {
    const bot_id= req.query.bot_id;
   const data= await sequelize.query("designer_custom_get_class_time :bot_id ", {
        replacements: {
          bot_id: bot_id,
        },
      });
      return res.send(data[0]);
    };

  const getWorkingHoursClass = (req,res)=>{
    res.send(req.custom_workingHours);
  }

  module.exports = {
    createWorkingHourForClass,
    getWorkingHoursClass,
    findClassById,
    findworkingHourClass,
    getWorkingHoursClass,
    getClassTime
  };