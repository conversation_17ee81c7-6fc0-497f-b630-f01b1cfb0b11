const BotDesign = require("../Models/Bot.Design");

const get = (req, res) => {
  res.send(req.design);
};

const find = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  BotDesign.findOne({ where: { bot_id } }).then((design) => {
    req.design = design;
    return next();
  });
};

const set = (req, res, next) => {
  const bot_id = req.bot.bot_id;
  BotDesign.create({
    bot_id,
  }).then((design) => {
    req.design = design;
    return next();
  });
};

const update = async (req, res, next) => {
  const design = req.design;
  const designData = req.body.design;
  if (designData && design) {
    design.updateInfo({ ...designData });
    await design.save();
    return next();
  } else {
    return next();
  }
};

const purge = (req, res, next) => {
  const data = { ...req.body };
  Bot.destroy({
    where: {
      bot_id: data.bot_id,
    },
  }).then(() => {
    return next();
  });
};

module.exports = {
  get,
  set,
  update,
  purge,
  find,
};
