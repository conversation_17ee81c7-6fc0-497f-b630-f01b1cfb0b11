const FallbackPlugin = require("../Models/FallbackPlugin");

const get = (req, res) => {
  res.send(req.fallbackplugin);
};

const set = (req, res, next) => {
  const data = req.body;
  FallbackPlugin.create({
    bot_id: data.bot_id,
  })
    .then((fallbackplugin) => {
      req.fallbackplugin = fallbackplugin;
      return next();
    })
    .catch((e) => res.send({ e, message: "an error occured" }));
};

const update = async (req, res, next) => {
  const fallbackPlugin = req.fallbackplugin;
  const data = { ...req.body };
  fallbackPlugin.updateInfo(data);
  req.fallbackPlugin = fallbackPlugin;
  await fallbackPlugin.save();
  return next();
};

module.exports = { get, set, update };
