const Editor = require("../Models/Editor");

const get = (req, res) => {
  res.send(req.editor);
};

const getAll = (req, res) => {
  res.send(req.editors);
};

const set = (req, res, next) => {
  const data = req.body;
  Editor.create(data).then((editor) => {
    req.editor = editor;
    return next();
  });
};

const update = async (req, res, next) => {
  const editor = req.editor;
  const editorData = req.body;
  if (editorData && editor) {
    editor.updateInfo({ ...editorData });
    await editor.save();
    return next();
  } else {
    return next();
  }
};

const purge = (req, res, next) => {
  const data = { ...req.body };
  Editor.destroy({
    where: {
      editor_id: data.editor_id,
    },
  }).then(() => {
    return res.send({ message: "editor deleted successfully" });
  });
};

const purgeMany = (req, res, next) => {
  const editor_ids = req.body.editors;
  const bot_id = req.body.bot_id;

  Editor.destroy({
    where: {
      editor_id: editor_ids,
      bot_id: bot_id,
    },
  }).then(() => {
    return next();
  });
};


const checkPrivilages = (req, res, next) => {
  const editor = req.editor;
  const user = req.user;
  const bot = req.bot;
  if (bot.user_id === user.user_id) {
    return res.send({ message: "User is an admin" });
  } else if (!editor) {
    return res.status(401).send({ message: "User is not authorizated" });
  } else {
    return next();
  }
};

module.exports = {
  get,
  getAll,
  set,
  update,
  purge,
  checkPrivilages,
  purgeMany,
};
