const Bot = require('../Models/Bot');
const DialogVersionControl = require ('../Models/DialogVersionControl');
const Dialogs = require('../Models/Dialogs');
const {uploadFile, getFile, generateStorageId} = require ('../calls/file.call');

const setDVC= async(req, {dialog_id}) =>{
    try {
        const dialog = await Dialogs.findOne({
            where: {dialog_id: dialog_id},
        });
        const url = dialog.url;
        const dialogversioncontrol = await  DialogVersionControl.create({dialog_id, url, is_staging:true, is_live:false})
        await dialogversioncontrol.save();
    } catch (error) {
        res.status(400).send({message: "Error creating DVC:", error});
    }
}

//for testing (create a DVC with out the dialog (DVCs are created automatically when creating a Dialog))
const set = async(req,res,next) =>{
    const {dialog_id, is_staging, is_live, bot_id, user_id} = req.body;
    const bot = await Bot.findOne({
        where:{bot_id: bot_id},
    })
    const url = `Bots/${bot.file_name}/Dialogs/${generateStorageId(12)}.json`;
    try {
        const dialogversioncontrol = await  DialogVersionControl.create({dialog_id, url, is_staging, is_live});
        await uploadFile({
            path: url,
            file_body: null,
        });
        await dialogversioncontrol.save();
        req.dialogversioncontrol = dialogversioncontrol;
        return next()
    } catch (error) {
        console.error("Error creating DVC:", error);
        res.send({message: 'Error creating DVC:', error})
    }
}

//returns all
const get = async(req, res, next) =>{
    res.send(req.dialogversioncontrol);
}

//returns all the DVCs that have the same dialog_id
const getAll = async(req, res, next) =>{
    const dialog_id = req.query.dialog_id;
    const dialogversioncontrol = await DialogVersionControl.findAll({
        where: { dialog_id: dialog_id }
    });
    res.send(dialogversioncontrol);
}

//returns all the DVCs that have the same dialog_id && is_staging===false
const getIsStagingFalse = async(req, res, next) =>{
    const dialog_id = req.query.dialog_id;
    const dialogversioncontrol = await DialogVersionControl.findAll({
        where: { dialog_id: dialog_id , is_staging : false}
    });
    res.send(dialogversioncontrol);
};

//return is_staging === true, if it doesnt exist return the is_live === true 
const getIsStagingTrue = async (req, res, next) => {
    const dialog_id = req.query.dialog_id;

    try {
      const dialogversioncontrol = await DialogVersionControl.findOne({
        where: { dialog_id: dialog_id, is_staging: true }
      });
  
      const dialog = await Dialogs.findOne({
        where: { dialog_id: dialog_id }
      });
  
      if (!dialog) {
        return res.sendStatus(404);
      }
  
      const bot = await Bot.findOne({
        where: { bot_id: dialog.bot_id }
      });
  
      if (!bot) {
        return res.sendStatus(404);
      }
  
      if (!dialogversioncontrol) {
        const dialogversioncontrolLive = await DialogVersionControl.findOne({
          where: { dialog_id: dialog_id, is_live: true }
        });
  
        if (!dialogversioncontrolLive) {
          // Create new DVC
          const newUrl = dialog.url;
          const DVCwithoutdialog = await DialogVersionControl.create({
            dialog_id: dialog_id,
            is_live: true,
            url: newUrl
          });
          const confg = await getFile(dialog.url);
          return res.send({
            ...dialog.dataValues,
            user_id: bot.user_id,
            url: DVCwithoutdialog.url,
            configuration: JSON.parse(confg?.file_data || "{}")
          });
        }
        const confg = await getFile(dialogversioncontrolLive.url);
        return res.send({
          ...dialog.dataValues,
          user_id: bot.user_id,
          url: dialogversioncontrolLive.url,
          configuration: JSON.parse(confg?.file_data || "{}")
        });
      }
  
      const configuration = await getFile(dialogversioncontrol.url);
      res.send({
        ...dialog.dataValues,
        user_id: bot.user_id,
        url: dialogversioncontrol.url,
        configuration: JSON.parse(configuration?.file_data || "{}")
      });
    } catch (error) {
      console.error(error);
      res.sendStatus(500);
    }
  };

//returns a single DVC (send the dvc_id) used when the pre versions clicked (one of the dialogs)
const getDVC = async(req, res, next)=>{
    const dialog_version_control_id = req.query.dialog_version_control_id;
    const dialogversioncontrol = await DialogVersionControl.findOne({
        where: { dialog_version_control_id: dialog_version_control_id}
    });
    const dialog_id = dialogversioncontrol.dialog_id;
    const dialog = await Dialogs.findOne({
        where: {dialog_id: dialog_id}
    });
    const configuration = await getFile(dialogversioncontrol.url);
    res.send({
        ...dialog.dataValues,
        url: dialogversioncontrol.url,
        configuration: JSON.parse(configuration?.file_data||"{}")
    });
}

// update the url body (digital ocean) where is_staging === true 
const update = async (req, res, next)=>{
    const {dialog_id, bot_id, user_id} = req.query;
    const bot = await Bot.findOne({where: {bot_id: bot_id}});
    if (!dialog_id) {
        return res.status(400).send({ message: "dialog_id is required" });
      }
    const dialogversioncontrol = await DialogVersionControl.findOne({
        where: { dialog_id: dialog_id, is_staging:true }
    });
    if (dialogversioncontrol) {
        const url = dialogversioncontrol.url;
        await uploadFile({
            path: url,
            file_body: JSON.stringify(req.body)
        });
        await DialogVersionControl.update(
            { is_staging: true },
            { where: { is_staging: true, dialog_id: dialog_id } }
        );
        return res.status(200).send({message: "Update successful"});
    }else {
        const newUrl = `Bots/${bot.file_name}/Dialogs/${generateStorageId(12)}.json`;
        uploadFile({
            path: newUrl,
            file_body: JSON.stringify(req.body)
        });
        await DialogVersionControl.create({
            dialog_id: dialog_id,
            is_staging: true,
            url: newUrl
        });
        return res.status(200).send({message: "Update successful"});
    
    }   
    
}

//publish the DVC where the is_staging === true 
const publish = async(req, res, next)=>{
    const {dialog_id, bot_id, user_id} = req.query;
    const bot = await Bot.findOne({
        where: {bot_id: bot_id},
    });
    const dialogversioncontrol = await DialogVersionControl.findOne({
        where: { dialog_id: dialog_id, is_staging:true }
    });
    const dialogversioncontrolold = await DialogVersionControl.findOne({
        where: {dialog_id: dialog_id, is_live: true}
    });

    if(!dialogversioncontrol){
        dialogversioncontrolold.update({is_staging: true, is_live: false})
    }

    if(dialogversioncontrol && dialogversioncontrolold){
        const test = await getFile(dialogversioncontrol.url);
        const test2 = await getFile(dialogversioncontrolold.url);
        const comapre = JSON.stringify(test) === JSON.stringify(test2)
        if(comapre){ 
            return res.status(200).send({message: "already published"})
        }
    }
    console.log('starting to publish')
    await DialogVersionControl.update(
        { is_live: false },
        { where: { is_live: true, dialog_id: dialog_id } }
    );
    const newUrl = `Bots/${bot.file_name}/Dialogs/${generateStorageId(12)}.json`;

    uploadFile({
        path: newUrl,
        file_body: JSON.stringify(req.body)
    });
    await DialogVersionControl.create({
        dialog_id: dialog_id,
        is_live: true,
        is_staging: false,
        url: newUrl
    });
    return res.status(200).send({message:"published"});
}

//change a DVC to make is_staging === true (make it editable)
const changeIsStaging = async (req, res, next) => {
    const {dialog_version_control_id, bot_id, user_id} = req.query;
    const bot = await Bot.findOne({
        where: {bot_id: bot_id},
    });
    const dialogversioncontrol = await DialogVersionControl.findOne({
        where: { dialog_version_control_id: dialog_version_control_id }
    });
    if (!dialogversioncontrol) {
        return res.status(404).send({ message: 'Dialog version control not found' });
    }
    if(dialogversioncontrol.is_staging){
        return res.status(200).send({message:"still the same"});
    }
    const dialog_id = dialogversioncontrol.dialog_id;
    await DialogVersionControl.update(
        { is_staging: false }, 
        { 
            where: { dialog_id: dialog_id, is_staging: true }
        }
    );
    const dialog = await Dialogs.findOne({
        where: {dialog_id: dialog_id}
    });
    const filecontent = await getFile(dialogversioncontrol.url);

    if (dialogversioncontrol.is_live) {
        const newURL = `Bots/${bot.file_name}/Dialogs/${generateStorageId(12)}.json`;
        dialogversioncontrol.is_staging = false;
        uploadFile({
            path: newURL,
            file_body: filecontent.file_data
        });
        await DialogVersionControl.create({
            dialog_id: dialog_id,
            url: newURL,
            is_staging: true,
            is_live: false,
        });
    } else {
        dialogversioncontrol.is_staging = true;
        await dialogversioncontrol.save();
    }
    res.send({
        ...dialog.dataValues,
        url: dialogversioncontrol.url,
        configuration: JSON.parse(filecontent?.file_data || "{}"),
    });

};

//change which one is the live one (publish an old DVC)
const changeIsLive = async(req, res, next) => {
    const {dialog_version_control_id, bot_id, user_id} = req.query;
    const bot = await Bot.findOne({
        where:{bot_id: bot_id},
    })
    const dialogversioncontrol = await DialogVersionControl.findOne({
        where: { dialog_version_control_id: dialog_version_control_id }
    });
    if(dialogversioncontrol.is_live === true){
        return res.status(200).send(dialogversioncontrol);
    }
    const dialog_id = dialogversioncontrol.dialog_id;
    await DialogVersionControl.update({ is_live: false }, {
        where: { dialog_id: dialog_id, is_live: true }
    });
    const dialog = await Dialogs.findOne({
        where: {dialog_id: dialog_id}
    })
    const config = await getFile(dialogversioncontrol.url);
    if(dialogversioncontrol.is_staging === true){
        const newURL = `Bots/${bot.file_name}/Dialogs/${generateStorageId(12)}.json`;
        dialogversioncontrol.is_staging = false;
        await DialogVersionControl.create({
            dialog_id: dialog_id,
            url: newURL,
            is_staging: true,
            is_live: false,
        });
        uploadFile({
            path: newURL,
            file_body: config.file_data
        });
    }
    dialogversioncontrol.is_live=true;
    const dialogversioncontrolstaging = DialogVersionControl.findOne({
        where: {is_staging: true}
    });
    const filecontent = await getFile(dialogversioncontrolstaging.url);

    await dialogversioncontrol.save();
    res.send({
        ...dialog.dataValues,
        url: dialogversioncontrol.url,
        configuration: JSON.parse(filecontent?.file_data || "{}"),
    });
}

//delete a DVC where its neither a live or staging 
const purge = async(req, res, next)=>{
    //add-> delete from digital ocean
    const dialog_version_control_id = req.query.dialog_version_control_id;
    const dialogversioncontrol = await DialogVersionControl.findOne({
        where: { dialog_version_control_id: dialog_version_control_id }
    });
    if(!dialogversioncontrol){
        return res.status(200).send({message: "can not find DVC"})
    }
    if(!(dialogversioncontrol.is_staging || dialogversioncontrol.is_live)){
        DialogVersionControl.destroy({
            where:{
                dialog_version_control_id: dialog_version_control_id,
            }
        })
    }else{
        return res.status(200).send({message: "can not delete a staging or a live DVC"})
    }
    const staging = await DialogVersionControl.findOne({
        where: {is_staging: true, dialog_id: dialogversioncontrol.dialog_id}
    });
    const dialog = await Dialogs.findOne({
        where: {dialog_id: staging.dialog_id}
    });
    const filecontent = await getFile(staging.url);
    res.send({
        ...dialog.dataValues,
        url: staging.url,
        configuration: JSON.parse(filecontent?.file_data || "{}"),
    });
}


//delete from the dialog 
const purgeDVC =(dialog_id)=>{
    DialogVersionControl.destroy({
        where:{
            dialog_id,
        }
    })
}

module.exports = {
    setDVC, 
    set, 
    get, 
    getDVC, 
    update, 
    publish, 
    changeIsStaging, 
    getAll, 
    getIsStagingFalse, 
    getIsStagingTrue,
    changeIsLive, 
    purge, 
    purgeDVC,
};
