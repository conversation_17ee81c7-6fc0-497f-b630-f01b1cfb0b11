const Trainee_Class = require("../Models/Custom.Class.Trainee");

const get = (req, res) => {
  res.send(req.trainee_class);
};


const set = async(req, res, next) => {
    var {bot_id,trainee_id,class_id} = req.body;
           Trainee_Class.create({
           trainee_id,
           bot_id,
           class_id
        })
        .then((trainee_class) => {
            req.trainee_class = trainee_class;
            return next();
          });
};


const update = async (req, res, next) => {
  const trainee_class = req.trainee_class;
  const data = { ...req.body };
  trainee_class.updateInfo(data);
  req.trainee_class = trainee_class;
  await trainee_class.save();
  return next();
};


const deleteRegisteration = (req, res, next) => {
  const trainee_class_id = req.body.trainee_class_id;
  const bot_id = req.body.bot_id;
  if(trainee_class_id){
  Trainee_Class.destroy({
    where: {
      trainee_class_id,bot_id
    },
  }).then(() => {
    return next();
  });
}
};


const purge = (req, res, next) => {
    const trainee_id = req.body.trainee_id;
    const bot_id = req.body.bot_id
    Trainee_Class.destroy({
      where: {
        trainee_id,bot_id
      },
    }).then(() => {
      res.send({ message: "Row is deleted" });
    });
  };


module.exports = {
  get,
  update,
  set,
  purge,
  deleteRegisteration
};
