const BotUser = require("../Models/Bot.User");
const getLocation = require("../Services/Location");

const set = (req, res, next) => {
  const phoneModel = req.phone;
  const data = req.body;
  if (!req.botuser) {
    BotUser.create({
      phone: phoneModel.phone,
      bot_id: phoneModel.bot_id,
      continent: data.continent,
      country: data.country,
      city: data.city,
    }).then((botuser) => {
      req.botuser = botuser;
      return next();
    });
  } else {
    return next();
  }
};

const find = (req, res, next) => {
  const phoneModel = req.phone;
  const data = req.body;
  BotUser.findOne({
    where: {
      phone: phoneModel.phone,
      bot_id: phoneModel.bot_id,
    },
  }).then((botuser) => {
    req.botuser = botuser;
    return next();
  });
};

const opts = (data) => {
  return {
    type: "staticmap", // This value is required if you want to render a static map

    center: `${data.latitude},${data.longitude}`, // This will be the center of your image.
    // It can also be a street address
    //// OPTIONAL ////
    key: "AIzaSyBl8jqw_9sE9qQv0SHmfhGHqnTGtaGA_u4", // Your Google API Key
    zoom: 16, // This will be how zoomed in the map will be. 0 is where you
    // can see the entire world, 21 is where you see streets. 14 is default
    size: "512x512", // This is the pixel size of your map. Default 320x240
    scale: 1,
    format: "JPEG",

    maptype: "roadmap",
    // 'roadmap', 'satellite', 'hybrid', and 'terrain'
    language: "en", // Language in which you want the map to be rendered
    region: `${data.country_code.toLowerCase()}`, // Country code in: ccTLD. defines the appropriate borders to display,
    markers: "color:red|label:Location|" + `${data.latitude},${data.longitude}`,
    // https://developers.google.com/maps/documentation/staticmaps/index#Markers
    path: `color:0x0000ff|weight:5|${data.latitude},${data.longitude}|${data.latitude},${data.longitude}`,
    visible: "Toronto", // you can specify locations that should be visible on the
    // rendered map. This can be either lat,long or a location name
    style:
      "feature:administrative|element:labels|weight:3.9|visibility:on|inverse_lightness:true",
  };
};

const createLocationMap = (req, res, next) => {
  const src = getLocation(opts(req.body));
  return res.send({ src: src });
};

module.exports = { set, find, createLocationMap };
