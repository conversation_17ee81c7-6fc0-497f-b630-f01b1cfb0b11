const PopupPlugin = require("../Models/PopupPlugin");

const get = (req, res) => {
  res.send(req.popupPlugin);
};
const set = (req, res, next) => {
  const data = req.body;
  PopupPlugin.create({
    bot_id: data.bot_id,
    frequency: data.frequency || 3
  })
    .then((popupPlugin) => {
      req.popupPlugin = popupPlugin;
      return next();
    })
    .catch((e) => res.send({ e, message: "an error occured" }));
};

const update = async (req, res, next) => {
  const popupPlugin = req.popupPlugin;
  const data = { ...req.body };
  popupPlugin.updateInfo(data);
  req.popupPlugin = popupPlugin;
  await popupPlugin.save();
  return next();
};

module.exports = { get, set, update };
