const Config = require("../Models/Bot.Config");

const get = (req, res) => {
  res.send(req.config);
};

const set = (req, res, next) => {
  const bot_id = req.bot.bot_id;
  Config.create({
    bot_id,
  }).then((config) => {
    req.config = config;
    return next();
  });
};

const create = (req, res, next) => {
  const data = { ...req.body };
  Config.create({
    bot_id: data.bot_id,
  }).then((config) => {
    req.config = config;
    return next();
  });
};

const update = async (req, res, next) => {
  const config = req.config;
  const data = { ...req.body };
  config.updateInfo(data);
  req.config = config;
  await config.save();
  return next();
};

module.exports = {
  get,
  create,
  update,
  set,
};
