const Custom_User = require("../Models/Custom.User");
const {
 wolvesUrl
} = require("../constants")

const get = (req, res) => {
  const custom_user = req.custom_user;
  res.send({ custom_user });
};

const register = async (req, res) => {
  const data = req.body;
  Custom_User.create({
    email: data.email,
    user_name: data.user_name,
    email_verification: false
  })
    .then(async (custom_user) => {
    custom_user.setPassword(req.body.password);
      await custom_user.save();
      res
        .status(201)
        .send({ message: "registeration successful verify you email", custom_user });
    })
    .catch((error) => {
      console.log(error);
      res.status(400).send(error);
    });
};

const login = async (req, res) => {
  const custom_user = req.custom_user;
  custom_user.updateRecord();
  await custom_user.save();
  return res.send({ custom_user: custom_user.toAuthJSON() });
};

const forgetPassword = async (req, res) => {
  const custom_user = req.custom_user;
  if (custom_user.reset) {
    return res.status(401).send({ message: "you have already set the email" });
  }
  custom_user.reset = true;
  custom_user.sendResetPassword();
  await custom_user.save();
  return res.send({ message: "sent went successfully check your email" });
};

const setNewPassword = async (req, res) => {
  const custom_user = req.custom_user;
  const password = req.body.password;
  custom_user.reset = false;
  custom_user.setPassword(password);
  await custom_user.save();
  return res.send({ message: "setting new password went successfully" });
};

const finalizeEmailVerification = async (req, res) => {
  const custom_user = req.custom_user;
  await custom_user.save();
  res
    .status(301)
    .redirect(
      wolvesUrl.concat(`/custom/login?auto=true&user_id=${custom_user.custom_user_id}`)
    );
};

const logout = (req, res) => {
  //const user = req.user; # NOTE in case of tracking
  res.send({ message: "logout successful" });
};

const updateProfile = async (req, res) => {
  const custom_user = req.custom_user;
  custom_user.update({ ...req.body });
  await custom_user.save();
  res.send({ ...custom_user.toAuthJSON() });
};

const generateToken = async (req, res) => {
  const custom_user = req.custom_user;
  res.send({ 
    token : custom_user.generateToken()
  });
};

module.exports = {
  register,
  login,
  forgetPassword,
  setNewPassword,
  finalizeEmailVerification,
  get,
  logout,
  updateProfile,
  generateToken
};
