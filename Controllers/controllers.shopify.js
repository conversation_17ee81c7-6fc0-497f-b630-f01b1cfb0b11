const ShopifyPlugin = require("../Models/Shopify");
const fetch = require("node-fetch");
const get = (req, res) => {
  res.send(req.shopifyplugin);
};

const find = (req, res, next) => {
  const bot_id = req.query.bot_id || req.body.bot_id;
  ShopifyPlugin.findOne({ where: { bot_id } }).then((shopifyplugin) => {
    if (!shopifyplugin) {
      return res.status(401).send({ message: "Plugin Shopify not found" });
    }
    req.shopifyplugin = shopifyplugin;
    return next();
  });
};

const set = (req, res, next) => {
  const bot_id = req.body.bot_id;
  ShopifyPlugin.create({
    bot_id,
  }).then((shopifyplugin) => {
    req.shopifyplugin = shopifyplugin;
    return next();
  });
};

const update = async (req, res, next) => {
  const shopifyplugin = req.shopifyplugin;
  const shopifyData = req.body;
  if (shopifyData && shopifyplugin) {
    shopifyplugin.updateInfo({ ...shopifyData });
    await shopifyplugin.save();
    return next();
  } else {
    return next();
  }
};
const getProducts = async (req, res, next) => {
  const shopifyplugin = req.shopifyplugin;
  console.log(shopifyplugin, "shopifyplugin");
  let URL = `https://${shopifyplugin.shop_domain}/admin/api/2022-01/products.json`;

  const data = await fetch(URL, {
    method: "GET",
    url: URL,
    headers: {
      "Content-Type": "application/json; charset=UTF-8",
      "X-Shopify-Access-Token": shopifyplugin.access_token,
      Authorization:
        "Basic " +
        Buffer.from(
          shopifyplugin.api_key + ":" + shopifyplugin.api_secret
        ).toString("base64"),
    },
  })
    .then((response) => {
      console.log("response", response);
      return response.json();
    })
    .catch((err) => console.log("ERR", err));
  return res.send(data);
};

module.exports = {
  get,
  set,
  update,
  find,
  getProducts,
};
