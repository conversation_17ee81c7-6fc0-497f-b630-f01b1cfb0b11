const Category = require("../Models/Category");
const { lemmtaizationLocal } = require("../helper/helper");

const get = (req, res) => {
  res.send(req.category);
};

const getAll = (req, res) => {
  console.log(req.categories, ":req form get all");
  res.send(req.categories);
};
const create = (req, res, next) => {
  const data = req.body;
  Category.create({
    bot_id: data.bot_id,
    category_description: data.category_description,
    category_name: data.category_name.toLowerCase(),
    category_icon: data.category_icon,
    lemmatized_category_name: data.lemmatized_category_name,
  }).then((category) => {
    req.category = category;
    return next();
  });
};

const createMany = (req, res, next) => {
  const data = req.body.categories;
  const bot_id = req.body.bot_id;
  Category.bulkCreate(
    data.map((cat) => {
      return {
        bot_id,
        category_description: cat.category_description,
        category_name: cat.category_name.toLowerCase(),
        category_icon: data.category_icon,
        lemmatized_category_name: cat.lemmatized_category_name,
      };
    })
  ).then((categories) => {
    req.categories = categories;
    return next();
  });
};

const update = async (req, res, next) => {
  const category = req.category;
  const data = { ...req.body };
  category.updateInfo(data);
  req.category = category;
  await category.save();
  return next();
};

const purge = async (req, res, next) => {
  const category_id = req.body.category_id;
  const category = await Category.findOne({
    where: {
      category_id,
    },
  });
  const bot_id = category.bot_id; 
  Category.destroy({
    where: {
      category_id,
    },
  }).then(() => {
    req.bot_id = bot_id;
    return next();
    // res.send({ message: "Category was deleted" });
  });
};

const purgeAll = (req, res, next) => {
  console.log(req, "reqreqreqreqreqreqreqreq");
  Category.destroy({
    where: {
      bot_id: req.body.bot_id,
    },
  }).then(() => {
    return next();
    // res.send({ message: "all Category was deleted" });
    // console.log(res, "fffffffffffffffffffffffffffffffffff");
  });
};

module.exports = {
  get,
  getAll,
  createMany,
  create,
  update,
  purge,
  purgeAll,
};
