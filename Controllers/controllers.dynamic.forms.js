const DynamicFormSchema = require("../Models/DynamicFormSchema");
const DynamicFormResponses = require("../Models/DynamicFormResponses");
// dynamic_form_schema_id
// dynamic_form_response_id

// done
const getAllSchemasByBotId = (req, res, next) => {
  const bot_id = req.query.bot_id;

  DynamicFormSchema.findAll({ where: { bot_id, deleted: false } })
    .then((dynamicFormSchemas) => {
      req.res = dynamicFormSchemas
        .map((s) => {
          try {
            return {
              ...s.dataValues,
              schema: JSON.parse(s.dataValues.schema),
            };
          } catch (e) {
            return null;
          }
        })
        .filter((i) => i);
      return next();
    })
    .catch((error) => {
      console.log("error", error);
      return res.status(404).send({ message: "Resources does not exist" });
    });
};
// done
const createSchema = (req, res, next) => {
  const schemaBody = req.body;

  DynamicFormSchema.create({
    schema: JSON.stringify(schemaBody.schema),
    bot_id: schemaBody.bot_id,
    deleted: false,
    title: schemaBody?.title || "",
  }).then((createdSchema) => {
    if (createdSchema) {
      req.res = { ...createdSchema.dataValues, ...schemaBody };
      return next();
    } else {
      return res.status(404).send({ message: "Resources does not exist" });
    }
  });
};
// done
const deleteSchema = (req, res, next) => {
  const dynamic_form_schema_id = req.body.dynamic_form_schema_id;
  const bot_id = req.body.bot_id;
  DynamicFormSchema.update(
    { deleted: true },
    { where: { dynamic_form_schema_id, bot_id } }
  ).then((updatedSchema) => {
    if (updatedSchema) {
      req.res = { message: "Deleted successfully!" };
      return next();
    } else {
      return res.status(404).send({ message: "Resources does not exist" });
    }
  });
};
// done
const getAllResponsesBySchemaId = (req, res, next) => {
  const dynamic_form_schema_id = req.body.bot_id || req.query.bot_id;
  DynamicFormResponses.findAll({ where: { dynamic_form_schema_id } }).then(
    (allResponses) => {
      if (allResponses) {
        req.res = allResponses;
        return next();
      } else {
        return res.status(404).send({ message: "Resources does not exist" });
      }
    }
  );
};

const getOneSchemaById = (req, res, next) => {
  const bot_id = req.body.bot_id || req.query.bot_id;
  const dynamic_form_schema_id =
    req.body.dynamic_form_schema_id || req.query.dynamic_form_schema_id;
  DynamicFormSchema.findOne({ where: { bot_id, dynamic_form_schema_id } }).then(
    (formSchema) => {
      if (formSchema) {
        try {
          req.res = {
            ...formSchema.dataValues,
            schema: JSON.parse(formSchema.schema),
          };
        } catch (e) {
          req.res = {
            ...formSchema.dataValues,
            schema: {},
          };
        }
        return next();
      } else {
        return res.status(404).send({ message: "Resources does not exist" });
      }
    }
  );
};
const createSchemaResponse = (req, res, next) => {
  const { dynamic_form_schema_id, response } = req.body;
  try {
    DynamicFormResponses.create({
      dynamic_form_schema_id: +dynamic_form_schema_id,
      response: JSON.stringify(response),
    }).then((createdSchemaRes) => {
      if (createdSchemaRes) {
        req.res = { ...createdSchemaRes.dataValues, ...response };
        return next();
      } else {
        return res.status(500).send({ message: "failed to create response" });
      }
    });
  } catch (e) {
    return res.status(500).send({ message: "failed to create response" });
  }
};

const getSchemaResponses = async (req, res, next) => {
  const { dynamic_form_schema_id } = req.query;

  try {
    const responses = await DynamicFormResponses.findAll({
      where: { dynamic_form_schema_id },
    });

    if (!responses || responses.length === 0) {
      return res.status(404).send({ message: "No resources found" });
    }

    const processedResponses = responses.map((r) => {
      try {
        return {
          ...r.dataValues,
          response: JSON.parse(r.response),
        };
      } catch (e) {
        return res.status(500).send({ message: "Failed to find response" });
      }
    });
    req.res = processedResponses;

    return next();
  } catch (e) {
    console.error("Error fetching schema responses:", e);
    return res.status(500).send({ message: "Failed to find response" });
  }
};

module.exports = {
  getAllSchemasByBotId,
  createSchema,
  deleteSchema,
  getAllResponsesBySchemaId,
  getOneSchemaById,
  createSchemaResponse,
  getSchemaResponses,
};
