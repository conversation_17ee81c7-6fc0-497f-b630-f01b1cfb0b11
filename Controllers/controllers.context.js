const FAQ = require("../Models/FAQ");
const FaqContext = require("../Models/FAQ.Context");
const TriggerContext = require("../Models/Trigger.Context");

const preprocessFAQs = (req, res, next) => {
  const faqs = req.faqs;
  const bot_id = req.body.bot_id;
  const groupedFaqs = groupBy(faqs, "answer");
  const faq_keys = Object.keys(groupedFaqs).map((key) => {
    return {
      answer: key,
      bot_id,
      context: dropDuplicates(
        groupedFaqs[key]
          .filter(
            (item, pos) =>
              groupedFaqs[key].findIndex(
                (a) => a.question === item.question
              ) === pos
          )
          .map((item, pos) => item.question)
          .join(" ")
      ),
    };
  });
  req.faq_keys = faq_keys;
  return next();
};

const preprocessTriggers = (req, res, next) => {
  const triggers = req.triggers;
  const bot_id = req.body.bot_id;
  const groupedTriggers = groupBy(triggers, "url");

  const trigger_keys = Object.keys(groupedTriggers).map((key) => {
    return {
      url: key,
      bot_id,
      trigger_type: groupedTriggers[key][0].trigger_type,
      context: dropDuplicates(
        groupedTriggers[key]
          .filter(
            (item, pos) =>
              groupedTriggers[key].findIndex(
                (a) => a.trigger === item.trigger
              ) === pos
          )
          .map((item, pos) => item.trigger)
          .join(" ")
      ),
    };
  });
  req.trigger_keys = trigger_keys;
  return next();
};

const publish = (req, res, next) => {
  FaqContext.bulkCreate(req.faq_keys).then(() => {
    TriggerContext.bulkCreate(req.trigger_keys).then(() => {
      res.send({ message: "success" });
    });
  });
};

const dropDuplicates = (str) => Array.from(new Set(str.split(" "))).join(" ");

const groupBy = (xs, key) => {
  return xs.reduce(function (rv, x) {
    (rv[x[key]] = rv[x[key]] || []).push(x);
    return rv;
  }, {});
};

const purge = (req, res, next) => {
  const bot_id = req.body.bot_id;
  FaqContext.destroy({ where: { bot_id } }).then(() => {
    TriggerContext.destroy({ where: { bot_id } }).then(() => {
      return next();
    });
  });
};

const findAll = (req, res, next) => {
  const bot_id = req.query.bot_id;
  FaqContext.findAll({ where: { bot_id } }).then((contexts) => {
    res.send(contexts);
  });
};

module.exports = {
  publish,
  purge,
  findAll,
  preprocessTriggers,
  preprocessFAQs,
};
