const ZendeskPlugin = require("../Models/ZendeskPlugin");
const fetch = require("node-fetch");

const get = (req, res) => {
  res.send(req.ZendeskPlugin);
};

const find = (req, res, next) => {
  const bot_id = req.query.bot_id || req.body.bot_id;
  ZendeskPlugin.findOne({ where: { bot_id } }).then((ZendeskPlugin) => {
    if (!ZendeskPlugin) {
      return res.status(401).send({ message: "Plugin not found" });
    }
    req.ZendeskPlugin = ZendeskPlugin;
    return next();
  });
};

const set = (req, res, next) => {
  const bot_id = req.body.bot_id;
  console.log(req.body, "req.body set");
  ZendeskPlugin.create({
    bot_id,
  })
    .then((ZendeskPlugin) => {
      req.ZendeskPlugin = ZendeskPlugin;
      return next();
    })
    .catch((error) => {
      res.send(error);
    });
};

const update = async (req, res, next) => {
  const ZendeskPlugin = req.ZendeskPlugin; //from db
  const zendeskpluginData = req.body; //from body
  console.log(ZendeskPlugin, "zendeskplugin");
  console.log(zendeskpluginData, "zendeskpluginData");

  if (Boolean(zendeskpluginData.code_from_url)) {
    let URL = `https://www.zopim.com/oauth2/token?grant_type=authorization_code&code=${zendeskpluginData.code_from_url}&state=%7B%7D&client_id=${zendeskpluginData.client_id}&client_secret=${zendeskpluginData.client_secret}&redirect_uri=http%3A%2F%2Flocalhost%3A3000&scope=read`;
    console.log(URL);
    const data = await fetch(URL, {
      method: "POST",
      url: URL,
      headers: {
        "Content-Type": "application/json; charset=UTF-8",
      },
    }).then((response) => response.json());
    // .then((data) =>
    //   console.log(data.access_token, data, "data from fetch token")
    // );
    console.log(data, "datadata");
    await ZendeskPlugin.updateInfo({
      ...zendeskpluginData,
      access_token: data.access_token,
    });
    await ZendeskPlugin.save();

    return next();
  } else if (ZendeskPlugin && zendeskpluginData) {
    ZendeskPlugin.updateInfo({ ...zendeskpluginData });
    await ZendeskPlugin.save();
    return next();
  } else {
    return next();
  }
};
const getWebSocketUrl = async (req, res, next) => {
  const zendestAccess_token = req.ZendeskPlugin.access_token;
  const CHAT_API_URL = "https://chat-api.zopim.com/graphql/request";

  const date = await fetch(CHAT_API_URL, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      query: `mutation {
        startAgentSession(access_token:${zendestAccess_token} ) {
          websocket_url
          session_id
          client_id
        }
      }`,
      variables: {},
    }),
  }).then((res) => res.json());
  res.send({
    ...data.data,
  });
};

module.exports = {
  get,
  set,
  update,
  find,
  getWebSocketUrl,
};
