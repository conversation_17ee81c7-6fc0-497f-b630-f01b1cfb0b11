const Report = require("../Models/Report");
const sequelize = require("../db");
const govPolygons = require("../govPolygons.json");
const liwaPolygons = require("../liwaPolygons.json");

const get = (req, res) => {
  res.send(req.report);
};

const getAll = (req, res) => {
  res.send({ reports: req.reports });
};

const getAllSscReports = (req, res) => {
  sequelize.query(`
  SELECT 
  COUNT(report_id) as reports,
  [including],
  [nationality],
  [municapility] as governorate,
  [building_name],
  [report_day],
  [report_week],
  [report_month],
  [report_year]
FROM [dbo].[ssc_reports]
GROUP BY 
nationality,
including,
municapility,
building_name,
report_day,
report_week,
report_month,
report_year
ORDER BY reports DESC
  `).then(data => {
    res.send({
      data: data[0],
      govPolygons,
      liwaPolygons
    }

    );
  })
}

const set = (req, res, next) => {
  const data = req.body;
  Report.create(data).then((report) => {
    req.report = report;
    return next();
  });
};

const purge = (req, res, next) => {
  const report_id = req.body.report_id;
  Report.destroy({
    where: {
      report_id,
    },
  }).then(() => {
    res.send({ message: "Report was deleted" });
  });
};

module.exports = {
  get,
  getAll,
  set,
  purge,
  getAllSscReports
};
