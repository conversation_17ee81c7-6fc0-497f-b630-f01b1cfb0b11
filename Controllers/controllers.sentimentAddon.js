const SentimentAddon = require("../Models/SentimentAddon");

const get = (req, res) => {
  return res.send(req.sentimentAddon);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  const botId = data.bot_id;

  SentimentAddon.findOne({ where: { bot_id: botId } }).then(
    (existingSentimentAddon) => {
      if (existingSentimentAddon) {
        existingSentimentAddon.updateInfo(data);
        existingSentimentAddon.save();
        req.sentimentAddon = existingSentimentAddon;
        return next();
      } else {
        SentimentAddon.create(data)
          .then((sentimentAddon) => {
            req.sentimentAddon = sentimentAddon;
            return next();
          })
          .catch((error) => {
            res.status(500).send({ message: "Internal server error" });
          });
      }
    }
  );
};

const update = async (req, res, next) => {
  const data = req.body;
  const sentimentAddon = req.sentimentAddon;
  if (req.body && sentimentAddon) {
    sentimentAddon.updateInfo(data);
    await sentimentAddon.save();
    return next();
  } else {
    return next();
  }
};
module.exports = { update, set, get };
