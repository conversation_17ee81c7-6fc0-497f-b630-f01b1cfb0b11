const UserAddress = require("../Models/User.Address");

const set = (req, res, next) => {
  const data = { ...req.body };
  UserAddress.create(data).then((address) => {
    req.address = address;
    return next();
  });
};

const get = (req, res) => {
  res.send(req.address);
};

const getAll = (req, res, next) => {
  res.send(req.addresses);
};

const update = async (req, res, next) => {
  const address = req.address;
  const data = { ...req.body };
  address.updateInfo(data);
  await address.save();
  return next();
};

const purge = (req, res, next) => {
  const data = { ...req.body };
  UserAddress.destroy({
    where: {
      address_id: data.address_id,
    },
  }).then(() => {
    return res.send({ message: "Address was deleted" });
  });
};

module.exports = {
  get,
  getAll,
  set,
  update,
  purge,
};
