const Monitor = require("../Models/Monitor");

const register = async (req, res, next) => {
  const data = { ...req.body };
  Monitor.create({
    email: data.email,
  })
    .then(async (monitor) => {
      monitor.setPassword(data.password);
      req.monitor = monitor;
      await monitor.save();
      return next();
    })
    .catch((e) => {
      res.status(409).send({ message: e });
    });
};

const login = (req, res, next) => {
  res.send(req.monitor.toAuthJSON());
};

const getAll = (req, res, next) => {
  res.send(
    req.monitorbots.map((a) => {
      var bot = req.bots.find((b) => a.bot_id === b.bot_id);
      return {
        ...bot.toJSON(),
        ...a.toJSON(),
      };
    })
  );
};

module.exports = { login, register, getAll };
