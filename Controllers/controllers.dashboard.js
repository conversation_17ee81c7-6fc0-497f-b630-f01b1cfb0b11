const Dashboard = require("../Models/Bot.Dashboard");
const DashboardCalculations = require("../Models/Dashboard.Calculation");
const sequelize = require("../db");

const get = (req, res, next) => {
  res.send(req.dashboard.toJSON());
};

const create = (req, res, next) => {
  const bot_id = req.bot.bot_id;
  Dashboard.create({
    bot_id,
  }).then((dashboard) => {
    req.dashboard = dashboard;
    return next();
  });
};

const update = (req, res, next) => {
  const dashboard = req.dashboard;
  const dbData = { ...req.body };
  dashboard.updateInfo(dbData);
  dashboard.save();
  return next();
};

const getTransactionsCount = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const period = req.query.period;
  sequelize
    .query("Get_Transactions_Count_Per_Period :Period , :botID", {
      replacements: {
        botID: bot_id,
        Period: period,
      },
    })
    .then((data) => {
      res.send(data[0][0]);
    });
};

const getTransactions = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const tr_channel = req.query.tr_channel;
  console.log("bot_id", bot_id);
  sequelize
    .query("select * from chatbot_transactions where bot_id = :bot_id and tr_channel = :tr_channel", {
      replacements: {
        bot_id: bot_id,
        tr_channel: tr_channel,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });

}

const getCountryTransactions = (req, res) => {
  const bot_id = req.query.bot_id;
  const day = req.query.day;
  console.log("day", day);
  sequelize
    .query("get_country_transactions :bot_id , :day", {
      replacements: {
        bot_id: +bot_id,
        day: +day,
      },
    })
    .then((data) => {
      console.log("data", data);
      res.send(data[0]);
    });
};

const getVoiceTransactionsCount = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const period = req.query.period;
  sequelize
    .query("Get_Transactions_Voice_Count_Per_Period :Period , :botID", {
      replacements: {
        botID: bot_id,
        Period: period,
      },
    })
    .then((data) => {
      res.send(data[0][0]);
    });
};

const getTransactionsFunction = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const period = req.query.period;
  sequelize
    .query("Get_Transactions_By_Function :botID,:Period", {
      replacements: {
        botID: bot_id,
        Period: period,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getTransactionsCategory = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const period = req.query.period;
  sequelize
    .query("Get_Designer_Transactions_By_Category :Period,:botID", {
      replacements: {
        botID: bot_id,
        Period: period,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getTransactionsDays = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const period = req.query.period;
  sequelize
    .query("Get_Transactions_By_Day :Period,:botID", {
      replacements: {
        botID: bot_id,
        Period: period,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getTransactionsConversation = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const period = req.query.period;
  sequelize
    .query("Get_Transactions_By_Conversation :Period,:botID", {
      replacements: {
        botID: bot_id,
        Period: period,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getTransactionsWAConversation = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const period = req.query.period;
  sequelize
    .query("Get_Transactions_By_WA_Conversation :Period,:botID", {
      replacements: {
        botID: bot_id,
        Period: period,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getTransactionsChannel = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const period = req.query.period;
  sequelize
    .query("Get_Transactions_By_Channel :Period,:botID", {
      replacements: {
        botID: bot_id,
        Period: period,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getTransactionsAnswer = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const period = req.query.period;
  sequelize
    .query("Get_Transactions_Designer_By_Answer :Period,:botID", {
      replacements: {
        botID: bot_id,
        Period: period,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getTransactionsTrend = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const period = req.query.period;
  sequelize
    .query("Get_Transactions_Designer_By_Answer_Trend :Period,:botID", {
      replacements: {
        botID: bot_id,
        Period: period,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getTransactionsAvgWeek = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const period = req.query.period;
  sequelize
    .query("Get_Transactions_Average_Per_Weekday :Period,:botID", {
      replacements: {
        botID: bot_id,
        Period: period,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getTransactionsAvgHour = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const period = req.query.period;
  sequelize
    .query("Get_Transactions_Average_Per_Hour :Period,:botID", {
      replacements: {
        botID: bot_id,
        Period: period,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getTransactionsAvgDay = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const period = req.query.period;
  sequelize
    .query("Get_Transactions_Average_Per_Day :Period,:botID", {
      replacements: {
        botID: bot_id,
        Period: period,
      },
    })
    .then((data) => {
      res.send(data[0][0]);
    });
};

const getTransactionsNotAnswered = (req, res, next) => {
  if(req.query.endDay === 'undefined' || req.query.endMonth === 'NaN' || req.query.endYear === 'undefined' || !req.query.endDay || !req.query.endMonth || !req.query.endYear ){
    const {bot_id, day, month, year} = req.query;
    sequelize
      .query("Get_Transactions_Designer_Not_Answered :botID, :day , :month , :year", {
        replacements: {
          botID: bot_id,
          // Period: period,
          day,
          month,
          year,
        },
      })
      .then((data) => {
        res.send(data[0]);
      });
  } else {
    const {bot_id, day, month, year, endDay, endMonth, endYear} = req.query;
    sequelize
      .query("Get_Transactions_Designer_Not_Answered :botID, :day , :month , :year, :endDay, :endMonth, :endYear", {
        replacements: {
          botID: bot_id,
          // Period: period,
          day,
          month,
          year,
          endDay,
          endMonth,
          endYear
        },
      })
      .then((data) => {
        res.send(data[0]);
      });
  }
  // const bot_id = req.query.bot_id;
  // const period = req.query.period;
};

const getSanadTransactionsNotAnswered = (req, res, next) => {
  const period = req.query.period;
  const bot_id = req.query.bot_id;
  sequelize
    .query("Get_Sanad_Transactions_Designer_Not_Answered :day, :ID", {
      replacements: {
        day: period,
        ID: bot_id,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getTransactionsLastN = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const No = req.query.No;
  sequelize
    .query("Get_Transactions_Last_N :No,:botID", {
      replacements: {
        botID: bot_id,
        No,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getSanadTransactionsLastN = (req, res, next) => {
  const No = req.query.No;
  const bot_id = req.query.bot_id;
  sequelize
    .query("Get_Sanad_Transactions_Last_N :No, :ID", {
      replacements: {
        No,
        ID: bot_id,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getDaysSinceLaunch = (req, res, next) => {
  const bot_id = req.query.bot_id;
  sequelize
    .query("Get_Days_Since_Launch :botID", {
      replacements: {
        botID: bot_id,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getWhatsappPhoneNumbers = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const period = req.query.period;
  sequelize
    .query("Get_Whatsapp_Phone_Numbers :Period,:botID", {
      replacements: {
        botID: bot_id,
        Period: period,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getSanadWhatsappPhoneNumbers = (req, res, next) => {
  const period = req.query.period;
  const bot_id = req.query.bot_id;
  console.log("period", period);
  console.log("bot_id", bot_id);
  sequelize
    .query("Get_Sanad_Whatsapp_Phone_Numbers :day, :ID", {
      replacements: {
        day: period,
        ID: bot_id,
      },
    })
    .then((data) => {
      console.log("data", data)
      res.send(data[0]);
    });
};

const getTransactionPerPeriod = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const period = req.query.period;
  const phone = req.query.phone;
  sequelize
    .query("Get_Transactions_Per_Period :Period,:botID,:Phone", {
      replacements: {
        botID: bot_id,
        Period: period,
        Phone: phone,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getTransactionsGrouped = (req, res, next) => {
  const bot_id = req.query.bot_id;
  console.log("bot_id", bot_id);
  sequelize
    .query("Get_Transactions_Group :botID", {
      replacements: {
        botID: bot_id,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getMostAskedPerCountry = (req, res) => {
  const bot_id = req.query.bot_id;
  sequelize
    .query("Get_Most_Asked_Per_Country :botID", {
      replacements: {
        botID: bot_id,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getSanadMostAskedPerCountry = (req, res) => {
  const bot_id = req.query.bot_id;
  sequelize.query("Get_Sanad_Most_Asked_Per_Country :ID", {
    replacements: {
      ID: bot_id,
    },
  }).then((data) => {
    res.send(data[0]);
  });
};

const insertTransaction = (req, res) => {
  const data = req.body;
  if (typeof data.Q === "string" && !data.Q?.includes("bot_id")) {
    sequelize.query(
      `bot_designer_transaction_set 
        :bot_id,
        :country,
        :city,
        :ip,
        :function,
        :Q,
        :A,
        :category,
        :answered,
        :continent,
        :session,
        :channel,
        :score,
        :owner,
        :is_voice_tr,
        :message_uuid
        `,
      {
        replacements: {
          bot_id: data.bot_id,
          country: data.country ? data.country : null,
          city: data.city ? data.city : null,
          ip: data.ip ? data.ip : null,
          is_voice_tr: data.is_voice_tr,
          continent: data.continent ? data.continent : null,
          session: data.session ? data.session : null,
          channel: data.channel ? data.channel : "Dev",

          function: data.function,
          Q: data.Q,
          A: data.A,
          category: data.category,
          answered: data.answered,
          score: parseFloat(data.score).toFixed(1),
          owner: data.owner,
          message_uuid: data?.message_uuid ? data.message_uuid : null,
        },
      }
    );
  }
  res.send({ message: "transaction has been inserted" });
};

const deleteTransaction = (req, res) => {
  const data = req.body;
  sequelize.query(
    `bot_designer_transaction_remove 
      :message_uuid
      `,
    {
      replacements: {
        message_uuid: data.message_uuid,
      },
    }
  );
  res.send({ message: "transaction has been removed" });
}

const insertSanadTransaction = (req, res) => {
  const data = req.body;
  if (typeof data.Q === "string" && !data.Q?.includes("bot_id")) {
    sequelize.query(
      `bot_designer_sanad_transaction_set 
        :bot_id,
        :country,
        :city,
        :ip,
        :function,
        :entity,
        :service,
        :Q,
        :A,
        :category,
        :answered,
        :continent,
        :session,
        :channel,
        :score,
        :owner,
        :is_voice_tr
        `,
      {
        replacements: {
          bot_id: data.bot_id,
          country: data.country ? data.country : null,
          city: data.city ? data.city : null,
          ip: data.ip ? data.ip : null,
          is_voice_tr: data.is_voice_tr,
          continent: data.continent ? data.continent : null,
          session: data.session ? data.session : null,
          channel: data.channel ? data.channel : "Dev",

          function: data.function,
          entity: data.entity,
          service: data.service,
          Q: data.Q,
          A: data.A,
          category: data.category,
          answered: data.answered,
          score: parseFloat(data.score).toFixed(1),
          owner: data.owner,
        },
      }
    );
  }
  res.send({ message: "transaction has been inserted" });
};

const getHealthStatus = (req, res, next) => {
  const bot_id = req.query.bot_id;

  sequelize
    .query("get_current_server_health :bot_id", {
      replacements: {
        bot_id: bot_id,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getTransactionsCalculations = (req, res, next) => {
  if(req.query.endDay === 'undefined' || req.query.endMonth === 'NaN' || req.query.endYear === 'undefined' || !req.query.endDay || !req.query.endMonth || !req.query.endYear ){
    const {bot_id, day, month, year} = req.query;
    sequelize
    .query(
      `select * from bot_designer_dashboard_calculations where bot_id = ${bot_id} and date_stamp = '${day}/${month}/${year}'`
    )
    .then((botDashCalc) => {
      if (botDashCalc[0]?.length) {
        const bdc = botDashCalc[0];
        res.send(JSON.parse(bdc[0]?.calculation));
      } else {
        sequelize
          .query("Get_Transactions_Calculated :ID , :day , :month , :year ", {
            replacements: {
              ID: bot_id,
              day,
              month,
              year,
            },
          }) .then((data) => {
            res.send(data[0]);
            DashboardCalculations.create({
              bot_id,
              calculation: JSON.stringify(data[0]),
              date_stamp: `${day}/${month}/${year}`,
              createdAt: new Date(),
              updatedAt: new Date(),
            });
          });
      }
    });
    
    } else {
      const {bot_id, day, month, year, endDay, endMonth, endYear} = req.query;
      sequelize
      .query("Get_Transactions_Calculated :ID , :day , :month , :year, :endDay, :endMonth, :endYear ", {
        replacements: {
          ID: bot_id,
          day,
          month,
          year,
          endDay,
          endMonth,
          endYear
        },
      }) .then((data) => {
        res.send(data[0]);
      });
    }
};

const getSanadTransactionsCalculations = (req, res, next) => {
  const bot_id = req.query.bot_id;
  const day = req.query.day;
  const month = req.query.month;
  const year = req.query.year;

  sequelize
    .query("Get_Sanad_Transactions_Calculated :ID , :day , :month , :year ", {
      replacements: {
        ID: bot_id,
        day,
        month,
        year,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};
const getSanadFeedbackStats = (req, res, next) => {
  sequelize.query("Get_Sanad_Feedback_Stats ").then((data) => {
    res.send(data[0]);
  });
};

const getConversationsCalculations = (req, res, next) => {
  const bot_id = req.query.bot_id;

  sequelize
    .query("Get_bot_user_conversations_calculated :ID", {
      replacements: {
        ID: bot_id,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getSanadConversation = (req, res, next) => {
  const conversation_id = req.query.conversation_id;
  sequelize
    .query("get_conversation_details_by_conversation_id :conversation_id", {
      replacements: {
        conversation_id: conversation_id,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getConversationsIds = (req, res, next) => {
  const bot_id = req.query.bot_id;
  console.log("bot_id", bot_id);
  sequelize
    .query("get_Chatbot_Conversations :ID", {
      replacements: {
        ID: bot_id,
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

const getConversationDetails = (req, res, next) => {
  const bot_id = req.query.bot_id
  const conversation_id = req.query.conversation_id;
  sequelize
    .query("get_conversation_details_2_by_conversation_id :conversation_id , :bot_id", {
      replacements: {
        conversation_id: conversation_id,
        bot_id: bot_id
      },
    })
    .then((data) => {
      res.send(data[0]);
    });
};

module.exports = {
  create,
  update,
  get,
  getTransactionsCount,
  getTransactionsFunction,
  getTransactionsCategory,
  getTransactionsDays,
  getTransactionsAnswer,
  getTransactionsTrend,
  getTransactionsAvgDay,
  getTransactionsAvgWeek,
  getTransactionsAvgHour,
  getTransactionsNotAnswered,
  getTransactionsLastN,
  getConversationsIds,
  getConversationDetails,
  getDaysSinceLaunch,
  getTransactionsConversation,
  getTransactionsChannel,
  getWhatsappPhoneNumbers,
  getTransactionPerPeriod,
  getTransactionsWAConversation,
  getVoiceTransactionsCount,
  getCountryTransactions,
  getTransactionsGrouped,
  getMostAskedPerCountry,
  insertTransaction,
  insertSanadTransaction,
  getHealthStatus,
  getTransactionsCalculations,
  getSanadTransactionsCalculations,
  getConversationsCalculations,
  getSanadConversation,
  getSanadWhatsappPhoneNumbers,
  getSanadTransactionsLastN,
  getSanadTransactionsNotAnswered,
  getSanadMostAskedPerCountry,
  getSanadFeedbackStats,
  deleteTransaction,
  getTransactions
};
