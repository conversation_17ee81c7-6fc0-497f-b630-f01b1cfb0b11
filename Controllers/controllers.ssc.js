const fetch = require("node-fetch");
const geolocationURL = "http://api.ipstack.com/";

const getLocation = (req, res, next) => {
  const ip_address = req.body.ip_address;
  fetch(
    geolocationURL.concat(
      ip_address,
      "?access_key=35b4a47097d19b00559c135bd8423124"
    ),
    {
      method: "GET",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json"
      },
    }
  )
    .then((response) => {
      return response.json();
    })
    .then((res_data) => {
      res.send(res_data);
    });
};

const sendMessages = (req, res, next) => {
  // console.log("run api ssc");
  const data = req.body;
  // console.log("sendMessages data", data);
  const url = "https://wa.ssc.gov.jo/";
  fetch(url.concat(`api/chatbot`), {
    method: "POST",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
    },
    // withCredentials: true,
    // crossdomain: true,
    // timeout: 5000,
    body: JSON.stringify(data),
  })
    .then((response) => {
      console.log("response", response)
      return response.json();
    })
    .then((res_data) => {
      console.log("ssc DATA");
      res.send(res_data);
    })
    .catch((err) => {
      console.log("ssc error");
      console.log(err);
    });
};

module.exports = {
  sendMessages,
  getLocation,
};
