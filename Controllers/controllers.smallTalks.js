const SmallTalk = require("../Models/SmallTalk");
const sequelize = require("../db");
const { toEnglishTokenize } = require("./controllers.engine");


const get = (req, res) => {
  res.send(req.smallTalk);
};
const getAll = (req, res) => {
  res.send(req.smallTalks);
};

const set = (req, res, next) => {
  const data = { ...req.body };

  SmallTalk.create({
    bot_id: data.bot_id,
    question: toEnglishTokenize(data.question),
    answer: data.answer,
    voice_path_male: data?.voice_path_male || '',
    voice_path_female: data?.voice_path_female || '',
  }).then((smallTalk) => {
    req.smallTalk = smallTalk;
    return next();
  });
};

const setMany = (req, res, next) => {
  const data = [...req.body];
  SmallTalk
    .bulkCreate(
      data.map((ent) => {
        return {
          ...ent,
          question: toEnglishTokenize(ent.question),
        };
      })
    )
    .then((smallTalks) => {
      req.smallTalks = smallTalks;
      return next();
      // res.send(smallTalks);
    });
};

const purge = async (req, res, next) => {
  const data = { ...req.body };
  const smalltalk = await SmallTalk.findOne({
    where: {
      small_talk_id: data.small_talk_id,
    },
  });
  const bot_id = smalltalk.bot_id; 
  SmallTalk
    .destroy({
      where: {
        small_talk_id: data.small_talk_id,
      },
    })
    .then(() => {
      req.bot_id = bot_id;
      return next();
      // return res.send({ message: "smallTalk deleted successfully" });
    });
};

const update = async (req, res, next) => {
  const smallTalk = req.smallTalk;
  const data = { ...req.body };
  smallTalk.updateInfo(data);
  req.smallTalk = smallTalk;
  await smallTalk.save();
  return next();
};


const updateMany = async (req, res, next) => {
  const smallTalks = req.smallTalks;
  const data = [...req.body];

  for (var i = 0; i < smallTalks.length; i++) {
    var smallTalk = smallTalks[i];
    var smallTalkData = data.find(
      (a) => a.small_talk_id === smallTalk.small_talk_id
    );
    smallTalk.updateInfo(smallTalkData);
    await smallTalk.save();
  }
  return next();
};

const purgeAllSmallTalks = (req, res, next) => {
  const bot_id = req.body.bot_id;
  SmallTalk
    .destroy({
      where: {
        bot_id,
      },
    })
    .then(() => {
      return next();
      // return res.send({ message: "many where deleted" });
    });
};

const purgeMany = (req, res, next) => {
  const small_talk_ids = req.body.smalltalks;
  const bot_id = req.body.bot_id;

  SmallTalk
    .destroy({
      where: {
        bot_id: bot_id,
        small_talk_id: small_talk_ids,
      },
    })
    .then(() => {
      return next();
    });
};



module.exports = {
  get,
  getAll,
  set,
  purge,
  update,
  setMany,
  purgeAllSmallTalks,
  purgeMany,
  updateMany,

};
