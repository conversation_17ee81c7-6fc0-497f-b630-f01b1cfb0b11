const NER = require("../Models/NER");

const get = (req, res) => {
  res.send(req.ner);
};

const getAll = (req, res) => {
  res.send(req.ners);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  NER.create(data).then((ner) => {
    req.ner = ner;
    return next();
  });
};

const purge = (req, res, next) => {
  const ner_id = req.body.ner_id;
  NER.destroy({
    where: {
      ner_id,
    },
  }).then(() => {
    res.send({ message: "NER deleted" });
  });
};

const update = async (req, res, next) => {
  const ner = req.ner;
  const nerData = req.body;
  if (nerData && ner) {
    ner.updateInfo({ ...nerData });
    await ner.save();
    return next();
  } else {
    return next();
  }
};

const createMany = (req, res, next) => {
  const data = req.body.ners;
  NER.bulkCreate(
    data
  ).then((ners) => {
    req.ners = ners;
    return next();
  });
};


const fetchNer = async (req, res) => {
  const limit = parseInt(req.query.limit);
  const offset = parseInt(req.query.skip);
  const language= req.query.language;
  const ner_type= req.query.ner_type;


  try {
    const NERCollection = await NER.findAndCountAll(
      {
        limit: limit,
        offset: offset * limit,
       
        where:{
          language:language,
          ner_type:ner_type,
        }
      }
    );
      console.log("NER",NERCollection)
      const totalPages = Math.ceil(NERCollection.count / limit);
      const currentPage = Math.ceil(offset);
    
    res.status(200).send({
      totalPages,
      currentPage,
      data: NERCollection,
    
    });
  } catch (e) {
    console.log("Error", e)
    res.status(500).send({
      data: null,
    });
  }
};

module.exports = { get, getAll, set, update, purge, createMany, fetchNer };
