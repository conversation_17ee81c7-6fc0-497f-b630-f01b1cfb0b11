// // const { createWorker } = require("tesseract.js");
// // const tesseract = require("node-tesseract");
// // const tesseractOcr = require("node-tesseract-ocr");
// const fs = require("fs");
// const https = require("https");
// var request = require("request");
// // var xml = require("xml");

// // const scanner = require("receipt-scanner");

// // const worker = createWorker();
// // const rectangle = { left: 0, top: 0, width: 1664, height: 3280 };

// async function download(url, dest) {
//   return await new Promise((resolve, reject) => {
//     // Check file does not exist yet before hitting network
//     fs.access(dest, fs.constants.F_OK, (err) => {
//       if (err === null) reject("File already exists");

//       const request = https.get(url, (response) => {
//         if (response.statusCode === 200) {
//           const file = fs.createWriteStream(dest, { flags: "wx" });
//           file.on("finish", () => resolve());
//           file.on("error", (err) => {
//             file.close();
//             if (err.code === "EEXIST") reject("File already exists");
//             else fs.unlink(dest, () => reject(err.message)); // Delete temp file
//           });
//           response.pipe(file);
//         } else if (response.statusCode === 302 || response.statusCode === 301) {
//           //Recursively follow redirects, only a 200 will resolve.
//           download(response.headers.location, dest).then(() => resolve());
//         } else {
//           reject(
//             `Server responded with ${response.statusCode}: ${response.statusMessage}`
//           );
//         }
//       });

//       request.on("error", (err) => {
//         reject(err.message);
//       });
//     });
//   });
// }

// // (async () => {
// //   await worker.load();
// //   await worker.loadLanguage("eng");
// //   await worker.initialize("eng");
// //   await worker.setParameters({
// //     tessedit_char_whitelist: "0123456789",
// //   });
// //   console.log("intialized");
// // })();

// const recognition = async (req, res, next) => {
//   // console.log("recognizer started");
//   // const {
//   //   data: { text },
//   // } = await worker.recognize(
//   //   req.body
//   //     .image_path /*"https://infotointell.fra1.digitaloceanspaces.com/Bots/OCR/20211013_165908.jpg"*/,
//   //   { rectangle }
//   // );
//   // res.send({ text });
// };

// const fate = {
//   text: "© | ياست تلت الأتدن\nعر الممسطامم ليا الل ل 1 عقا لا\n00888827 هلزني\nقشر الاسهسل | _-_ ل ...ايت : إلى\nرقم الملف/ العقد | 001000575201 ...ادق نقطة ارسي[ 10218964\n099 انشع الجينة| 05040381\n3 فاط قرم فا -\nاربع روت القراءة| 18/06/2021 07:50:24 الخصم )| و\nأ للحتت لقتعا لمر يا\nمؤور مايق ااا 7 ‎١‏ الكمية المفوترة 1\nا ب رقم المرجع : 05/70413/002629 :\nمن إلى (فلس/ك.و.س) الكمية الشعر\nاخ لخدم الا ا\nتابع صفحتتا ووفر في الإستهلاك ا و تيجا\nلااعتجاءماء_مدة»وز© (©) و لع ا لع\n7 اس با جم ا ا ا بكو حت لا\n1ع ا ل\nدده لا 0 ا\nتفاصيل الذمع التتايعة\n| جمس | | .7 7\nناا هذ اكد كر ‎٠‏ المتمك لو وا افر\n| ذا م امس مها ..\nل ,\nم اح ء|اجة 0 ل لمي اعم | م.م\nالواح أ اذا الأمسم .م .\n([ذا :_ ل\nَل امعد ال ا م اا ا ءام\n| ب [] سس عا >\nاسح تند سس الحا\nتن لوحي قود حاو دين متا لضا ا الو اا وو اخصار جل\n* لا تمتي ر هده القسيمة وصلاً بقيمة المطالبة إلا بعد حَتمها بآلة او تتم صتذوق الشركة و الينوك المعتمدة -\nب ا ا ا ل ل\n7\nِ\n",
// };

// var options = {
//   l: "ara",
//   psm: 6,
//   binary: "/usr/local/bin/tesseract",
// };

// const recognitionV2 = async (req, res, next) => {
//   // tesseract.process(req.body.image_path, options, function (err, text) {
//   //   if (err) {
//   //     res.send({ err });
//   //   } else {
//   //     res.send({ text });
//   //   }
//   // });
// };

// const recognitionV3 = async (req, res, next) => {
//   var receiptOcrEndpoint = "https://ocr.asprise.com/api/v1/general";
//   var imageFile =
//     "https://infotointell.fra1.digitaloceanspaces.com/Bots/OCR/20211013_165908.jpg"; // Modify this to use your own file if necessary

//   //await download(imageFile, dest);
//   request.post(
//     {
//       url: receiptOcrEndpoint,
//       formData: {
//         client_id: "infotointell_X3YplZX82ue18r9t", // Use 'TEST' for testing purpose
//         recognizer: "auto",
//         output_format: req.body.file_type, // can be 'xml' or 'html'
//         ref_no: "ocr_nodejs_123", // optional caller provided ref code
//         file: fs.createReadStream(req.dest), // the image file
//       },
//     },
//     function (error, response, body) {
//       if (error) {
//         return res.status(422).send({ error });
//       }
//       const invoice_type = req.body.invoice_type;
//       const data = JSON.parse(body).output;
//       if (invoice_type === "jordan_electricity") {
//         // const priceMatch = [...data.matchAll(/<span[^>]*>[0-9]{1,3}</gm)];
//         // var price = priceMatch
//         //   .slice(priceMatch.length - 2, priceMatch.length)
//         //   .map((a) =>
//         //     a[0].replace(new RegExp("<span[^>]*>", "gm"), "").replace("<", "")
//         //   );

//         // const file_num = [...data.matchAll(/<span[^>]*>[0-9]{12}</gm)]
//         //   .slice(0, 2)
//         //   .map((a) =>
//         //     a[0].replace(new RegExp("<span[^>]*>", "gm"), "").replace("<", "")
//         //   );
//         const reference_num = data
//           ? [
//               ...data
//                 .replace(/[oO]/g, "0")
//                 .replace(/\s*/g, "")
//                 .matchAll(/[0-9]{1,2}\/[0-9]{5}\/[0-9]{6}/gm),
//             ]
//           : [];

//         res.send({ reference_num, data: data.replace(/\s*/g, "") });
//         fs.unlink(req.dest, () => {});
//       } else if (invoice_type === "miyahuna") {
//         const price = [...data.matchAll(/<span[^>]*>[0-9]{1,3}</gm)]
//           .slice(0, 2)
//           .map((a) =>
//             a[0].replace(new RegExp("<span[^>]*>", "gm"), "").replace("<", "")
//           );
//         const customer_num = [...data.matchAll(/<span[^>]*>[0-9]{6}</gm)]
//           .slice(0, 2)
//           .map((a) =>
//             a[0].replace(new RegExp("<span[^>]*>", "gm"), "").replace("<", "")
//           );
//         res.send({ customer_num, price, data });
//       }
//     }
//   );
// };

// const downloadImage = async (req, res, next) => {
//   const dest = "image" + Math.random() + ".jpg";
//   req.dest = dest;
//   await download(req.body.image_path, dest);
//   return next();
// };

// const recognitionV4 = (req, res, next) => {
//   scanner(stream_or_file_path).parse(function (err, results) {
//     if (err) return console.error(err);
//     else console.log(results);
//   });
// };

// module.exports = {
//   recognition,
//   recognitionV2,
//   recognitionV3,
//   downloadImage,
//   recognitionV4,
// };
