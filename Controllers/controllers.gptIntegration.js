const GPTIntegration = require("../Models/GPTIntegration");

const get = (req, res) => {
  return res.send(req.gptIntegration);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  const botId = data.bot_id;

  GPTIntegration.findOne({ where: { bot_id: botId } }).then(
    (existingGPTIntegration) => {
      if (existingGPTIntegration) {
        // res.status(409).send({ message: "gpt integration already exists " });
        existingGPTIntegration.updateInfo(data);
        existingGPTIntegration.save();
        req.gptIntegration = existingGPTIntegration;
        return next();
      } else {
        GPTIntegration.create(data)
          .then((gptIntegration) => {
            req.gptIntegration = gptIntegration;
            return next();
          })
          .catch((error) => {
            res.status(500).send({ message: "Internal server error" });
          });
      }
    }
  );
};

const purge = (req, res, next) => {
  const bot_id = req.body.bot_id;
  GPTIntegration.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    res.send({ message: "gpt integration data deleted" });
  });
};

const update = async (req, res, next) => {
  const data = req.body;
  const gptIntegration = req.gptIntegration;
  if (req.body && gptIntegration) {
    gptIntegration.updateInfo(data);
    await gptIntegration.save();
    return next();
  } else {
    return next();
  }
};
module.exports = { update, purge, set, get };
