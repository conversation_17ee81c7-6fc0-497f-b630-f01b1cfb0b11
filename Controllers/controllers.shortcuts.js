const Shortcut = require("../Models/Shortcut");

const get = (req, res) => {
  return res.send(req.shortcut);
};

const getAll = (req, res) => {
  res.send(req.shortcuts);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  // create or update
  Shortcut.findOne({ where: { user_id: data.user_id } }).then(
    (existingShortcut) => {
      if (existingShortcut) {
        existingShortcut.updateInfo(data);
        existingShortcut.save();
        req.shortcut = existingShortcut;
        return next();
      } else {
        Shortcut.create(data)
          .then((shortcut) => {
            req.shortcut = shortcut;
            return next();
          })
          .catch((error) => {
            res.status(500).send({ message: "Internal server error" });
          });
      }
    }
  );
};

const setMany = (req, res, next) => {
  const data = [...req.body];
  Shortcut.bulkCreate(data).then((shortcuts) => {
    req.shortcuts = shortcuts;
    return next();
  });
};

const purge = (req, res, next) => {
  const shortcut_id = req.body.shortcut_id;
  Shortcut.destroy({
    where: {
      shortcut_id,
    },
  }).then(() => {
    res.send({ message: "shortcut deleted" });
  });
};

const update = async (req, res, next) => {
  const data = req.body;
  const shortcut = req.shortcut;
  if (req.body && shortcut) {
    shortcut.updateInfo(data);
    await shortcut.save();
    return next();
  } else {
    return next();
  }
};

const updateMany = async (req, res, next) => {
  const data = [...req.body];
  const shortcuts = req.shortcuts;

  for (var i = 0; i < shortcuts.length; i++) {
    var shortcut = shortcuts[i];
    var shortcutData = data.find((a) => a.shortcut_id === shortcut.shortcut_id);
    shortcut.updateInfo(shortcutData);
    await shortcut.save();
  }

  return next();
};

module.exports = {
  get,
  getAll,
  set,
  setMany,
  purge,
  update,
  updateMany,
};
