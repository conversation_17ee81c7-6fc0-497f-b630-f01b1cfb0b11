const GoogleSheetPlugin = require("../Models/GoogleSheetPlugin");
const sequelize = require("../db");

const get = (req, res) => {
  res.send(req.googlesheetplugin);
};

const find = (req, res, next) => {
  const bot_id = req.query.bot_id || req.body.bot_id;
  GoogleSheetPlugin.findAll({ where: { bot_id } }).then((googlesheetplugin) => {
    if (!googlesheetplugin.length) {
      return res.status(401).send({ message: "Plugin not found" });
    }
    req.googlesheetplugin = googlesheetplugin;
    return next();
  });
};

const set = (req, res, next) => {
  const bot_id = req.body.bot_id;
  const email = req.body.email;
  const refresh_token = req.body.refresh_token;
  GoogleSheetPlugin.create({
    bot_id,
    email,
    refresh_token,
  }).then((googlesheetplugin) => {
    req.googlesheetplugin = googlesheetplugin;
    return next();
  });
};

const purge = (req, res, next) => {
  const bot_id = req.body.bot_id;
  const email = req.body.email;
  GoogleSheetPlugin.destroy({
    where: {
      bot_id,
      email,
    },
  }).then(() => {
    res.send({ message: "account data deleted " });
  });
};

const update = async (req, res, next) => {
  const googlesheetpluginData = req.body;
  GoogleSheetPlugin.findOne({
    where: {
      sheet_plugin_id: googlesheetpluginData.sheet_plugin_id,
      bot_id: googlesheetpluginData.bot_id,
    },
  }).then(async (googlesheetplugin) => {
    googlesheetplugin.updateInfo({ ...googlesheetpluginData });
    await googlesheetplugin.save();
    return next();
  });
};
const findOne = (req, res, next) => {
  const bot_id = req.query.bot_id || req.body.bot_id;
  const sheet_plugin_id = req.body.sheet_plugin_id;
  GoogleSheetPlugin.findOne({ where: { bot_id, sheet_plugin_id } }).then(
    (googlesheetplugin) => {
      if (!googlesheetplugin) {
        return res.status(401).send({ message: "Plugin not found" });
      }
      req.googlesheetplugin = googlesheetplugin;
      return next();
    }
  );
};

const getAllIntergationStatus = (req, res, next) => {
  const bot_id = req.query.bot_id || req.body.bot_id;
  sequelize
    .query("get_get_integration :bot_id", {
      replacements: {
        bot_id,
      },
    })
    .then((data) => {
      req.allIntegartion = data[0];
      return next();
    });
};

const getAllIntegation = (req, res, next) => {
  res.send(req.allIntegartion);
};
module.exports = {
  get,
  set,
  update,
  find,
  findOne,
  purge,
  getAllIntergationStatus,
  getAllIntegation,
};
