const Popup = require("../Models/Popup");

const get = (req, res) => {
  res.send(req.popup);
};

const getAll = (req, res) => {
  res.send({ popups: req.popups, plugin: req.popupPlugin });
};
const set = (req, res, next) => {
  const data = req.body;
  Popup.create({
    bot_id: data.bot_id,
    popup_type: data.popup_type,
    popup_url: data.popup_url,
    popup_message: data.popup_message,
    popup_description: data.popup_description,
    popup_lifetime: data.popup_lifetime,
    popup_active: data.popup_active,
    plugin_type: data.plugin_type || "marketing",
  }).then((popup) => {
    req.popup = popup;
    return next();
  });
};

const update = async (req, res, next) => {
  const popup = req.popup;
  const data = { ...req.body };
  popup.updateInfo(data);
  await popup.save().then(() => next());
};

const purge = (req, res, next) => {
  const popup_id = req.body.popup_id;
  Popup.destroy({
    where: {
      popup_id,
    },
  }).then(() => {
    res.send({ message: "Popup was deleted" });
  });
};

module.exports = {
  get,
  getAll,
  set,
  update,
  purge,
};
