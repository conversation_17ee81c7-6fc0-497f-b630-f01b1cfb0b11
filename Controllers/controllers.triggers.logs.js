const Triggers_Logs = require("../Models/Triggers.Logs")

const get = (req, res) => {
  res.send(req.triggers_logs);
};

const set = (req, res, next) => {
  const data = {...req.body}
  Triggers_Logs.create(data)
    .then((triggers_logs) => {
      req.triggers_logs = triggers_logs;
      return next();
    })
    .catch((e) => res.send({ e, message: "an error occured" }));
};

const update = async (req, res, next) => {
  const triggers_logs = req.triggers_logs;
  const data = { ...req.body };
  triggers_logs.updateInfo(data);
  req.triggers_logs = triggers_logs;
  await triggers_logs.save();
  return next();
};

module.exports = { get, set, update };
