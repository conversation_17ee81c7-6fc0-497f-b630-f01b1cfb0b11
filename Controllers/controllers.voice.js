const Voice = require("../Models/Voice");

const get = (req, res) => {
  res.send(req.voice);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  const bot_id = +data.project_name.split("-")[1];
  if (data.project_name === "Sa3i-63") {
    data.project_name = "JOPOST_Sa3i";
  }
  Voice.create({ ...data, bot_id })
    .then((voice) => {
      req.voice = voice;
      return next();
    })
    .catch(() => {
      res.status(409).send({ message: "An error occured" });
    });
};

module.exports = { set, get };
