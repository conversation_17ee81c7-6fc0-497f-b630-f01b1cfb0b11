const Reservation = require("../Models/Reservation");

const get = (req, res) => {
  res.send(req.reservation);
};
const set = (req, res, next) => {
  const data = req.body;
  Reservation.create({
    bot_id: data.bot_id,
  })
    .then((reservation) => {
      req.reservation = reservation;
      return next();
    })
    .catch(() => res.send({ message: "an error occured" }));
};

const update = async (req, res, next) => {
  const reservation = req.reservation;
  const data = { ...req.body };
  reservation.updateInfo(data);
  req.reservation = reservation;
  await reservation.save();
  return next();
};

module.exports = { get, set, update };
