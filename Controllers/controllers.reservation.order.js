const ReservationOrder = require("../Models//Reservation.Order");

const get = (req, res) => {
  res.send(req.order);
};

const getAll = (req, res, next) => {
  res.send(req.orders);
};

const update = async (req, res, next) => {
  const order = req.order;
  const data = { ...req.body };
  order.updateInfo(data);
  await order.save();
  return next();
};

module.exports = {
  get,
  getAll,
  update,
};
