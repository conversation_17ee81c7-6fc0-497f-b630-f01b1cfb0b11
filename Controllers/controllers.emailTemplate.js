const EmailTemplate = require("../Models/EmailTemplates");

const get = (req, res) => {
  res.send(req.emailTemplate);
};

const getAll = (req, res) => {
  res.send(req.emailTemplates);
};

const set = (req, res, next) => {
  const data = req.body;
  EmailTemplate.create(data).then((emailTemplate) => {
    req.emailTemplate = emailTemplate;
    return next();
  });
};

const update = async (req, res, next) => {
    const data = req.body;
    const emailTemplate = req.emailTemplate;
    if (req.body && emailTemplate) {
        emailTemplate.updateInfo(data);
        await emailTemplate.save();
        return next();
    } else {
        return next();
    }
};

const purge = (req, res, next) => {
  const data = { ...req.body };
  EmailTemplate.destroy({
    where: {
      email_template_id: data.email_template_id,
    },
  }).then(() => {
    return res.send({ message: "email template deleted successfully" });
  });
};


module.exports = {
  get,
  getAll,
  set,
  update,
  purge,
};
