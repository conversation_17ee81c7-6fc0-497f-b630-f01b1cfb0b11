const WorkingHours = require("../Models/WorkingHours");

const getAll = (req, res) => {
  res.send(req.workinghours);
};

const setAll = (req, res, next) => {
  const data = req.body;
  const workinghoursData = req.body.workinghours;
  const bot_id = req.body.bot_id;
  WorkingHours.bulkCreate(
    workinghoursData.map((a) => {
      return { ...a, bot_id };
    })
  ).then((workinghours) => {
    req.workinghours = workinghours;
    return next();
  });
};

const purgeAll = (req, res, next) => {
  const bot_id = req.body.bot_id;
  WorkingHours.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    return next();
  });
};

module.exports = {
  getAll,
  setAll,
  purgeAll,
};
