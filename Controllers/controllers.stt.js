const STT = require("../Models/STT");

const get = (req, res) => {
  res.send(req.stt);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  STT.create({
    user_input: data.user_input,
    lang: data.lang,
    score: data.score,
    long: data.long,
    lat: data.lat,
    city: data.city,
    country: data.country,
    ip_address: data.ip_address,
  }).then((stt) => {
    req.stt = stt;
    return next();
  });
};

module.exports = {
  get,
  set,
};
