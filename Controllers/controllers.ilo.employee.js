const Employee = require("../Models/Employee.ILO");

const set = (req, res, next) => {
    const data = { ...req.body };
    Employee.create({
        ...data
    }).then((employee) => {
        req.employee = employee;
        return next();
    });
};

const get = (req, res) => {
    res.send(req.employee);
};

const getAll = (req, res) => {
    res.send(req.employees);
};
const purge = (req, res, next) => {
    const employee_id = req.body.employee_id;
    Employee.destroy({
        where: {
            employee_id
        },
    }).then(() => {
        res.send({ message: "employee data deleted " });
    });
};

const update = async (req, res, next) => {
    const employee = req.employee;
    const employeeData = req.body;
    if (employeeData && employee) {
        employee.updateInfo({ ...employeeData });
        await employee.save();
        req.employee = employee;
        return next();
    } else {
        return next();
    }
};
module.exports = {
    set,
    get,
    getAll,
    purge,
    update
};
