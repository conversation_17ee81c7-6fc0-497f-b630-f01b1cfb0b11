const BotVoice = require("../Models/Bot.Voice");

const get = (req, res) => {
  res.send(req.botvoice);
};
const set = (req, res, next) => {
  const data = req.body;
  BotVoice.create({
    bot_id: data.bot_id,
  })
    .then((botvoice) => {
      req.botvoice = botvoice;
      return next();
    })
    .catch(() => res.send({ message: "an error occured" }));
};

const update = async (req, res, next) => {
  const botvoice = req.botvoice;
  const data = { ...req.body };
  botvoice.updateInfo(data);
  req.botvoice = botvoice;
  await botvoice.save();
  return next();
};

module.exports = { get, set, update };
