const Custom_Attendance = require("../Models/Custom.Attendance");

const get = (req, res) => {
  res.send(req.custom_attendance);
};

const set = (req, res, next) => {
    const data = {...req.body};
    Custom_Attendance.create(data).then((custom_attendance) => {
      req.custom_attendance = custom_attendance;
      return next();
    });
  };

const update = async (req, res, next) => {
  const custom_attendance = req.custom_attendance;
  const data = { ...req.body };
  custom_attendance.updateInfo(data);
  req.custom_attendance = custom_attendance;
  await custom_attendance.save();
  return next();
};

const purge = (req, res, next) => {
    const attendance_id = req.body.attendance_id;
    const bot_id = req.body.bot_id
    Custom_Attendance.destroy({
      where: {
        attendance_id,bot_id
      },
    }).then(() => {
      res.send({ message: "Attendance is deleted" });
    });
  };


module.exports = {
  get,
  update,
  set,
  purge
};
