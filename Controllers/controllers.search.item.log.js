const SearchItemLog = require("../Models/Search.item.log");

const set = (req, res, next) => {
  const data = { ...req.body };
  SearchItemLog.create({
    item_id: data.item_id,
    user_id: data.user_id,
    city: data.city,
  }).then((searchitemlog) => {
    req.searchitemlog = searchitemlog;
    return next();
  });
};

const get = (req, res, next) => {
  res.send(req.searchitemlog);
};

module.exports = {
  set,
  get,
};
