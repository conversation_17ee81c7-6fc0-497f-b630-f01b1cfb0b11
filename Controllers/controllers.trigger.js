const Dialogs = require("../Models/Dialogs");
const Trigger = require("../Models/Trigger");
const Triggers_Logs = require("../Models/Triggers.Logs");
const { lemmtaizationLocal } = require("../helper/helper");

const get = (req, res) => {
  res.send(req.trigger);
};

const getAll = (req, res) => {
  res.send(req.triggers);
};

const getBotTriggers = async (req, res) => {
  const bot_id = req.query.bot_id;
  
  try {
    const triggers = await Trigger.findAll({
      where: { bot_id },
      include: {
        model: Dialogs,
        as: 'dialog',
      }
    });
    console.log(triggers.dialog?.dialog_name);
    res.send(triggers)
  } catch (error) {
    console.error('Error fetching triggers and dialogs:', error);
    res.status(500).send('Error fetching triggers and dialogs');
  }
};


const trigger_types = [
  "suggestion",
  "dialog",
  "card",
  "whatsapp_buttons",
  "whatsapp_list",
  "whatsapp_template",
];

const preproccess = (req, res, next) => {
  const trigger_type = req.body.trigger_type;

  if (trigger_types.indexOf(trigger_type) === -1) {
    return res.send({ message: "invalid trigger_type" });
  } else {
    return next();
  }
};

const preproccessAll = (req, res, next) => {
  const data = req.body;
  var exit = false;
  data.map((trig) => {
    if (!exit && trigger_types.indexOf(trig.trigger_type) === -1) {
      exit = true;
      return res.send({ message: "invalid trigger_type", trigger: trig });
    }
  });
  if (!exit) {
    return next();
  }
};

const set = (req, res, next) => {
  const data = { ...req.body };
  var type = ""
  if(data.trigger_type === "card"){
   type = "add_card";
  }else if (data.trigger_type === "dialog"){
    type = "add_dialog"
  }
  Trigger.create({
    trigger: data.trigger,
    trigger_name: data.trigger_name,
    bot_id: data.bot_id,
    trigger_type: data.trigger_type,
    url: data.url,
    topic_id: data.topic_id || null,
    dialog_id: data.dialog_id,
  }).then((trigger) => {
    req.trigger = trigger;
    req.type = type;
    return next();
  });
};

const createLogs = async(req, res, next) => {
  const trigger = req.trigger;
  const type = req.type;
  const data = req.body;
    Triggers_Logs.create({
      trigger_id:trigger.trigger_id,
      bot_id:data.bot_id,
      trigger_type:type,
      user_id:data.user_id
    })
    return next()
};

const createLogsForDELETE = (req, res, next) => {
  const data = req.body;
  const trigger=req.trigger;
  if(trigger.trigger_type === "card"){
    Triggers_Logs.create({
      trigger_id:data.trigger_id,
      bot_id:trigger.bot_id,
      trigger_type:"delete_card",
      user_id:data.user_id
    })
      .then(() => {
        return next();
      })
  }else if(trigger.trigger_type === "dialog"){
    Triggers_Logs.create({
      trigger_id:data.trigger_id,
      bot_id:trigger.bot_id,
      trigger_type:"delete_dialog",
      user_id:data.user_id
    })
      .then(() => {
        return next();
      })
  }
};
const setMany = (req, res, next) => {
  const data = [...req.body];
  Trigger.bulkCreate(data).then((triggers) => {
    req.triggers = triggers;
    return next();
  });
};

const purgeAll = (req, res, next) => {
  const bot_id = req.body.bot_id;
  Trigger.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    res.send({ message: "triggers deleted" });
  });
};

const purgeMany = (req, res, next) => {
  const trigger_ids = [...req.body].map((a) => a.trigger_id);
  const bot_ids = [...req.body].map((a) => a.bot_id);

  Trigger.destroy({
    where: {
      bot_id: bot_ids,
      trigger_id: trigger_ids,
    },
  }).then(() => {
    return next();
  });
};

const purge = async (req, res, next) => {
  const trigger_id = req.body.trigger_id;
  const trigger = await Trigger.findOne({
    where: {
      trigger_id,
    },
  });
  const bot_id = trigger.bot_id; 
  Trigger.destroy({
    where: {
      trigger_id,
    },
  }).then(() => {
    req.bot_id = bot_id;
    return next();
    // res.send({ message: "trigger deleted" });
  });
};

const update = async (req, res, next) => {
  const trigger = req.trigger;
  const data = { ...req.body };
  var type = ""
  if(data.trigger_type === "card"){
   type = "update_card";
  }else if (data.trigger_type === "dialog"){
    type = "update_dialog"
  }
    trigger.updateInfo(data);
    req.trigger = trigger;
    req.type=type;
    await trigger.save();
    return next();
};

const updateMany = async (req, res, next) => {
  const triggers = req.triggers;
  const data = [...req.body];
  // triggers.map(async (trigger) => {
  for (var i = 0; i < triggers.length; i++) {
    var trigger = triggers[i];
    var triggerData = data.find((a) => a.trigger_id === trigger.trigger_id);
    trigger.updateInfo(triggerData);
    await trigger.save();
  }
  // });
  return next();
};

const setDefaultTriggers = (req, res, next) => {
  const bot_id = req.bot.bot_id;
  const data = [
    {
      trigger_name: "__Weather__trigger__" + bot_id,
      trigger_type: "dialog",
      trigger: "weather",
      url: "",
      bot_id,
    },
    {
      trigger_name: "__Weather__trigger__" + bot_id,
      trigger_type: "dialog",
      trigger: "الطقس",
      url: "",
      bot_id,
    },
    {
      trigger_name: "__Working__hours__trigger__" + bot_id,
      trigger_type: "card",
      trigger: "working hours",
      url: "",
      bot_id,
    },
    {
      trigger_name: "__Working__hours__trigger__" + bot_id,
      trigger_type: "card",
      trigger: "ساعات العمل",
      url: "",
      bot_id,
    },
    {
      trigger_name: "__address__trigger__" + bot_id,
      trigger_type: "card",
      trigger: "العنوان address",
      url: "",
      bot_id,
    },
    {
      trigger_name: "__address__trigger__" + bot_id,
      trigger_type: "card",
      trigger: "address",
      url: "",
      bot_id,
    },
    {
      trigger_name: "__privacy__trigger__" + bot_id,
      trigger_type: "card",
      trigger: "privacy",
      url: "",
      bot_id,
    },
    {
      trigger_name: "__terms__trigger__" + bot_id,
      trigger_type: "dialog",
      trigger: "terms of service",
      url: "",
      bot_id,
    },
    {
      trigger_name: "__trading__trigger__" + bot_id,
      trigger_type: "dialog",
      trigger: "trading",
      url: "",
      bot_id,
    },
    {
      trigger_name: "__reservation__table__trigger__" + bot_id,
      trigger_type: "dialog",
      trigger: "reservation",
      url: "",
      bot_id,
    },
    {
      trigger_name: "__cart__trigger__" + bot_id,
      trigger_type: "dialog",
      trigger: "reservation",
      url: "",
      bot_id,
    },
  ];
  Trigger.bulkCreate(data).then((triggers) => {
    req.triggers = triggers;
    return next();
  });
};
const lemmtaizationMany = async (req, res, next) => {
  try {
    const data = [...req.body];
    const newData = await Promise.all(
      data.map(async (ent) => {
        return {
          ...ent,
          lemmatized_trigger: ent?.lemmatized_trigger
            ? (await lemmtaizationLocal(ent?.lemmatized_trigger))?.data?.answer ||
              ent.trigger
            : "",
        };
      })
    );
    req.body = newData;
    next();
  } catch (error) {
console.log(error)
next()
  }
};
module.exports = {
  get,
  getAll,
  set,
  update,
  purge,
  purgeMany,
  updateMany,
  setMany,
  preproccess,
  preproccessAll,
  setDefaultTriggers,
  createLogsForDELETE,
  createLogs,
  lemmtaizationMany,
  getBotTriggers,
};
