const { captcha_secret_key ,captcha_url} = require("../constants");

const verifyCaptcha = async (req, res, next) => {
const captchaToken = req?.body?.captcha_token || ''
  try {
    const response = await fetch(
      captcha_url,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams({
          secret: captcha_secret_key,
          response: captchaToken,
        }),
      }
    );

    const data = await response.json();

    if (data.success) {
      console.log("Captcha verification successful!");
      return next();
    } else {
      console.error("Captcha verification failed:", data["error-codes"]);
      return res.status(401).send({ message: "Captcha verification failed:" });
    }
  } catch (error) {
    console.error("Error verifying captcha:", error);
    return res.status(401).send({ message: "Error verifying captcha:" });
  }
};
const sendCaptchaSuccessResponse = async (req, res, next) => {
  return res.status(200).send({ message: "Captcha verification successful:" });
};

module.exports = {
  verifyCaptcha,
  sendCaptchaSuccessResponse 
};
