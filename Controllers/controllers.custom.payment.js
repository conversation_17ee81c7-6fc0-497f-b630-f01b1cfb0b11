const Custom_Payment = require("../Models/Custom.Payment");
const Custom_Class = require("../Models/Custom.Class")

const get = (req, res) => {
  res.send(req.custom_payment);
};


const updatePayment = async (req,res,next) => {
  const class_id = req.body.class_id;
  const payment = req.custom_payment;
  const paid_balance = req.body.paid_balance
  Custom_Class.findOne({ where: { class_id: class_id } }).then((custom_class)=>{
    payment.updateInfo({
      ...payment,
      unpaid_balance: (custom_class.amount - paid_balance)
  });
   payment.save();
   return next()
  })
}

const set = (req, res, next) => {
  const data = {...req.body};
  Custom_Payment.create(data).then((custom_payment) => {
    req.custom_payment = custom_payment;
    return next();
  });
};


const update = async (req, res, next) => {
  const custom_payment = req.custom_payment;
  const data = { ...req.body };
  custom_payment.updateInfo(data);
  req.custom_payment = custom_payment;
  await custom_payment.save();
  return next();
};

const purge = (req, res, next) => {
    const payment_id = req.body.payment_id;
    const bot_id = req.body.bot_id
    Custom_Payment.destroy({
      where: {
        payment_id,bot_id
      },
    }).then(() => {
      res.send({ message: "Payment is deleted" });
    });
  };


module.exports = {
  get,
  update,
  set,
  purge,
  updatePayment
};
