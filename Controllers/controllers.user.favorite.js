const UserFavorite = require("../Models/User.Favorite");

const set = (req, res, next) => {
  const data = { ...req.body };
  UserFavorite.create({
    item_id: data.item_id,
    user_id: data.user_id,
  }).then((userfavorite) => {
    req.userfavorite = userfavorite;
    return next();
  });
};

const get = (req, res, next) => {
  res.send(req.userfavorite);
};

const getAll = (req, res, next) => {
  res.send(req.userfavorites);
};

const purge = (req, res, next) => {
  const data = { ...req.body };
  UserFavorite.destroy({
    where: {
      favorite_id: data.favorite_id,
      // user_id: data.user_id,
    },
  }).then(() => {
    res.send({ message: "Fav deleted successfully" });
  });
};

module.exports = {
  set,
  get,
  getAll,
  purge,
};
