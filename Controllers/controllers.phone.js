const { sendSMS } = require("../Services/SMS");
const Phone = require("../Models/Phone");
const { toEnglishTokenize } = require("./controllers.engine");

function makeCode(length) {
  var result = "";
  var characters = "0123456789";
  var charactersLength = characters.length;
  for (var i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

const set = (req, res, next) => {
  const phone_number = toEnglishTokenize(req.body.phone);
  const bot_id = req.body.bot_id;
  const code = makeCode(3);
  console.log("phone_number", phone_number);
  console.log("bot_id", bot_id);
  Phone.create({
    code,
    bot_id,
    phone: phone_number,
  }).then((phone) => {
    req.phone = phone;
    return next();
  });
};

const sendCode = async (req, res, next) => {
  const { phone, code } = req.phone;
  await sendSMS(phone, code);

  res.send({ message: "code was sent to your phone please check" });
};

const checkCode = (req, res, next) => {
  const phone = req.phone;
  const userCode = req.body.code;
  if (phone.code === userCode) {
    return next();
  } else {
    res.send({ message: "The submitted code is incorrect" });
  }
};

const checkVerified = (req, res, next) => {
  const phone = req.phone;
  if (!phone.verified) {
    return next();
  } else {
    res.send({ message: "Already verified" });
  }
};

const verifyPhone = (req, res, next) => {
  const phone = req.phone;
  phone.verify();
  phone.save();
  return next();
};

const sendVerifyMessage = (req, res) => {
  res.send({
    message: "Verified successfully",
    success: true,
    phone: req.phone,
    botuser: req.botuser,
  });
};

const purge = (req, res, next) => {
  const phone_number = req.body.phone;
  const bot_id = req.body.bot_id;

  Phone.destroy({
    where: {
      verified: false,
      phone: phone_number,
      bot_id,
    },
  }).then(() => next());
};

const getAll = (req, res, next) => {
  res.send(req.phones);
};

module.exports = {
  sendCode,
  set,
  checkCode,
  checkVerified,
  verifyPhone,
  sendVerifyMessage,
  purge,
  getAll,
};
