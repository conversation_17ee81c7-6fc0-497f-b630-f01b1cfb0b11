const WhatsappBroadCast = require("../Models/Whatsapp.broadcasting");

const get = (req, res) => {
  res.send(req.broadcast);
};

const getLogs = (req, res) => {
    res.send(req.logs);
};

const getAll = (req, res) => {
    res.send(req.broadcasts);
};

const set = (req, res, next) => {
  const data = req.body;
  WhatsappBroadCast.create({
    ...data
  })
    .then((broadcast) => {
      req.broadcast = broadcast;
      return next();
    })
    .catch((err) => res.send({ message: err }));
};


const update = async (req, res, next) => {
  const broadcast = req.broadcast;
  const data = { ...req.body };
  broadcast.updateInfo(data);
  await broadcast.save();
  return next();
};

const purge = async (req, res, next) => {
  const broadcast_id = req.body.broadcast_id;
  WhatsappBroadCast.destroy({
    where: {
        broadcast_id,
    },
  }).then(() => {
    res.send({ message: "broadcast data deleted" });
  });
};

const getAllNumbers = (req, res) => {
  res.send(req.numbers);
};
module.exports = { 
    getAll,
    get, 
    set, 
    update,
    purge,
    getLogs,
    getAllNumbers
};
