const SanadTrigger = require("../Models/Sanad.Trigger");
const sequelize = require("../db");

const get = (req, res) => {
  res.send(req.trigger);
};

const getAll = (req, res) => {
  res.send(req.triggers);
};

const trigger_types = [
  "suggestion",
  "dialog",
  "card",
  "whatsapp_buttons",
  "whatsapp_list",
  "whatsapp_template",
];

const preproccess = (req, res, next) => {
  const trigger_type = req.body.trigger_type;

  if (trigger_types.indexOf(trigger_type) === -1) {
    return res.send({ message: "invalid trigger_type" });
  } else {
    return next();
  }
};

const preproccessAll = (req, res, next) => {
  const data = req.body;
  var exit = false;
  data.map((trig) => {
    if (!exit && trigger_types.indexOf(trig.trigger_type) === -1) {
      exit = true;
      return res.send({ message: "invalid trigger_type", trigger: trig });
    }
  });
  if (!exit) {
    return next();
  }
};

const set = (req, res, next) => {
  const data = { ...req.body };
  var type = ""
  if(data.trigger_type === "card"){
   type = "add_card";
  }else if (data.trigger_type === "dialog"){
    type = "add_dialog"
  }
  SanadTrigger.create({
    trigger: data.trigger,
    trigger_name: data.trigger_name,
    sanad_service_id:data.sanad_service_id,
    lemmatized_trigger:data.lemmatized_trigger,
    bot_id: data.bot_id,
    trigger_type: data.trigger_type,
    url: data.url,
  }).then((trigger) => {
    req.trigger = trigger;
    req.type = type;
    return next();
  });
};

const setMany = (req, res, next) => {
  const data = [...req.body];
  SanadTrigger.bulkCreate(data).then((triggers) => {
    req.triggers = triggers;
    return next();
  });
};

const purgeAll = (req, res, next) => {
  const bot_id = req.body.bot_id;
  SanadTrigger.destroy({
    where: {
      bot_id,
    },
  }).then(() => {
    res.send({ message: "triggers deleted" });
  });
};

const purgeMany = (req, res, next) => {
  const trigger_ids = [...req.body].map((a) => a.sanad_trigger_id);
  const bot_ids = [...req.body].map((a) => a.bot_id);

  SanadTrigger.destroy({
    where: {
      bot_id: bot_ids,
      sanad_trigger_id: trigger_ids,
    },
  }).then(() => {
    return next();
  });
};

const purge = (req, res, next) => {
  const sanad_trigger_id = req.body.sanad_trigger_id;
  SanadTrigger.destroy({
    where: {
        sanad_trigger_id,
    },
  }).then(() => {
    res.send({ message: "trigger deleted" });
  });
};

const update = async (req, res, next) => {
  const trigger = req.trigger;
  const data = { ...req.body };
  var type = ""
  if(data.trigger_type === "card"){
   type = "update_card";
  }else if (data.trigger_type === "dialog"){
    type = "update_dialog"
  }
    trigger.updateInfo(data);
    req.trigger = trigger;
    req.type=type;
    await trigger.save();
    return next();
};

const updateMany = async (req, res, next) => {
  const triggers = req.triggers;
  const data = [...req.body];
  // triggers.map(async (trigger) => {
  for (var i = 0; i < triggers.length; i++) {
    var trigger = triggers[i];
    var triggerData = data.find((a) => a.sanad_trigger_id === trigger.sanad_trigger_id);
    trigger.updateInfo(triggerData);
    await trigger.save();
  }
  // });
  return next();
};

const setDefaultTriggers = (req, res, next) => {
  const bot_id = req.bot.bot_id;
  const data = [
    {
      trigger_name: "__Weather__trigger__" + bot_id,
      trigger_type: "dialog",
      trigger: "weather",
      url: "",
      bot_id,
    },
    {
      trigger_name: "__Weather__trigger__" + bot_id,
      trigger_type: "dialog",
      trigger: "الطقس",
      url: "",
      bot_id,
    },
    {
      trigger_name: "__Working__hours__trigger__" + bot_id,
      trigger_type: "card",
      trigger: "working hours",
      url: "",
      bot_id,
    },
    {
      trigger_name: "__Working__hours__trigger__" + bot_id,
      trigger_type: "card",
      trigger: "ساعات العمل",
      url: "",
      bot_id,
    },
    {
      trigger_name: "__address__trigger__" + bot_id,
      trigger_type: "card",
      trigger: "العنوان address",
      url: "",
      bot_id,
    },
    {
      trigger_name: "__address__trigger__" + bot_id,
      trigger_type: "card",
      trigger: "address",
      url: "",
      bot_id,
    },
    {
      trigger_name: "__privacy__trigger__" + bot_id,
      trigger_type: "card",
      trigger: "privacy",
      url: "",
      bot_id,
    },
    {
      trigger_name: "__terms__trigger__" + bot_id,
      trigger_type: "dialog",
      trigger: "terms of service",
      url: "",
      bot_id,
    },
    {
      trigger_name: "__trading__trigger__" + bot_id,
      trigger_type: "dialog",
      trigger: "trading",
      url: "",
      bot_id,
    },
    {
      trigger_name: "__cart__trigger__" + bot_id,
      trigger_type: "dialog",
      trigger: "reservation",
      url: "",
      bot_id,
    },
  ];
  SanadTrigger.bulkCreate(data).then((triggers) => {
    req.triggers = triggers;
    return next();
  });
};

const getAllTriggersPerSer = async (req, res) => {
  const bot_id= req.query.bot_id;
 const data= await sequelize.query("designer_get_triggers_related_service :bot_id ", {
      replacements: {
        bot_id: bot_id,
      },
    });
    return res.send(data[0]);
  };

module.exports = {
  get,
  getAll,
  set,
  update,
  purge,
  purgeMany,
  updateMany,
  setMany,
  preproccess,
  preproccessAll,
  setDefaultTriggers,
  purgeAll,
  getAllTriggersPerSer
};
