const BadWord = require("../Models/BadWord");
const { toEnglishTokenize } = require("./controllers.engine");


const get = (req, res) => {
  res.send(req.badWord);
};
const getAll = (req, res) => {
  res.send(req.badWords);
};

const set = (req, res, next) => {
  const data = { ...req.body };
  BadWord.create({
    bot_id: data.bot_id,
    bad_word: toEnglishTokenize(data.bad_word),
  }).then((badWord) => {
    req.badWord = badWord;
    return next();
  });
};

const setMany = (req, res, next) => {
  const data = [...req.body];
  BadWord
    .bulkCreate(
      data.map((ent) => {
        return {
          ...ent,
          bad_word: toEnglishTokenize(ent.bad_word),
        };
      })
    )
    .then((badWords) => {
      req.badWords = badWords;
      return next();
      // res.send(badWords);
    });
};

const checkExists = async(req, res, next) => {
  const data = { ...req.body };
 const badword = await BadWord.findOne({
  where: {
    bot_id: data.bot_id,
    bad_word:data.bad_word,
  },
});
if(badword?.bad_word) {
  return res.send({ message: `${data?.bad_word} Already Exists` })
}
return next()
}

const purge = async (req, res, next) => {
  const data = { ...req.body };
  const smalltalk = await BadWord.findOne({
    where: {
      bad_word_id: data.bad_word_id,
    },
  });
  const bot_id = smalltalk.bot_id; 
  BadWord
    .destroy({
      where: {
        bad_word_id: data.bad_word_id,
      },
    })
    .then(() => {
      req.bot_id = bot_id;
      return next();
      // return res.send({ message: "badWord deleted successfully" });
    });
};

const update = async (req, res, next) => {
  const badWord = req.badWord;
  const data = { ...req.body };
  badWord.updateInfo(data);
  req.badWord = badWord;
  await badWord.save();
  return next();
};


const updateMany = async (req, res, next) => {
  const badWords = req.badWords;
  const data = [...req.body];

  for (var i = 0; i < badWords.length; i++) {
    var badWord = badWords[i];
    var badWordData = data.find(
      (a) => a.bad_word_id === badWord.bad_word_id
    );
    badWord.updateInfo(badWordData);
    await badWord.save();
  }
  return next();
};

const purgeAllBadWords = (req, res, next) => {
  const bot_id = req.body.bot_id;
  BadWord
    .destroy({
      where: {
        bot_id,
      },
    })
    .then(() => {
      return next();
      // return res.send({ message: "many where deleted" });
    });
};

const purgeMany = (req, res, next) => {
  const bad_word_ids = req.body.badwords;
  const bot_id = req.body.bot_id;
  BadWord
    .destroy({
      where: {
        bot_id: bot_id,
        bad_word_id: bad_word_ids,
      },
    })
    .then(() => {
      return next();
    });
};


module.exports = {
  get,
  getAll,
  set,
  purge,
  update,
  setMany,
  purgeAllBadWords,
  purgeMany,
  updateMany,
  checkExists
};
