const data =  require("./countries.json");


const fetch = require("node-fetch");
const {
serverUrl
} = require("./constants")

const getHeader = () => {
  const header = {
    Accept: "application/json",
    "Content-Type": "application/json",
  };
  return header;
};
const dest = "ner";

const createPostJSON = (data) => {
  const lbc = {
    method: "POST",
    headers: getHeader(),
    withCredentials: true,
    crossdomain: true,
    body: JSON.stringify(data),
  };
  return lbc;
};

const createPutJSON = (data) => {
  const lbc = {
    method: "PUT",
    headers: getHeader(),
    withCredentials: true,
    crossdomain: true,
    body: JSON.stringify(data),
  };
  return lbc;
};
const createDELETEJSON = (data) => {
  const lbc = {
    method: "DELETE",
    headers: getHeader(),
    withCredentials: true,
    crossdomain: true,
    body: JSON.stringify(data),
  };
  return lbc;
};

const createGetJSON = () => {
  const lbc = {
    method: "GET",
    headers: getHeader(),
    withCredentials: true,
    crossdomain: true,
    timeout: 5000,
  };
  return lbc;
};

const distinct = (arr, by) => arr.reduce((acc, current) => {
    const x = acc.find(item => item[by] === current[by]);
    if (!x) {
      return acc.concat([current]);
    } else {
      return acc;
    }
  }, []);

  const countries = distinct(data, "country").map(a => a.country);
  const cities = distinct(data, "subcountry").map(a => a.subcountry);



  console.log("countries",countries.length)
  console.log("cities",cities.length)


  const creatOne = (data) => {
    return fetch(serverURL.concat(dest), createPostJSON(data)).then((response) =>
      response.json()
    );
  };
  
  cities.forEach(city => {
    creatOne({
        ner_type: "city",
        ner_value: city,
        language: "en"
    });
  });