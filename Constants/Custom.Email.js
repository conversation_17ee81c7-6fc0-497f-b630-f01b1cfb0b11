const {
    serverUrl,
    wolvesUrl
  } = require("../constants")
  
  const createVerifyEmail = (email, custom_user_id) => {
    return `HI ${email} <a href="${serverUrl}/api/custom_verify?custom_user_id=${custom_user_id}">CLICK HERE</a>`;
  };
  const createResetPassword = (email, custom_user_id) => {
    return `HI ${email} <a href="${wolvesUrl}/api/custom/new-password?custom_user_id=${custom_user_id}">CLICK HERE</a>`;
  };
  
  const createOrderEmail = (items, cart, info) => {
    const element = `
    <h1>LEGEND's order</h1>
    <h2>NAME: ${info.name} </h2>
    <h2>ADDRESS: ${info.address} </h2>
    <h2>PHONE: ${info.phone} </h2>
    <h2>PRICE: ${info.total_price} </h2>
    <br/>
    <ul>
    <li>${JSON.stringify(cart)}</li>
    <li>${JSON.stringify(items)}</li>
    </ul>`;
    return `${element}`;
  };
  
  module.exports = { createVerifyEmail, createResetPassword, createOrderEmail };
  