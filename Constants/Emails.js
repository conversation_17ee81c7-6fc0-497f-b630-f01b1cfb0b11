const {
  serverUrl,
  builderUrl
} = require("../constants")

const createVerifyEmail = (email, user_id) => {
  return `HI ${email} <a href="${serverUrl}/api/verify?user_id=${user_id}">CLICK HERE</a>`;
};
const createResetPassword = (email, user_id) => {
  return `HI ${email} <a href="${builderUrl}/reset-password?user_id=${user_id}">CLICK HERE</a>`;
};

const createOrderEmail = (items, cart, info) => {
  const element = `
  <div style="font-family: Arial, sans-serif; line-height: 1.6;">
    <table align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="max-width: 100%; margin: 0 auto; padding: 20px 0 48px; width: 660px;">
      <tbody>
        <tr>
          <td>
            <table align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: collapse; border-radius: 3px; margin: 30px 0 15px 0; padding: 20px; background-color: #ffffff;">
              <tbody>
                <!-- Heading Section -->
                <tr>
                  <td style="padding-bottom: 20px; border-bottom: 1px solid #ddd;">
                    <h1 style="font-size: 24px; font-weight: 600; color: #000; margin: 0;">New Order Received</h1>
                    <p style="font-size: 16px; font-weight: 400; color: #000; margin: 5px 0 0 0;">An order has been placed by ${info.name}.</p>
                    <p style="font-size: 16px; font-weight: 400; color: #000; margin: 5px 0 0 0;">Please prepare the following items for shipping.</p>
                  </td>
                </tr>

                <!-- Order Summary Section -->
                <tr>
                  <td style="padding-top: 20px;">
                    <h3 style="font-size: 18px; font-weight: 600; color: #000; margin-bottom: 10px;">Order Summary:</h3>
                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                      <tbody>
                        ${items.map((item, i) => `
                          <tr>
                            <td style="padding: 10px; border-bottom: 1px solid #eee; font-size: 14px; vertical-align: top;">
                              <img src="${item.dataValues.item_icons}" alt="${item.dataValues.item_title}" style="width: 50px; height: 50px; object-fit: cover;" />
                            </td>
                            <td style="padding: 10px; border-bottom: 1px solid #eee; font-size: 14px;">
                              <strong>${item.dataValues.item_title}</strong><br />
                              ${item.dataValues.item_description}<br />
                              <strong>Price:</strong> ${item.dataValues.item_price.toFixed(2)} ${item.currency}<br />
                              <strong>Qty:</strong> ${cart[i].qty}
                            </td>
                            <td style="padding: 10px; text-align: right; border-bottom: 1px solid #eee; font-size: 14px; color: #0070c9;">
                              ${(item.dataValues.item_price * cart[i].qty).toFixed(2)} ${item.currency}
                            </td>
                          </tr>`).join('')}
                        <!-- Total Price Row -->
                        <tr>
                          <td colspan="2" style="padding: 10px; font-size: 16px; font-weight: 600; text-align: right; background-color: #f8f8f8; border-top: 2px solid #ddd; color: #000;">
                            Total Price:
                          </td>
                          <td style="padding: 10px; font-size: 16px; font-weight: 600; text-align: right; background-color: #f8f8f8; border-top: 2px solid #ddd; color: #0070c9;">
                            ${info.total_price}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>

                <!-- Customer Information Section -->
                <tr>
                  <td style="padding-top: 20px;">
                    <h3 style="font-size: 18px; font-weight: 600; color: #000; margin-bottom: 10px;">Customer Info:</h3>
                    <table width="100%" style="border-collapse: collapse; margin-bottom: 20px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
                      <tbody>
                        <tr>
                          <td style="padding: 15px; background-color: #f5f5f5; border-bottom: 1px solid #ddd; width: 150px;">
                            <strong style="font-size: 16px; color: #555;">&#128100; Name:</strong>
                          </td>
                          <td style="padding: 15px; background-color: #f5f5f5; border-bottom: 1px solid #ddd;">
                            <span style="font-size: 16px; color: #333;">${info.name}</span>
                          </td>
                        </tr>
                        <tr>
                          <td style="padding: 15px; background-color: #fff; border-bottom: 1px solid #ddd; width: 150px;">
                            <strong style="font-size: 16px; color: #555;">&#127968; Address:</strong>
                          </td>
                          <td style="padding: 15px; background-color: #fff; border-bottom: 1px solid #ddd;">
                            <span style="font-size: 16px; color: #333;">${info.address}</span>
                          </td>
                        </tr>
                        <tr>
                          <td style="padding: 15px; background-color: #f5f5f5; width: 150px;">
                            <strong style="font-size: 16px; color: #555;">&#9742; Phone:</strong>
                          </td>
                          <td style="padding: 15px; background-color: #f5f5f5;">
                            <span style="font-size: 16px; color: #333;">${info.phone}</span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  `;
  return element;
};



const createCustomerEmail = (items, cart, info) => {
  const element = `
  <div style="font-family: Arial, sans-serif; line-height: 1.6;">
    <table align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="max-width: 100%; margin: 0 auto; padding: 20px 0 48px; width: 660px;">
      <tbody>
        <tr>
          <td>
            <table align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse: collapse; border-radius: 3px; margin: 30px 0 15px 0; padding: 20px; background-color: #ffffff;">
              <tbody>
                <!-- Heading Section -->
                <tr>
                  <td style="padding-bottom: 20px; border-bottom: 1px solid #ddd;">
                    <h1 style="font-size: 24px; font-weight: 600; color: #000; margin: 0;">Thank you for your order</h1>
                    <p style="font-size: 16px; font-weight: 400; color: #000; margin: 5px 0 0 0;">Dear ${info.name},</p>
                    <p style="font-size: 16px; font-weight: 400; color: #000; margin: 5px 0 0 0;">Your order has been placed successfully.</p>
                  </td>
                </tr>

                <!-- Order Summary Section -->
                <tr>
                  <td style="padding-top: 20px;">
                    <h3 style="font-size: 18px; font-weight: 600; color: #000; margin-bottom: 10px;">Your Order:</h3>
                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                      <tbody>
                        ${items.map((item, i) => 
                          `<tr>
                            <td style="padding: 10px; border-bottom: 1px solid #eee; font-size: 14px; vertical-align: top;">
                              <img src="${item.dataValues.item_icons}" alt="${item.dataValues.item_title}" style="width: 50px; height: 50px; object-fit: cover;" />
                            </td>
                            <td style="padding: 10px; border-bottom: 1px solid #eee; font-size: 14px;">
                              <strong>${item.dataValues.item_title}</strong><br />
                              ${item.dataValues.item_description}<br />
                              <strong>Price:</strong> ${item.dataValues.item_price.toFixed(2)} ${item.currency}<br />
                              <strong>Qty:</strong> ${cart[i].qty}
                            </td>
                            <td style="padding: 10px; text-align: right; border-bottom: 1px solid #eee; font-size: 14px; color: #0070c9;">
                              ${(item.dataValues.item_price * cart[i].qty).toFixed(2)} ${item.currency}
                            </td>
                          </tr>`).join('')}
                        <!-- Total Price Row -->
                        <tr>
                          <td colspan="2" style="padding: 10px; font-size: 16px; font-weight: 600; text-align: right; background-color: #f8f8f8; border-top: 2px solid #ddd; color: #000;">
                            Total Price:
                          </td>
                          <td style="padding: 10px; font-size: 16px; font-weight: 600; text-align: right; background-color: #f8f8f8; border-top: 2px solid #ddd; color: #0070c9;">
                            ${info.total_price}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>

                <!-- Customer Information Section -->
                <tr>
                  <td style="padding-top: 20px;">
                    <h3 style="font-size: 18px; font-weight: 600; color: #000; margin-bottom: 10px;">Customer Info:</h3>
                    <table width="100%" style="border-collapse: collapse; margin-bottom: 20px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
                      <tbody>
                        <tr>
                          <td style="padding: 15px; background-color: #f5f5f5; border-bottom: 1px solid #ddd; width: 150px;">
                            <strong style="font-size: 16px; color: #555;">&#128100; Name:</strong>
                          </td>
                          <td style="padding: 15px; background-color: #f5f5f5; border-bottom: 1px solid #ddd;">
                            <span style="font-size: 16px; color: #333;">${info.name}</span>
                          </td>
                        </tr>
                        <tr>
                          <td style="padding: 15px; background-color: #fff; border-bottom: 1px solid #ddd; width: 150px;">
                            <strong style="font-size: 16px; color: #555;">&#127968; Address:</strong>
                          </td>
                          <td style="padding: 15px; background-color: #fff; border-bottom: 1px solid #ddd;">
                            <span style="font-size: 16px; color: #333;">${info.address}</span>
                          </td>
                        </tr>
                        <tr>
                          <td style="padding: 15px; background-color: #f5f5f5; width: 150px;">
                            <strong style="font-size: 16px; color: #555;">&#9742; Phone:</strong>
                          </td>
                          <td style="padding: 15px; background-color: #f5f5f5;">
                            <span style="font-size: 16px; color: #333;">${info.phone}</span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  </div>`
;

  return `${element}`;
};

const createTicketNewCommentEmail = (name, info, bot) => {
  return `<html dir="ltr" lang="en">

  <head>
    <link rel="preload" as="image" href=${bot.bot_icon} />
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type" />
    <meta name="x-apple-disable-message-reformatting" /><!--$-->
  </head>


  <body style="background-color:#fff;font-family:-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Roboto,Oxygen-Sans,Ubuntu,Cantarell,&quot;Helvetica Neue&quot;,sans-serif">
    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="max-width:37.5em">
      <tbody>
        <tr style="width:100%">
          <td>
            <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="padding:30px 20px">
              <tbody>
                <tr>
                  <td style="display: flex; gap: 5px; align-items: center; width: 100%;">
                    <img src="${bot.bot_icon}" style="display: block; outline: none; border: none; text-decoration: none; width: 50px; height: auto;" />
                    <h3 style="font-size: 26px; text-align: center; margin: 0 5px;">${bot.bot_name}</h3>
                  </td>
                </tr>
              </tbody>
            </table>
            <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="border:1px solid rgb(0,0,0, 0.1);border-radius:3px;overflow:hidden">
              <tbody>
                <tr>
                  <td>
                    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="padding:20px;padding-bottom:0">
                      <tbody style="width:100%">
                        <tr style="width:100%">
                          <td data-id="__react-email-column">
                            <h1 style="font-size:32px;font-weight:bold;text-align:center">Hi ${name},</h1>
                            <h2 style="font-size:26px;font-weight:bold;text-align:center">A new comment has been added to your ticket by ${info.supportAgent?.editor?.email}.</h2>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                             <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="padding:20px;padding-bottom:0">
                      <tbody style="width:100%">
                        <tr style="width:100%">
                          <td data-id="__react-email-column">
                            <p style="font-size:16px;line-height:24px;margin:16px 0;margin-top:-5px">
                              ${info.comment.comment}
                            </p>
                          </td>
                        </tr>
                      </tbody>
                    </table>
               <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="padding:20px;padding-top:0;margin: 0 auto;">
                  <tbody style="width:100%">
                    <tr style="width:100%">
                      <td colSpan="2" data-id="__react-email-column" style="display:flex;justify-content:center;width:100%;text-align:center">
                        <a style="line-height:100%;text-decoration:none;display:inline-block;max-width:100%;mso-padding-alt:0px;background-color:#a855f7;border-radius:3px;color:#FFF;font-weight:bold;border:1px solid rgb(0,0,0, 0.1);cursor:pointer;padding:12px 30px 12px 30px;margin: 0 auto;" target="_blank" href="${builderUrl}/tickets/${info.ticket.ticket_uuid}">
                          <span><!--[if mso]><i style="mso-font-width:500%;mso-text-raise:18" hidden>&#8202;&#8202;&#8202;</i><![endif]--></span>
                          <span style="max-width:100%;display:inline-block;line-height:120%;mso-padding-alt:0px;mso-text-raise:9px">View Ticket</span>
                          <span><!--[if mso]><i style="mso-font-width:500%" hidden>&#8202;&#8202;&#8202;&#8203;</i><![endif]--></span>
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>
                  </td>
                </tr>
              </tbody>
            </table>
            <p style="font-size:12px;line-height:24px;margin:16px 0;text-align:center;color:rgb(0,0,0, 0.7)">Powered by SearChat. | www.searchat.com</p>
          </td>
        </tr>
      </tbody>
    </table><!--/$-->
  </body>

</html>`;
}

const createTicketUpdatesEmail = (name, info, bot) => {
  return `<html dir="ltr" lang="en">

  <head>
    <link rel="preload" as="image" href=${bot.bot_icon} />
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type" />
    <meta name="x-apple-disable-message-reformatting" /><!--$-->
  </head>


  <body style="background-color:#fff;font-family:-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Roboto,Oxygen-Sans,Ubuntu,Cantarell,&quot;Helvetica Neue&quot;,sans-serif">
    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="max-width:37.5em">
      <tbody>
        <tr style="width:100%">
          <td>
            <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="padding:30px 20px">
              <tbody>
                <tr>
                  <td style="display: flex; gap: 5px; align-items: center; width: 100%;">
                    <img src="${bot.bot_icon}" style="display: block; outline: none; border: none; text-decoration: none; width: 50px; height: auto;" />
                    <h3 style="font-size: 26px; text-align: center; margin: 0 5px;">${bot.bot_name}</h3>
                  </td>
                </tr>
              </tbody>
            </table>
            <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="border:1px solid rgb(0,0,0, 0.1);border-radius:3px;overflow:hidden">
              <tbody>
                <tr>
                  <td>
                    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="padding:20px;padding-bottom:0">
                      <tbody style="width:100%">
                        <tr style="width:100%">
                          <td data-id="__react-email-column">
                            <h1 style="font-size:32px;font-weight:bold;text-align:center">Hi ${name},</h1>
                            <h2 style="font-size:26px;text-align:center">Your ticket status has beed updated to ${info.ticket.status}.</h2>
                            ${
                              info.ticket.status === "closed" ?
                              `
                            <p style="font-size: 16px; font-weight: 400; color: #000; margin: 5px;">
                            Please rate your experience with the support agent here: 
                            ` : ''
                            }
                          </td>
                        </tr>
                      </tbody>
                    </table>

               <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="padding:20px;padding-top:0;margin: 0 auto;">
                  <tbody style="width:100%">
                    <tr style="width:100%">
                      <td colSpan="2" data-id="__react-email-column" style="display:flex;justify-content:center;width:100%;text-align:center">
                        <a style="line-height:100%;text-decoration:none;display:inline-block;max-width:100%;mso-padding-alt:0px;background-color:#a855f7;border-radius:3px;color:#FFF;font-weight:bold;border:1px solid rgb(0,0,0, 0.1);cursor:pointer;padding:12px 30px 12px 30px;margin: 0 auto;" target="_blank" href="${builderUrl}/tickets/${info.ticket.ticket_uuid}">
                          <span><!--[if mso]><i style="mso-font-width:500%;mso-text-raise:18" hidden>&#8202;&#8202;&#8202;</i><![endif]--></span>
                          <span style="max-width:100%;display:inline-block;line-height:120%;mso-padding-alt:0px;mso-text-raise:9px">
                            ${info.ticket.status === "closed" ? "Rate Ticket" : "View Ticket"}
                          </span>
                          <span><!--[if mso]><i style="mso-font-width:500%" hidden>&#8202;&#8202;&#8202;&#8203;</i><![endif]--></span>
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>
                  </td>
                </tr>
              </tbody>
            </table>
            <p style="font-size:12px;line-height:24px;margin:16px 0;text-align:center;color:rgb(0,0,0, 0.7)">Powered by SearChat. | www.searchat.com</p>
          </td>
        </tr>
      </tbody>
    </table><!--/$-->
  </body>

</html>`;
};

const createTicketBotOwnerEmail = (name, info) => {
  return `<html dir="ltr" lang="en">

  <head>
    <link rel="preload" as="image" href=${info.bot.bot_icon} />
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type" />
    <meta name="x-apple-disable-message-reformatting" /><!--$-->
  </head>


  <body style="background-color:#fff;font-family:-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Roboto,Oxygen-Sans,Ubuntu,Cantarell,&quot;Helvetica Neue&quot;,sans-serif">
    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="max-width:37.5em">
      <tbody>
        <tr style="width:100%">
          <td>
            <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="padding:30px 20px">
              <tbody>
                <tr>
                  <td style="display: flex; gap: 5px; align-items: center; width: 100%;">
                    <img src="${info.bot.bot_icon}" style="display: block; outline: none; border: none; text-decoration: none; width: 50px; height: auto;" />
                    <h3 style="font-size: 26px; text-align: center; margin: 0 5px;">${info.bot.bot_name}</h3>
                  </td>
                </tr>
              </tbody>
            </table>
            <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="border:1px solid rgb(0,0,0, 0.1);border-radius:3px;overflow:hidden">
              <tbody>
                <tr>
                  <td>
                    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="padding:20px;padding-bottom:0">
                      <tbody style="width:100%">
                        <tr style="width:100%">
                          <td data-id="__react-email-column">
                            <h1 style="font-size:32px;font-weight:bold;text-align:center">Hi ${name},</h1>
                            <h2 style="font-size:26px;font-weight:bold;text-align:center">A new ticket (${info.ticket.title}) has been created with ticket ID: ${info.ticket.ticket_uuid},
                  In your bot: ${info.bot.bot_name}
                  .</h2>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
            <p style="font-size:12px;line-height:24px;margin:16px 0;text-align:center;color:rgb(0,0,0, 0.7)">Powered by SearChat. | www.searchat.com</p>
          </td>
        </tr>
      </tbody>
    </table><!--/$-->
  </body>

</html>`;
}

const ticketCreatedCustomerEmail = (name,info, bot) => {
  return `<html dir="ltr" lang="en">

  <head>
    <link rel="preload" as="image" href=${bot.bot_icon} />
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type" />
    <meta name="x-apple-disable-message-reformatting" /><!--$-->
  </head>


  <body style="background-color:#fff;font-family:-apple-system,BlinkMacSystemFont,&quot;Segoe UI&quot;,Roboto,Oxygen-Sans,Ubuntu,Cantarell,&quot;Helvetica Neue&quot;,sans-serif">
    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="max-width:37.5em">
      <tbody>
        <tr style="width:100%">
          <td>
            <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="padding:30px 20px">
              <tbody>
                <tr>
                  <td style="display: flex; gap: 5px; align-items: center; width: 100%;">
                    <img src="${bot.bot_icon}" style="display: block; outline: none; border: none; text-decoration: none; width: 50px; height: auto;" />
                    <h3 style="font-size: 26px; text-align: center; margin: 0 5px;">${bot.bot_name}</h3>
                  </td>
                </tr>
              </tbody>
            </table>
            <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="border:1px solid rgb(0,0,0, 0.1);border-radius:3px;overflow:hidden">
              <tbody>
                <tr>
                  <td>
                    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="padding:20px;padding-bottom:0">
                      <tbody style="width:100%">
                        <tr style="width:100%">
                          <td data-id="__react-email-column">
                            <h1 style="font-size:32px;font-weight:bold;text-align:center">Hi ${name},</h1>
                            <h2 style="font-size:26px;font-weight:bold;text-align:center">Your ticket                   (${info.ticket.title}) has been created with ticket ID: ${info.ticket.ticket_uuid}.</h2>
                          </td>
                        </tr>
                      </tbody>
                    </table>
               <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="padding:20px;padding-top:0;margin: 0 auto;">
                  <tbody style="width:100%">
                    <tr style="width:100%">
                      <td colSpan="2" data-id="__react-email-column" style="display:flex;justify-content:center;width:100%;text-align:center">
                        <a style="line-height:100%;text-decoration:none;display:inline-block;max-width:100%;mso-padding-alt:0px;background-color:#a855f7;border-radius:3px;color:#FFF;font-weight:bold;border:1px solid rgb(0,0,0, 0.1);cursor:pointer;padding:12px 30px 12px 30px;margin: 0 auto;" target="_blank" href="${builderUrl}/tickets/${info.ticket.ticket_uuid}">
                          <span><!--[if mso]><i style="mso-font-width:500%;mso-text-raise:18" hidden>&#8202;&#8202;&#8202;</i><![endif]--></span>
                          <span style="max-width:100%;display:inline-block;line-height:120%;mso-padding-alt:0px;mso-text-raise:9px">View Ticket</span>
                          <span><!--[if mso]><i style="mso-font-width:500%" hidden>&#8202;&#8202;&#8202;&#8203;</i><![endif]--></span>
                        </a>
                      </td>
                    </tr>
                  </tbody>
                </table>
                  </td>
                </tr>
              </tbody>
            </table>
            <p style="font-size:12px;line-height:24px;margin:16px 0;text-align:center;color:rgb(0,0,0, 0.7)">Powered by SearChat. | www.searchat.com</p>
          </td>
        </tr>
      </tbody>
    </table><!--/$-->
  </body>

</html>`;
}

module.exports = { createVerifyEmail, createResetPassword, createOrderEmail, createCustomerEmail,createTicketUpdatesEmail, createTicketNewCommentEmail, createTicketBotOwnerEmail, ticketCreatedCustomerEmail };
