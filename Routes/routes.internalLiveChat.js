
const Routes = {
  createAgent: "/create-agent",
  createAgents: "/internal-livechat/create-agents",
  updateAgent: "/internal-livechat/update-agent",
  requestChat: "/request-chat",
  assignChat: "/assign-chat",
  acceptChat: "/accept-chat",
  rejectChat: "/internal-livechat/reject-chat",
  closeChat: "/internal-livechat/close-chat",
  getAllSessions: "/internal-livechat/sessions",
  integration: "/internal-livechat/integration",
  createTransaction: "/internal-livechat/transaction",
  createPendingMessage: "/internal-livechat/pending-message",
  transferPendingMessages: "/internal-livechat/transfer-pending-messages",
  transactions: "/internal-livechat/transactions",
  dropQueue: "/internal-livechat/drop-queue",
  updateAgentStatus: "/update-agent-status",
  updateSessionStatus: "/update-session-status",
  getAgent: "/agent",
  allEditorsAgents: "/all-editors-agents",
  agentLog: "/internal-livechat/agent-log",
  liveChatcalc: "/internal-livechat/live-chat-calc",
  getAgentsData: "/internal-livechat/get-agents-Data",
  getSessionsData: "/internal-livechat/get-sessions-Data",
  getQueuesData: "/internal-livechat/get-queues-data",
  createRating: "/internal-livechat/create-rating",
  getAgentRatings: "/internal-livechat/get-ratings"
};

module.exports = { Routes };
