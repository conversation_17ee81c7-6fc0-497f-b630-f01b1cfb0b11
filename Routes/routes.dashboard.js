const Routes = {
  base: "/dashboard",

  count: "/dashboard/count",
  voice: "/dashboard/voice",
  function: "/dashboard/function",
  category: "/dashboard/category",
  days: "/dashboard/days",
  answer: "/dashboard/answer",
  trend: "/dashboard/trend",

  avgweekday: "/dashboard/avgweekday",
  avgday: "/dashboard/avgday",
  avghour: "/dashboard/avghour",

  notanswered: "/dashboard/notanswered",
  sanadnotanswered: "/dashboard/sanad/notanswered",

  lastn: "/dashboard/lastn",
  sanadlastn: "/dashboard/sanad/lastn",

  channel: "/dashboard/channel",

  launch: "/dashboard/launch",

  conversation: "/dashboard/conversation",

  waconversation: "/dashboard/wa_conversation",

  whatsappPhoneNumbers: "/dashboard/whatsapp/numbers",

  sanadWhatsappPhoneNumbers: "/dashboard/sanad/whatsapp/numbers",

  tansactionsPerPeriod: "/dashboard/trasactions/period",

  countryTransactions: "/dashboard/countryTransactions",

  transactionsGrouped: "/dashboard/transactions/grouped",
  transactionCalculation: "/dashboard/transaction/calculation",
  botUserConversationCalculation: "/dashboard/conversations/calculation",
  healthStatusCheck: "/dashboard/health/current",
  mostAskedPerCountry: "/dashboard/mostAsked/country",
  sanadMostAskedPerCountry: "/dashboard/sanad/mostAsked/country",
  inserTransaction: "/dashboard/transaction/insert",
  deleteTransaction: "/dashboard/transaction/delete",
  transactions: "/dashboard/transactions/get",

  insertSanadTransaction: "/dashboard/sanad/transaction/insert",
  sanadTransactionCalculation: "/dashboard/sanad/transaction/calculation",

  getSanadConversation: "/dashboard/sanad/conversation",
  conversationIds: "/dashboard/conversationIds",
  conversationDetails: "/dashboard/conversationDetails",
  sanadFeedbackStats: "/dashboard/sanad/feedback/stats",
};

module.exports = { Routes };
