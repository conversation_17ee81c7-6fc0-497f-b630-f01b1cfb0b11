const { BOT_MESSAGING_SERVER_URL } = require("../constants");
const { createPostJSON } =require("../helper/header");

const sendToSignalR = async (data) => {
    console.log('data sendToAgent',data)
    return fetch(BOT_MESSAGING_SERVER_URL.concat(`/SendMessage`), createPostJSON(data))
      .catch((err) => console.log('sendToAgent error',err,'sendToAgent error'));
};
module.exports = { sendToSignalR }

