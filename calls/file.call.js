const { createGetJSON, createPostJSON } = require("../helper/header");
const { storageServerUrl } = require("../constants");
const fetch = require("node-fetch");
var characters =
  "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
const generateStorageId = (len = characters.length) => {
  var result = [];
  var charactersLength = characters.length;
  for (var i = 0; i < len; i++) {
    result.push(
      characters.charAt(Math.floor(Math.random() * charactersLength))
    );
  }
  return result.join("");
};
const getFile = (path) => {
  return fetch(
    storageServerUrl.concat(`/getFile?path=${path}`),
    createGetJSON()
  )
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const uploadFile = (data) => {
  return fetch(storageServerUrl.concat("/uploadFile"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};
module.exports = { getFile, uploadFile, generateStorageId };
