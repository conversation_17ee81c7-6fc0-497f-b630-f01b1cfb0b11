const { i2i_RAG_SERVER } = require("../constants");
const { createPostJSON } = require("../helper/header");

const uploadRag = async (data) => {
  return await fetch(
    i2i_RAG_SERVER.concat("upload"),
    createPostJSON({ ...data })
  ).then((response) => {
    return response.json();
  });
};

const testLLM = async (data) => {
  return fetch(i2i_RAG_SERVER.concat("test/llm"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const deleteRag = async (data) => {
  return fetch(i2i_RAG_SERVER.concat("deleteFile"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const refine = async (data) => {
  return fetch(i2i_RAG_SERVER.concat("refinev2"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const testStore = async (data) => {
  return fetch(i2i_RAG_SERVER.concat("test/store"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

//not used
const getFiles = async (data) => {
  return fetch(i2i_RAG_SERVER.concat("getFiles"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};
const getDocuments = async (data) => {
  return fetch(i2i_RAG_SERVER.concat("getFileDocuments"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};
const LLMQuery = async (data) => {
  return fetch(i2i_RAG_SERVER.concat("query"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};
const LLMQueryV1 = async (data) => {
  return fetch(i2i_RAG_SERVER.concat("query/v1"), createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

module.exports = {
  uploadRag,
  testLLM,
  deleteRag,
  refine,
  testStore,
  getFiles,
  getDocuments,
  LLMQuery,
  LLMQueryV1,
};
