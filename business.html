<!DOCTYPE html>

<html dir="rtl">

<head>

    <meta charset="utf-8" />

    <title></title>

</head>

<body>

    <h1 for="w3review">النص الذي تم التعرف عليه</h1>

    <textarea id="w3review" name="w3review" rows="4" cols="50">

    this is a test

    </textarea>

    <input type="text" id="chabgew" />

    <input type="text" id="prevchabgew" hidden/>

    <br><br>

    <button id="lstn">تحدث</button>

    <button id="correct">تصحيح</button>

    <button id="update">تحديث</button>

    <script>

        document.getElementById("correct").addEventListener("click", myCorrectr);

        document.getElementById("update").addEventListener("click", myUpdate);

        document.getElementById("lstn").addEventListener("click", myLstn);

        function myLstn() {

            alert("i am listening");

        }

        function myUpdate() {

           var oldtext = document.getElementById("prevchabgew").value;

            var newtext = document.getElementById("chabgew").value;

            var text = document.getElementById("w3review").value;

            document.getElementById("w3review").innerHTML = text.replace(oldtext, newtext + " ");;

        }

        function myCorrectr() {

            ShowSelection();

        }

        function ShowSelection() {

            var textComponent = document.getElementById('w3review');

            var selectedText;

 

            if (textComponent.selectionStart !== undefined) {// Standards Compliant Version

                var startPos = textComponent.selectionStart;

                var endPos = textComponent.selectionEnd;

                selectedText = textComponent.value.substring(startPos, endPos);

            }

            else if (document.selection !== undefined) {// IE Version

                textComponent.focus();

                var sel = document.selection.createRange();

                selectedText = sel.text;

            }

            document.getElementById("prevchabgew").value = selectedText;

            document.getElementById("chabgew").value = selectedText;

            //alert("You selected: " + selectedText);

        }

 

 

 

    </script>

</body>

</html>