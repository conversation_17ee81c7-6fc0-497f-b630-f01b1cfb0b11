const redis = require("ioredis");
const sequelize = require("./db");

const config = {
  HOST: "i2i-messaging.redis.cache.windows.net",
  KEY: "57K03blw1QahwiHHtCshAoJrUG8rpzxPPAzCaNb0lHw=",
  TIMEOUT: 300,
  KEY_PREFIX: "bot-designer-server:",
};

const configuration = {
  host: config.HOST,
  port: 6380,
  password: config.KEY,
  timeout: config.TIMEOUT,
  tls: {
    servername: config.HOST,
  },
  database: 0,
  keyPrefix: config.KEY_PREFIX,
};

const connect = () => {
  return redis.createClient(configuration);
};

const test = async () => {
  // connect
  const dbConnection = await connect();

  const bot_id = 105;

  const data = await sequelize.query("designer_get_all_tags :bot_id", {
    replacements: {
      bot_id,
    },
  });
  const poolKey = `pool_${bot_id}`;

  dbConnection.set(poolKey, JSON.stringify(data));

  const poolKeyval = JSON.stringify(dbConnection.get(key));

  // done
  disconnect(dbConnection);
};

module.exports = connect();
