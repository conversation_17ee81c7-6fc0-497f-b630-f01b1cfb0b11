const router = require("express").Router();
const controllers = require("../Controllers/controllers.reservation");
const utils = require("../Utils/utils.reservation");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.reservation");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.checkExistance,
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findReservationByBotId,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.findReservationByBotId,
  controllers.update,
  controllers.get
);

module.exports = router;
