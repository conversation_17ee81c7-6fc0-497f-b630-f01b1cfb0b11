const router = require("express").Router();
const controllers = require("../Controllers/controllers.genesysIntegration");
const { validate, validateQueryId } = require("../Utils/utils");
const utils = require("../Utils/utils.genesysIntegrationSessions");
const { Routes } = require("../Routes/routes.genesysIntegration");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.preventDuplicateGenesysIntegration,
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"] ),
  utils.findGenesysIntegrationByBotId,
  controllers.get
);

router.get(
  Routes.all,
  utils.findAllGenesysIntegrations,
  controllers.getAll
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.findGenesysIntegrationByBotId,
  utils.preventDuplicateGenesysIntegration,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.purge,
  controllers.get
);

router.get(
  Routes.session,
  // (req, res, next) => validateQueryId(req, res, next, ["chat_id", "conversation_id"] ),
  utils.findGenesysIntegrationOneSession,
  controllers.getOneSession
);

router.post(
  Routes.session,
  (req, res, next) => validate(req, res, next, ["genesys_integration_id", "channel", "conversation_id", "chat_id","eventStreamUri","jwt","member_id"]),
  controllers.setSession,
  controllers.getOneSession
);

router.post(
Routes.tr,
(req, res, next) => validate(req, res, next, ["genesys_integration_session_id", "from", "message"]),
controllers.setSessionTr,
controllers.getOneSessionTr
);

module.exports = router;
