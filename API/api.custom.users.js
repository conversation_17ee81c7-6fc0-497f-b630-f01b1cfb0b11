const router = require("express").Router();
const controlers = require("../Controllers/controllers.custom.users");
const utils = require("../Utils/utils.custom.users");
const { validate, validateQueryId, contactUS } = require("../Utils/utils");
const { params, methods } = require("../Params/params.custom.user");
const { Routes } = require("../Routes/routes.custom.user");

router.param(params.email, methods.getUserEmailParam);
router.param(params.custom_user_id, methods.getUserIdParam);

router.get(Routes.userEmail, controlers.get);
router.get(Routes.userId, controlers.get);

//SECTION Register new user and company
router.post(
  Routes.register,
  (req, res, next) =>
  validate(req, res, next, ["email", "password", "user_name"]),
  utils.checkEmailExistance,
  controlers.register
);


//SECTION login
router.post(
  Routes.login,
  (req, res, next) => validate(req, res, next, ["email", "password"]),
  utils.findUserByEmail,
  utils.authenticatePassword,
  //utils.checkEmailVerification,
  controlers.login
);

//SECTION generate token 
router.post(
  Routes.generateToken,
  (req, res, next) => validate(req, res, next, ["custom_user_id"]),
  utils.findUserByQueryId,
  controlers.generateToken
);

//SECTION Update profile
router.put(Routes.user, utils.findUserById, controlers.updateProfile);

//SECTION set email for forgot password
router.post(
  Routes.forget,
  (req, res, next) => validate(req, res, next, ["email"]),
  utils.findUserByEmail,
  controlers.forgetPassword
);


//SECTION logout
router.post(Routes.logout, utils.findUserById, controlers.logout);

//SECTION set new password
router.post(Routes.password, utils.findUserById, controlers.setNewPassword);

//SECTION verify email
router.get(
  Routes.verify,
  utils.findUserByQueryId,
  utils.verifyEmail,
  controlers.finalizeEmailVerification
);

//SECTION Get email
router.get(
  Routes.user,
  (req, res, next) => validateQueryId(req, res, next, ["custom_user_id"]),
  utils.findUserByQueryId,
  controlers.get
);

// router.post(Routes.contactus, contactUS);

module.exports = router;
