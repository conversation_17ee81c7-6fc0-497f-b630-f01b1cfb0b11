const router = require("express").Router();
const { Routes } = require("../Routes/routes.message");
const { validate, validateQueryArray, validateQueryId } = require("../Utils/utils");
const controllers = require("../Controllers/controllers.message");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["language"]),
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryArray(req, res, next, ["language"]),
  controllers.find,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["language"]),
  controllers.find,
  controllers.update,
  controllers.get
);

router.get(
  Routes.sessions,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  controllers.getSessions
)

router.get(
  Routes.messagesPerSession,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id","tr_session"]),
  controllers.getMessagesPerSession
)

module.exports = router;
