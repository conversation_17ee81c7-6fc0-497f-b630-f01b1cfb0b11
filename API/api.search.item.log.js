const router = require("express").Router();
const controllers = require("../Controllers/controllers.search.item.log");
const utils = require("../Utils/utils.search.item");
const { validate } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.search.item");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["user_id", "item_id", "city"]),
  utils.checkExistance,
  controllers.set,
  controllers.get
);

module.exports = router;
