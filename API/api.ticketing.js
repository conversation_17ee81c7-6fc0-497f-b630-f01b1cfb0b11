const router = require("express").Router();
const { Routes } = require("../Routes/routes.ticketing");
const controllers = require("../Controllers/controllers.ticketing");
const utils = require("../Utils/utils.ticketing");
const {
  validate,
  validateQueryId,
  validateQueryArray,
} = require("../Utils/utils");
const { findBotById } = require("../Utils/utils.bot");
const { findUserById } = require("../Utils/utils.user");
const { findOneByBot } = require("../Utils/utils.editor");

router.post(
  Routes.ticket,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "channel",
      "conversation_id",
      "ticketing_integration_id",
    ]),
  controllers.createTicket,
  controllers.sendTicketingEmailToBotCreator,
  controllers.sendTicketingEmail,
  controllers.sendTicketingNotificationToBot,
  controllers.getTicket
);

router.get(Routes.ticketuuid, utils.findTicketByUUID, controllers.getTicket);

router.get(
  Routes.ticketingOverview,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "day", "month", "year"]),
  controllers.getTicketingOverview
)

router.put(
  Routes.ticketuuid,
  utils.findTicketByUUID,
  controllers.updateTicket,
  controllers.sendTicketingEmail,
  controllers.sendTicketingNotificationToBot,
  controllers.getTicket
);

router.post(
  Routes.comment,
  (req, res, next) => validate(req, res, next, ["ticket_id", "comment"]),
  controllers.createComment,
  controllers.sendTicketingEmail,
  controllers.sendTicketingNotificationToBot,
  utils.findAllTicketComments,
  controllers.getComments
);

router.get(
  Routes.supportAgent,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "user_id"]),
  findBotById,
  findUserById,
  findOneByBot,
  utils.findSupportAgentByEditor,
  controllers.getSupportAgent
);

router.get(
  Routes.ticketsforagent,
  utils.findTicketsByAgent,
  controllers.getTickets
);

router.get(
  Routes.ticketsforagentdepartments,
  utils.findTicketsByAgentDepartments,
  controllers.getTickets
);

router.post(
  Routes.ticketassign,
  (req, res, next) =>
    validate(req, res, next, [
      "ticket_id",
      "support_agent_id",
      "assigned_by",
      "ticket_uuid",
    ]),
  utils.findTicketByUUID,
  controllers.assignTicket,
  controllers.getTicket
);

router.get(
  Routes.integration,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findIntegrationByBotId,
  controllers.getIntegration
);
router.get(
  Routes.categories,
  (req, res, next) => 
    validateQueryId(req, res, next, ["bot_id"]),
  utils.findCategoriesFromDepartment,
  controllers.getCategories
);

router.get(
  Routes.departments,
  (req, res, next) => 
    validateQueryId(req, res, next, ["bot_id"]),
  utils.findDepartmentsByBotId,
  controllers.getDepartments
);


router.post(
  Routes.integration,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.createIntegration
)

router.put(
  Routes.integration,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.updateIntegration
)

router.post(
  Routes.categories,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.createDepartmentWithCategories,
)

router.get(
  Routes.allEditorsAgents,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findEditorsAgentsByBotID,
  controllers.getAllEditorsAgents
)

router.post(
  Routes.agent,
  (req, res, next)=>validate(req, res, next, ["bot_id", "editor"]),
  utils.createAgent
)

router.post(
  Routes.rating,
  (req, res, next) =>
    validate(req, res, next, ["ticket_id", "rating", "rating_msg"]),
  controllers.createRating,
  controllers.getRating
);

router.get(
  Routes.rating,
  (req, res, next) => validateQueryId(req, res, next, "ticket_id"),
  utils.findTicketRating,
  controllers.getRating
);

router.get(
  Routes.agentDepartments,
  (req, res, next)=>validateQueryArray(req, res, next, ["editor_id"]),
  utils.findAgentDepartment,
  controllers.getDepartments,
);

router.put(
  Routes.updateAgent,
  (req, res, next) => validate(req, res, next, ["editor_id"]),
  utils.updateAgent
)

router.delete(
  Routes.delete,
  (req, res, next) => validate(req, res, next, ["editor_id"]),
  utils.purge
)

router.get(
  Routes.category,
  (req, res, next) => validateQueryId(req, res, next, ["department_id"]),
  utils.getCategories,
  controllers.getCategories,
)

router.delete(
  Routes.departments,
  (req, res, next) => validate(req, res, next, ["department_id"]),
  utils.purgeDepartment,
)

router.put(
  Routes.categories,
  (req, res, next) => validate(req, res, next, ["department_id"]),
  utils.updateCategories,
)

module.exports = router;
