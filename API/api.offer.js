const router = require("express").Router();
const controllers = require("../Controllers/controllers.offer");
const utils = require("../Utils/utils.offer");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.offer");
const OfferItemControllers = require("../Controllers/controllers.offer.item");

//SECTION Offer CRUD
router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["offer_description", "bot_id"]),
  utils.preprocessOffer,
  utils.lemmtaizationOne,
  controllers.set,
  utils.postprocessOffer,
  //revalidateBotPoolStorage,
  controllers.get
);

router.post(Routes.all, controllers.setAll, controllers.getAll);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["offer_id"]),
  utils.findOfferByQueryId,
  controllers.get
);

router.get(
  Routes.all,
  //   (req, res, next) => validateQueryId(req, res, next),
  utils.findOffersByBotId,
  controllers.getAll
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["offer_id"]),
  utils.findOfferById,
  utils.lemmtaizationOne,
  controllers.update,
  //revalidateBotPoolStorage,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["offer_id"]),
  OfferItemControllers.purge,
  controllers.purge,
  //revalidateBotPoolStorage,
  (req, res, next) => res.send({ message: "Offer deleted successfully" })
);
// router.purge(Routes.all, (req, res, next) => {
//   validate(req, res, next, ["bot_id"]), controllers.purgeAll;
// });
module.exports = router;
