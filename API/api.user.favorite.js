const router = require("express").Router();
const controllers = require("../Controllers/controllers.user.favorite");
const utils = require("../Utils/utils.user.favorite");
const { validate, validateQueryArray } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.user.favorite");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["item_id", "user_id"]),
  utils.checkExistance,
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryArray(req, res, next, ["user_id"]),
  utils.findByUserId,
  controllers.getAll
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["favorite_id"]),
  controllers.purge
);

module.exports = router;
