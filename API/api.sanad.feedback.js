const router = require("express").Router();
const controllers = require("../Controllers/controllers.sanad.feedback");
const utils = require("../Utils/utils.sanad.feedback");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.sanad.feedback");

//SECTION Item CRUD
router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "conversation_id",
      "rating",
      "feedback_body",
    ]),
  controllers.set,
  controllers.get
);

router.put(Routes.base, utils.find, controllers.update, controllers.get);

router.get(Routes.all, utils.findAll, controllers.getAll);

module.exports = router;
