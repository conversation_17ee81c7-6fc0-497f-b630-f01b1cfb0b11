const router = require("express").Router();
const controllers = require("../Controllers/controllers.livechatIntegration");
const { validate, validateQueryId } = require("../Utils/utils");
const utils = require("../Utils/utils.livechatIntegrationSessions");
const { Routes } = require("../Routes/routes.livechatIntegration");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.preventDuplicateLiveChatIntegration,
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"] ),
  utils.findLiveChatIntegrationByBotId,
  controllers.get
);



router.get(
  Routes.all,
  utils.findAllLiveChatIntegrations,
  controllers.getAll
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.findLiveChatIntegrationByBotId,
  utils.preventDuplicateLiveChatIntegration,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.purge,
  controllers.get
);

router.get(
    Routes.session,
    // (req, res, next) => validateQueryId(req, res, next, ["chat_id", "conversation_id"] ),
    utils.findLiveChatIntegrationOneSession,
    controllers.getOneSession
);

router.post(
    Routes.session,
    (req, res, next) => validate(req, res, next, ["livechat_integration_id", "channel", "conversation_id", "chat_id"]),
    controllers.setSession,
    controllers.getOneSession
);

router.put(
  Routes.session,
  (req, res, next) => validate(req, res, next, ["livechat_integration_id"]),
  utils.findLiveChatIntegrationOneSession,
  controllers.updateSession,
  controllers.getOneSession
)

router.post(
  Routes.tr,
  (req, res, next) => validate(req, res, next, ["livechat_integration_session_id", "from", "message"]),
  controllers.setSessionTr,
  controllers.getOneSessionTr
);

module.exports = router;
