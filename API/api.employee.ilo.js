const router = require("express").Router();
const controllers = require("../Controllers/controllers.ilo.employee");
const utils = require("../Utils/utils.employee.ilo");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.employee.ilo");

//SECTION Entity CRUD




router.post(
    Routes.base,
    (req, res, next) =>
        validate(req, res, next, [
            "employment_status",
            "user_gender",
            "user_age",
            "nationality",
            "document_type",
            "personal_number",
            "national_number",
            "ssc_status",
            "job_type",
            "governorate",
            "income",
            "name",
            "phone_number"
        ]),
    controllers.set,
    controllers.get
);


router.get(
    Routes.base,
    (req, res, next) => validateQueryId(req, res, next, ["employee_id"]),
    utils.findById,
    controllers.get
);

router.get(
    Routes.all,
    utils.findAll,
    controllers.getAll
);


router.put(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["employee_id"]),
    utils.findById,
    controllers.update,
    controllers.get
);

router.delete(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["employee_id"]),
    controllers.purge
);

module.exports = router;
