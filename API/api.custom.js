const router = require("express").Router();
const { Routes } = require("../Routes/routes.custom");
const controllers = require("../Controllers/controllers.custom");
// const {
//   recognition,
//   recognitionV2,
//   recognitionV3,
//   downloadImage,
//   recognitionV4,
// } = require("../Controllers/controllers.ocr");
const {
  validate,
  validateQueryId,
  validateQueryArray,
} = require("../Utils/utils");

router.post(
  Routes.sa3i,
  (req, res, next) => validate(req, res, next, ["id"]),
  controllers.getSa3iTracking
);

router.post(
  "/ocr",
  (req, res, next) => validate(req, res, next, ["image_path"]),
  recognition
);

router.post(
  "/ocr/v2",
  (req, res, next) => validate(req, res, next, ["image_path"]),
  recognitionV2
);

router.post(
  "/ocr/v3",
  (req, res, next) => validate(req, res, next, ["image_path"]),
  downloadImage,
  recognitionV3
);

router.post(
  "/ocr/v4",
  (req, res, next) => validate(req, res, next, ["image_path"]),
  recognitionV4
);

router.get(
  "/custom/voice",
  (req, res, next) => validateQueryArray(req, res, next, ["language", "text"]),
  controllers.getGoogleVoice
);

router.post(
  "/custom/sheet",
  (req, res, next) => validate(req, res, next, ["range", "sheetID"]),
  controllers.getGoogleSheet
);

router.post(
  "/custom/outboundcall",
  (req, res, next) => validate(req, res, next, ["phone", "text"]),
  controllers.createOutboundCall
);

router.post(
  "/custom/ssc_report",
  (req, res, next) => validate(req, res, next, ["recipient"]),
  controllers.insertReport
);

router.post("/custom/health", controllers.checkHealth);

module.exports = router;
