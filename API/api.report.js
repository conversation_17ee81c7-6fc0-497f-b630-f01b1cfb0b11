const router = require("express").Router();
const controllers = require("../Controllers/controllers.report");
const utils = require("../Utils/utils.report");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.report");


router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.set,
  controllers.get
);


router.get(
  Routes.all,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findReportsByBotId,
  controllers.getAll
);

router.get(
  Routes.sscReports,
  controllers.getAllSscReports
);

module.exports = router;
