const router = require("express").Router();
const { Routes } = require("../Routes/routes.bot.uniqueUser");
const { validate, validateQueryId } = require("../Utils/utils");
const controllers = require("../Controllers/controllers.bot.uniqueUser");
const utils = require("../Utils/utils.bot.uniqueUser");


router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.set,
  controllers.get
);


router.get(
    Routes.all,
    (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
    utils.findBotUniqueUsers,
    controllers.getAll,
);
  

module.exports = router;
