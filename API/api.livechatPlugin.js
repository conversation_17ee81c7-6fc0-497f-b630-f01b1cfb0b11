const router = require("express").Router();
const { Routes } = require("../Routes/routes.livechatPlugin");
const {
  validate,
  validateQueryId,
  validateQueryArray,
} = require("../Utils/utils");
const controllers = require("../Controllers/controllers.livechatPlugin");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "seat_num"]),
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id"]),
  controllers.find,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),

  controllers.update,
  controllers.find,
  controllers.get
);

module.exports = router;
