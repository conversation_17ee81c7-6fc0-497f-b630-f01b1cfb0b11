const router = require("express").Router();
const controlers = require("../Controllers/controllers.faq");
const {
  getAnswer,
  preProcessQuestion,
  findSimilarity,
  preProcessText,
  lookupQuestion,
  fuzzyQuestion,
  findFAQContainerSimilarity,
  findTriggerContainerSimilarity,
  findTaggedSimilarity,
  fallbackMessage,
  getPotentialMatches,
  generateTips,
  getAllBotsTags,
  findTaggedPool,
  findTaggedPoolCached,
} = require("../Controllers/controllers.engine");
const {
  validate,
  validateQueryId,
  validateQueryArray,
} = require("../Utils/utils");
const LogControllers = require("../Controllers/controllers.search.log");
const { findBotByIdSP } = require("../Utils/utils.bot");
const { Routes } = require("../Routes/routes.faq");
const utils = require("../Utils/utils.faq");
const multer = require("multer");
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, "blob/");
  },
  filename: (req, file, cb) => {
    cb(null, "test." + file.originalname.split(".")[1]);
  },
});

const upload = multer({ storage: storage });
const botLogControlers = require("../Controllers/controllers.bot.log");

//SECTION BOT CRUD
router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, ["user_id", "answer", "question", "bot_id"]),
  utils.checkExistance,
  controlers.set,
  controlers.createLogs,
  botLogControlers.createBotFaqLogs
  //revalidateBotPoolStorage,
);

router.delete(
  Routes.all,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  botLogControlers.createBotFaqLogs,
  controlers.purgeAllQna,
  //revalidateBotPoolStorage,
  (req, res, next) => res.send({ message: "All FAQs are deleted" })
);

router.post(
  Routes.many,
  controlers.lemmtaizationMany,
  controlers.setMany,
  //revalidateBotPoolStorage,
  botLogControlers.createBotFaqLogs,
  controlers.getAll
);

router.delete(
  Routes.many,
  botLogControlers.createBotFaqLogs,
  controlers.purgeMany,
  utils.findBotFAQsById,
  controlers.getAll
);

router.delete(
  Routes.many_v2,
  botLogControlers.createBotFaqLogs,
  controlers.purgeManyV2,
  utils.findBotFAQsById,
  //revalidateBotPoolStorage,
  controlers.getAll
);

router.get(
  Routes.many,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findBotFAQsByQueryId,
  controlers.getAll
);

router.get(
  Routes.recursive,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findBotFAQsByQueryId,
  controlers.convertFAQsRecursively,
  controlers.getAll
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, "faq_id"),
  utils.findFAQByQueryId,
  controlers.get
);

router.get(
  Routes.first,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findFirstAnswerFAQ
);

router.get(
  Routes.fag_logs,
  (req, res, next) => validateQueryId(req, res, next, "faq_id"),
  utils.findFAQLogs
);

router.get(
  Routes.alternative,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findQuestionAlternatives
);

router.put(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "user_id",
      "bot_id",
      "faq_id",
      "question",
      "answer",
    ]),
  utils.checkExistance,
  utils.findFAQById,
  controlers.update,
  botLogControlers.createBotFaqLogs,

  //revalidateBotPoolStorage,
  // controlers.createLogs,
  controlers.get
);
router.put(
  "/faqV",
  (req, res, next) => validate(req, res, next, ["bot_id", "faq_id"]),
  // utils.checkExistance,
  utils.findFAQById,
  controlers.update,
  botLogControlers.createBotFaqLogs,

  //revalidateBotPoolStorage,
  // controlers.createLogs,
  controlers.get
);

router.put(
  Routes.many,
  utils.findManyFAQs,
  controlers.lemmtaizationMany,
  controlers.updateMany,
  utils.findBotFAQsById,
  botLogControlers.createBotFaqLogs,

  //revalidateBotPoolStorage,
  controlers.getAll
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "faq_id", "user_id"]),
  utils.checkExistance,
  // controlers.createLogsForDELETE,
  botLogControlers.createBotFaqLogs,
  controlers.purge,

  //revalidateBotPoolStorage,
  (req, res, next) => res.send({ message: "Deleted" })
);

//SECTION Get Answer
router.post(Routes.answer, preProcessText, findSimilarity, getAnswer);

router.post("/faq/lookup", lookupQuestion);
router.post("/faq/fuzzy", fuzzyQuestion);
router.post(
  "/faq/similarity",
  (req, res, next) => validate(req, res, next, ["bot_id", "question"]),
  findBotByIdSP,
  preProcessQuestion,
  // findTriggerSimilarity,
  // checkLossyIntent,
  // findCaterogiesSimilarity,
  // findItemsSimilarity,
  // findFAQSimilarity,
  findTaggedSimilarity,
  fallbackMessage
);

router.post(
  "/faq/tips",
  (req, res, next) => validate(req, res, next, ["bot_id", "question"]),
  findBotByIdSP,
  preProcessQuestion,
  getPotentialMatches
);

router.post(
  "/search-engine",
  (req, res, next) => validate(req, res, next, ["question"]),
  preProcessQuestion,
  LogControllers.set,
  getAllBotsTags
);

router.post(
  "/context/similarity",
  findBotByIdSP,
  findTriggerContainerSimilarity,
  findFAQContainerSimilarity
);

router.post("/context/tips", generateTips);

router.get("/tagged-pool", findTaggedPool);

router.get("/tagged-pool-cached", findTaggedPoolCached);

// router.post(
//   "/faqs/scraping",
//   (req, res, next) => validate(req, res, next, ["page_url", "bot_id"]),
//   controlers.faqsScrapper,
//   controlers.setMany
// );

router.post(
  "/faqs/pdf",
  upload.single("file"),
  controlers.extractFaqsPdf,
  controlers.setMany
);

module.exports = router;
