const router = require("express").Router();
const controllers = require("../Controllers/controllers.custom.class");
const utils = require("../Utils/utils.custom.class");
const { validate, validateQueryId, contactUS } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.custom.class");

router.post(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["bot_id","class_desc","location"]),
    controllers.set,
    controllers.get
  );
  
  router.get(
    Routes.allClasses,
    (req, res, next) => validateQueryId(req, res, next,"bot_id"),
    utils.findAll,
    controllers.get
  );
  
  router.get(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["class_id","bot_id"]),
    utils.findClassById,
    controllers.get
  );
  
  router.put(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["class_id","bot_id"]),
    utils.findClassById,
    controllers.update,
    controllers.get
  );

  router.delete(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["bot_id","class_id"]),
    controllers.purge
  );
;
  
module.exports = router;
