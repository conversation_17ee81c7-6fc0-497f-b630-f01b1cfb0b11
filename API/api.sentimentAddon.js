const router = require("express").Router();
const controllers = require("../Controllers/controllers.sentimentAddon");
const utils = require("../Utils/utils.sentimentAddon");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.sentimentAddon");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findByBotId,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.findByBotId,
  controllers.update,
  controllers.get
);

module.exports = router;
