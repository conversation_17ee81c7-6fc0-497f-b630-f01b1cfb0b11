const router = require("express").Router();
const controllers = require("../Controllers/controllers.cart");
const utils = require("../Utils/utils.cart");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.cart");

//SECTION Facebook Channel CRUD
router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "email",
      "phone_number",
      "enable_api",
      "vat_ammount",
      "currency",
    ]),
  utils.checkExistance,
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findCartByBotId,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.findCartByBotId,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.purge
);

module.exports = router;
