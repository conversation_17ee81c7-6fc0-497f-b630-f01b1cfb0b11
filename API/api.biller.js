const router = require("express").Router();
const { Routes } = require("../Routes/routes.biller");
const { validate, validateQueryId } = require("../Utils/utils");
const { findUserBillers } = require("../Utils/utils.biller");
const { findOneByCountry } = require("../Utils/utils.countryPlan");
const { findPlanById } = require("../Utils/utils.plan");
const { checkPromotionExistance } = require("../Utils/utils.promotion")
const controllers = require("../Controllers/controllers.biller");

router.get(
  Routes.many,
  (req, res, next) => validateQueryId(req, res, next, ["user_id"]),
  findUserBillers,
  controllers.getAll
);

router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "user_id",
      "country",
      "city",
      "company_name",
      "email",
    ]),
  controllers.set,
  controllers.get
);

router.post(
  Routes.price,
  (req, res, next) =>
    validate(req, res, next, ["plan_id", "months", "country"]),
  findPlanById,
  findOneByCountry,
  checkPromotionExistance,
  controllers.generatePrice,
  controllers.get
);

module.exports = router;
