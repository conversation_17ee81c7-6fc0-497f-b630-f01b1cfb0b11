const router = require("express").Router();
const controllers = require("../Controllers/controllers.performit");
const { preProcessPerformit } = require("../Controllers/controllers.engine");
const { Routes } = require("../Routes/routes.performit");
const { validate, validateQueryId } = require("../Utils/utils");

router.post(
  Routes.v1,
  (req, res, next) => validate(req, res, next, ["question", "bot_id"]),
  preProcessPerformit,
  controllers.v1Performit
);

module.exports = router;
