const router = require("express").Router();
const controllers = require("../Controllers/controllers.botTemplates");
const utils = require("../Utils/utils.botTemplate");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.botTemplate");

router.post(
  Routes.base,
  (req, res, next) => 
    validate(
        req, 
        res, 
        next, 
        [
            "bot_id", 
            "bot_template_title", 
            "bot_template_category",
            "bot_template_made_by", 
            "bot_template_photo", 
            "bot_template_description"
        ]
    ),
  controllers.set,
  controllers.get
);

router.get(
    Routes.all,
    utils.findAlBotTemplate,
    controllers.getAll
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, "bot_template_slug"),
  utils.findBotTemplateBySlug,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_template_id"]),
  utils.findBotTemplateByID,
  controllers.update,
  controllers.get
);

router.put(
  Routes.rateTemp,
  (req, res, next) => validate(req, res, next, ["bot_template_id","rate"]),
  utils.findBotTemplateByID,
  controllers.addRate,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_template_id"]),
  controllers.purge
);

module.exports = router;
