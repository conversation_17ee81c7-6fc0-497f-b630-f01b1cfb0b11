const router = require("express").Router();
const controllers = require("../Controllers/controllers.email");
const utils = require("../Utils/utils.email");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.email");

//SECTION Plan CRUD
router.post(
  Routes.generate,
  (req, res, next) => validate(req, res, next, ["email", "bot_id"]),
  // controllers.purge,
  controllers.set,
  controllers.sendCode
);

router.post(
  Routes.html,
  (req, res, next) => validate(req, res, next, ["email", "body", "title"]),
  // controllers.purge,
  controllers.sendHtml
);

router.post(
  Routes.send,
  (req, res, next) => validate(req, res, next, ["email", "bot_id", "code"]),
  utils.findEmail,
  controllers.checkVerified,
  controllers.checkCode,
  controllers.verifyEmail,
  controllers.sendVerifyMessage
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findEmailsByBotId,
  controllers.getAll
);

module.exports = router;