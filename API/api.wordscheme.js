const router = require("express").Router();
const controllers = require("../Controllers/controllers.wordscheme");
const utils = require("../Utils/utils.wordscheme");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.wordscheme");

//SECTION Entity CRUD
router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "alternative_word",
      "main_word",
      "dialect",
      "language",
    ]),
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["word_id"]),
  utils.findById,
  controllers.get
);

router.get(Routes.all, utils.findAll, controllers.getAll);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["intent_id"]),
  utils.findById,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["intent_id"]),
  controllers.purge
);

module.exports = router;
