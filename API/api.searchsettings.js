const router = require("express").Router();
const controllers = require("../Controllers/controllers.searchsettings");
const utils = require("../Utils/utils.searchsettings");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.searchsettings");

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findSearchSettingsByBotId,
  controllers.get
);

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.set,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.findSearchSettingsByBotId,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.purge,
  controllers.get
);

module.exports = router;
