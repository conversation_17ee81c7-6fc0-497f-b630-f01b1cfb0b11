const router = require("express").Router();
const controllers = require("../Controllers/controllers.watester");
const utils = require("../Utils/utils.watester");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.watester");

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findAll,
  controllers.getAll
);
router.get(
  Routes.allNum,
  (req, res, next) => validateQueryId(req, res, next, "phone"),
  utils.findAllNumber,
  controllers.getAll
);
router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "phone"]),
  utils.checkExistance,
  controllers.set,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.findOne,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "phone"]),
  controllers.purgeOne,
  utils.findAll,
  controllers.getAll
);
module.exports = router;
