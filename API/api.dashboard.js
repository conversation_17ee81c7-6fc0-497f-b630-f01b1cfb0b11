const router = require("express").Router();
const { Routes } = require("../Routes/routes.dashboard");
const {
  validate,
  validateQueryId,
  validateQueryArray,
} = require("../Utils/utils");
const {
  findDashboardById,
  findDashboardByQueryId,
} = require("../Utils/utils.dashboard");
const {
  getTransactionsCount,
  getVoiceTransactionsCount,
  getTransactionsFunction,
  getTransactionsCategory,
  getTransactionsDays,
  getTransactionsAnswer,
  getTransactionsTrend,
  getTransactionsAvgDay,
  getTransactionsConversation,
  getTransactionsAvgHour,
  getTransactionsAvgWeek,
  getTransactionsNotAnswered,
  getTransactionsLastN,
  getConversationsIds,
  getConversationDetails,
  getTransactionsChannel,
  getDaysSinceLaunch,
  getWhatsappPhoneNumbers,
  getSanadWhatsappPhoneNumbers,
  getTransactionPerPeriod,
  getCountryTransactions,
  getTransactionsGrouped,
  getMostAskedPerCountry,
  insertTransaction,
  getHealthStatus,
  getConversationsCalculations,
  getTransactionsCalculations,
  getSanadTransactionsCalculations,
  insertSanadTransaction,
  getSanadConversation,
  getSanadTransactionsLastN,
  getSanadTransactionsNotAnswered,
  getSanadMostAskedPerCountry,
  getSanadFeedbackStats,
} = require("../Controllers/controllers.dashboard");
const { findBotById } = require("../Utils/utils.bot");
const controllers = require("../Controllers/controllers.dashboard");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  findBotById,
  controllers.create,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  findDashboardByQueryId,
  controllers.get
);

router.get(
  Routes.countryTransactions,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "day"]),
  getCountryTransactions
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  findDashboardById,
  controllers.update,
  controllers.get
);

router.get(
  Routes.count,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "period"]),
  getTransactionsCount
);

router.get(
  Routes.voice,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "period"]),
  getVoiceTransactionsCount
);

router.get(
  Routes.function,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "period"]),
  getTransactionsFunction
);

router.get(
  Routes.category,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "period"]),
  getTransactionsCategory
);

router.get(
  Routes.days,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "period"]),
  getTransactionsDays
);

router.get(
  Routes.answer,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "period"]),
  getTransactionsAnswer
);

router.get(
  Routes.trend,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "period"]),
  getTransactionsTrend
);

router.get(
  Routes.avgday,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "period"]),
  getTransactionsAvgDay
);

router.get(
  Routes.avgweekday,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "period"]),
  getTransactionsAvgWeek
);

router.get(
  Routes.avghour,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "period"]),
  getTransactionsAvgHour
);

router.get(
  Routes.notanswered,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "day", "month", "year"]),
  getTransactionsNotAnswered
);

router.get(
  Routes.sanadnotanswered,
  (req, res, next) => validateQueryArray(req, res, next, ["period"]),
  getSanadTransactionsNotAnswered
);

router.get(
  Routes.lastn,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "No"]),
  getTransactionsLastN
);

router.get(
  Routes.conversationDetails,
  (req, res, next) => validateQueryArray(req, res, next, ["conversation_id", "bot_id"]),
  getConversationDetails
);

router.get(
  Routes.conversationIds,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  getConversationsIds
);

router.get(
  Routes.sanadlastn,
  (req, res, next) => validateQueryArray(req, res, next, ["No"]),
  getSanadTransactionsLastN
);

router.get(
  Routes.launch,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id"]),
  getDaysSinceLaunch
);

router.get(
  Routes.conversation,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "period"]),
  getTransactionsConversation
);

router.get(
  Routes.waconversation,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "period"]),
  controllers.getTransactionsWAConversation
);

router.get(
  Routes.channel,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "period"]),
  getTransactionsChannel
);

router.get(
  Routes.whatsappPhoneNumbers,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "period"]),
  getWhatsappPhoneNumbers
);

router.get(
  Routes.sanadWhatsappPhoneNumbers,
  (req, res, next) => validateQueryArray(req, res, next, ["period"]),
  getSanadWhatsappPhoneNumbers
);

router.get(
  Routes.tansactionsPerPeriod,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "period"]),
  getTransactionPerPeriod
);

router.get(
  Routes.transactionsGrouped,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  getTransactionsGrouped
);

router.get(
  Routes.mostAskedPerCountry,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  getMostAskedPerCountry
);

router.get(
  Routes.sanadMostAskedPerCountry,
  // (req, res, next) => validateQueryId(req, res, next),
  getSanadMostAskedPerCountry
);

router.get(
  Routes.transactionCalculation,
  (req, res, next) =>
    validateQueryArray(req, res, next, ["bot_id", "day", "month", "year"]),
  getTransactionsCalculations
);
router.get(
  Routes.sanadTransactionCalculation,
  (req, res, next) =>
    validateQueryArray(req, res, next, ["bot_id", "day", "month", "year"]),
  getSanadTransactionsCalculations
);

router.get(Routes.sanadFeedbackStats, getSanadFeedbackStats);

router.get(
  Routes.botUserConversationCalculation,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  getConversationsCalculations
);

router.get(
  Routes.healthStatusCheck,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  getHealthStatus
);

router.get(
  Routes.transactions,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "tr_channel"]),
  controllers.getTransactions
)

router.post(
  Routes.inserTransaction,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "country",
      "city",
      "ip",
      "is_voice_tr",
      "continent",
      "session",
      "channel",
      "function",
      "Q",
      "A",
      "category",
      "score",
      "owner",
    ]),
  insertTransaction
);

router.delete(
  Routes.deleteTransaction,
  (req, res, next) => validate(req, res, next, ["message_uuid"]),
  controllers.deleteTransaction
)

router.post(
  Routes.insertSanadTransaction,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "country",
      "city",
      "ip",
      "is_voice_tr",
      "continent",
      "session",
      "channel",
      "function",
      "entity",
      "service",
      "Q",
      "A",
      "category",
      "score",
      "owner",
    ]),
  insertSanadTransaction
);

router.get(
  Routes.getSanadConversation,
  (req, res, next) => validateQueryId(req, res, next, ["conversation_id"]),
  getSanadConversation
);

module.exports = router;
