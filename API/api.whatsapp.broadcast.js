const router = require("express").Router();
const controllers = require("../Controllers/controllers.whatsapp.broadcast");
const utils = require("../Utils/utils.whatsapp.broadcast");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.whatsapp.broadcast");


router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "broadcast_name", "message", "image_url"]),
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["broad_cast_id"]),
  utils.findBroadCastById,
  controllers.get
);

router.get(
    Routes.all,
    (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
    utils.findBroadCastsByBotId,
    controllers.getAll
);

router.get(
    Routes.logsByBroadCast,
    (req, res, next) => validateQueryId(req, res, next, ["broadcast_id"]),
    utils.findBroadCastLogsByBotId,
    controllers.getLogs
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.findBroadCastById,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.purge
);

router.get(
  Routes.allPoneNumebrs,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.GetWhatsappPhoneNumbers,
  controllers.getAllNumbers
);

module.exports = router;
