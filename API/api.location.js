const router = require("express").Router();
const controllers = require("../Controllers/controllers.location");
// const utils = require("../Utils/utils.location");
const { validate /* validateQueryId */ } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.location");

//SECTION Location CRUD
router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, ["bot_id", "continent", "country", "city"]),
  controllers.set,
  controllers.get
);

module.exports = router;
