const router = require("express").Router();
const controllers = require("../Controllers/controllers.voice");
const { validate } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.voice");

router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "project_name",
      "user_id",
      "user_question",
      "bot_answer",
      "similarity",
    ]),
  controllers.set,
  controllers.get
);

module.exports = router;
