const router = require("express").Router();
const { Routes } = require("../Routes/routes.ssc");
const {
  validate,
  validateQueryId,
  validateQueryArray,
} = require("../Utils/utils");
const { 
    sendMessages,
    getLocation
} = require("../Controllers/controllers.ssc")


router.post(
    Routes.base,
    // (req, res, next) => validate(req, res, next, ["bot_id", "text", "conversation_id"]),
    sendMessages
);

router.post(
  Routes.location,
  getLocation
);
  
module.exports = router;