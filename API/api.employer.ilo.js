const router = require("express").Router();
const controllers = require("../Controllers/controllers.ilo.employer");
const utils = require("../Utils/utils.employer.ilo");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.employer.ilo");

//SECTION Entity CRUD
router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
        "employment_status", 
        "company_sector", 
        "company_location",
        "insurance_number",
        "employees_number",
        "owner_gender",
        "name",
        "phone_number"
    ]),
  controllers.set,
  controllers.get
);


router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["employer_id"]),
  utils.findById,
  controllers.get
);

router.get(
  Routes.all,
  utils.findAll,
  controllers.getAll
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["employer_id"]),
  utils.findById,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["employer_id"]),
  controllers.purge
);

module.exports = router;
