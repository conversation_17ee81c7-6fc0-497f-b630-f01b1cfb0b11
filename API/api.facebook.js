const router = require("express").Router();
const controllers = require("../Controllers/controllers.facebook");
const utils = require("../Utils/utils.facebook");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.facebook");
const botLogControlers = require("../Controllers/controllers.bot.log");

//SECTION Facebook Channel CRUD
router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, ["bot_id", "pageId", "page_token"]),
  controllers.set,
  controllers.setSubscription,
  botLogControlers.createBotChannelLogs,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["pageId"]),
  utils.findFacebookByQueryPageId,
  controllers.get
);
router.get(
  Routes.allPages,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findAllFacebookPagesByBotId,
  controllers.getAll
);

router.post(
  Routes.pages,
  (req, res, next) => validate(req, res, next, ["bot_id", "channel"]),
  utils.findFacebookByBotId,
  botLogControlers.createBotChannelLogs,

  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "pageId",
      "page_token",
      "channel",
      "name",
    ]),
  utils.findFacebookByBotId,
  controllers.update,
  controllers.setSubscription,
  botLogControlers.createBotChannelLogs,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "facebook_channel_id"]),
  controllers.disconnectPage
);

router.post(Routes.oauth, controllers.getPagesFromToken);

router.post(Routes.instagram_oauth, controllers.getInstagramPages);

router.post(
  Routes.get_started,
  (req, res, next) =>
    validate(req, res, next, ["bot_id", "pageId", "get_started_trigger"]),
  utils.findPageByPageId,
  controllers.setGetStartedTrigger
);

router.get(
  Routes.messages,
  (req, res, next) => validateQueryId(req, res, next, ["session_id"]),
  controllers.getMessagesBySession
);

router.delete(
  Routes.pages,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.purgePage
);

module.exports = router;
