const router = require("express").Router();
const controllers = require("../Controllers/controllers.phone");
const botUserControllers = require("../Controllers/controllers.bot.user");
const utils = require("../Utils/utils.phone");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.phone");

//SECTION Plan CRUD
router.post(
  Routes.generate,
  (req, res, next) => validate(req, res, next, ["phone", "bot_id"]),
  // controllers.purge,
  controllers.set,
  controllers.sendCode
);

router.post(
  Routes.send,
  (req, res, next) => validate(req, res, next, ["phone", "bot_id", "code"]),
  utils.findPhone,
  controllers.checkVerified,
  controllers.checkCode,
  controllers.verifyPhone,
  botUserControllers.find,
  botUserControllers.set,
  controllers.sendVerifyMessage
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next,"bot_id"),
  utils.findPhonesByBotId,
  controllers.getAll,
);


module.exports = router;
