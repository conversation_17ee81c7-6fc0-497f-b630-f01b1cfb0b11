const router = require("express").Router();
const { Routes } = require("../Routes/routes.config");
const { validate, validateQueryId } = require("../Utils/utils");
const {
  findBotConfigById,
  findBotConfigByQueryId,
  checkConfigExistance,
  findBotConfigByQueryIdProcedure,
} = require("../Utils/utils.config");
const controllers = require("../Controllers/controllers.config");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  checkConfigExistance,
  controllers.create,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  // findBotConfigByQueryId,
  findBotConfigByQueryIdProcedure,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  findBotConfigById,
  controllers.update,
  controllers.get
);

module.exports = router;
