const router = require("express").Router();
const { Routes } = require("../Routes/routes.zendeskPlugin");
const {
  validate,
  validateQueryId,
  validateQueryArray,
} = require("../Utils/utils");
const controllers = require("../Controllers/controllers.zendeskplugin");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id"]),
  controllers.find,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.find,
  controllers.update,
  controllers.get
);
router.post(
  Routes.zendesk,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.set,
  controllers.get
);
module.exports = router;
