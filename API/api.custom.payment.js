const router = require("express").Router();
const controllers = require("../Controllers/controllers.custom.payment");
const utils = require("../Utils/utiles.custom.payment");
const { validate, validateQueryId, contactUS } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.custom.payment");

router.post(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["trainee_id","class_id","bot_id","payment_date","paid_balance"]),
    controllers.set,
    controllers.updatePayment,
    controllers.get
  );

  
  router.get(
    Routes.paid,
    (req, res, next) => validateQueryId(req, res, next,"bot_id"),
    utils.findPayingTrainees,
    controllers.get
  );
  
  router.get(
    Routes.non_paid,
    (req, res, next) => validateQueryId(req, res, next, "bot_id"),
    utils.findNonPayingTrainees,
    controllers.get
  );
  
  router.put(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["payment_id","bot_id"]),
    utils.findPaymentByID,
    controllers.update,
    controllers.get
  );

  router.delete(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["bot_id","payment_id"]),
    controllers.purge
  );
  router.get(
    Routes.all_info,
    (req, res, next) => validateQueryId(req, res, next,"bot_id"),
    utils.findPaymentInfo
      );
  
module.exports = router;
