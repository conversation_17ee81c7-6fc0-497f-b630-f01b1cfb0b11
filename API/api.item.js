const router = require("express").Router();
const controllers = require("../Controllers/controllers.item");
const utils = require("../Utils/utils.item");
const cartUtils = require("../Utils/utils.cart");
const cartLogControllers = require("../Controllers/controllers.cart.log");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.item");
const offerItemControllers = require("../Controllers/controllers.offer.item");
const offerItemUtils = require("../Utils/utils.offer.item");
const offerUtils = require("../Utils/utils.offer");
const categoryUtils = require("../Utils/utils.category");

//SECTION Item CRUD
router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "item_title",
      "item_description",
      "item_price",
      "item_qty",
      "item_unit",
      "currency",
      "item_condition",
      "category_id",
      "item_icons",
    ]),
  utils.preprocessItem,
  utils.lemmtaizationOne,
  controllers.set,
  utils.postprocessItem,
  //revalidateBotPoolStorage,
  controllers.get
);

router.post(
  Routes.all,
  categoryUtils.findBotCategoriesByNames,
  utils.lemmtaizationMany,
  controllers.setAll,
  //revalidateBotPoolStorage,
  controllers.getAll
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["item_id"]),
  utils.findItemByQueryId,
  controllers.get
);

router.get(
  Routes.details,
  // (req, res, next) => validateQueryId(req, res, next, ["item_id", "bot_id"]),
  offerItemUtils.findByQueryBotId,
  offerUtils.findOffersByOfferItem,
  cartUtils.findCartByBotIdNoValidation,
  utils.findItemsByQueryBotId,
  utils.findItemFromBotItems,
  controllers.getDetails
)

router.get(
  Routes.all,
  //   (req, res, next) => validateQueryId(req, res, next),
  offerItemUtils.findByQueryBotId,
  offerUtils.findOffersByOfferItem,
  cartUtils.findCartByBotIdNoValidation,
  utils.findItemsByQueryBotId,
  controllers.getAll
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["item_id"]),
  utils.findItemById,
  controllers.update,
  //revalidateBotPoolStorage,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["item_id"]),
  offerItemControllers.purgeOne,
  controllers.purge,
  //revalidateBotPoolStorage,
  (req, res, next) => res.send({ message: "Item deleted successfully" })
);

router.purge(
  Routes.all,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.purgeAll,
  //revalidateBotPoolStorage,
  (req, res, next) => res.send({ message: "All items are deleted" })
);

router.post(
  Routes.order,
  (req, res, next) =>
    validate(req, res, next, [
      "cart",
      "phone",
      "name",
      "address",
      "total_price",
    ]),
  utils.findItemsFromList,
  cartLogControllers.set,
  cartLogControllers.setItems,
  cartUtils.findCartByBotId,
  controllers.updateItemsQuantities,
  controllers.sendOrderByEmail
);

module.exports = router;
