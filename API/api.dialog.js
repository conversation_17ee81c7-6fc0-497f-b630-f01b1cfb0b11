const router = require("express").Router();
const controllers = require("../Controllers/controllers.dialog");
const utils = require("../Utils/utils.dialog");
const { validate, validateQueryId, contactUS } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.dialog");
const botLogControlers = require("../Controllers/controllers.bot.log");

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, "dialog_id"),
  utils.findDialogById,
  controllers.get
);

// router.get(
//   Routes.all,
//   (req, res, next) => validateQueryId(req, res, next, "bot_id"),
//   utils.findDialogsdBotId,
//   controllers.getAll
// );

router.get(
  Routes.triggers,
  (req, res, next) => validateQueryId(req, res, next, "dialog_id"),
  utils.findDialogById,
  utils.findTriggersV2,
  // utils.findTriggers,
  controllers.getTriggers
);

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.set,
  botLogControlers.createBotDialogLogs,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.findDialogById,
  controllers.update,
  botLogControlers.createBotDialogLogs,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["dialog_id"]),
  utils.findDialogById,
  utils.findTriggersV2,
  // utils.findTriggers,
  botLogControlers.createBotDialogLogs,
  controllers.purge,
  controllers.get
);

router.get(
  Routes.all,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findDialogsdBotId,
  controllers.getAll
);

router.get(
  Routes.url,
  (req, res, next) => validateQueryId(req, res, next, "url"),
  utils.findDialogByUrl,
  controllers.get
);

router.get(
  Routes.isLive,
  (req, res, next) => validateQueryId(req, res, next, "dialog_id"),
  controllers.getIsLiveURL,
);

router.put(
  Routes.fallback,
  (req, res, next) => validateQueryId(req, res, next, "dialog_id"),
  controllers.setFallback
);

router.put(
  Routes.welcome,
  (req, res, next) => validateQueryId(req, res, next, "dialog_id"),
  controllers.setWelcome
);

router.get(
  Routes.fallback,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  controllers.getFallback
);

router.get(
  Routes.welcome,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  controllers.getWelcome
);

router.put(
  Routes.fallbackremove,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  controllers.removeFallback
);

router.put(
  Routes.welcomeremove,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  controllers.removeWelcome
);

router.post(
  Routes.duplicate,
  (req, res, next) => validateQueryId(req, res, next, "dialog_id"),
  controllers.duplicate,
)

module.exports = router;
