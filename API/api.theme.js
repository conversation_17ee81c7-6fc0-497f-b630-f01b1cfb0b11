const router = require("express").Router();
const controllers = require("../Controllers/controllers.theme");
const utils = require("../Utils/utils.theme");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.theme");

router.post(Routes.base, controllers.create, controllers.get);

router.get(Routes.all, utils.findThems, controllers.getAll);
router.get(Routes.base, utils.findThemeByThemeId, controllers.get);
router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["theme_id"]),
  utils.findThemeByThemeId,
  controllers.update,
  controllers.get
);

module.exports = router;
