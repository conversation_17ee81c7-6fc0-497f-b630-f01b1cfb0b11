const router = require("express").Router();
const controlers = require("../Controllers/controllers.monitor.bot");
const utils = require("../Utils/utils.monitor.bot");
const { validate, validateQueryArray } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.monitor.bot");

router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "email",
      "bot_id",
      "dashboard",
      "order",
      "active",
    ]),
  utils.checkExistance,
  controlers.set,
  controlers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id"]),
  utils.findMonitorBotById,
  controlers.getAll
);

router.put(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "email",
      "bot_id",
      "dashboard",
      "order",
      "active",
    ]),
  utils.findOneMonitorBot,
  controlers.update,
  controlers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "email"]),
  controlers.purge
);

module.exports = router;
