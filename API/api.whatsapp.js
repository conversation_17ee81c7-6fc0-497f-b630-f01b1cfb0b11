const router = require("express").Router();
const controllers = require("../Controllers/controllers.whatsapp");
const utils = require("../Utils/utils.whatsapp");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.whatsapp");
const multer = require("multer");

const storage = multer.memoryStorage();
const upload = multer({ storage: storage });
const botLogControlers = require("../Controllers/controllers.bot.log");

//SECTION WhatsApp Channel CRUD
router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "channel_id"]),
  utils.checkExistance,
  controllers.set,
  botLogControlers.createBotChannelLogs,
  controllers.get
);

router.get(
  Routes.base,
  // (req, res, next) => validateQueryId(req, res, next, ["phone"]),
  utils.findOneByPhoneOrBotId,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["whatsapp_id"]),
  utils.findByID,
  controllers.update,
  botLogControlers.createBotChannelLogs,
  controllers.get
);

router.post(
  Routes.media,
  (req, res, next) => validate(req, res, next, ["path", "type"]),
  controllers.uploadMedia
);

router.post(
  Routes.mediaVonage,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findOneByBotId,
  upload.single("file"),
  controllers.uploadMediaVonage
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  botLogControlers.createBotChannelLogs,
  controllers.purge
);

router.get(
  Routes.templates,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findOneByBotId,
  controllers.getTemplates
);

router.post(
  Routes.templates,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findOneByBotId,
  botLogControlers.createBotChannelLogs,
  controllers.createTemplate
);

module.exports = router;
