const router = require("express").Router();
const controllers = require("../Controllers/controllers.custom.time.class");
const utils = require("../Utils/utils.custom.class");
const { validate, validateQueryId, contactUS,validateQueryArray } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.custom.time.class");

router.post(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["bot_id","class_id","workinghours"]),
    controllers.createWorkingHourForClass,
    controllers.getWorkingHoursClass
  );
  
//   router.get(
//     Routes.base,
//     (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "class_id"]),
//     controllers.findClassById,
//     controllers.findworkingHourClass,
//     controllers.getWorkingHoursClass
//   );
  
  router.get(
    Routes.base,
    (req, res, next) => validateQueryId(req, res, next, "bot_id"),
    controllers.getClassTime,
  );

  
module.exports = router;
