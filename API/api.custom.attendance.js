const router = require("express").Router();
const controllers = require("../Controllers/controllers.custom.attendance");
const utils = require("../Utils/utils.custom.attendance");
const { validate, validateQueryId, validateQueryArray } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.custom.attendance");

router.post(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["bot_id","trainee_id","class_id","attendance_date","is_attended"]),
    controllers.set,
    controllers.get
  );
  
  router.get(
    Routes.attended,
    (req, res, next) => validateQueryArray(req, res, next,["bot_id","className","date_year","date_month","date_day"]),
    utils.findAttendedTrainees
  );

  
  router.get(
    Routes.non_attended,
    (req, res, next) => validateQueryArray(req, res, next, ["bot_id","className","date_year","date_month","date_day"]),
    utils.findNonAttendedTrainees
  );
  
  router.put(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["trainee_id","class_id","bot_id"]),
    utils.findAttendaceByID,
    controllers.update,
    controllers.get
  );

  router.delete(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["bot_id","attendance_id"]),
    controllers.purge
  );

  router.get(
    Routes.all_info,
    (req, res, next) => validateQueryId(req, res, next,"bot_id"),
    utils.findAttendanceInfo
      );

  
module.exports = router;
