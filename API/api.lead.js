const router = require("express").Router();
const controllers = require("../Controllers/controllers.lead");
const { validate, validateQueryId } = require("../Utils/utils");
const utils = require("../Utils/utils.leads");
const { Routes } = require("../Routes/routes.lead");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.create,
  controllers.get
);

router.get(
  Routes.all,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"] ),
  utils.findByBotId,
  controllers.getAll
)

module.exports = router;
