const { route } = require("./api.botTemplate");

var router = require("express").Router();

const root = "/api";

router.use(root, require("./api.user"));
router.use(root, require("./api.context"));
router.use(root, require("./api.faq"));
router.use(root, require("./api.bot"));
router.use(root, require("./api.trigger"));
router.use(root, require("./api.entity"));
router.use(root, require("./api.intent"));
router.use(root, require("./api.dashboard"));
router.use(root, require("./api.config"));
router.use(root, require("./api.offer.item"));
router.use(root, require("./api.item"));
router.use(root, require("./api.offer"));
router.use(root, require("./api.facebook"));
router.use(root, require("./api.category"));
router.use(root, require("./api.nlp"));
router.use(root, require("./api.plan"));
router.use(root, require("./api.voice"));
router.use(root, require("./api.item.option"));
router.use(root, require("./api.plan.function"));
router.use(root, require("./api.cart.log"));
router.use(root, require("./api.cart"));
router.use(root, require("./api.weather"));
router.use(root, require("./api.phone"));
router.use(root, require("./api.email"));
router.use(root, require("./api.bot.voice"));
router.use(root, require("./api.bot.user"));
router.use(root, require("./api.popup"));
router.use(root, require("./api.popupPlugin"));
router.use(root, require("./api.location"));
router.use(root, require("./api.cart.log.item"));
router.use(root, require("./api.fallbackPlugin"));
router.use(root, require("./api.bot.info"));
router.use(root, require("./api.monitor.bot"));
router.use(root, require("./api.monitor"));
router.use(root, require("./api.workinghours"));
router.use(root, require("./api.reservation"));
router.use(root, require("./api.reservation.room"));
router.use(root, require("./api.reservation.table"));
router.use(root, require("./api.reservation.order"));
router.use(root, require("./api.feature"));
router.use(root, require("./api.item.feature"));
router.use(root, require("./api.search.item.log"));
router.use(root, require("./api.user.favorite"));
router.use(root, require("./api.user.follow"));
router.use(root, require("./api.user.address"));
router.use(root, require("./api.feedback"));
router.use(root, require("./api.sanad.feedback"));
router.use(root, require("./api.message"));
router.use(root, require("./api.graph"));
router.use(root, require("./api.performit"));
// router.use(root, require("./api.custom"));
router.use(root, require("./api.conversion"));
router.use(root, require("./api.lead.plugin"));
router.use(root, require("./api.lead"));
router.use(root, require("./api.biller"));
router.use(root, require("./api.editor"));
router.use(root, require("./api.stt"));
router.use(root, require("./api.whatsapp"));
router.use(root, require("./api.promotion"));
router.use(root, require("./api.bot.uniqueUser"));
router.use(root, require("./api.report"));
router.use(root, require("./api.reportPlugin"));
router.use(root, require("./api.googlesheetplugin"));
router.use(root, require("./api.shopify"));
router.use(root, require("./api.zendeskplugin"));
router.use(root, require("./api.zendeskticketplugin"));
router.use(root, require("./api.wordscheme"));
router.use(root, require("./api.watester"));
router.use(root, require("./api.theme"));
router.use(root, require("./api.ner"));
router.use(root, require("./api.trelloplugin"));
router.use(root, require("./api.seat"));
router.use(root, require("./api.sendGrid"));
router.use(root, require("./api.botTemplate"));
router.use(root, require("./api.livechatPlugin"));
router.use(root, require("./api.customs.trainee"));
router.use(root, require("./api.bot.custom"));
router.use(root, require("./api.custom.class"));
router.use(root, require("./api.trainee.class"));
router.use(root, require("./api.custom.attendance"));
router.use(root, require("./api.custom.payment"));
router.use(root, require("./api.ssc"));
router.use(root, require("./api.custom.class.time"));
// router.use(root, require("./api.custom.graphs"));
router.use(root, require("./api.bot.domain"));
router.use(root, require("./api.custom.users"));
router.use(root, require("./api.sanad.service"));
router.use(root, require("./api.sanad.faq"));
router.use(root, require("./api.sanad.trigger"));
router.use(root, require("./api.whatsapp.broadcast"));
router.use(root, require("./api.livechatIntegration"));
router.use(root, require("./api.gptintegration"));
router.use(root, require("./api.searchsettings"));
router.use(root, require("./api.unstructured.resources"));
router.use(root, require("./api.shortcuts"));
router.use(root, require("./api.dialog"));
router.use(root, require("./api.calendlyIntergration"));
router.use(root, require("./api.sentimentAddon"));
router.use(root, require("./api.emailtemplate"));
router.use(root, require("./api.smallTalks"));
router.use(root, require("./api.employee.ilo"));
router.use(root, require("./api.employer.ilo"));
router.use(root, require("./api.slackIntegration"));
router.use(root, require("./api.telegramIntegration"));
router.use(root, require("./api.feedback.ilo"));
router.use(root, require("./api.payoneRecord"));

router.use(root, require("./api.genesysIntegration"));
router.use(root, require("./api.smtp"));
router.use(root, require("./api.topics"));
router.use(root, require("./api.dialog.checkpoints"));
router.use(root, require("./api.dialog.expiryLogs"));
router.use(root, require("./api.log"));
router.use(root, require("./api.dialog.version.control"));
router.use(root, require("./api.custom.dashboard"));
router.use(root, require("./api.badWords"));

router.use(root, require("./api.jettInvoices"));
router.use(root, require("./api.internalLiveChat"));
router.use(root, require("./api.ticketing"));
router.use(root, require("./api.LLMIntegration"));
router.use(root, require("./api.dynamic.forms"));
router.use(root, require("./api.transactions"));
router.use(root, require("./api.transactions.dashboard"));

router.use(root, require("./api.recaptcha"));

router.use(function (err, req, res, next) {
  if (err.name === "ValidationError") {
    return res.status(422).json({
      errors: Object.keys(err.errors).reduce(function (errors, key) {
        errors[key] = err.errors[key].message;
        return errors;
      }, {}),
    });
  }

  return next(err);
});
module.exports = router;
