const router = require("express").Router();
const controllers = require("../Controllers/controllers.fallbackPlugin");
const utils = require("../Utils/utils.fallback.plugin");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.popup.fallback.plugin");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.checkExistance,
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findFallbackPluginByBotId,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.findFallbackPluginByBotId,
  controllers.update,
  controllers.get
);

module.exports = router;
