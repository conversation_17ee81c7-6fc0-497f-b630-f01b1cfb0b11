const router = require("express").Router();
const controllers = require("../Controllers/controllers.transactions.dashboard");
const { validate, sendResponse } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.transactions.dashboard");

// create one user interactions
router.post(
  Routes.tr_stats,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "start_day",
      "start_month",
      "start_year",
      "end_day",
      "end_month",
      "end_year",
    ]),
  controllers.getStats,
  sendResponse
);
router.post(
  Routes.tr_heatmap,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "start_day",
      "start_month",
      "start_year",
      "end_day",
      "end_month",
      "end_year",
    ]),
  controllers.getHeatmap,
  sendResponse
);

router.post(
  Routes.tr_messages_users_date,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "start_day",
      "start_month",
      "start_year",
      "end_day",
      "end_month",
      "end_year",
    ]),
  controllers.getMessagesUserDate,
  sendResponse
);
router.post(
  Routes.tr_answered_date,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "start_day",
      "start_month",
      "start_year",
      "end_day",
      "end_month",
      "end_year",
    ]),
  controllers.getAnsweredDate,
  sendResponse
);

router.post(
  Routes.tr_messages_users_channel,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "start_day",
      "start_month",
      "start_year",
      "end_day",
      "end_month",
      "end_year",
    ]),
  controllers.getMessagesUserChannel,
  sendResponse
);

router.post(
  Routes.tr_functions_requests_count,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "start_day",
      "start_month",
      "start_year",
      "end_day",
      "end_month",
      "end_year",
    ]),
  controllers.getFunctionsRequestsCount,
  sendResponse
);

router.post(
  Routes.tr_users_year,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.getUserPerYear,
  sendResponse
);

router.post(
  Routes.tr_last_messages,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.getLastMessages,
  sendResponse
);
router.post(
  Routes.tr_messages_users_country,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "start_day",
      "start_month",
      "start_year",
      "end_day",
      "end_month",
      "end_year",
    ]),
  controllers.getMessagesUserCountry,
  sendResponse
);

router.post(
  Routes.tr_top_not_answered,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "start_day",
      "start_month",
      "start_year",
      "end_day",
      "end_month",
      "end_year",
    ]),
  controllers.getTopNotAnswered,
  sendResponse
);

router.post(
  Routes.tr_not_answered_info,
  (req, res, next) => validate(req, res, next, ["bot_id", "message"]),
  controllers.getNotAnsweredInfo,
  sendResponse
);

router.post(
  Routes.tr_chat_history,
  (req, res, next) => validate(req, res, next, ["bot_id", "conversation_id"]),
  controllers.getChatHistory,
  sendResponse
);

router.post(
  Routes.tr_conversations,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.getConversations,
  sendResponse
);

router.post(
  Routes.tr_conversations_find,
  (req, res, next) => validate(req, res, next, ["bot_id", "searchQuery"]),
  controllers.getConversationsSearched,
  sendResponse
);

router.post(
  Routes.tr_checkpoints,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "start_day",
      "start_month",
      "start_year",
      "end_day",
      "end_month",
      "end_year",
    ]),
  controllers.getCheckpointData,
  sendResponse
);

module.exports = router;
