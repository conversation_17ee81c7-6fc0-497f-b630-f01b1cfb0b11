const router = require("express").Router();
const controllers = require("../Controllers/controllers.ner");
const utils = require("../Utils/utils.ner");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.ner");

//SECTION Entity CRUD
router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, ["ner_value", "ner_type", "language"]),
  controllers.set,
  controllers.get
);

router.post(
  Routes.all,
  (req, res, next) => validate(req, res, next, ["ners"]),
  controllers.createMany,
  controllers.getAll
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["ner_id"]),
  utils.findById,
  controllers.get
);

router.get(Routes.all,controllers.fetchNer);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["ner_id"]),
  utils.findById,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["ner_id"]),
  controllers.purge
);

module.exports = router;
