const router = require("express").Router();
const controllers = require("../Controllers/controllers.dynamic.forms");
const utils = require("../Utils/utils.dynamic.forms");
const {
  validate,
  validateQueryId,
  validateQueryArray,
} = require("../Utils/utils");
const { Routes } = require("../Routes/routes.dynamic.forms");

// getAllSchemasByBotId;
router.get(
  Routes.schemas,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id"]),
  controllers.getAllSchemasByBotId,
  utils.sendResponse
);
// // getOneSchemaById
router.get(
  Routes.schema,
  (req, res, next) =>
    validateQueryArray(req, res, next, ["dynamic_form_schema_id", "bot_id"]),
  controllers.getOneSchemaById,
  utils.sendResponse
);
// // createSchema
router.post(
  Routes.schema,
  (req, res, next) => validate(req, res, next, ["bot_id", "schema", "title"]),
  controllers.createSchema,
  utils.sendResponse
);
// // deleteSchema
router.delete(
  Routes.schema,
  (req, res, next) =>
    validate(req, res, next, ["bot_id", "dynamic_form_schema_id"]),
  controllers.deleteSchema,
  utils.sendResponse
);

// create a schema response;
router.post(
  Routes.response,
  (req, res, next) =>
    validate(req, res, next, ["dynamic_form_schema_id", "response"]),
  controllers.createSchemaResponse,
  utils.sendResponse
);

router.get(
  Routes.response,
  (req, res, next) =>
    validateQueryArray(req, res, next, ["dynamic_form_schema_id"]),
  controllers.getSchemaResponses,
  utils.sendResponse
);

// router.put(
//   Routes.base,
//   (req, res, next) => validate(req, res, next, ["resource_id"]),
//   utils.findUnstructuredResourceById,
//   controllers.update,
//   controllers.get
// );

// router.put(
//   Routes.all,
//   utils.findManyUnstructuredResources,
//   controllers.updateMany,
//   controllers.getAll
// );

// router.delete(
//   Routes.base,
//   (req, res, next) => validate(req, res, next, ["resource_id"]),
//   controllers.purge,
//   controllers.get
// );

// router.delete(Routes.all, controllers.purgeMany, controllers.getAll);

module.exports = router;
