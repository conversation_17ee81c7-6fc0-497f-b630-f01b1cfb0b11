const router = require("express").Router();
const controllers = require("../Controllers/controllers.item.option");
const utils = require("../Utils/utils.item.option");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.item.option");

//SECTION Item CRUD
router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "item_id",
      "option_title",
      "option_description",
      "option",
    ]),
  utils.preprocess,
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["item_id"]),
  utils.findOptionByItemId,
  controllers.getAll
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["item_option_id"]),
  utils.findOptionById,
  utils.preprocess,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["item_option_id"]),
  controllers.purgeOne
);

module.exports = router;
