const router = require("express").Router();
const controllers = require("../Controllers/controllers.seat");
const {
  validate,
  validateQueryArray,
  validateModels,
} = require("../Utils/utils");
const { Routes } = require("../Routes/routes.seat");
const controllerslivechatPlugin = require("../Controllers/controllers.livechatPlugin");

router.post(
  Routes.login,
  (req, res, next) => validate(req, res, next, ["email", "password"]),
  controllers.findSeat,
  controllers.seatLogin
);

router.post(
  Routes.workingHour,
  (req, res, next) =>
    validate(req, res, next, ["bot_id", "workinghours", "email", "seatbot_id"]),
  controllers.purgeAllSeatHours,
  controllers.findSeatBot,
  controllers.createWorkingHourForSeat,
  controllers.getSeatHours
);

router.get(
  Routes.workingHour,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "email"]),
  controllers.findSeatBot,
  controllers.findworkingHour,
  controllers.getseatBothour
);

router.post(
  Routes.new_password,
  (req, res, next) => validate(req, res, next, ["email", "password"]),
  controllers.findSeat,
  controllers.updateSeatPassword
);

router.post(
  Routes.logout,
  (req, res, next) => validate(req, res, next, ["email"]),
  controllers.findSeat,
  controllers.seatLogout
);

router.post(
  Routes.seat,
  (req, res, next) => validate(req, res, next, ["email", "password"]),
  controllers.checkSeatExistance,
  controllers.createSeat,
  controllers.getSeatAuth
);

//FIXME
router.post(
  Routes.seatBot,
  (req, res, next) => validate(req, res, next, ["email", "bot_id"]),
  // controllers.findSeat,
  // (req, res, next) => validateModels(req, res, next, ["seat"]),
  // controllers.checkSeatExistance,
  // controllers.createSeat,

  controllers.checkSeatBotExistance,
  controllers.createSeatBot,
  controllers.getSeatBot
);

router.get(
  Routes.seatBot,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id"]),

  controllers.findAll,
  controllers.getSeatBot
);
router.get(
  Routes.available,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id"]),
  controllers.findLiveChatPlugin,
  controllers.findAvailableSeat,
  controllers.getSeat
);

router.post(
  Routes.conversation,
  (req, res, next) =>
    validate(req, res, next, ["conversation_id", "liveperson_id", "bot_id"]),
  controllers.checkConversationExistance,
  controllers.createConversation,
  controllers.getConversation
);

router.put(
  Routes.conversation,
  (req, res, next) => validate(req, res, next, ["conversation_id"]),
  controllers.findConversation,
  controllers.endConversation,
  controllers.getConversation
);

router.get(
  Routes.conversation,
  (req, res, next) => validateQueryArray(req, res, next, ["liveperson_id"]),
  controllers.findConversations,
  controllers.getConversations
);

router.get(
  Routes.messages,
  (req, res, next) => validateQueryArray(req, res, next, ["conversation_id"]),
  controllers.findMessages,
  controllers.getMessages
);

router.post(Routes.messages, controllers.addMessages, controllers.getMessages);

module.exports = router;
