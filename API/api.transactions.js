const router = require("express").Router();
const controllers = require("../Controllers/controllers.transactions");
const utils = require("../Utils/utils.custom.trainee.class");
const { validate, sendResponse } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.transactions");

// create one user interactions
router.post(
  Routes.user_interactions,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "channel",
      "conversation_id",
      "message",
      "country",
      "ip_address",
      "city",
      "continent",
      "is_voice",
      "message_uuid",
      "session_uuid",
      "button_payload",
      "type",
    ]),
  controllers.createUserInteraction,
  sendResponse
);

// create one cube
router.post(
  Routes.tr_set,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "result_status",
      "engine",
      "user_interaction_id",
      "bot_answers",
    ]),
  controllers.createFinalTr,
  sendResponse
);

// create many bot responses
router.delete(
  Routes.user_interactions,
  (req, res, next) => validate(req, res, next, ["message_uuid"]),
  controllers.deleteUserInteraction,
  sendResponse
);

module.exports = router;
