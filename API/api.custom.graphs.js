// const router = require("express").Router();
// const controllers = require("../Controllers/controllers.custom.graphs");
// const {
//   validate,
//   validateQueryId,
//   validateQueryArray,
// } = require("../Utils/utils");
// const { Routes } = require("../Routes/routes.custom.graphs");

// router.get(
//   Routes.base,
//   (req, res, next) => validateQueryId(req, res, next, "bot_id"),
//   controllers.getAllGraph
// );

// router.post(
//   Routes.base,
//   (req, res, next) => validate(req, res, next, ["bot_id", "graph_name"]),
//   controllers.createGraphForBot
// );
// router.get(
//   Routes.onelineUser,
//   (req, res, next) => validateQueryId(req, res, next, "bot_id"),
//   controllers.getOnline_users
// );
// router.get(
//   Routes.perday,
//   (req, res, next) => validateQueryId(req, res, next, "bot_id"),
//   controllers.get_avg_per_day
// );
// router.get(
//   Routes.perhour,
//   (req, res, next) => validateQueryId(req, res, next, "bot_id"),
//   controllers.get_avg_per_hour
// );
// module.exports = router;
