const router = require("express").Router();
const controllers = require("../Controllers/controllers.plan");
const utils = require("../Utils/utils.plan");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.plan");

//SECTION Plan CRUD
router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "plan_id",
      "plan_name",
      "plan_description",
      "plan_price",
      "currency",
    ]),
  controllers.create,
  controllers.get
);

router.get(Routes.all, utils.findPlans, controllers.getAll);

router.put(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "plan_id",
      "plan_name",
      "plan_description",
      "plan_price",
      "currency",
    ]),
  utils.findPlanById,
  controllers.update,
  controllers.get
);

module.exports = router;
