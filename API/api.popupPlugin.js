const router = require("express").Router();
const controllers = require("../Controllers/controllers.popupPlugin");
const utils = require("../Utils/utils.popup.plugin");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.popup.plugin");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.checkExistance,
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findPopupPluginByBotId,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.findPopupPluginByBotId,
  controllers.update,
  controllers.get
);

module.exports = router;
