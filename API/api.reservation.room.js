const router = require("express").Router();
const controllers = require("../Controllers/controllers.reservation.room");
const utils = require("../Utils/utils.reservation.room");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.reservation.room");

//SECTION Offer CRUD
router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "room_name",
      "bot_id",
      "room_description",
      "smoking_free",
      "floor",
      "room_status",
      "placement",
    ]),
  controllers.set,
  controllers.get
);

//router.post(Routes.all, controllers.setAll, controllers.getAll);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["room_id"]),
  utils.findRoomById,
  controllers.get
);

router.get(
  Routes.all,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findRoomsByBotId,
  controllers.getAll
);

router.put(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "room_id",
      "room_name",
      "bot_id",
      "room_description",
      "smoking_free",
      "floor",
      "room_status",
      "placement",
    ]),
  utils.findRoomById,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["room_id"]),
  controllers.purge
);

module.exports = router;
