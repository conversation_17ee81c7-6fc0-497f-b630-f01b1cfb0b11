const router = require("express").Router();
const controllers = require("../Controllers/controllers.dialog.checkpoints");
const utils = require("../Utils/utils.dialog.checkpoints");
const { validate, validateQueryId, sendResponse } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.dialog.checkpoints");

router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, ["tag", "conversation_id", "bot_id", "channel", "dialog_id"]),
  controllers.set,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["dialog_checkpoint_id"]),
  utils.findDialogCheckpointById,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["dialog_checkpoint_id"]),
  controllers.purge
);

// Keep the GET endpoint for backward compatibility
router.get(
  Routes.many,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findCheckpointsByBotID,
  sendResponse
);

// Add POST endpoint with date range parameters
router.post(
  Routes.many,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.findCheckpointsByBotID,
  sendResponse
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, "dialog_checkpoint_id"),
  utils.findDialogCheckpointById,
  controllers.get
);

// Keep the GET endpoint for backward compatibility
router.get(
  Routes.checkpoints_dialogs,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findDialogsWithCheckpoints,
  sendResponse
);

// Add POST endpoint with date range parameters
router.post(
  Routes.checkpoints_dialogs,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.findDialogsWithCheckpoints,
  sendResponse
);

// Keep the GET endpoint for backward compatibility
router.get(
  Routes.dialog_checkpoints,
  (req, res, next) => validateQueryId(req, res, next, "dialog_id"),
  utils.findCheckpointsByDialogID,
  sendResponse
);

// Add POST endpoint with date range parameters
router.post(
  Routes.dialog_checkpoints,
  (req, res, next) => validate(req, res, next, ["dialog_id"]),
  utils.findCheckpointsByDialogID,
  sendResponse
);

// Add search endpoint for checkpoints
router.post(
  Routes.search,
  (req, res, next) => {
    // At least one search parameter is required
    if (
      !req.body.bot_id &&
      !req.body.dialog_id &&
      !req.body.conversation_id &&
      !req.body.tag &&
      !req.body.channel
    ) {
      return res.status(400).send({
        message:
          "At least one search parameter is required (bot_id, dialog_id, conversation_id, tag, or channel)",
      });
    }
    next();
  },
  utils.searchCheckpoints,
  sendResponse
);

module.exports = router;
