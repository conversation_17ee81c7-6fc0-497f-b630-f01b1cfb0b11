const router = require("express").Router();
const controllers = require("../Controllers/controllers.entity");
const utils = require("../Utils/utils.entity");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.entity");

//SECTION Entity CRUD
router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["entity_name"]),
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["entity_id"]),
  utils.findEntityByQueryId,
  controllers.get
);

router.get(
  Routes.all,
//   (req, res, next) => validateQueryId(req, res, next),
  utils.findEntities,
  controllers.getAll
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["entity_id"]),
  utils.findEntityById,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["entity_id"]),
  controllers.purge
);

module.exports = router;
