const router = require("express").Router();
const controllers = require("../Controllers/controllers.bot.custom");
const utils = require("../Utils/utils.bot.custom");
const { validate, validateQueryId, contactUS, validateQueryArray } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.bot.custom");

router.post(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["bot_id","table_name","form_details","pk_name"]),
    controllers.set,
    controllers.get
  );
  
  router.get(
    Routes.tables_of_bot,
    (req, res, next) => validateQueryId(req, res, next,"bot_id"),
    utils.findAll,
    controllers.get
  );
  

  router.put(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["table_id"]),
    utils.findCustomBotById,
    controllers.update
  );

  router.delete(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["bot_id"]),
    controllers.purge
  );

  router.post(
    Routes.bot_table_name,
    (req, res, next) => validate(req, res, next, ["bot_id","table_name"]),
    controllers.getDataByTableBotId
  );

  router.delete(
    Routes.deleteFromCustomTable,
    (req, res, next) => validate(req, res, next, ["pk_name","pk","table_name"]),
    controllers.deleteFromCustomTable
  )

  router.put(
    Routes.updateFromCustomTable,
    (req, res, next) => validate(req, res, next, ["table_name","bot_id"]),
    controllers.updateDataTableFromCustTble,
    controllers.getPaymentInfo

  )
  
  router.post(
    Routes.addDatatoCustomTable,
    (req, res, next) => validate(req, res, next, ["table_name","bot_id"]),
    controllers.addDatatoCustTble,
    controllers.getPaymentInfo
  )

  // router.get(
  //   Routes.getColumnNames,
  //   (req, res, next) => validate(req, res, next, ["table_name"]),
  //   controllers.getColumnsCustTble
  // )

    router.get(
    Routes.getTraineesBasedOnClass,
    (req, res, next) => validateQueryArray(req, res, next, ["bot_id","className"]),
    utils.findTraineesInClass
  )


  router.get(
    Routes.checkAttendance,
    (req, res, next) => validateQueryArray(req, res, next, ["bot_id","trainee_class_id","date_year","date_month","date_day"]),
    utils.checkAttendance
  )

  router.get(
    Routes.not_registered,
    (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
    utils.getNotRegisteredTrainees
  )


  router.get(
    Routes.trialTrainee,
    (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
    utils.trialTrainee
  )
  router.get(
    Routes.id_number_trainee,
    (req, res, next) => validateQueryId(req, res, next, ["unique_code"]),
    utils.findIdNumberTrainee
  )
  router.get(
    Routes.active_trainees,
    (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
    utils.activeTrainees
  )
  router.get(
    Routes.not_active_trainees,
    (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
    utils.notActiveTrainees
  )
  router.get(
    Routes.reminder_active_trainees,
    (req, res, next) => validateQueryArray(req, res, next, ["bot_id","day"]),
    utils.reminderActiveTrainees
  )

  router.get(
    Routes.get_all_trainees,
    (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
    utils.getAllTrainees
  )
  
module.exports = router;
