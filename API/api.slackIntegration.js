const router = require("express").Router();
const controllers = require("../Controllers/controllers.slackIntegration");
const { validate } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.slackIntegration");
const botLogControlers = require("../Controllers/controllers.bot.log");

// GET by channel_id
router.get(Routes.getByChannel, controllers.getByChannel);

// GET by bot_id
router.get(Routes.getByBot, controllers.getByBot);

// POST create
router.post(
  Routes.create,
  (req, res, next) =>
    validate(req, res, next, ["channel_id", "api_token", "channel"]),
  botLogControlers.createBotChannelLogs,

  controllers.create
);

// PUT update
router.put(
  Routes.update,
  (req, res, next) => validate(req, res, next, ["channel_id", "bot_id"]),
  controllers.update,
  botLogControlers.createBotChannelLogs
);

router.delete(
  Routes.delete,
  (req, res, next) => validate(req, res, next, ["channel", "bot_id"]),
  botLogControlers.createBotChannelLogs,
  controllers.deleteConnection
);
module.exports = router;
