const router = require("express").Router();
const controllers = require("../Controllers/controllers.dialog.expiryLogs");
const utils = require("../Utils/utils.dialog.expiryLogs");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.dialog.expiryLogs");

router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, ["conversation_id", "bot_id", "channel", "action_config", "expiry_min","dialog_url", "dialog_id" ]),
  controllers.set,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["dialog_expiry_log_id"]),
  utils.findDialogExpiryLogById,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["dialog_expiry_log_id"]),
  utils.findDialogExpiryLogById,
  controllers.purge,
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, "dialog_checkpoint_id"),
  utils.findDialogExpiryLogById,
  controllers.get
);

module.exports = router;
