const router = require("express").Router();
const controllers = require("../Controllers/controllers.monitor");
const utils = require("../Utils/utils.monitor");
const { validate, validateQueryArray } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.monitor");
const {
  findAllMonitorBots,
  findMonitorBots,
} = require("../Utils/utils.monitor.bot");

router.post(
  Routes.register,
  (req, res, next) => validate(req, res, next, ["email", "password"]),
  utils.checkEmailExistance,
  controllers.register,
  controllers.login
);

router.post(
  Routes.login,
  (req, res, next) => validate(req, res, next, ["email", "password"]),
  utils.checkLoginExistance,
  utils.authenticatePassword,
  controllers.login
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryArray(req, res, next, ["email"]),
  findAllMonitorBots,
  findMonitorBots,
  controllers.getAll
);

module.exports = router;
