const router = require("express").Router();

const controllers = require("../Controllers/controllers.badWords");
const {
  validate,
  validateQueryId,
  validateQueryArray,
} = require("../Utils/utils");
const { Routes } = require("../Routes/routes.badWords");
const utils = require("../Utils/utils.badWords");
const botLogControlers = require("../Controllers/controllers.bot.log");
const { findBotConfigByQueryIdProcedure } = require("../Utils/utils.config");

router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [ "bad_word", "bot_id"]),
  controllers.checkExists,
  controllers.set,
  botLogControlers.createBotBadWordLogs,
  //revalidateBotPoolStorage,
  controllers.get
);

router.delete(
  Routes.all,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  botLogControlers.createBotBadWordLogs,
  controllers.purgeAllBadWords,
  //revalidateBotPoolStorage,
  (req, res, next) => res.send({ message: "All badWords are deleted" })
);

router.delete(
  Routes.many,
  botLogControlers.createBotBadWordLogs,
  controllers.purgeMany,
  utils.findBadWordsByBotId,
  //revalidateBotPoolStorage,
  controllers.getAll
);

router.post(
  Routes.many,
  controllers.setMany,
  botLogControlers.createBotBadWordLogs,
  //revalidateBotPoolStorage,
  controllers.getAll
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bad_word_id"]),
  utils.findBadWordById,
  controllers.update,
  botLogControlers.createBotBadWordLogs,

  //revalidateBotPoolStorage,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bad_word_id"]),
  utils.findBadWordById,
  botLogControlers.createBotBadWordLogs,
  controllers.purge,
  //revalidateBotPoolStorage,
  (req, res, next) => res.send({ message: "badWord deleted successfully" })
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["bad_word_id"]),
  utils.findBadWordById,
  controllers.get
);

router.get(
  Routes.many,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  findBotConfigByQueryIdProcedure,
  utils.findBadWordsByBotId,
  controllers.getAll
);


module.exports = router;
