const router = require("express").Router();
const controllers = require("../Controllers/controllers.offer.item");
const utils = require("../Utils/utils.offer.item");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.offer.item");

//SECTION Offer Item CRUD
router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["offer_id", "bot_id"]),
  utils.preprocessAll,
  controllers.purgeAll,
  controllers.setAll,
  controllers.getAll
);

// router.delete(
//   Routes.base,
//   (req, res, next) => validate(req, res, next, ["offer_id"]),
//   controllers.purge
// );

router.get(Routes.base, utils.findByOfferId, controllers.getAll);

// router.delete(
//   Routes.base,
//   (req, res, next) => validate(req, res, next, ["item_id"]),
//   controllers.purge
// );

module.exports = router;
