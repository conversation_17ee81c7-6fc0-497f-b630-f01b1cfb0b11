const router = require("express").Router();
const controllers = require("../Controllers/controllers.unstructured.resources");
const utils = require("../Utils/utils.unstructured.resources");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.unstructured.resources");

router.get(
  Routes.all,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findByBotId,
  controllers.getAll
);

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "resource_url"]),
  controllers.set,
  controllers.get
);

router.post(
  Routes.all,
  // (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.setMany,
  controllers.getAll
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["resource_id"]),
  utils.findUnstructuredResourceById,
  controllers.update,
  controllers.get
);

router.put(
  Routes.all,
  utils.findManyUnstructuredResources,
  controllers.updateMany,
  controllers.getAll
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["resource_id"]),
  controllers.purge,
  controllers.get
);

router.delete(Routes.all, controllers.purgeMany, controllers.getAll);

module.exports = router;
