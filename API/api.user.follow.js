const router = require("express").Router();
const controllers = require("../Controllers/controllers.user.follow");
const utils = require("../Utils/utils.user.follow");
const { validate, validateQueryArray } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.user.follow");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "user_id"]),
  utils.checkExistance,
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryArray(req, res, next, ["user_id"]),
  utils.findByUserId,
  controllers.getAll
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["follow_id"]),
  controllers.purge
);

module.exports = router;
