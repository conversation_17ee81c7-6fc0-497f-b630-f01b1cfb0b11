const router = require("express").Router();
const { Routes } = require("../Routes/routes.shopify");
const {
  validate,
  validateQueryId,
  validateQueryArray,
} = require("../Utils/utils");
const controllers = require("../Controllers/controllers.shopify");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id"]),
  controllers.find,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.find,
  controllers.update,
  controllers.get
);
router.post(
  Routes.shopify,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.find,
  controllers.getProducts
);

module.exports = router;
