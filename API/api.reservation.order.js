const router = require("express").Router();
const controllers = require("../Controllers/controllers.reservation.order");
const utils = require("../Utils/utils.reservation.order");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.reservation.order");

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findBotOrders,
  controllers.getAll
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["order_id","order_status"]),
  utils.findOrder,
  controllers.update,
  controllers.get
);

module.exports = router;
