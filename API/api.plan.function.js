const router = require("express").Router();
const controllers = require("../Controllers/controllers.plan.function");
const utils = require("../Utils/utils.plan.function");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.plan.function");

//SECTION Plan CRUD
router.post(
  Routes.base,
  //   (req, res, next) => validate(req, res, next, []),
  controllers.create,
  controllers.get
);

router.get(Routes.all, utils.findPlanFunctions, controllers.getAll);

router.put(
  Routes.base,
  // (req, res, next) =>
    // validate(req, res, next, [
    //   "plan_id",
    //   "plan_name",
    //   "plan_description",
    //   "plan_price",
    //   "currency",
    // ]),
    utils.findFunctionByPlanById,
  controllers.update,
  controllers.get
);

module.exports = router;
