const router = require("express").Router();
const controllers = require("../Controllers/controllers.user.address");
const utils = require("../Utils/utils.user.address");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.user.address");

//SECTION Item CRUD
router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, ["user_id", "city", "country", "long", "lat"]),
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["user_id"]),
  utils.findByUserId,
  controllers.getAll
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["address_id"]),
  utils.findById,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["address_id"]),
  controllers.purge
);

module.exports = router;
