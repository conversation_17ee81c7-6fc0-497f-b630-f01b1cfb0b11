const router = require("express").Router();
const { Routes } = require("../Routes/routes.googlesheetplugin");
const {
  validate,
  validateQueryId,
  validateQueryArray,
} = require("../Utils/utils");
const controllers = require("../Controllers/controllers.googlesheetplugin");

router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, ["bot_id", "refresh_token", "email"]),
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id"]),
  controllers.find,
  controllers.get
);

router.get(
  Routes.allintegration,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id"]),
  controllers.getAllIntergationStatus,
  controllers.getAllIntegation
);
router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),

  controllers.update,
  controllers.findOne,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "email"]),
  controllers.purge
);

module.exports = router;
