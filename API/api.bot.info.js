const router = require("express").Router();
const { Routes } = require("../Routes/routes.bot.info");
const { validate, validateQueryId } = require("../Utils/utils");
const {
  findBotBotInfoById,
  checkBotInfoExistance,
} = require("../Utils/utils.bot.info");
const controllers = require("../Controllers/controllers.bot.info");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  checkBotInfoExistance,
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  //findBotConfigByQueryId,
  findBotBotInfoById,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  findBotBotInfoById,
  controllers.update,
  controllers.get
);

module.exports = router;
