const router = require("express").Router();
const controllers = require("../Controllers/controllers.custom.dashboard");
const utils = require("../Utils/utils.custom.dashboard");
const { Routes } = require("../Routes/routes.custom.dashboard");
const { validate, validateQueryId } = require("../Utils/utils");

//SECTION  CRUD

router.post(
    Routes.base,
    (req, res, next) => validateQueryId(req, res, next, "bot_id"),
    controllers.set,
    controllers.getAll
  );

router.get(Routes.base, utils.findCustomDashboardBotId, controllers.getAll);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "custom_dashboard_id"]),
  utils.findCustomDashboardById,
  controllers.update,
  controllers.getAll
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "custom_dashboard_id"]),
  utils.findCustomDashboardById,
  controllers.purge,
);

module.exports = router;
