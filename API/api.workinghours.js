const router = require("express").Router();
const controllers = require("../Controllers/controllers.workinghours");
const utils = require("../Utils/utils.workinghours");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.workinghours");

//SECTION Facebook Channel CRUD
router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "workinghours"]),
  controllers.purgeAll,
  controllers.setAll,
  controllers.getAll
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findAllWorkingHours,
  controllers.getAll
);

module.exports = router;
