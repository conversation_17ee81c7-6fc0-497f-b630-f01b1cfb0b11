const router = require("express").Router();
const { Routes } = require("../Routes/routes.llmIntegration");
const controllers = require("../Controllers/controllers.llmIntegration");
const utils = require("../Utils/utils.llmIntegration");
const {
  validate,
  validateQueryId,
  validateQueryArray,
} = require("../Utils/utils");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.getByBotID,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, ["bot_id", "LLM_integration_id"]),
  utils.updateIntegration,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, ["bot_id", "LLM_integration_id"]),
  utils.deleteIntegration
);

router.post(
  Routes.check,
  (req, res, next) => validate(req, res, next, ["llm_config"]),
  utils.checkLLM
);

router.get(
  Routes.getAllTags,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.getAllTags,
  controllers.getTags
);

router.get(
  Routes.similaritySearch,
  (req, res, next) =>
    validateQueryArray(req, res, next, ["bot_id", "question"]),
  utils.getAllTags,
  utils.getByBotID,
  utils.performSimilaritySearch,
  controllers.getSimilaritySearchResults
);

router.post(
  Routes.refine,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.getAllTags,
//   utils.getByBotID,
  utils.performSimilaritySearch,
  utils.refineLLM,
  controllers.afterRefine
);

router.post(
  Routes.store,
  (req, res, next) => validate(req, res, next, ["store_config"]),
  utils.checkStore
);

module.exports = router;
