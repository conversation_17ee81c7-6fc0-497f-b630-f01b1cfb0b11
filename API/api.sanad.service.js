const router = require("express").Router();
const controllers = require("../Controllers/controllers.sanad.service");
const utils = require("../Utils/utils.sanad.service");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.sanad.service");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "title"]),
  utils.lemmtaizationOne,
  controllers.set,
  controllers.get
);

router.post(
    Routes.all,
    utils.lemmtaizationMany,
    controllers.setAll,
    controllers.getAll
);
  
router.get(
  Routes.all,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findSanadServiceByBotId,
  controllers.getAll
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["sanad_service_id"]),
  utils.findSanadServiceById,
  controllers.get
);

//GET Not Reviewed List 
router.get(
  Routes.notReviewedList,
  controllers.getNotReviewedList
);

//GET Not Sanad List 
router.get(
  Routes.sanadkb,
  controllers.getSanadKb
);

router.put(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, ["bot_id", "sanad_service_id"]),
  utils.findSanadServiceById,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, ["sanad_service_id"]),
  controllers.purge
);

router.delete(
  Routes.many,
  controllers.purgeMany,
  utils.findBotSanadServicesById,
  controllers.getAll
);

router.put(
  Routes.all,
  utils.findManyServices,
  controllers.updateMany,
  utils.findBotSanadServicesById,
  controllers.getAll
);

router.get(
  Routes.brodcastingDetails,
  controllers.getBroadcastingDetails
);

module.exports = router;
