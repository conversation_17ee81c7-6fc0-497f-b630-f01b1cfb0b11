const router = require("express").Router();
const { Routes } = require("../Routes/routes.editor");
const {
  validate,
  validateQueryId,
  validateQueryArray,
} = require("../Utils/utils");
const {
  findEditorsByBotId,
  findEditorById,
  findOneByBot,
} = require("../Utils/utils.editor");

const { findBotById } = require("../Utils/utils.bot");
const { findUserById } = require("../Utils/utils.user");

const controllers = require("../Controllers/controllers.editor");

router.get(
  Routes.many,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  findEditorsByBotId,
  controllers.getAll
);

router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "email",
      "bot_id",
      "appearance_privilege",
      "builder_privilege",
      "store_privilege",
    ]),
  controllers.set,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["editor_id"]),
  findEditorById,
  controllers.update,
  controllers.get
);

router.get(
  Routes.privilage,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "user_id"]),
  findBotById,
  findUserById,
  findOneByBot,
  controllers.checkPrivilages,
  controllers.get
);


router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["editor_id"]),
  controllers.purge
)

router.delete(
  Routes.many,
  controllers.purgeMany,
  findEditorsByBotId,
  controllers.getAll
);

module.exports = router;
