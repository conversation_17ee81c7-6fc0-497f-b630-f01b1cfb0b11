const router = require("express").Router();
const controllers = require("../Controllers/controllers.internalLiveChat");
const utils = require("../Utils/utils.internalLiveChat");
const {
  validate,
  validateQueryId,
  validateQueryArray,
} = require("../Utils/utils");
const { Routes } = require("../Routes/routes.internalLiveChat");
const { findUserById } = require("../Utils/utils.user");
const { findOneByBot } = require("../Utils/utils.editor");
const { findBotById } = require("../Utils/utils.bot");

router.post(
  Routes.createAgent,
  (req, res, next) => validate(req, res, next, ["editor_id", "bot_id"]),
  controllers.createAgent,
  controllers.getAgent
);

router.post(
  Routes.createAgents,
  (req, res, next) => validate(req, res, next, ["editors", "bot_id"]),
  controllers.bulkCreateAgents,
)

router.put(
  Routes.updateAgent,
  (req, res, next) => validate(req, res, next, ["agent_id"]),
  controllers.updateAgent,
  controllers.getAgent
)

router.post( 
  Routes.requestChat,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "conversation_id",
      "channel",
      "livechat_integration_id",
    ]),
  controllers.requestChat,
  utils.getSessionDataCount,
  controllers.getQueue,
);

router.post( 
  Routes.assignChat,
  (req, res, next) => validate(req, res, next, ["queue_id", "agent_id", "bot_id"]),
  controllers.assignChat,
  utils.getSessionDataCount,
  utils.getAgentDataCount,
  utils.getQueueDataCount,
  controllers.getSessionData,
);

router.post( 
  Routes.acceptChat,
  (req, res, next) => validate(req, res, next, ["session_id", "agent_id", "bot_id"]),
  controllers.acceptChat,
  utils.getSessionDataCount,
  controllers.getSession
);

router.post(
  Routes.rejectChat,
  (req, res, next) => validate(req, res, next, ["session_id", "agent_id", "bot_id"]),
  controllers.rejectChat,
  utils.getSessionDataCount,
  utils.getAgentDataCount,
  utils.getQueueDataCount,
  controllers.getSessionData,
);

router.post(
  Routes.closeChat,
  (req, res, next) => validate(req, res, next, ["session_id", "agent_id", "bot_id"]),
  controllers.closeChat,
  utils.getSessionDataCount,
  utils.getAgentDataCount,
  utils.getQueueDataCount,
  controllers.getSessionData
);

router.get(  
  Routes.getAllSessions,
  (req, res, next) => validateQueryArray(req, res, next, ["agent_id", "bot_id"]),
  utils.findSessionsByAgentID,
  utils.getSessionDataCount,
  controllers.getSessions,
);

router.get(
  Routes.getAgent,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "user_id"]),
  findBotById,
  findUserById,
  findOneByBot,
  utils.findAgentByEditor,
  controllers.getAgent
);

router.post(
  Routes.integration,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.createIntegration,
  controllers.getIntegration
);

router.post(
  Routes.createTransaction,
  (req, res, next) =>
    validate(req, res, next, ["session_id", "message", "sender"]),
  controllers.createTransaction,
  controllers.getTransaction
);

router.post(  
  Routes.createPendingMessage,
  (req, res, next) => validate(req, res, next, ["queue_id", "message", "bot_id"]),
  controllers.createPendingMessage,
  utils.getSessionDataCount,
  controllers.getPendingMessage
);

router.post(  
  Routes.transferPendingMessages,
  (req, res, next) => validate(req, res, next, ["queue_id", "session_id", "bot_id"]),
  controllers.transferPendingMessages,
  utils.getSessionDataCount,
  controllers.getPendingMessage,
);

router.get(
  Routes.transactions,
  (req, res, next) => validateQueryId(req, res, next, "session_id"),
  utils.findTransactionsBySessionID,
  controllers.getTransactions
);

router.post(
  Routes.dropQueue,
  (req, res, next) => validate(req, res, next, ["queue_id", "bot_id"]),
  controllers.dropQueue,
  utils.getQueueDataCount,
  controllers.getQueue
);

router.get(
  Routes.integration,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findIntegrationByBotID,
  controllers.getIntegration
);

router.put(
  Routes.integration,
  (req, res, next) => validate(req, res, next, ["livechat_integration_id"]),
  controllers.updateIntegration,
  controllers.getIntegration
);

// update agent's status
router.post(
  Routes.updateAgentStatus,
  (req, res, next) => validateQueryArray(req, res, next, ["agent_id", "bot_id"]),
  controllers.updateAgentStatus,
  utils.getAgentDataCount,
  controllers.getAgent
);

// update session status
router.post(
  Routes.updateSessionStatus,
  (req, res, next) => validate(req, res, next, ["session_id", "status", "bot_id"]),
  controllers.updateSessionStatus,
  utils.getSessionDataCount,
  controllers.getSession
);

router.get(
  Routes.liveChatcalc,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "day", "month", "year"]),
  controllers.getLiveChatCalculated
)

router.get(
  Routes.allEditorsAgents,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findEditorsAgentsByBotID,
  controllers.getAllEditorsAgents
)

router.post(
  Routes.agentLog,
  (req, res, next) => validate(req, res, next, ["agent_id", "status"]),
  controllers.createAgentLog,
  controllers.getAgentLog
);

router.get(
  Routes.getAgentsData,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id"]),
  utils.getAgentsData,
  controllers.getAgent,
)

router.get(
  Routes.getSessionsData,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id"]),
  utils.getSessionsData,
  controllers.getSession,
)

router.get(
  Routes.getQueuesData,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id"]),
  utils.getQueueDataCount,
  controllers.getQueue
)

router.post(
  Routes.createRating,
  (req, res, next) => validate(req, res, next, ["session_id", "agent_id", "rating", "rating_msg"]),
  controllers.createRating
);

router.get(
  Routes.getAgentRatings,
  (req, res, next) => validateQueryId(req, res, next, ["agent_id"]),
  controllers.getAgentRatings
)

module.exports = router;
