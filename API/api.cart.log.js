const router = require("express").Router();
const controllers = require("../Controllers/controllers.cart.log");
const utils = require("../Utils/utils.cart.log");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.cart.log");

router.get(
  Routes.all,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findCartLogByBotId,
  controllers.getAll
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "cart_log_id"]),
  utils.findCartLog,
  controllers.update,
  controllers.get
);

module.exports = router;
