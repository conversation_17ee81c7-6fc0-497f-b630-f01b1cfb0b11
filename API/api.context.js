const router = require("express").Router();
const { Routes } = require("../Routes/routes.context");
const { validate, validateQueryId } = require("../Utils/utils");
const { findBotFAQsById } = require("../Utils/utils.faq");
const { findBotTriggersById } = require("../Utils/utils.trigger");
const controllers = require("../Controllers/controllers.context");

router.post(
  Routes.publish,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  findBotFAQsById,
  findBotTriggersById,
  controllers.purge,
  controllers.preprocessFAQs,
  controllers.preprocessTriggers,
  controllers.publish
);

router.get(
  Routes.all,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  controllers.findAll
);

module.exports = router;
