const router = require("express").Router();
const controllers = require("../Controllers/controllers.telegramIntegration");
const { validate } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.telegramIntegration");


// POST create
router.post(
  Routes.create,
  (req, res, next) =>
    validate(req, res, next, ["bot_id", "token"]),
  controllers.create
);

// GET get
router.get(
  Routes.get,
  controllers.get
);

router.delete(
  Routes.delete,
  (req, res, next) =>
    validate(req, res, next, ["telegram_integration_id"]),
  controllers.deleteConnection
);
module.exports = router;
