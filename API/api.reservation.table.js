const router = require("express").Router();
const controllers = require("../Controllers/controllers.reservation.table");
const utils = require("../Utils/utils.reservation.table");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.reservation.table");

//SECTION Offer CRUD
router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "room_id",
      "table_name",
      "min_guests",
      "max_guests",
    ]),
  controllers.set,
  controllers.get
);

//router.post(Routes.all, controllers.setAll, controllers.getAll);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["table_id"]),
  utils.findTableById,
  controllers.get
);

router.get(
  Routes.all,
  // (req, res, next) => validateQueryId(req, res, next),
  utils.findTables,
  controllers.getAll
);

// router.get(
//   Routes.all,
//   (req, res, next) => validateQueryId(req, res, next, "bot_id"),
//   utils.findRoomsByBotId,
//   controllers.getAll
// );

router.put(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "room_id",
      "table_name",
      "min_guests",
      "max_guests",
    ]),
  utils.findTableById,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["table_id"]),
  controllers.purge
);

router.post(
  Routes.available,
  (req, res, next) =>
    validate(req, res, next, ["guests", "room_id", "bot_id", "to", "from"]),
  utils.findTables,
  utils.checkAvailability
);

router.post(
  Routes.order,
  (req, res, next) =>
    validate(req, res, next, ["guests", "room_id", "bot_id", "to", "from"]),
  controllers.setOrder
);

module.exports = router;
