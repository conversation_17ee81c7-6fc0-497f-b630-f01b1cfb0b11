const router = require("express").Router();
const controllers = require("../Controllers/controllers.emailTemplate");
const utils = require("../Utils/utils.emailTemplate");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.emailTemplate");


  
router.get(
    Routes.base,
    (req, res, next) => validateQueryId(req, res, next, "email_template_id"),
    utils.findEmailTemplateById,
    controllers.get
);
  

router.post(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["template"]),
    controllers.set,
    controllers.get
);

router.put(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["email_template_id"]),
    utils.findEmailTemplateById,
    controllers.update,
    controllers.get
);

router.delete(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["email_template_id"]),
    controllers.purge,
    controllers.get
);
  

module.exports = router;

