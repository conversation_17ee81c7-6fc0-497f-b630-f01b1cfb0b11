const router = require("express").Router();
const { Routes } = require("../Routes/routes.category");
const { validate, validateQueryId } = require("../Utils/utils");
const {
  findCategoryById,
  findBotCategoriesByQueryId,
  lemmtaizationOne,
  lemmtaizationMany,
} = require("../Utils/utils.category");
const controllers = require("../Controllers/controllers.category");

router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "category_name",
      "category_description",
    ]),
  lemmtaizationOne,
  controllers.create,
  //revalidateBotPoolStorage,
  controllers.get
);

router.post(
  Routes.all,
  (req, res, next) => validate(req, res, next, ["bot_id", "categories"]),
  lemmtaizationMany,
  controllers.createMany,
  //revalidateBotPoolStorage,
  controllers.getAll
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["category_id"]),
  controllers.purge,
  //revalidateBotPoolStorage,
  (req, res, next) => res.send({ message: "Category deleted successfully" })
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  findBotCategoriesByQueryId,
  controllers.getAll
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["category_id"]),
  findCategoryById,
  lemmtaizationOne,
  controllers.update,
  //revalidateBotPoolStorage,
  controllers.get
);

router.delete(
  Routes.all,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  // controllers.getAll,
  controllers.purgeAll,
  //revalidateBotPoolStorage,
  (req, res, next) => res.send({ message: "All Categories are deleted" })
);

module.exports = router;
