const router = require("express").Router();
const controllers = require("../Controllers/controllers.bot.voice");
const utils = require("../Utils/utils.bot.voice");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.bot.voice");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.checkExistance,
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findBotVoiceByBotId,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.findBotVoiceByBotId,
  controllers.update,
  controllers.get
);

module.exports = router;
