const router = require("express").Router();
const controllers = require("../Controllers/controllers.item.feature");
const utils = require("../Utils/utils.item.feature");
const featureUtils = require("../Utils/utils.feature");
const featureControllers = require("../Controllers/controllers.feature");
const {
  validate,
  validateQueryId,
  validateQueryArray,
  //revalidateBotPoolStorage,
} = require("../Utils/utils");
const { Routes } = require("../Routes/routes.item.feature");

//SECTION Feature CRUD
router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "feature_id",
      "bot_id",
      "feature_value",
      "item_id",
    ]),
  featureUtils.findById,
  // utils.preprocess,
  controllers.set,
  utils.postprocess,
  //revalidateBotPoolStorage,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryArray(req, res, next, ["item_feature_id"]),
  utils.findById,
  controllers.get
);

router.get(
  Routes.all,
  // (req, res, next) => validateQueryArray(req, res, next, ["item_id"]),
  utils.findAll,
  utils.parseAll,
  controllers.getAll
);

router.get(
  Routes.criteria,
  utils.findByFeatureCriteria,
  utils.parseAll,
  controllers.getAll
);

router.put(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "feature_id",
      "bot_id",
      "feature_value",
      "item_id",
    ]),
  utils.findById,
  featureUtils.findById,
  // utils.preprocess,
  controllers.update,
  //revalidateBotPoolStorage,
  // utils.parseOne,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["item_feature_id"]),
  controllers.purge,
  //revalidateBotPoolStorage,
  (req, res, next) => res.send({ message: "Item feature deleted successfully" })
);

router.post(
  Routes.map,
  (req, res, next) => validate(req, res, next, ["bot_id", "data"]),
  featureControllers.purgeAll,
  controllers.purgeAll,
  featureControllers.setAll,
  controllers.setAllMap,
  controllers.getAll
);
// router.purge(Routes.all, (req, res, next) => {
//   validate(req, res, next, ["bot_id"]), controllers.purgeAll;
// });

module.exports = router;
