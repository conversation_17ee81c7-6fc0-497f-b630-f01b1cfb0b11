const router = require("express").Router();
const controllers = require("../Controllers/controllers.jettInvoices");
const utils = require("../Utils/utils.jettInvoices");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.jettInvoices");

// create invoice
router.post(Routes.base, controllers.createInvoice, controllers.getInvoice);

// get invoice
router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, "invoice_id"),
  utils.findInvoice,
  controllers.getInvoice
);
// update invoice
router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["invoice_id"]),
  controllers.updateInvoice,
  controllers.getInvoice
);

// pay invoice
// router.post(Routes.pay, utils.payInvoice);

router.post(
  Routes.payBooking,
  (req, res, next) => validate(req, res, next, ["invoice_id", "payment"]),
  controllers.payBookingInvoice,
  controllers.getInvoice
);

router.post(
  Routes.payCardRecharge,
  (req, res, next) =>
    validate(req, res, next, [
      "invoice_id",
      "payment",
      "card_number",
      "subscription_number",
      "amount",
    ]),
  controllers.payCardRecharge,
  controllers.getInvoice
);

router.post(
  Routes.khbMsrTicket,
  // (req, res, next) => validate(req, res, next, ["invoice_id"]),
  controllers.createMSRKHBTickets,
  controllers.getTicket
);

router.post(
  Routes.khbSlvTicket,
  // (req, res, next) => validate(req, res, next, ["invoice_id"]),
  controllers.createSLVKHBTickets,
  controllers.getTicket
);

router.get(Routes.all, utils.findAllInvoices, controllers.getAllInvoices);
router.get(Routes.deleted, utils.findUnpaidInvoices, controllers.getAllInvoices);

router.get(Routes.paid, utils.findPaidInvoices, controllers.getAllInvoices);

router.post(
  Routes.track,
  (req, res, next) =>
    validate(req, res, next, [
      "path",
      "path_ar",
      "from_en",
      "from_ar",
      "from_lemmatized_ar",
      "destination_en",
      "destination_ar",
      "destination_lemmatized_ar",
      "roundTrip_id",
      "isInternational",
    ]),
  controllers.createTrack,
  controllers.getTrack
);

router.put(
  Routes.track,
  (req, res, next) => validate(req, res, next, ["jett_track_id"]),
  controllers.updateTrack,
  controllers.getTrack
);

router.delete(
  Routes.track,
  (req, res, next) => validate(req, res, next, ["jett_track_id"]),
  controllers.deleteTrack
);

router.get(
  Routes.track,
  (req, res, next) => validateQueryId(req, res, next, "jett_track_id"),
  utils.findTrack,
  controllers.getTrack
);

router.get(Routes.tracks, utils.findTracks, controllers.getTracks);

module.exports = router;
