const router = require("express").Router();
const controllers = require("../Controllers/controllers.sanad.faq");
const utils = require("../Utils/utils.sanad.faq");
const { validate, validateQueryId, validateQueryArray } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.sanad.faq");


router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["sanad_service_id", "question", "answer"]),
  controllers.lemmtaizationOne,
  controllers.set,
  controllers.get
);

router.post(
    Routes.all,
    // controllers.lemmtaizationMany,
    controllers.setAll,
    controllers.getAll
  );
  

router.get(
  Routes.all,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findSanadFaqByBotId,
  controllers.getAll
);

router.get(
    Routes.pool,
    controllers.getPool
);



router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["sanad_faq_id"]),
  utils.findSanadFaqById,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, ["bot_id", "sanad_faq_id"]),
  utils.findSanadFaqById,
  controllers.update,
  controllers.get
);
router.put(
  "/sanad/faqV",
  (req, res, next) =>
  validate(req, res, next, ["bot_id", "sanad_faq_id"]),
  utils.findSanadFaqById,
  controllers.update,
  controllers.get
);


router.delete(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, ["sanad_faq_id"]),
  controllers.purge
);

router.delete(
  Routes.all,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.purgeAllQna
);

router.delete(
  Routes.many,
  controllers.purgeMany,
  utils.findBotSanadFAQsById,
  controllers.getAll
);

router.put(
  Routes.all,
  utils.findManySanadFAQs,
  controllers.lemmtaizationMany,
  controllers.updateMany,
  utils.findBotSanadFAQsById,
  controllers.getAll
);

router.get(
  Routes.alternative,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id","sanad_faq_id"]),
  utils.findQuestionAlternatives
);


router.get(
  Routes.faqs_per_service,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  controllers.getAllFaqsPerSer
);

router.get(
  Routes.approved_faqs,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findApprovedFaqs,
  controllers.getAll
);

router.get(
  Routes.not_approved_faqs,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findNotApprovedFaqs,
  controllers.getAll
);
module.exports = router;
