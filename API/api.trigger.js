const router = require("express").Router();
const controllers = require("../Controllers/controllers.trigger");
const utils = require("../Utils/utils.trigger");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.trigger");

//SECTION Trigger CRUD
router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [ "trigger", "bot_id"]),
  controllers.preproccess,
  // utils.checkExistance,
  controllers.set,
  //revalidateBotPoolStorage,
  // controllers.createLogs,
  controllers.get
);
router.post(
  Routes.all,
  controllers.preproccessAll,
  controllers.lemmtaizationMany,
  controllers.setMany,
  //revalidateBotPoolStorage,
  controllers.getAll
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["trigger_id"]),
  utils.findTriggerByQueryId,
  controllers.get
);

router.get(
  Routes.trigger_bots,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  controllers.getBotTriggers,
  
)

router.get(
  Routes.name,
  (req, res, next) => validateQueryId(req, res, next, ["trigger_name"]),
  utils.findBotTriggersByName,
  controllers.getAll
);

router.get(
  Routes.all,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findBotTriggersByQueryId,
  controllers.getAll
);

router.put(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [ "trigger_id", "trigger"]),
  // utils.checkExistance,
  utils.findTriggerById,
  controllers.update,
  //revalidateBotPoolStorage,
  // controllers.createLogs,
  controllers.get
);

router.put(
  Routes.all,
  utils.findManyTriggers,
  controllers.lemmtaizationMany,
  controllers.updateMany,
  utils.findBotTriggersById,
  //revalidateBotPoolStorage,
  controllers.getAll
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["trigger_id"]),
  // utils.checkExistance,
  // utils.findTriggerByQueryId,
  // controllers.createLogsForDELETE,
  controllers.purge,
  //revalidateBotPoolStorage,
  (req, res, next) => res.send("Deleted"),
);

router.delete(
  Routes.all,
  controllers.purgeMany,
  utils.findBotTriggersById,
  //revalidateBotPoolStorage,
  controllers.getAll
);

router.get(
  Routes.first,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findBotTriggersFirst,
  controllers.getAll
);

router.get(
  Routes.trigger_logs,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findTriggersLogs
);

router.get(
  Routes.alternative,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findBotTriggersAlternative,
  controllers.getAll
);

module.exports = router;
