const router = require("express").Router();

const controllers = require("../Controllers/controllers.smallTalks");
const {
  validate,
  validateQueryId,
  validateQueryArray,
} = require("../Utils/utils");
const { Routes } = require("../Routes/routes.smallTalks");
const utils = require("../Utils/utils.smallTalks");
const botLogControlers = require("../Controllers/controllers.bot.log");

router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, ["answer", "question", "bot_id"]),
  controllers.set,
  botLogControlers.createBotSmallTalkLogs,
  //revalidateBotPoolStorage,
  controllers.get
);

router.delete(
  Routes.all,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  botLogControlers.createBotSmallTalkLogs,
  controllers.purgeAllSmallTalks,
  //revalidateBotPoolStorage,
  (req, res, next) => res.send({ message: "All smallTalks are deleted" })
);

router.delete(
  Routes.many,
  botLogControlers.createBotSmallTalkLogs,
  controllers.purgeMany,
  utils.findSmallTalksByBotId,
  //revalidateBotPoolStorage,
  controllers.getAll
);

router.post(
  Routes.many,
  controllers.setMany,
  botLogControlers.createBotSmallTalkLogs,
  //revalidateBotPoolStorage,
  controllers.getAll
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["small_talk_id"]),
  utils.findSmallTalkById,
  controllers.update,
  botLogControlers.createBotSmallTalkLogs,

  //revalidateBotPoolStorage,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["small_talk_id"]),
  utils.findSmallTalkById,
  botLogControlers.createBotSmallTalkLogs,
  controllers.purge,
  //revalidateBotPoolStorage,
  (req, res, next) => res.send({ message: "smallTalk deleted successfully" })
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["small_talk_id"]),
  utils.findSmallTalkById,
  controllers.get
);

router.get(
  Routes.many,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findSmallTalksByBotId,
  controllers.getAll
);

module.exports = router;
