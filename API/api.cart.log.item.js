const router = require("express").Router();
const controllers = require("../Controllers/controllers.cart.log.item");
const { validate, validateQueryArray } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.cart.log.item");

router.get(
  Routes.sold,
  (req, res, next) =>
    validateQueryArray(req, res, next, ["bot_id", "N", "day", "month", "year"]),
  controllers.getTopNItemsSales
);

router.get(
  Routes.category,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "day", "month", "year"]),
  controllers.getTopSalesByCategory
);

router.get(
  Routes.offer,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "day", "month", "year"]),
  controllers.getTopSalesByOffer
);

router.get(
  Routes.month,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id", "year"]),
  controllers.getSalesByMonth
);

router.get(
  Routes.day,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id"]),
  controllers.getSalesByDay
);

router.get(
  Routes.city,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id","day", "month", "year"]),
  controllers.getSalesByCity
);

router.get(
  Routes.country,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id","day", "month", "year"]),
  controllers.getSalesByCountry
);

router.get(
  Routes.continent,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id","day", "month", "year"]),
  controllers.getSalesByContinent
);

module.exports = router;
