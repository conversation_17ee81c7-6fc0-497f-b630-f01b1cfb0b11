const router = require("express").Router();
const controllers = require("../Controllers/controllers.topics");
const utils = require("../Utils/utils.topics");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.topics");

//SECTION Topic CRUD
router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "display_name"]),
  utils.lemmatizeKeywords,
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, "topic_id"),
  utils.find,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["topic_id"]),
  utils.find,
  utils.lemmatizeKeywords,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["topic_id"]),
  controllers.purge
);

router.get(
  Routes.many,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findMany,
  controllers.getMany
);

router.get(
  Routes.kb,
  (req, res, next) => validateQueryId(req, res, next, "topic_id"),
  utils.findTopicKB,
  // controllers.getTopicKB
);

module.exports = router;