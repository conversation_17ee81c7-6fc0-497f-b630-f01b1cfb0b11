const router = require("express").Router();
const controllers = require("../Controllers/controllers.shortcuts");
const utils = require("../Utils/utils.shortcuts");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.shortcuts");

router.get(
  Routes.all,
  (req, res, next) => validateQueryId(req, res, next, ["user_id"]),
  utils.findUserShortcutsById,
  controllers.getAll
);

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["user_id", "shortcuts"]),
  utils.preprocess,
  controllers.set,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["shortcut_id"]),
  utils.findShortcutById,
  utils.preprocess,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["shortcut_id"]),
  controllers.purge
  //   controllers.get
);

module.exports = router;
