const router = require("express").Router();
const controllers = require("../Controllers/controllers.feature");
const utils = require("../Utils/utils.feature");
const { validate, validateQueryArray } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.feature");

//SECTION Feature CRUD
router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "feature_name"]),
  //utils.preprocess,
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryArray(req, res, next, ["feature_id"]),
  utils.findById,
  controllers.get
);

router.get(
  Routes.all,
  (req, res, next) => validateQueryArray(req, res, next, ["bot_id"]),
  utils.findAll,
  controllers.getAll
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["feature_id"]),
  utils.findById,
  //utils.preprocess,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["feature_id"]),
  controllers.purge
);

module.exports = router;
