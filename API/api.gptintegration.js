const router = require("express").Router();
const controllers = require("../Controllers/controllers.gptIntegration");
const utils = require("../Utils/utils.gptIntegration");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.gpt");

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findGPTIntegrationByBotId,
  controllers.get
);

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "openai_key"]),
  utils.validateApi<PERSON>ey,
  controllers.set,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.findGPTIntegrationByBotId,
  utils.validate<PERSON><PERSON><PERSON><PERSON>,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.purge,
  controllers.get
);

module.exports = router;
