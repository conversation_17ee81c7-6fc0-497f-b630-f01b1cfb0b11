const router = require("express").Router();
const controllers = require("../Controllers/controllers.weather");
const utils = require("../Utils/utils.weather");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.weather");

//SECTION Facebook Channel CRUD
router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.checkExistance,
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findWeatherByBotId,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.findWeatherByBotId,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.purge
);

module.exports = router;
