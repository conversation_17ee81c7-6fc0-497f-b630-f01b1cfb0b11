const router = require("express").Router();
const controllers = require("../Controllers/controllers.popup");
const utils = require("../Utils/utils.popup");
const { findPopupPluginByBotId } = require("../Utils/utils.popup.plugin");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.popup");

//SECTION Popup CRUD
router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "popup_type",
      "bot_id",
      // "popup_url",
      // "popup_message",
      "popup_description",
    ]),
  controllers.set,
  controllers.get
);

router.get(
  Routes.all,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  findPopupPluginByBotId,
  utils.findPopupsByBotID,
  controllers.getAll
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, "popup_id"),
  utils.findPopup,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "popup_id",
      "popup_type",
      // "popup_url",
      // "popup_message",
      "popup_description",
    ]),
  utils.findPopup,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["popup_id"]),
  controllers.purge
);

module.exports = router;
