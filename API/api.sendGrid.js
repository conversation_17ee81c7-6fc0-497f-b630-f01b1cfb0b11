const router = require("express").Router();
const controllers = require("../Controllers/controllers.sendGrid");
const utils = require("../Utils/utils.sendGrid");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.sendGrid");

//SECTION Plan CRUD
router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  // controllers.purge,
  controllers.set,
  controllers.get
);



router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  controllers.find,
  controllers.get
);

router.post(
  Routes.send,
  (req, res, next) => validate(req, res, next,["bot_id", "email", "title", "body"]),
  controllers.find,
  controllers.sendEmail
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["apiKey","email","bot_id"]),
  controllers.find,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.purge,
  controllers.get
);

module.exports = router;
