const router = require("express").Router();
const { Routes } = require("../Routes/routes.bot.user");
const { validate, validateQueryId } = require("../Utils/utils");
const controllers = require("../Controllers/controllers.bot.user");

router.post(
  Routes.location,
  (req, res, next) => validate(req, res, next, ["longitude","latitude","country_code"]),
  controllers.createLocationMap,
);

module.exports = router;
