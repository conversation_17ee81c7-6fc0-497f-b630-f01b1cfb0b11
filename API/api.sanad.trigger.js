const router = require("express").Router();
const controllers = require("../Controllers/controllers.sanad.trigger");
const utils = require("../Utils/utils.sanad.trigger");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.sanad.trigger");

//SECTION Trigger CRUD
router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["trigger", "bot_id"]),
  controllers.preproccess,
  utils.lemmtaizationOne,
  controllers.set,
  controllers.get
);
router.post(
  Routes.all,
  controllers.preproccessAll,
  utils.lemmtaizationMany,
  controllers.setMany,
  controllers.getAll
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["sanad_trigger_id"]),
  utils.findTriggerByQueryId,
  controllers.get
);


router.get(
  Routes.name,
  (req, res, next) => validateQueryId(req, res, next, ["trigger_name"]),
  utils.findBotTriggersByName,
  controllers.getAll
);

router.get(
  Routes.all,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findBotTriggersByQueryId,
  controllers.getAll
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["sanad_trigger_id", "trigger"]),
  utils.findTriggerById,
  controllers.update,
  controllers.get
);

router.put(
  Routes.all,
  utils.findManyTriggers,
  utils.lemmtaizationMany,
  controllers.updateMany,
  utils.findBotTriggersById,
  controllers.getAll
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["sanad_trigger_id"]),
  utils.findTriggerByQueryId,
  controllers.purge
);

router.delete(
  Routes.all,
  controllers.purgeMany,
  utils.findBotTriggersById,
  controllers.getAll
);

router.get(
  Routes.first,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findBotTriggersFirst,
  controllers.getAll
);



router.get(
  Routes.alternative,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findBotTriggersAlternative,
  controllers.getAll
);

router.get(
  Routes.triggers_per_service,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  controllers.getAllTriggersPerSer
);

router.get(
  Routes.approved_triggers,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findApprovedTriggers,
  controllers.getAll
);

router.get(
  Routes.not_approved_triggers,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findNotApprovedTriggers,
  controllers.getAll
);

module.exports = router;
