const router = require("express").Router();
const controllers = require("../Controllers/controllers.feedback");
const utils = require("../Utils/utils.feedback");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.feedback");

//SECTION Item CRUD
router.post(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, [
      "bot_id",
      "bot_user_id",
      "rate",
      "feedback_body",
    ]),
  controllers.set,
  controllers.get
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  utils.findByBotId,
  controllers.getAll
);

module.exports = router;
