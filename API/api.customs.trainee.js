const router = require("express").Router();
const controllers = require("../Controllers/controllers.customs.trainee");
const utils = require("../Utils/utils.customs.trainee");
const { validate, validateQueryId, contactUS } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.customs.trainee");

router.post(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["bot_id","name","gender","date_of_birth","first_phone","second_phone"]),
    controllers.set,
    controllers.get
  );
  
  router.get(
    Routes.base,
    (req, res, next) => validateQueryId(req, res, next, "trainee_id"),
    utils.findStudentById,
    controllers.get
  );
  
  router.get(
    Routes.all,
    (req, res, next) => validateQueryId(req, res, next,"bot_id"),
    utils.findAll,
    controllers.get
  );
  
  
  router.put(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["trainee_id"]),
    utils.findStudentById,
    controllers.update,
    controllers.get
  );

  
  router.delete(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["bot_id","trainee_id"]),
    controllers.purge
      );

module.exports = router;
