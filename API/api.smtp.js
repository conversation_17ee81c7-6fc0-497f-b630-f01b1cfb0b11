const router = require("express").Router();
const controllers = require("../Controllers/controllers.smtp");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.smtp");

//SECTION Plan CRUD
router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.set,
  controllers.get
);



router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  controllers.find,
  controllers.get
);


router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["host","username","password"]),
  controllers.find,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.purge,
  controllers.get
);

module.exports = router;
