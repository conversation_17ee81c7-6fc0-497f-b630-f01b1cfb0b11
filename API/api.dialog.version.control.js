const router = require("express").Router();
const {validate ,validateQueryId } = require("../Utils/utils");
const {Routes} = require("../Routes/routes.dialog.version.control");
const controllers = require("../Controllers/controllers.dialog.version.control");
const {findLogs, findLogsfalse}= require("../Utils/utils.dialog.version.control")

//not wanted 
router.post(
    Routes.base,
    (req, res, next)=> validate(req, res, next, ["dialog_id", "bot_id", "user_id"]),
        controllers.set,
        controllers.get,
);

router.get(
    Routes.all,
    (req, res, next)=> validateQueryId(req, res, next, "dialog_id"),
    findLogs,
    controllers.getAll,
);

router.get(
    Routes.stagingFalse,
    (req, res, next)=> validateQueryId(req, res, next, "dialog_id"),
    findLogsfalse,
    controllers.getIsStagingFalse,
);

router.get(
    Routes.dvc,
    (req, res, next) => validateQueryId(req, res, next, "dialog_version_control_id"),
    controllers.getDVC,
);

router.get(
    Routes.stagingTrue,
    (req, res, next) => validateQueryId(req, res, next, "dialog_id"),
    controllers.getIsStagingTrue,
)

router.put(
    Routes.update,
    (req, res, next)=> validateQueryId(req, res, next,"dialog_id"),
    controllers.update
);

//done
router.put(
    Routes.publish,
    (req,res,next)=> validateQueryId(req, res, next, "dialog_id"),
    controllers.publish
);

//done
router.put(
    Routes.changeIsLive,
    (req,res,next)=> validateQueryId(req, res, next, "dialog_version_control_id"),
    controllers.changeIsLive
);

//done
router.put(
    Routes.changeIsStaging,
    (req,res,next)=> validateQueryId(req, res, next, "dialog_version_control_id"),
    controllers.changeIsStaging
);

//delete
router.delete(
    Routes.base,
    (req, res, next)=> validateQueryId(req, res, next,"dialog_version_control_id"),
    controllers.purge
)
module.exports = router;