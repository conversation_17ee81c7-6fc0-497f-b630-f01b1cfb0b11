const router = require("express").Router();
const controllers = require("../Controllers/controllers.payoneRecord");
const utils = require("../Utils/utils.payoneRecord");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.payoneRecord");

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["invoice_id"]),
  utils.findPayoneInvoiceByInvoiceID,
  controllers.get
);

router.get(
  Routes.all,
  controllers.getAll
);

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "invoice_id", "channel", "conversation_id"]),
  controllers.set,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["invoice_id"]),
  utils.findPayoneInvoiceByInvoiceID,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["invoice_id"]),
  controllers.purge,
  controllers.get
);

router.post(
  Routes.log,
  (req, res, next) =>
    validate(req, res, next, [
      "template_name",
      "lang",
      "recipient",
      "template_type",
      "bot_id",
    ]),
  controllers.insertLog,
  controllers.getTLog
);

module.exports = router;
