const router = require("express").Router();
const controllers = require("../Controllers/controllers.bot.domain");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.bot.domain");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "host"]),
  controllers.set,
  controllers.get
);

router.post(
  Routes.validate,
  (req, res, next) => validate(req, res, next, ["bot_id", "host"]),
  controllers.validate
);

module.exports = router;
