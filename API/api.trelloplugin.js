const router = require("express").Router();
const controllers = require("../Controllers/controllers.trelloPlugin");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.trello");
router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
  controllers.find,
  controllers.get
);

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.set,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.find,
  controllers.update,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.purge,
  controllers.get
);

module.exports = router;
