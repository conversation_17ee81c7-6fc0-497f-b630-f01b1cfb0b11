const router = require("express").Router();
const controllers = require("../Controllers/controllers.promotion");
const utils = require("../Utils/utils.promotion");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.promotion");

router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["promotion_percentage","promotion_value"]),
  controllers.set,
  controllers.get
);

router.get(
  Routes.token,
  (req, res, next) => validateQueryId(req, res, next, "promotion_token"),
  utils.findPromotionByToken,
  controllers.get
);

router.post(
    Routes.generateURL,
    (req, res, next) => validate(req, res, next, ["promotion_id"]),
    controllers.generatePromotionURL
);

router.put(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["promotion_id"]),
  utils.findPromotionById,
  controllers.update,
  controllers.get
);

router.delete(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["promotion_id"]),
    utils.findPromotionById,
    controllers.update,
    controllers.get
);

module.exports = router;
