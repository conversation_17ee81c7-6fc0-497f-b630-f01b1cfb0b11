const router = require("express").Router();
const controllers = require("../Controllers/controllers.feedback.ilo");
const utils = require("../Utils/utils.feedback.ilo");
const { validate, validateQueryId } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.feedback.ilo");

//SECTION Entity CRUD

router.post(
    Routes.base,
    (req, res, next) =>
        validate(req, res, next, [
            "phone_number",
            "name",
            "dialog_id"
        ]),
    controllers.set,
    controllers.get
);

router.get(
    Routes.base,
    (req, res, next) => validateQueryId(req, res, next, ["feedback_id"]),
    utils.findById,
    controllers.get
); 

router.get(
    Routes.all,
    (req, res, next) => validateQueryId(req, res, next, ["dialog_id"]),
    utils.findAll,
    controllers.getAll
); 

router.delete(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["feedback_id"]),
    controllers.purge
);

router.post(
    Routes.base_log,
    (req, res, next) =>
        validate(req, res, next, [
            "feedback_id",
            "feedback_log_entry"
        ]),
    controllers.setLog,
    controllers.getLog
);


router.get(
    Routes.base_log,
    (req, res, next) => validateQueryId(req, res, next, ["feedback_log_id"]),
    utils.findLogById,
    controllers.getLog
); 

router.get(
    Routes.all_log,
    (req, res, next) => validateQueryId(req, res, next, ["feedback_id"]),
    utils.findLogAll,
    controllers.getLogAll
);


router.delete(
    Routes.base_log,
    (req, res, next) => validate(req, res, next, ["feedback_log_id"]),
    controllers.purgeLog
);

router.post(
    Routes.base_question,
    (req, res, next) =>
        validate(req, res, next, [
            "feedback_question_ar",
            "feedback_question_en",
            "dialog_id",
        ]),
    controllers.setQuestion,
    controllers.getQuestion
);


router.get(
    Routes.base_question,
    (req, res, next) => validateQueryId(req, res, next, ["feedback_question_id"]),
    utils.findQuestionById,
    controllers.getQuestion
);

router.get(
    Routes.all_question,
    (req, res, next) => validateQueryId(req, res, next, ["dialog_id"]),
    utils.findQuestionAll,
    controllers.getQuestionAll
);


router.delete(
    Routes.base_question,
    (req, res, next) => validate(req, res, next, ["feedback_question_id"]),
    controllers.purgeQuestion
);

module.exports = router;
