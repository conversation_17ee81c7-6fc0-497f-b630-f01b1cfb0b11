const router = require("express").Router();
const controllers = require("../Controllers/controllers.trainee.class");
const utils = require("../Utils/utils.custom.trainee.class");
const { validate, validateQueryId, validateQueryArray } = require("../Utils/utils");
const { Routes } = require("../Routes/routes.trainee.class");

router.post(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["bot_id","trainee_id","class_id"]),
    controllers.set,
    controllers.get
      );


  router.get(
        Routes.get_trainee_by_name,
        (req, res, next) => validateQueryArray(req, res, next,["bot_id","first_name","family_name"]),
        utils.findOneTraineeClassInfo
    );

  
  router.get(
    Routes.all_trainees_classes,
    (req, res, next) => validateQueryId(req, res, next,"bot_id"),
    utils.findTraineeClassInfo
      );
  
  router.get(
    Routes.base,
    (req, res, next) => validateQueryId(req, res, next, ["bot_id"]),
    utils.findTraineeClassInfo,
    controllers.get
  );
  
  router.put(
    Routes.edit_registeration,
    (req, res, next) => validate(req, res, next, ["trainee_class_id","bot_id","trainee_id","class_id"]),
    controllers.deleteRegisteration,
    controllers.set,
    controllers.get
  );

  router.delete(
    Routes.base,
    (req, res, next) => validate(req, res, next, ["bot_id","trainee_id"]),
    controllers.purge,
    
  );

  
module.exports = router;
