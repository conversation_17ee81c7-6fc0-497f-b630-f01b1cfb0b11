const router = require("express").Router();
const controllers = require("../Controllers/controllers.bot");
const DesignControllers = require("../Controllers/controllers.design");
const TriggerUtils = require("../Utils/utils.trigger");
const { findFunctionByPlanById } = require("../Utils/utils.plan.function");
const ConfigControllers = require("../Controllers/controllers.config");
const { findBotConfigByQueryIdProcedure } = require("../Utils/utils.config");
const InfoControllers = require("../Controllers/controllers.bot.info");
const { findBotVoiceByBotId } = require("../Utils/utils.bot.voice");
const { findBotOrders } = require("../Utils/utils.reservation.order");
const {
  validate,
  validateQueryId,
  createBotStorage,
  deleteBotStorage,
  duplicateBotStorage,
  pullBotPools,
} = require("../Utils/utils");
const { checkPrivilages } = require("../Utils/utils.editor");
const { findUserById } = require("../Utils/utils.user");
const BotDashboard = require("../Controllers/controllers.dashboard");
const { setDefaultTriggers } = require("../Controllers/controllers.trigger");
const { Routes } = require("../Routes/routes.bot");
const utils = require("../Utils/utils.bot");
const { findHiddenFAQs } = require("../Utils/utils.faq");
const FAQutils = require("../Utils/utils.faq");
const TopicUtils = require("../Utils/utils.topics");
const SmallTalkUtils = require("../Utils/utils.smallTalks");
const DialogUtils = require("../Utils/utils.dialog");
const TriggersUtils = require("../Utils/utils.trigger");
const CategoriesUtils = require("../Utils/utils.category");
const ItemUtils = require("../Utils/utils.item");
const ItemFeatureUtils = require("../Utils/utils.item.feature");
const FeatureUtils = require("../Utils/utils.feature");
const OfferUtils = require("../Utils/utils.offer");
const OfferItemsUtils = require("../Utils/utils.offer.item");
const ConfigUtils = require("../Utils/utils.config");
const BotInfoUtils = require("../Utils/utils.bot.info");
const dvcUtils = require("../Utils/utils.dialog.version.control");
const badWordUtils = require("../Utils/utils.badWords");

//SECTION BOT CRUD
router.post(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["user_id", "bot_name"]),
  utils.findUserBotsById,
  utils.checkUniqueName,
  controllers.set,
  DesignControllers.set,
  InfoControllers.set,
  ConfigControllers.set,
  createBotStorage,
  //setDefaultTriggers,
  BotDashboard.create,
  findFunctionByPlanById,
  controllers.get,
);
router.post(
  Routes.duplicate,
  (req, res, next) => validate(req, res, next, ["user_id", "bot_id"]),
  // utils.findUserBotsById,
  // utils.checkUniqueName,
  // utils.findBotById,
  DesignControllers.find,
  BotInfoUtils.findBotBotInfoById,
  DialogUtils.findDialogsdBotId,
  FAQutils.findAllBotFAQsByQueryId,
  TopicUtils.findMany,
  SmallTalkUtils.findSmallTalksByBotId,
  TriggersUtils.findBotTriggersByQueryId,
  CategoriesUtils.findAllBotCategoriesByQueryId,
  ItemUtils.findItemsByBot,
  ItemFeatureUtils.findAllByBot,
  FeatureUtils.findAll,
  OfferUtils.findAllByBot,
  OfferItemsUtils.findByQueryBotId,
  ConfigUtils.findBotConfigByQueryId,
  utils.findUserBotsById,
  utils.checkUniqueName,
  dvcUtils.getDvcByBotId,
  badWordUtils.findBadWordsByBotId,
  controllers.set,

  createBotStorage,
  BotDashboard.create,
  findFunctionByPlanById,
  controllers.duplicateBotInfo,
  controllers.duplicateStore,
  controllers.duplicateSmallTalks,
  controllers.duplicateFAQs,
  controllers.duplicateConfig,
  controllers.duplicateBotDesign,
  controllers.transfer_v2,

  // utils.findBotById,
  // DesignControllers.find,
  findFunctionByPlanById,
  controllers.get
);

router.post(
  Routes.restart,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.findBotById,
  DesignControllers.find,
  //TriggerUtils.findBotTriggersById,
  //findBotVoiceByBotId,
  //findHiddenFAQs,
  createBotStorage,
  findFunctionByPlanById,
  controllers.get
);

router.post(
  Routes.restartAll,
  // (req, res, next) => validate(req, res, next, ["bot_id"]),
  utils.findAllBots,
  utils.restartAll
);

router.get(
  Routes.all,
  (req, res, next) => validateQueryId(req, res, next, "user_id"),
  findUserById,
  checkPrivilages,
  controllers.findAll,
  controllers.getAll
);

router.get(
  Routes.log,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  controllers.getLateLog
);

router.get(
  Routes.details,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  findBotConfigByQueryIdProcedure,
  controllers.getDetails
);

router.get(
  Routes.base,
  (req, res, next) => validateQueryId(req, res, next, "bot_id"),
  utils.findBotByQueryId,
  DesignControllers.find,
  findFunctionByPlanById,
  controllers.get
);

router.put(
  Routes.base,
  (req, res, next) =>
    validate(req, res, next, ["bot_id", "user_id", "bot_name"]),
  utils.findBotById,
  DesignControllers.find,
  controllers.update,
  DesignControllers.update,
  findBotVoiceByBotId,
  // createBotStorage,
  findFunctionByPlanById,
  controllers.get
);

router.delete(
  Routes.base,
  (req, res, next) => validate(req, res, next, ["bot_id", "user_id"]),
  utils.findBotById,
  // deleteBotStorage,
  controllers.purge
);

router.get(Routes.checkout, controllers.prepareCheckout);
router.get(
  Routes.status,
  (req, res, next) => validateQueryId(req, res, next, "checkoutId"),
  controllers.getCheckoutStatus
);

// router.post(
//   Routes.transfer,
//   (req, res, next) => validate(req, res, next, ["src_bot_id", "dest_bot_id"]),
//   controllers.transfer,
//   duplicateBotStorage,
//   controllers.countingNumOfdowTemp,
//   // findBotVoiceByBotId,
//   // createBotStorage,
//   controllers.get
// );

router.post(
  Routes.publish,
  (req, res, next) => validate(req, res, next, ["bot_id"]),
  controllers.publishBot
);

// router.get(
//   "/pullbotpools",
//   utils.findAllBots,
//   pullBotPools
// )

module.exports = router;
