const URL = "http://localhost:5003/api/faq/similarity";
const ninteenfortynine = require("./1949.json");
const fetch = require("node-fetch");

const call = async (question, bot_id) => {
  await fetch(URL, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ question, bot_id }),
  });
};

(async () => {
  const filtered = ninteenfortynine.filter(
    (a) => a.answered && !isNaN(a.botID)
  );
  for (var i = 0; i < filtered.length; i++) {
    var a = filtered[i];
    await call(a.user_q, a.botID);
  }
})();
