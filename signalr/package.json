{"_from": "@microsoft/signalr", "_id": "@microsoft/signalr@6.0.9", "_inBundle": false, "_integrity": "sha512-DGVYe3ycT2PfRU7m3xCbv1HjhvClKl2VB1HyFlvf8SqBGXz3Cx+oalNWGYrGIgADA6Q2xaB4GaDmDdprTa2U0Q==", "_location": "/@microsoft/signalr", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "@microsoft/signalr", "name": "@microsoft/signalr", "escapedName": "@microsoft%2fsignalr", "scope": "@microsoft", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/@microsoft/signalr/-/signalr-6.0.9.tgz", "_shasum": "50f0960e2858e2c8b1656dd0810af12639302fa9", "_spec": "@microsoft/signalr", "_where": "/Users/<USER>/Repo/bot-designer-server", "author": {"name": "Microsoft"}, "bugs": {"url": "https://github.com/dotnet/aspnetcore/issues"}, "bundleDependencies": false, "dependencies": {"abort-controller": "^3.0.0", "eventsource": "^1.0.7", "fetch-cookie": "^0.11.0", "node-fetch": "^2.6.7", "ws": "^7.4.5"}, "deprecated": false, "description": "ASP.NET Core SignalR Client", "devDependencies": {"@types/eventsource": "^1.1.5", "@types/jest": "^26.0.20", "@types/node": "^14.14.31", "@types/tough-cookie": "^4.0.0", "process": "^0.11.10"}, "directories": {"test": "spec"}, "files": ["dist/**/*", "src/**/*"], "homepage": "https://github.com/dotnet/aspnetcore/tree/main/src/SignalR#readme", "keywords": ["signalr", "aspnetcore"], "license": "MIT", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "name": "@microsoft/signalr", "repository": {"type": "git", "url": "git+https://github.com/dotnet/aspnetcore.git"}, "resolutions": {"ansi-regex": "5.0.1"}, "scripts": {"build": "yarn run build:lint && yarn run build:esm && yarn run build:cjs && yarn run build:browser && yarn run build:webworker", "build:browser": "node ../common/node_modules/webpack-cli/bin/cli.js", "build:cjs": "node ../common/node_modules/typescript/bin/tsc --project ./tsconfig.json --module commonjs --outDir ./dist/cjs", "build:esm": "node ../common/node_modules/typescript/bin/tsc --project ./tsconfig.json --module es2015 --outDir ./dist/esm -d && node ./build/process-dts.js", "build:lint": "node ../common/node_modules/eslint/bin/eslint ./src --ext .ts --resolve-plugins-relative-to ../common", "build:webworker": "node ../common/node_modules/webpack-cli/bin/cli.js --env platform=webworker", "clean": "node ../common/node_modules/rimraf/bin.js ./dist", "prebuild": "yarn run clean && yarn install --mutex network --frozen-lockfile", "preclean": "cd ../common && yarn install --mutex network --frozen-lockfile", "prepack": "node ../build/embed-version.js", "test": "echo \"Run 'yarn test' in the 'clients/ts' folder to test this package\" && exit 1"}, "sideEffects": false, "typings": "./dist/esm/index.d.ts", "umd": "./dist/browser/signalr.js", "umd_name": "signalR", "unpkg": "./dist/browser/signalr.js", "version": "6.0.9"}