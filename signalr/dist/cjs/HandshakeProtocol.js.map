{"version": 3, "file": "HandshakeProtocol.js", "sourceRoot": "", "sources": ["../../src/HandshakeProtocol.ts"], "names": [], "mappings": ";AAAA,gEAAgE;AAChE,uEAAuE;;;AAEvE,2DAAwD;AACxD,mCAAwC;AAcxC,eAAe;AACf,MAAa,iBAAiB;IAC1B,mCAAmC;IAC5B,qBAAqB,CAAC,gBAAyC;QAClE,OAAO,qCAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACrE,CAAC;IAEM,sBAAsB,CAAC,IAAS;QACnC,IAAI,WAAmB,CAAC;QACxB,IAAI,aAAkB,CAAC;QAEvB,IAAI,qBAAa,CAAC,IAAI,CAAC,EAAE;YACrB,4EAA4E;YAC5E,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,qCAAiB,CAAC,mBAAmB,CAAC,CAAC;YACjF,IAAI,cAAc,KAAK,CAAC,CAAC,EAAE;gBACvB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;aAC7C;YAED,iDAAiD;YACjD,gDAAgD;YAChD,MAAM,cAAc,GAAG,cAAc,GAAG,CAAC,CAAC;YAC1C,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC;YAC/G,aAAa,GAAG,CAAC,UAAU,CAAC,UAAU,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;SAC7G;aAAM;YACH,MAAM,QAAQ,GAAW,IAAI,CAAC;YAC9B,MAAM,cAAc,GAAG,QAAQ,CAAC,OAAO,CAAC,qCAAiB,CAAC,eAAe,CAAC,CAAC;YAC3E,IAAI,cAAc,KAAK,CAAC,CAAC,EAAE;gBACvB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;aAC7C;YAED,iDAAiD;YACjD,gDAAgD;YAChD,MAAM,cAAc,GAAG,cAAc,GAAG,CAAC,CAAC;YAC1C,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;YACpD,aAAa,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;SAClG;QAED,iEAAiE;QACjE,MAAM,QAAQ,GAAG,qCAAiB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QACtD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,IAAI,QAAQ,CAAC,IAAI,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;SACrE;QACD,MAAM,eAAe,GAA6B,QAAQ,CAAC;QAE3D,sDAAsD;QACtD,sEAAsE;QACtE,OAAO,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;IAC5C,CAAC;CACJ;AAjDD,8CAiDC", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { TextMessageFormat } from \"./TextMessageFormat\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\n\r\n/** @private */\r\nexport interface HandshakeRequestMessage {\r\n    readonly protocol: string;\r\n    readonly version: number;\r\n}\r\n\r\n/** @private */\r\nexport interface HandshakeResponseMessage {\r\n    readonly error: string;\r\n    readonly minorVersion: number;\r\n}\r\n\r\n/** @private */\r\nexport class HandshakeProtocol {\r\n    // Handshake request is always JSON\r\n    public writeHandshakeRequest(handshakeRequest: HandshakeRequestMessage): string {\r\n        return TextMessageFormat.write(JSON.stringify(handshakeRequest));\r\n    }\r\n\r\n    public parseHandshakeResponse(data: any): [any, HandshakeResponseMessage] {\r\n        let messageData: string;\r\n        let remainingData: any;\r\n\r\n        if (isArrayBuffer(data)) {\r\n            // Format is binary but still need to read JSON text from handshake response\r\n            const binaryData = new Uint8Array(data);\r\n            const separatorIndex = binaryData.indexOf(TextMessageFormat.RecordSeparatorCode);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            const responseLength = separatorIndex + 1;\r\n            messageData = String.fromCharCode.apply(null, Array.prototype.slice.call(binaryData.slice(0, responseLength)));\r\n            remainingData = (binaryData.byteLength > responseLength) ? binaryData.slice(responseLength).buffer : null;\r\n        } else {\r\n            const textData: string = data;\r\n            const separatorIndex = textData.indexOf(TextMessageFormat.RecordSeparator);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            const responseLength = separatorIndex + 1;\r\n            messageData = textData.substring(0, responseLength);\r\n            remainingData = (textData.length > responseLength) ? textData.substring(responseLength) : null;\r\n        }\r\n\r\n        // At this point we should have just the single handshake message\r\n        const messages = TextMessageFormat.parse(messageData);\r\n        const response = JSON.parse(messages[0]);\r\n        if (response.type) {\r\n            throw new Error(\"Expected a handshake response from the server.\");\r\n        }\r\n        const responseMessage: HandshakeResponseMessage = response;\r\n\r\n        // multiple messages could have arrived with handshake\r\n        // return additional data to be parsed as usual, or null if all parsed\r\n        return [remainingData, responseMessage];\r\n    }\r\n}\r\n"]}