{"version": 3, "file": "DefaultReconnectPolicy.js", "sourceRoot": "", "sources": ["../../src/DefaultReconnectPolicy.ts"], "names": [], "mappings": ";AAAA,gEAAgE;AAChE,uEAAuE;;;AAIvE,wDAAwD;AACxD,MAAM,oCAAoC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AAE3E,eAAe;AACf,MAAa,sBAAsB;IAG/B,YAAY,WAAsB;QAC9B,IAAI,CAAC,YAAY,GAAG,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,oCAAoC,CAAC;IAClH,CAAC;IAEM,4BAA4B,CAAC,YAA0B;QAC1D,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;IAC9D,CAAC;CACJ;AAVD,wDAUC", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { IRetryPolicy, RetryContext } from \"./IRetryPolicy\";\r\n\r\n// 0, 2, 10, 30 second delays before reconnect attempts.\r\nconst DEFAULT_RETRY_DELAYS_IN_MILLISECONDS = [0, 2000, 10000, 30000, null];\r\n\r\n/** @private */\r\nexport class DefaultReconnectPolicy implements IRetryPolicy {\r\n    private readonly _retryDelays: (number | null)[];\r\n\r\n    constructor(retryDelays?: number[]) {\r\n        this._retryDelays = retryDelays !== undefined ? [...retryDelays, null] : DEFAULT_RETRY_DELAYS_IN_MILLISECONDS;\r\n    }\r\n\r\n    public nextRetryDelayInMilliseconds(retryContext: RetryContext): number | null {\r\n        return this._retryDelays[retryContext.previousRetryCount];\r\n    }\r\n}\r\n"]}