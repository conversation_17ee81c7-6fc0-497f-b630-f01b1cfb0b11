{"version": 3, "file": "FetchHttpClient.js", "sourceRoot": "", "sources": ["../../src/FetchHttpClient.ts"], "names": [], "mappings": ";AAAA,gEAAgE;AAChE,uEAAuE;;;AAKvE,qCAA+D;AAC/D,6CAAqE;AACrE,uCAA8C;AAC9C,mCAAkD;AAElD,MAAa,eAAgB,SAAQ,uBAAU;IAO3C,YAAmB,MAAe;QAC9B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;YAC9B,oFAAoF;YACpF,gDAAgD;YAChD,MAAM,WAAW,GAAG,OAAO,mBAAmB,KAAK,UAAU,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,OAAO,CAAC;YAElG,iHAAiH;YACjH,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;YAC1D,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC;YAE5C,qEAAqE;YACrE,2FAA2F;YAC3F,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;SAC7E;aAAM;YACH,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,qBAAa,EAAE,CAAC,CAAC;SACjD;QACD,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE;YACxC,oFAAoF;YACpF,gDAAgD;YAChD,MAAM,WAAW,GAAG,OAAO,mBAAmB,KAAK,UAAU,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,OAAO,CAAC;YAElG,gGAAgG;YAChG,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC,kBAAkB,CAAC,CAAC;SAC/D;aAAM;YACH,IAAI,CAAC,oBAAoB,GAAG,eAAe,CAAC;SAC/C;IACL,CAAC;IAED,kBAAkB;IACX,KAAK,CAAC,IAAI,CAAC,OAAoB;QAClC,wDAAwD;QACxD,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE;YACpD,MAAM,IAAI,mBAAU,EAAE,CAAC;SAC1B;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;SACzC;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;SACtC;QAED,MAAM,eAAe,GAAG,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAExD,IAAI,KAAU,CAAC;QACf,iDAAiD;QACjD,IAAI,OAAO,CAAC,WAAW,EAAE;YACrB,OAAO,CAAC,WAAW,CAAC,OAAO,GAAG,GAAG,EAAE;gBAC/B,eAAe,CAAC,KAAK,EAAE,CAAC;gBACxB,KAAK,GAAG,IAAI,mBAAU,EAAE,CAAC;YAC7B,CAAC,CAAC;SACL;QAED,iEAAiE;QACjE,sEAAsE;QACtE,IAAI,SAAS,GAAQ,IAAI,CAAC;QAC1B,IAAI,OAAO,CAAC,OAAO,EAAE;YACjB,MAAM,SAAS,GAAG,OAAO,CAAC,OAAQ,CAAC;YACnC,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBACxB,eAAe,CAAC,KAAK,EAAE,CAAC;gBACxB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,OAAO,EAAE,4BAA4B,CAAC,CAAC;gBACjE,KAAK,GAAG,IAAI,qBAAY,EAAE,CAAC;YAC/B,CAAC,EAAE,SAAS,CAAC,CAAC;SACjB;QAED,IAAI,QAAkB,CAAC;QACvB,IAAI;YACA,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAI,EAAE;gBAC3C,IAAI,EAAE,OAAO,CAAC,OAAQ;gBACtB,KAAK,EAAE,UAAU;gBACjB,WAAW,EAAE,OAAO,CAAC,eAAe,KAAK,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa;gBACzE,OAAO,EAAE;oBACL,cAAc,EAAE,0BAA0B;oBAC1C,kBAAkB,EAAE,gBAAgB;oBACpC,GAAG,OAAO,CAAC,OAAO;iBACrB;gBACD,MAAM,EAAE,OAAO,CAAC,MAAO;gBACvB,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,QAAQ;gBAClB,MAAM,EAAE,eAAe,CAAC,MAAM;aACjC,CAAC,CAAC;SACN;QAAC,OAAO,CAAC,EAAE;YACR,IAAI,KAAK,EAAE;gBACP,MAAM,KAAK,CAAC;aACf;YACD,IAAI,CAAC,OAAO,CAAC,GAAG,CACZ,kBAAQ,CAAC,OAAO,EAChB,4BAA4B,CAAC,GAAG,CACnC,CAAC;YACF,MAAM,CAAC,CAAC;SACX;gBAAS;YACN,IAAI,SAAS,EAAE;gBACX,YAAY,CAAC,SAAS,CAAC,CAAC;aAC3B;YACD,IAAI,OAAO,CAAC,WAAW,EAAE;gBACrB,OAAO,CAAC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC;aACtC;SACJ;QAED,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;YACd,MAAM,YAAY,GAAG,MAAM,kBAAkB,CAAC,QAAQ,EAAE,MAAM,CAAW,CAAC;YAC1E,MAAM,IAAI,kBAAS,CAAC,YAAY,IAAI,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;SAC7E;QAED,MAAM,OAAO,GAAG,kBAAkB,CAAC,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;QACnE,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC;QAE9B,OAAO,IAAI,yBAAY,CACnB,QAAQ,CAAC,MAAM,EACf,QAAQ,CAAC,UAAU,EACnB,OAAO,CACV,CAAC;IACN,CAAC;IAEM,eAAe,CAAC,GAAW;QAC9B,IAAI,OAAO,GAAW,EAAE,CAAC;QACzB,IAAI,gBAAQ,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE;YAC9B,8BAA8B;YAC9B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SAC/D;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;CACJ;AApID,0CAoIC;AAED,SAAS,kBAAkB,CAAC,QAAkB,EAAE,YAAyC;IACrF,IAAI,OAAO,CAAC;IACZ,QAAQ,YAAY,EAAE;QAClB,KAAK,aAAa;YACd,OAAO,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM;QACV,KAAK,MAAM;YACP,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC1B,MAAM;QACV,KAAK,MAAM,CAAC;QACZ,KAAK,UAAU,CAAC;QAChB,KAAK,MAAM;YACP,MAAM,IAAI,KAAK,CAAC,GAAG,YAAY,oBAAoB,CAAC,CAAC;QACzD;YACI,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC1B,MAAM;KACb;IAED,OAAO,OAAO,CAAC;AACnB,CAAC", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// @ts-ignore: This will be removed from built files and is here to make the types available during dev work\r\nimport { CookieJar } from \"@types/tough-cookie\";\r\n\r\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { Platform, getGlobalThis } from \"./Utils\";\r\n\r\nexport class FetchHttpClient extends HttpClient {\r\n    private readonly _abortControllerType: { prototype: AbortController, new(): AbortController };\r\n    private readonly _fetchType: (input: RequestInfo, init?: RequestInit) => Promise<Response>;\r\n    private readonly _jar?: CookieJar;\r\n\r\n    private readonly _logger: ILogger;\r\n\r\n    public constructor(logger: ILogger) {\r\n        super();\r\n        this._logger = logger;\r\n\r\n        if (typeof fetch === \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: T<PERSON> doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n\r\n            // Cookies aren't automatically handled in Node so we need to add a CookieJar to preserve cookies across requests\r\n            this._jar = new (requireFunc(\"tough-cookie\")).CookieJar();\r\n            this._fetchType = requireFunc(\"node-fetch\");\r\n\r\n            // node-fetch doesn't have a nice API for getting and setting cookies\r\n            // fetch-cookie will wrap a fetch implementation with a default CookieJar or a provided one\r\n            this._fetchType = requireFunc(\"fetch-cookie\")(this._fetchType, this._jar);\r\n        } else {\r\n            this._fetchType = fetch.bind(getGlobalThis());\r\n        }\r\n        if (typeof AbortController === \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n\r\n            // Node needs EventListener methods on AbortController which our custom polyfill doesn't provide\r\n            this._abortControllerType = requireFunc(\"abort-controller\");\r\n        } else {\r\n            this._abortControllerType = AbortController;\r\n        }\r\n    }\r\n\r\n    /** @inheritDoc */\r\n    public async send(request: HttpRequest): Promise<HttpResponse> {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            throw new AbortError();\r\n        }\r\n\r\n        if (!request.method) {\r\n            throw new Error(\"No method defined.\");\r\n        }\r\n        if (!request.url) {\r\n            throw new Error(\"No url defined.\");\r\n        }\r\n\r\n        const abortController = new this._abortControllerType();\r\n\r\n        let error: any;\r\n        // Hook our abortSignal into the abort controller\r\n        if (request.abortSignal) {\r\n            request.abortSignal.onabort = () => {\r\n                abortController.abort();\r\n                error = new AbortError();\r\n            };\r\n        }\r\n\r\n        // If a timeout has been passed in, setup a timeout to call abort\r\n        // Type needs to be any to fit window.setTimeout and NodeJS.setTimeout\r\n        let timeoutId: any = null;\r\n        if (request.timeout) {\r\n            const msTimeout = request.timeout!;\r\n            timeoutId = setTimeout(() => {\r\n                abortController.abort();\r\n                this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\r\n                error = new TimeoutError();\r\n            }, msTimeout);\r\n        }\r\n\r\n        let response: Response;\r\n        try {\r\n            response = await this._fetchType(request.url!, {\r\n                body: request.content!,\r\n                cache: \"no-cache\",\r\n                credentials: request.withCredentials === true ? \"include\" : \"same-origin\",\r\n                headers: {\r\n                    \"Content-Type\": \"text/plain;charset=UTF-8\",\r\n                    \"X-Requested-With\": \"XMLHttpRequest\",\r\n                    ...request.headers,\r\n                },\r\n                method: request.method!,\r\n                mode: \"cors\",\r\n                redirect: \"follow\",\r\n                signal: abortController.signal,\r\n            });\r\n        } catch (e) {\r\n            if (error) {\r\n                throw error;\r\n            }\r\n            this._logger.log(\r\n                LogLevel.Warning,\r\n                `Error from HTTP request. ${e}.`,\r\n            );\r\n            throw e;\r\n        } finally {\r\n            if (timeoutId) {\r\n                clearTimeout(timeoutId);\r\n            }\r\n            if (request.abortSignal) {\r\n                request.abortSignal.onabort = null;\r\n            }\r\n        }\r\n\r\n        if (!response.ok) {\r\n            const errorMessage = await deserializeContent(response, \"text\") as string;\r\n            throw new HttpError(errorMessage || response.statusText, response.status);\r\n        }\r\n\r\n        const content = deserializeContent(response, request.responseType);\r\n        const payload = await content;\r\n\r\n        return new HttpResponse(\r\n            response.status,\r\n            response.statusText,\r\n            payload,\r\n        );\r\n    }\r\n\r\n    public getCookieString(url: string): string {\r\n        let cookies: string = \"\";\r\n        if (Platform.isNode && this._jar) {\r\n            // @ts-ignore: unused variable\r\n            this._jar.getCookies(url, (e, c) => cookies = c.join(\"; \"));\r\n        }\r\n        return cookies;\r\n    }\r\n}\r\n\r\nfunction deserializeContent(response: Response, responseType?: XMLHttpRequestResponseType): Promise<string | ArrayBuffer> {\r\n    let content;\r\n    switch (responseType) {\r\n        case \"arraybuffer\":\r\n            content = response.arrayBuffer();\r\n            break;\r\n        case \"text\":\r\n            content = response.text();\r\n            break;\r\n        case \"blob\":\r\n        case \"document\":\r\n        case \"json\":\r\n            throw new Error(`${responseType} is not supported.`);\r\n        default:\r\n            content = response.text();\r\n            break;\r\n    }\r\n\r\n    return content;\r\n}\r\n"]}