{"version": 3, "file": "ServerSentEventsTransport.js", "sourceRoot": "", "sources": ["../../src/ServerSentEventsTransport.ts"], "names": [], "mappings": ";AAAA,gEAAgE;AAChE,uEAAuE;;;AAIvE,uCAA8C;AAC9C,6CAA0D;AAC1D,mCAAwF;AAGxF,eAAe;AACf,MAAa,yBAAyB;IAWlC,YAAY,UAAsB,EAAE,kBAAgE,EAAE,MAAe,EACzG,OAA+B;QACvC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAC9C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,GAAW,EAAE,cAA8B;QAC5D,WAAG,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC3B,WAAG,CAAC,UAAU,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;QACjD,WAAG,CAAC,IAAI,CAAC,cAAc,EAAE,2BAAc,EAAE,gBAAgB,CAAC,CAAC;QAE3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,6BAA6B,CAAC,CAAC;QAEhE,sIAAsI;QACtI,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAEhB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC/C,IAAI,KAAK,EAAE;gBACP,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,gBAAgB,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;aAC3F;SACJ;QAED,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACzC,IAAI,MAAM,GAAG,KAAK,CAAC;YACnB,IAAI,cAAc,KAAK,2BAAc,CAAC,IAAI,EAAE;gBACxC,MAAM,CAAC,IAAI,KAAK,CAAC,2EAA2E,CAAC,CAAC,CAAC;gBAC/F,OAAO;aACV;YAED,IAAI,WAAwB,CAAC;YAC7B,IAAI,gBAAQ,CAAC,SAAS,IAAI,gBAAQ,CAAC,WAAW,EAAE;gBAC5C,WAAW,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAY,CAAC,GAAG,EAAE,EAAE,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,CAAC;aACzG;iBAAM;gBACH,gDAAgD;gBAChD,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;gBACtD,MAAM,OAAO,GAAmB,EAAE,CAAC;gBACnC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC;gBACzB,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,0BAAkB,EAAE,CAAC;gBAC3C,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;gBAEtB,WAAW,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAY,CAAC,GAAG,EAAE,EAAE,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAC,EAAqB,CAAC,CAAC;aAC9K;YAED,IAAI;gBACA,WAAW,CAAC,SAAS,GAAG,CAAC,CAAe,EAAE,EAAE;oBACxC,IAAI,IAAI,CAAC,SAAS,EAAE;wBAChB,IAAI;4BACA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,KAAK,EAAE,kCAAkC,qBAAa,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,iBAAkB,CAAC,GAAG,CAAC,CAAC;4BAC/H,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;yBAC1B;wBAAC,OAAO,KAAK,EAAE;4BACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;4BACnB,OAAO;yBACV;qBACJ;gBACL,CAAC,CAAC;gBAEF,yCAAyC;gBACzC,WAAW,CAAC,OAAO,GAAG,CAAC,CAAQ,EAAE,EAAE;oBAC/B,4EAA4E;oBAC5E,IAAI,MAAM,EAAE;wBACR,IAAI,CAAC,MAAM,EAAE,CAAC;qBACjB;yBAAM;wBACH,MAAM,CAAC,IAAI,KAAK,CAAC,iFAAiF;8BAChG,0GAA0G;8BAC1G,uEAAuE,CAAC,CAAC,CAAC;qBAC/E;gBACL,CAAC,CAAC;gBAEF,WAAW,CAAC,MAAM,GAAG,GAAG,EAAE;oBACtB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAQ,CAAC,WAAW,EAAE,oBAAoB,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;oBACxE,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;oBAChC,MAAM,GAAG,IAAI,CAAC;oBACd,OAAO,EAAE,CAAC;gBACd,CAAC,CAAC;aACL;YAAC,OAAO,CAAC,EAAE;gBACR,MAAM,CAAC,CAAC,CAAC,CAAC;gBACV,OAAO;aACV;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,IAAS;QACvB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC,CAAC;SACpF;QACD,OAAO,mBAAW,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAK,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACzH,CAAC;IAEM,IAAI;QACP,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAEO,MAAM,CAAC,CAAS;QACpB,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;YAC1B,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;YAE9B,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;aACnB;SACJ;IACL,CAAC;CACJ;AAxHD,8DAwHC", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { MessageHeaders } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { ITransport, TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, Platform, sendMessage } from \"./Utils\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\n\r\n/** @private */\r\nexport class ServerSentEventsTransport implements ITransport {\r\n    private readonly _httpClient: HttpClient;\r\n    private readonly _accessTokenFactory: (() => string | Promise<string>) | undefined;\r\n    private readonly _logger: ILogger;\r\n    private readonly _options: IHttpConnectionOptions;\r\n    private _eventSource?: EventSource;\r\n    private _url?: string;\r\n\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((error?: Error) => void) | null;\r\n\r\n    constructor(httpClient: HttpClient, accessTokenFactory: (() => string | Promise<string>) | undefined, logger: ILogger,\r\n                options: IHttpConnectionOptions) {\r\n        this._httpClient = httpClient;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n        this._logger = logger;\r\n        this._options = options;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n\r\n    public async connect(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n\r\n        this._logger.log(LogLevel.Trace, \"(SSE transport) Connecting.\");\r\n\r\n        // set url before accessTokenFactory because this.url is only for send and we set the auth header instead of the query string for send\r\n        this._url = url;\r\n\r\n        if (this._accessTokenFactory) {\r\n            const token = await this._accessTokenFactory();\r\n            if (token) {\r\n                url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(token)}`;\r\n            }\r\n        }\r\n\r\n        return new Promise<void>((resolve, reject) => {\r\n            let opened = false;\r\n            if (transferFormat !== TransferFormat.Text) {\r\n                reject(new Error(\"The Server-Sent Events transport only supports the 'Text' transfer format\"));\r\n                return;\r\n            }\r\n\r\n            let eventSource: EventSource;\r\n            if (Platform.isBrowser || Platform.isWebWorker) {\r\n                eventSource = new this._options.EventSource!(url, { withCredentials: this._options.withCredentials });\r\n            } else {\r\n                // Non-browser passes cookies via the dictionary\r\n                const cookies = this._httpClient.getCookieString(url);\r\n                const headers: MessageHeaders = {};\r\n                headers.Cookie = cookies;\r\n                const [name, value] = getUserAgentHeader();\r\n                headers[name] = value;\r\n\r\n                eventSource = new this._options.EventSource!(url, { withCredentials: this._options.withCredentials, headers: { ...headers, ...this._options.headers} } as EventSourceInit);\r\n            }\r\n\r\n            try {\r\n                eventSource.onmessage = (e: MessageEvent) => {\r\n                    if (this.onreceive) {\r\n                        try {\r\n                            this._logger.log(LogLevel.Trace, `(SSE transport) data received. ${getDataDetail(e.data, this._options.logMessageContent!)}.`);\r\n                            this.onreceive(e.data);\r\n                        } catch (error) {\r\n                            this._close(error);\r\n                            return;\r\n                        }\r\n                    }\r\n                };\r\n\r\n                // @ts-ignore: not using event on purpose\r\n                eventSource.onerror = (e: Event) => {\r\n                    // EventSource doesn't give any useful information about server side closes.\r\n                    if (opened) {\r\n                        this._close();\r\n                    } else {\r\n                        reject(new Error(\"EventSource failed to connect. The connection could not be found on the server,\"\r\n                        + \" either the connection ID is not present on the server, or a proxy is refusing/buffering the connection.\"\r\n                        + \" If you have multiple servers check that sticky sessions are enabled.\"));\r\n                    }\r\n                };\r\n\r\n                eventSource.onopen = () => {\r\n                    this._logger.log(LogLevel.Information, `SSE connected to ${this._url}`);\r\n                    this._eventSource = eventSource;\r\n                    opened = true;\r\n                    resolve();\r\n                };\r\n            } catch (e) {\r\n                reject(e);\r\n                return;\r\n            }\r\n        });\r\n    }\r\n\r\n    public async send(data: any): Promise<void> {\r\n        if (!this._eventSource) {\r\n            return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\r\n        }\r\n        return sendMessage(this._logger, \"SSE\", this._httpClient, this._url!, this._accessTokenFactory, data, this._options);\r\n    }\r\n\r\n    public stop(): Promise<void> {\r\n        this._close();\r\n        return Promise.resolve();\r\n    }\r\n\r\n    private _close(e?: Error) {\r\n        if (this._eventSource) {\r\n            this._eventSource.close();\r\n            this._eventSource = undefined;\r\n\r\n            if (this.onclose) {\r\n                this.onclose(e);\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}