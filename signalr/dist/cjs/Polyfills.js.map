{"version": 3, "file": "Polyfills.js", "sourceRoot": "", "sources": ["../../src/Polyfills.ts"], "names": [], "mappings": ";AAAA,gEAAgE;AAChE,uEAAuE", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Not exported from index\r\n\r\n/** @private */\r\nexport type EventSourceConstructor = new(url: string, eventSourceInitDict?: EventSourceInit) => EventSource;\r\n\r\n/** @private */\r\nexport interface WebSocketConstructor {\r\n    new(url: string, protocols?: string | string[], options?: any): WebSocket;\r\n    readonly CLOSED: number;\r\n    readonly CLOSING: number;\r\n    readonly CONNECTING: number;\r\n    readonly OPEN: number;\r\n}\r\n"]}