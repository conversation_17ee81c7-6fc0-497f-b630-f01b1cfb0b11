{"version": 3, "file": "TextMessageFormat.js", "sourceRoot": "", "sources": ["../../src/TextMessageFormat.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,uEAAuE;AAEvE,0BAA0B;AAC1B,eAAe;AACf,MAAM,OAAO,iBAAiB;IAInB,MAAM,CAAC,KAAK,CAAC,MAAc;QAC9B,OAAO,GAAG,MAAM,GAAG,iBAAiB,CAAC,eAAe,EAAE,CAAC;IAC3D,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,KAAa;QAC7B,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,iBAAiB,CAAC,eAAe,EAAE;YAC/D,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;SAC7C;QAED,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;QAChE,QAAQ,CAAC,GAAG,EAAE,CAAC;QACf,OAAO,QAAQ,CAAC;IACpB,CAAC;;AAfa,qCAAmB,GAAG,IAAI,CAAC;AAC3B,iCAAe,GAAG,MAAM,CAAC,YAAY,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,CAAC", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Not exported from index\r\n/** @private */\r\nexport class TextMessageFormat {\r\n    public static RecordSeparatorCode = 0x1e;\r\n    public static RecordSeparator = String.fromCharCode(TextMessageFormat.RecordSeparatorCode);\r\n\r\n    public static write(output: string): string {\r\n        return `${output}${TextMessageFormat.RecordSeparator}`;\r\n    }\r\n\r\n    public static parse(input: string): string[] {\r\n        if (input[input.length - 1] !== TextMessageFormat.RecordSeparator) {\r\n            throw new Error(\"Message is incomplete.\");\r\n        }\r\n\r\n        const messages = input.split(TextMessageFormat.RecordSeparator);\r\n        messages.pop();\r\n        return messages;\r\n    }\r\n}\r\n"]}