{"version": 3, "file": "DefaultHttpClient.js", "sourceRoot": "", "sources": ["../../src/DefaultHttpClient.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,uEAAuE;AAEvE,OAAO,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AACtC,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,UAAU,EAA6B,MAAM,cAAc,CAAC;AAErE,OAAO,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AACnC,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,uEAAuE;AACvE,MAAM,OAAO,iBAAkB,SAAQ,UAAU;IAG7C,yJAAyJ;IACzJ,YAAmB,MAAe;QAC9B,KAAK,EAAE,CAAC;QAER,IAAI,OAAO,KAAK,KAAK,WAAW,IAAI,QAAQ,CAAC,MAAM,EAAE;YACjD,IAAI,CAAC,WAAW,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;SAClD;aAAM,IAAI,OAAO,cAAc,KAAK,WAAW,EAAE;YAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;SAChD;aAAM;YACH,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAClD;IACL,CAAC;IAED,kBAAkB;IACX,IAAI,CAAC,OAAoB;QAC5B,wDAAwD;QACxD,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE;YACpD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,UAAU,EAAE,CAAC,CAAC;SAC3C;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACjB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;SAC1D;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YACd,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;SACvD;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAEM,eAAe,CAAC,GAAW;QAC9B,OAAO,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;IACjD,CAAC;CACJ", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortError } from \"./Errors\";\r\nimport { FetchHttpClient } from \"./FetchHttpClient\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nimport { ILogger } from \"./ILogger\";\r\nimport { Platform } from \"./Utils\";\r\nimport { XhrHttpClient } from \"./XhrHttpClient\";\r\n\r\n/** Default implementation of {@link @microsoft/signalr.HttpClient}. */\r\nexport class DefaultHttpClient extends HttpClient {\r\n    private readonly _httpClient: HttpClient;\r\n\r\n    /** Creates a new instance of the {@link @microsoft/signalr.DefaultHttpClient}, using the provided {@link @microsoft/signalr.ILogger} to log messages. */\r\n    public constructor(logger: ILogger) {\r\n        super();\r\n\r\n        if (typeof fetch !== \"undefined\" || Platform.isNode) {\r\n            this._httpClient = new FetchHttpClient(logger);\r\n        } else if (typeof XMLHttpRequest !== \"undefined\") {\r\n            this._httpClient = new XhrHttpClient(logger);\r\n        } else {\r\n            throw new Error(\"No usable HttpClient found.\");\r\n        }\r\n    }\r\n\r\n    /** @inheritDoc */\r\n    public send(request: HttpRequest): Promise<HttpResponse> {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            return Promise.reject(new AbortError());\r\n        }\r\n\r\n        if (!request.method) {\r\n            return Promise.reject(new Error(\"No method defined.\"));\r\n        }\r\n        if (!request.url) {\r\n            return Promise.reject(new Error(\"No url defined.\"));\r\n        }\r\n\r\n        return this._httpClient.send(request);\r\n    }\r\n\r\n    public getCookieString(url: string): string {\r\n        return this._httpClient.getCookieString(url);\r\n    }\r\n}\r\n"]}