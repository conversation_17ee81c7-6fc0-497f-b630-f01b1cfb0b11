{"version": 3, "file": "Stream.js", "sourceRoot": "", "sources": ["../../src/Stream.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,uEAAuE", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// This is an API that is similar to Observable, but we don't want users to confuse it for that so we rename things. Someone could\r\n// easily adapt it into the Rx interface if they wanted to. Unlike in C#, we can't just implement an \"interface\" and get extension\r\n// methods for free. The methods have to actually be added to the object (there are no extension methods in JS!). We don't want to\r\n// depend on RxJS in the core library, so instead we duplicate the minimum logic needed and then users can easily adapt these into\r\n// proper RxJS observables if they want.\r\n\r\n/** Defines the expected type for a receiver of results streamed by the server.\r\n *\r\n * @typeparam T The type of the items being sent by the server.\r\n */\r\nexport interface IStreamSubscriber<T> {\r\n    /** A boolean that will be set by the {@link @microsoft/signalr.IStreamResult} when the stream is closed. */\r\n    closed?: boolean;\r\n    /** Called by the framework when a new item is available. */\r\n    next(value: T): void;\r\n    /** Called by the framework when an error has occurred.\r\n     *\r\n     * After this method is called, no additional methods on the {@link @microsoft/signalr.IStreamSubscriber} will be called.\r\n     */\r\n    error(err: any): void;\r\n    /** Called by the framework when the end of the stream is reached.\r\n     *\r\n     * After this method is called, no additional methods on the {@link @microsoft/signalr.IStreamSubscriber} will be called.\r\n     */\r\n    complete(): void;\r\n}\r\n\r\n/** Defines the result of a streaming hub method.\r\n *\r\n * @typeparam T The type of the items being sent by the server.\r\n */\r\nexport interface IStreamResult<T> {\r\n    /** Attaches a {@link @microsoft/signalr.IStreamSubscriber}, which will be invoked when new items are available from the stream.\r\n     *\r\n     * @param {IStreamSubscriber<T>} observer The subscriber to attach.\r\n     * @returns {ISubscription<T>} A subscription that can be disposed to terminate the stream and stop calling methods on the {@link @microsoft/signalr.IStreamSubscriber}.\r\n     */\r\n    subscribe(subscriber: IStreamSubscriber<T>): ISubscription<T>;\r\n}\r\n\r\n/** An interface that allows an {@link @microsoft/signalr.IStreamSubscriber} to be disconnected from a stream.\r\n *\r\n * @typeparam T The type of the items being sent by the server.\r\n */\r\n// @ts-ignore: We can't remove this, it's a breaking change, but it's not used.\r\nexport interface ISubscription<T> {\r\n    /** Disconnects the {@link @microsoft/signalr.IStreamSubscriber} associated with this subscription from the stream. */\r\n    dispose(): void;\r\n}\r\n"]}