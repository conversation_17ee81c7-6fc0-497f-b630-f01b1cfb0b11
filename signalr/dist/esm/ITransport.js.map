{"version": 3, "file": "ITransport.js", "sourceRoot": "", "sources": ["../../src/ITransport.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,uEAAuE;AAEvE,6FAA6F;AAC7F,gDAAgD;AAChD,MAAM,CAAN,IAAY,iBASX;AATD,WAAY,iBAAiB;IACzB,yCAAyC;IACzC,yDAAQ,CAAA;IACR,0CAA0C;IAC1C,qEAAc,CAAA;IACd,kDAAkD;IAClD,iFAAoB,CAAA;IACpB,4CAA4C;IAC5C,uEAAe,CAAA;AACnB,CAAC,EATW,iBAAiB,KAAjB,iBAAiB,QAS5B;AAED,sDAAsD;AACtD,MAAM,CAAN,IAAY,cAKX;AALD,WAAY,cAAc;IACtB,6EAA6E;IAC7E,mDAAQ,CAAA;IACR,0EAA0E;IAC1E,uDAAU,CAAA;AACd,CAAC,EALW,cAAc,KAAd,cAAc,QAKzB", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// This will be treated as a bit flag in the future, so we keep it using power-of-two values.\r\n/** Specifies a specific HTTP transport type. */\r\nexport enum HttpTransportType {\r\n    /** Specifies no transport preference. */\r\n    None = 0,\r\n    /** Specifies the WebSockets transport. */\r\n    WebSockets = 1,\r\n    /** Specifies the Server-Sent Events transport. */\r\n    ServerSentEvents = 2,\r\n    /** Specifies the Long Polling transport. */\r\n    LongPolling = 4,\r\n}\r\n\r\n/** Specifies the transfer format for a connection. */\r\nexport enum TransferFormat {\r\n    /** Specifies that only text data will be transmitted over the connection. */\r\n    Text = 1,\r\n    /** Specifies that binary data will be transmitted over the connection. */\r\n    Binary = 2,\r\n}\r\n\r\n/** An abstraction over the behavior of transports. This is designed to support the framework and not intended for use by applications. */\r\nexport interface ITransport {\r\n    connect(url: string, transferFormat: TransferFormat): Promise<void>;\r\n    send(data: any): Promise<void>;\r\n    stop(): Promise<void>;\r\n    onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    onclose: ((error?: Error) => void) | null;\r\n}\r\n"]}