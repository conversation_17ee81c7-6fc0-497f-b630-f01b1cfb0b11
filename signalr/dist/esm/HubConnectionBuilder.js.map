{"version": 3, "file": "HubConnectionBuilder.js", "sourceRoot": "", "sources": ["../../src/HubConnectionBuilder.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,uEAAuE;AAEvE,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC;AAClE,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAGhD,OAAO,EAAW,QAAQ,EAAE,MAAM,WAAW,CAAC;AAG9C,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AACvC,OAAO,EAAE,GAAG,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAE7C,MAAM,mBAAmB,GAA4B;IACjD,KAAK,EAAE,QAAQ,CAAC,KAAK;IACrB,KAAK,EAAE,QAAQ,CAAC,KAAK;IACrB,IAAI,EAAE,QAAQ,CAAC,WAAW;IAC1B,WAAW,EAAE,QAAQ,CAAC,WAAW;IACjC,IAAI,EAAE,QAAQ,CAAC,OAAO;IACtB,OAAO,EAAE,QAAQ,CAAC,OAAO;IACzB,KAAK,EAAE,QAAQ,CAAC,KAAK;IACrB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;IAC3B,IAAI,EAAE,QAAQ,CAAC,IAAI;CACtB,CAAC;AAEF,SAAS,aAAa,CAAC,IAAY;IAC/B,6CAA6C;IAC7C,oFAAoF;IACpF,iFAAiF;IACjF,MAAM,OAAO,GAAG,mBAAmB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;IACxD,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;QAChC,OAAO,OAAO,CAAC;KAClB;SAAM;QACH,MAAM,IAAI,KAAK,CAAC,sBAAsB,IAAI,EAAE,CAAC,CAAC;KACjD;AACL,CAAC;AAED,oFAAoF;AACpF,MAAM,OAAO,oBAAoB;IA0CtB,gBAAgB,CAAC,OAAoC;QACxD,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAEnC,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE;YACnB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;SACzB;aAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YACpC,MAAM,QAAQ,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;YACxC,IAAI,CAAC,MAAM,GAAG,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC;SAC7C;aAAM;YACH,IAAI,CAAC,MAAM,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC;SAC5C;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IA0BM,OAAO,CAAC,GAAW,EAAE,sBAAmE;QAC3F,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC3B,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAE3B,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QAEf,gHAAgH;QAChH,wHAAwH;QACxH,IAAI,OAAO,sBAAsB,KAAK,QAAQ,EAAE;YAC5C,IAAI,CAAC,qBAAqB,GAAG,EAAE,GAAG,IAAI,CAAC,qBAAqB,EAAE,GAAG,sBAAsB,EAAE,CAAC;SAC7F;aAAM;YACH,IAAI,CAAC,qBAAqB,GAAG;gBACzB,GAAG,IAAI,CAAC,qBAAqB;gBAC7B,SAAS,EAAE,sBAAsB;aACpC,CAAC;SACL;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,QAAsB;QACzC,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAErC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAmBM,sBAAsB,CAAC,4BAAsD;QAChF,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC9D;QAED,IAAI,CAAC,4BAA4B,EAAE;YAC/B,IAAI,CAAC,eAAe,GAAG,IAAI,sBAAsB,EAAE,CAAC;SACvD;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,4BAA4B,CAAC,EAAE;YACpD,IAAI,CAAC,eAAe,GAAG,IAAI,sBAAsB,CAAC,4BAA4B,CAAC,CAAC;SACnF;aAAM;YACH,IAAI,CAAC,eAAe,GAAG,4BAA4B,CAAC;SACvD;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,qFAAqF;QACrF,8BAA8B;QAC9B,MAAM,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,EAAE,CAAC;QAE/D,8EAA8E;QAC9E,IAAI,qBAAqB,CAAC,MAAM,KAAK,SAAS,EAAE;YAC5C,gGAAgG;YAChG,qBAAqB,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;SAC9C;QAED,4BAA4B;QAC5B,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,0FAA0F,CAAC,CAAC;SAC/G;QACD,MAAM,UAAU,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,qBAAqB,CAAC,CAAC;QAEvE,OAAO,aAAa,CAAC,MAAM,CACvB,UAAU,EACV,IAAI,CAAC,MAAM,IAAI,UAAU,CAAC,QAAQ,EAClC,IAAI,CAAC,QAAQ,IAAI,IAAI,eAAe,EAAE,EACtC,IAAI,CAAC,eAAe,CAAC,CAAC;IAC9B,CAAC;CACJ;AAED,SAAS,QAAQ,CAAC,MAAW;IACzB,OAAO,MAAM,CAAC,GAAG,KAAK,SAAS,CAAC;AACpC,CAAC", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { DefaultReconnectPolicy } from \"./DefaultReconnectPolicy\";\r\nimport { HttpConnection } from \"./HttpConnection\";\r\nimport { HubConnection } from \"./HubConnection\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\nimport { IHubProtocol } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { IRetryPolicy } from \"./IRetryPolicy\";\r\nimport { HttpTransportType } from \"./ITransport\";\r\nimport { JsonHubProtocol } from \"./JsonHubProtocol\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { Arg, ConsoleLogger } from \"./Utils\";\r\n\r\nconst LogLevelNameMapping: {[k: string]: LogLevel} = {\r\n    trace: LogLevel.Trace,\r\n    debug: LogLevel.Debug,\r\n    info: LogLevel.Information,\r\n    information: LogLevel.Information,\r\n    warn: LogLevel.Warning,\r\n    warning: LogLevel.Warning,\r\n    error: LogLevel.Error,\r\n    critical: LogLevel.Critical,\r\n    none: LogLevel.None,\r\n};\r\n\r\nfunction parseLogLevel(name: string): LogLevel {\r\n    // Case-insensitive matching via lower-casing\r\n    // Yes, I know case-folding is a complicated problem in Unicode, but we only support\r\n    // the ASCII strings defined in LogLevelNameMapping anyway, so it's fine -anurse.\r\n    const mapping = LogLevelNameMapping[name.toLowerCase()];\r\n    if (typeof mapping !== \"undefined\") {\r\n        return mapping;\r\n    } else {\r\n        throw new Error(`Unknown log level: ${name}`);\r\n    }\r\n}\r\n\r\n/** A builder for configuring {@link @microsoft/signalr.HubConnection} instances. */\r\nexport class HubConnectionBuilder {\r\n    /** @internal */\r\n    public protocol?: IHubProtocol;\r\n    /** @internal */\r\n    public httpConnectionOptions?: IHttpConnectionOptions;\r\n    /** @internal */\r\n    public url?: string;\r\n    /** @internal */\r\n    public logger?: ILogger;\r\n\r\n    /** If defined, this indicates the client should automatically attempt to reconnect if the connection is lost. */\r\n    /** @internal */\r\n    public reconnectPolicy?: IRetryPolicy;\r\n\r\n    /** Configures console logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {LogLevel} logLevel The minimum level of messages to log. Anything at this level, or a more severe level, will be logged.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public configureLogging(logLevel: LogLevel): HubConnectionBuilder;\r\n\r\n    /** Configures custom logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {ILogger} logger An object implementing the {@link @microsoft/signalr.ILogger} interface, which will be used to write all log messages.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public configureLogging(logger: ILogger): HubConnectionBuilder;\r\n\r\n    /** Configures custom logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {string} logLevel A string representing a LogLevel setting a minimum level of messages to log.\r\n     *    See {@link https://docs.microsoft.com/aspnet/core/signalr/configuration#configure-logging|the documentation for client logging configuration} for more details.\r\n     */\r\n    public configureLogging(logLevel: string): HubConnectionBuilder;\r\n\r\n    /** Configures custom logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {LogLevel | string | ILogger} logging A {@link @microsoft/signalr.LogLevel}, a string representing a LogLevel, or an object implementing the {@link @microsoft/signalr.ILogger} interface.\r\n     *    See {@link https://docs.microsoft.com/aspnet/core/signalr/configuration#configure-logging|the documentation for client logging configuration} for more details.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public configureLogging(logging: LogLevel | string | ILogger): HubConnectionBuilder;\r\n    public configureLogging(logging: LogLevel | string | ILogger): HubConnectionBuilder {\r\n        Arg.isRequired(logging, \"logging\");\r\n\r\n        if (isLogger(logging)) {\r\n            this.logger = logging;\r\n        } else if (typeof logging === \"string\") {\r\n            const logLevel = parseLogLevel(logging);\r\n            this.logger = new ConsoleLogger(logLevel);\r\n        } else {\r\n            this.logger = new ConsoleLogger(logging);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use HTTP-based transports to connect to the specified URL.\r\n     *\r\n     * The transport will be selected automatically based on what the server and client support.\r\n     *\r\n     * @param {string} url The URL the connection will use.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withUrl(url: string): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use the specified HTTP-based transport to connect to the specified URL.\r\n     *\r\n     * @param {string} url The URL the connection will use.\r\n     * @param {HttpTransportType} transportType The specific transport to use.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withUrl(url: string, transportType: HttpTransportType): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use HTTP-based transports to connect to the specified URL.\r\n     *\r\n     * @param {string} url The URL the connection will use.\r\n     * @param {IHttpConnectionOptions} options An options object used to configure the connection.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withUrl(url: string, options: IHttpConnectionOptions): HubConnectionBuilder;\r\n    public withUrl(url: string, transportTypeOrOptions?: IHttpConnectionOptions | HttpTransportType): HubConnectionBuilder {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isNotEmpty(url, \"url\");\r\n\r\n        this.url = url;\r\n\r\n        // Flow-typing knows where it's at. Since HttpTransportType is a number and IHttpConnectionOptions is guaranteed\r\n        // to be an object, we know (as does TypeScript) this comparison is all we need to figure out which overload was called.\r\n        if (typeof transportTypeOrOptions === \"object\") {\r\n            this.httpConnectionOptions = { ...this.httpConnectionOptions, ...transportTypeOrOptions };\r\n        } else {\r\n            this.httpConnectionOptions = {\r\n                ...this.httpConnectionOptions,\r\n                transport: transportTypeOrOptions,\r\n            };\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use the specified Hub Protocol.\r\n     *\r\n     * @param {IHubProtocol} protocol The {@link @microsoft/signalr.IHubProtocol} implementation to use.\r\n     */\r\n    public withHubProtocol(protocol: IHubProtocol): HubConnectionBuilder {\r\n        Arg.isRequired(protocol, \"protocol\");\r\n\r\n        this.protocol = protocol;\r\n        return this;\r\n    }\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to automatically attempt to reconnect if the connection is lost.\r\n     * By default, the client will wait 0, 2, 10 and 30 seconds respectively before trying up to 4 reconnect attempts.\r\n     */\r\n    public withAutomaticReconnect(): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to automatically attempt to reconnect if the connection is lost.\r\n     *\r\n     * @param {number[]} retryDelays An array containing the delays in milliseconds before trying each reconnect attempt.\r\n     * The length of the array represents how many failed reconnect attempts it takes before the client will stop attempting to reconnect.\r\n     */\r\n    public withAutomaticReconnect(retryDelays: number[]): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to automatically attempt to reconnect if the connection is lost.\r\n     *\r\n     * @param {IRetryPolicy} reconnectPolicy An {@link @microsoft/signalR.IRetryPolicy} that controls the timing and number of reconnect attempts.\r\n     */\r\n    public withAutomaticReconnect(reconnectPolicy: IRetryPolicy): HubConnectionBuilder;\r\n    public withAutomaticReconnect(retryDelaysOrReconnectPolicy?: number[] | IRetryPolicy): HubConnectionBuilder {\r\n        if (this.reconnectPolicy) {\r\n            throw new Error(\"A reconnectPolicy has already been set.\");\r\n        }\r\n\r\n        if (!retryDelaysOrReconnectPolicy) {\r\n            this.reconnectPolicy = new DefaultReconnectPolicy();\r\n        } else if (Array.isArray(retryDelaysOrReconnectPolicy)) {\r\n            this.reconnectPolicy = new DefaultReconnectPolicy(retryDelaysOrReconnectPolicy);\r\n        } else {\r\n            this.reconnectPolicy = retryDelaysOrReconnectPolicy;\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Creates a {@link @microsoft/signalr.HubConnection} from the configuration options specified in this builder.\r\n     *\r\n     * @returns {HubConnection} The configured {@link @microsoft/signalr.HubConnection}.\r\n     */\r\n    public build(): HubConnection {\r\n        // If httpConnectionOptions has a logger, use it. Otherwise, override it with the one\r\n        // provided to configureLogger\r\n        const httpConnectionOptions = this.httpConnectionOptions || {};\r\n\r\n        // If it's 'null', the user **explicitly** asked for null, don't mess with it.\r\n        if (httpConnectionOptions.logger === undefined) {\r\n            // If our logger is undefined or null, that's OK, the HttpConnection constructor will handle it.\r\n            httpConnectionOptions.logger = this.logger;\r\n        }\r\n\r\n        // Now create the connection\r\n        if (!this.url) {\r\n            throw new Error(\"The 'HubConnectionBuilder.withUrl' method must be called before building the connection.\");\r\n        }\r\n        const connection = new HttpConnection(this.url, httpConnectionOptions);\r\n\r\n        return HubConnection.create(\r\n            connection,\r\n            this.logger || NullLogger.instance,\r\n            this.protocol || new JsonHubProtocol(),\r\n            this.reconnectPolicy);\r\n    }\r\n}\r\n\r\nfunction isLogger(logger: any): logger is ILogger {\r\n    return logger.log !== undefined;\r\n}\r\n"]}