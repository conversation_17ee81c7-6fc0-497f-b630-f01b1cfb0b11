{"version": 3, "file": "HttpConnection.js", "sourceRoot": "", "sources": ["../../src/HttpConnection.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,uEAAuE;AAEvE,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,eAAe,EAAE,sBAAsB,EAAE,gCAAgC,EAAE,2BAA2B,EAAE,SAAS,EAAE,yBAAyB,EAAE,MAAM,UAAU,CAAC;AACxK,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAI5C,OAAO,EAAW,QAAQ,EAAE,MAAM,WAAW,CAAC;AAC9C,OAAO,EAAE,iBAAiB,EAAc,cAAc,EAAE,MAAM,cAAc,CAAC;AAC7E,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AACxE,OAAO,EAAE,GAAG,EAAE,YAAY,EAAE,kBAAkB,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AAC1E,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AA2B1D,MAAM,aAAa,GAAG,GAAG,CAAC;AAE1B,eAAe;AACf,MAAM,OAAO,cAAc;IA0BvB,YAAY,GAAW,EAAE,UAAkC,EAAE;QAbrD,yBAAoB,GAAwC,GAAG,EAAE,GAAE,CAAC,CAAC;QAK7D,aAAQ,GAAQ,EAAE,CAAC;QAMlB,sBAAiB,GAAW,CAAC,CAAC;QAG3C,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAE3B,IAAI,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAErC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,OAAO,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC;QACxG,IAAI,OAAO,OAAO,CAAC,eAAe,KAAK,SAAS,IAAI,OAAO,CAAC,eAAe,KAAK,SAAS,EAAE;YACvF,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;SACpG;aAAM;YACH,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;SACtF;QACD,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;QAE/E,IAAI,eAAe,GAAQ,IAAI,CAAC;QAChC,IAAI,iBAAiB,GAAQ,IAAI,CAAC;QAElC,IAAI,QAAQ,CAAC,MAAM,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;YACnD,oFAAoF;YACpF,gDAAgD;YAChD,MAAM,WAAW,GAAG,OAAO,mBAAmB,KAAK,UAAU,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,OAAO,CAAC;YAClG,eAAe,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YACpC,iBAAiB,GAAG,WAAW,CAAC,aAAa,CAAC,CAAC;SAClD;QAED,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YAC5E,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;SACjC;aAAM,IAAI,QAAQ,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YAC9C,IAAI,eAAe,EAAE;gBACjB,OAAO,CAAC,SAAS,GAAG,eAAe,CAAC;aACvC;SACJ;QAED,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,OAAO,WAAW,KAAK,WAAW,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAChF,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;SACrC;aAAM,IAAI,QAAQ,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAChD,IAAI,OAAO,iBAAiB,KAAK,WAAW,EAAE;gBAC1C,OAAO,CAAC,WAAW,GAAG,iBAAiB,CAAC;aAC3C;SACJ;QAED,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7E,IAAI,CAAC,gBAAgB,oCAA+B,CAAC;QACrD,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IAIM,KAAK,CAAC,KAAK,CAAC,cAA+B;QAC9C,cAAc,GAAG,cAAc,IAAI,cAAc,CAAC,MAAM,CAAC;QAEzD,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;QAE3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,6CAA6C,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAElH,IAAI,IAAI,CAAC,gBAAgB,sCAAiC,EAAE;YACxD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC,CAAC;SAC/G;QAED,IAAI,CAAC,gBAAgB,gCAA6B,CAAC;QAEnD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;QACjE,MAAM,IAAI,CAAC,qBAAqB,CAAC;QAEjC,iHAAiH;QACjH,IAAI,IAAI,CAAC,gBAAuB,wCAAkC,EAAE;YAChE,8EAA8E;YAC9E,MAAM,OAAO,GAAG,8DAA8D,CAAC;YAC/E,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAE1C,uGAAuG;YACvG,MAAM,IAAI,CAAC,YAAY,CAAC;YAExB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;SAC7C;aAAM,IAAI,IAAI,CAAC,gBAAuB,gCAA8B,EAAE;YACnE,8EAA8E;YAC9E,MAAM,OAAO,GAAG,6GAA6G,CAAC;YAC9H,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAC1C,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;SAC7C;QAED,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACnC,CAAC;IAEM,IAAI,CAAC,IAA0B;QAClC,IAAI,IAAI,CAAC,gBAAgB,gCAA8B,EAAE;YACrD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC,CAAC;SAC3G;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,UAAU,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,SAAU,CAAC,CAAC;SAC7D;QAED,mDAAmD;QACnD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,KAAa;QAC3B,IAAI,IAAI,CAAC,gBAAgB,sCAAiC,EAAE;YACxD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,+BAA+B,KAAK,wEAAwE,CAAC,CAAC;YAC/I,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;QAED,IAAI,IAAI,CAAC,gBAAgB,wCAAkC,EAAE;YACzD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,+BAA+B,KAAK,yEAAyE,CAAC,CAAC;YAChJ,OAAO,IAAI,CAAC,YAAY,CAAC;SAC5B;QAED,IAAI,CAAC,gBAAgB,sCAAgC,CAAC;QAEtD,IAAI,CAAC,YAAY,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACxC,0DAA0D;YAC1D,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,sDAAsD;QACtD,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAChC,MAAM,IAAI,CAAC,YAAY,CAAC;IAC5B,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,KAAa;QACrC,kEAAkE;QAClE,kFAAkF;QAClF,2CAA2C;QAC3C,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,IAAI;YACA,MAAM,IAAI,CAAC,qBAAqB,CAAC;SACpC;QAAC,OAAO,CAAC,EAAE;YACR,sFAAsF;SACzF;QAED,wFAAwF;QACxF,mGAAmG;QACnG,qDAAqD;QACrD,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI;gBACA,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;aAC/B;YAAC,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,gDAAgD,CAAC,IAAI,CAAC,CAAC;gBACxF,IAAI,CAAC,eAAe,EAAE,CAAC;aAC1B;YAED,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;SAC9B;aAAM;YACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,wFAAwF,CAAC,CAAC;SAC9H;IACL,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,cAA8B;QACvD,iFAAiF;QACjF,yBAAyB;QACzB,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;QACvB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC;QAE5D,IAAI;YACA,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE;gBAC/B,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,KAAK,iBAAiB,CAAC,UAAU,EAAE;oBAC1D,8CAA8C;oBAC9C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;oBACxE,qDAAqD;oBACrD,yCAAyC;oBACzC,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;iBACnD;qBAAM;oBACH,MAAM,IAAI,KAAK,CAAC,8EAA8E,CAAC,CAAC;iBACnG;aACJ;iBAAM;gBACH,IAAI,iBAAiB,GAA8B,IAAI,CAAC;gBACxD,IAAI,SAAS,GAAG,CAAC,CAAC;gBAElB,GAAG;oBACC,iBAAiB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;oBAC5D,iEAAiE;oBACjE,IAAI,IAAI,CAAC,gBAAgB,wCAAkC,IAAI,IAAI,CAAC,gBAAgB,sCAAiC,EAAE;wBACnH,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;qBACrE;oBAED,IAAI,iBAAiB,CAAC,KAAK,EAAE;wBACzB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;qBAC5C;oBAED,IAAK,iBAAyB,CAAC,eAAe,EAAE;wBAC5C,MAAM,IAAI,KAAK,CAAC,8LAA8L,CAAC,CAAC;qBACnN;oBAED,IAAI,iBAAiB,CAAC,GAAG,EAAE;wBACvB,GAAG,GAAG,iBAAiB,CAAC,GAAG,CAAC;qBAC/B;oBAED,IAAI,iBAAiB,CAAC,WAAW,EAAE;wBAC/B,8DAA8D;wBAC9D,4BAA4B;wBAC5B,MAAM,WAAW,GAAG,iBAAiB,CAAC,WAAW,CAAC;wBAClD,IAAI,CAAC,mBAAmB,GAAG,GAAG,EAAE,CAAC,WAAW,CAAC;qBAChD;oBAED,SAAS,EAAE,CAAC;iBACf,QACM,iBAAiB,CAAC,GAAG,IAAI,SAAS,GAAG,aAAa,EAAE;gBAE3D,IAAI,SAAS,KAAK,aAAa,IAAI,iBAAiB,CAAC,GAAG,EAAE;oBACtD,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;iBAC5D;gBAED,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,iBAAiB,EAAE,cAAc,CAAC,CAAC;aAChG;YAED,IAAI,IAAI,CAAC,SAAS,YAAY,oBAAoB,EAAE;gBAChD,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC;aAC1C;YAED,IAAI,IAAI,CAAC,gBAAgB,kCAA+B,EAAE;gBACtD,0GAA0G;gBAC1G,8GAA8G;gBAC9G,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,4CAA4C,CAAC,CAAC;gBAC/E,IAAI,CAAC,gBAAgB,8BAA4B,CAAC;aACrD;YAED,0GAA0G;YAC1G,wHAAwH;YACxH,yGAAyG;SAC5G;QAAC,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,kCAAkC,GAAG,CAAC,CAAC,CAAC;YACzE,IAAI,CAAC,gBAAgB,oCAA+B,CAAC;YACrD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;YAE3B,4FAA4F;YAC5F,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SAC5B;IACL,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,GAAW;QAC7C,MAAM,OAAO,GAA0B,EAAE,CAAC;QAC1C,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC/C,IAAI,KAAK,EAAE;gBACP,OAAO,CAAC,WAAW,CAAC,aAAa,CAAC,GAAG,UAAU,KAAK,EAAE,CAAC;aAC1D;SACJ;QAED,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,kBAAkB,EAAE,CAAC;QAC3C,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAEtB,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,gCAAgC,YAAY,GAAG,CAAC,CAAC;QAClF,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE;gBACvD,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;gBACjD,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO;gBAC9B,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe;aACjD,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE;gBAC7B,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,mDAAmD,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;aAC/G;YAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAiB,CAAuB,CAAC;YACvF,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,IAAI,iBAAiB,CAAC,gBAAgB,GAAG,CAAC,EAAE;gBAC/E,kDAAkD;gBAClD,2HAA2H;gBAC3H,iBAAiB,CAAC,eAAe,GAAG,iBAAiB,CAAC,YAAY,CAAC;aACtE;YACD,OAAO,iBAAiB,CAAC;SAC5B;QAAC,OAAO,CAAC,EAAE;YACR,IAAI,YAAY,GAAG,kDAAkD,GAAG,CAAC,CAAC;YAC1E,IAAI,CAAC,YAAY,SAAS,EAAE;gBACxB,IAAI,CAAC,CAAC,UAAU,KAAK,GAAG,EAAE;oBACtB,YAAY,GAAG,YAAY,GAAG,qFAAqF,CAAC;iBACvH;aACJ;YACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;YAE/C,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,gCAAgC,CAAC,YAAY,CAAC,CAAC,CAAC;SAC7E;IACL,CAAC;IAEO,iBAAiB,CAAC,GAAW,EAAE,eAA0C;QAC7E,IAAI,CAAC,eAAe,EAAE;YAClB,OAAO,GAAG,CAAC;SACd;QAED,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,eAAe,EAAE,CAAC;IACjF,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,GAAW,EAAE,kBAA8D,EAAE,iBAAqC,EAAE,uBAAuC;QACtL,IAAI,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,iBAAiB,CAAC,eAAe,CAAC,CAAC;QAChF,IAAI,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,EAAE;YACxC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,yEAAyE,CAAC,CAAC;YAC5G,IAAI,CAAC,SAAS,GAAG,kBAAkB,CAAC;YACpC,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,uBAAuB,CAAC,CAAC;YAEhE,IAAI,CAAC,YAAY,GAAG,iBAAiB,CAAC,YAAY,CAAC;YACnD,OAAO;SACV;QAED,MAAM,mBAAmB,GAAU,EAAE,CAAC;QACtC,MAAM,UAAU,GAAG,iBAAiB,CAAC,mBAAmB,IAAI,EAAE,CAAC;QAC/D,IAAI,SAAS,GAAmC,iBAAiB,CAAC;QAClE,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE;YAC/B,MAAM,gBAAgB,GAAG,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,kBAAkB,EAAE,uBAAuB,CAAC,CAAC;YAC9G,IAAI,gBAAgB,YAAY,KAAK,EAAE;gBACnC,qFAAqF;gBACrF,mBAAmB,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,SAAS,UAAU,CAAC,CAAC;gBAC1D,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aAC9C;iBAAM,IAAI,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,EAAE;gBAC7C,IAAI,CAAC,SAAS,GAAG,gBAAgB,CAAC;gBAClC,IAAI,CAAC,SAAS,EAAE;oBACZ,IAAI;wBACA,SAAS,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;qBACvD;oBAAC,OAAO,EAAE,EAAE;wBACT,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;qBAC7B;oBACD,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,SAAS,CAAC,eAAe,CAAC,CAAC;iBACvE;gBACD,IAAI;oBACA,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,uBAAuB,CAAC,CAAC;oBAChE,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;oBAC3C,OAAO;iBACV;gBAAC,OAAO,EAAE,EAAE;oBACT,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,kCAAkC,QAAQ,CAAC,SAAS,MAAM,EAAE,EAAE,CAAC,CAAC;oBACjG,SAAS,GAAG,SAAS,CAAC;oBACtB,mBAAmB,CAAC,IAAI,CAAC,IAAI,2BAA2B,CAAC,GAAG,QAAQ,CAAC,SAAS,YAAY,EAAE,EAAE,EAAE,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAExI,IAAI,IAAI,CAAC,gBAAgB,kCAA+B,EAAE;wBACtD,MAAM,OAAO,GAAG,sDAAsD,CAAC;wBACvE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;wBAC1C,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;qBAC7C;iBACJ;aACJ;SACJ;QAED,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;YAChC,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,eAAe,CAAC,yEAAyE,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,mBAAmB,CAAC,CAAC,CAAC;SAC7K;QACD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAC,CAAC;IACpH,CAAC;IAEO,mBAAmB,CAAC,SAA4B;QACpD,QAAQ,SAAS,EAAE;YACf,KAAK,iBAAiB,CAAC,UAAU;gBAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;oBAC1B,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;iBACxE;gBACD,OAAO,IAAI,kBAAkB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,iBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;YACpL,KAAK,iBAAiB,CAAC,gBAAgB;gBACnC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;oBAC5B,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;iBAC1E;gBACD,OAAO,IAAI,yBAAyB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClH,KAAK,iBAAiB,CAAC,WAAW;gBAC9B,OAAO,IAAI,oBAAoB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7G;gBACI,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,GAAG,CAAC,CAAC;SAC3D;IACL,CAAC;IAEO,eAAe,CAAC,GAAW,EAAE,cAA8B;QAC/D,IAAI,CAAC,SAAU,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC3C,IAAI,CAAC,SAAU,CAAC,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC,SAAU,CAAC,OAAO,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;IACxD,CAAC;IAEO,wBAAwB,CAAC,QAA6B,EAAE,kBAAiD,EAAE,uBAAuC;QACtJ,MAAM,SAAS,GAAG,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACxD,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,SAAS,EAAE;YAC/C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,uBAAuB,QAAQ,CAAC,SAAS,+CAA+C,CAAC,CAAC;YAC3H,OAAO,IAAI,KAAK,CAAC,uBAAuB,QAAQ,CAAC,SAAS,+CAA+C,CAAC,CAAC;SAC9G;aAAM;YACH,IAAI,gBAAgB,CAAC,kBAAkB,EAAE,SAAS,CAAC,EAAE;gBACjD,MAAM,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/E,IAAI,eAAe,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE;oBACvD,IAAI,CAAC,SAAS,KAAK,iBAAiB,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;wBACxE,CAAC,SAAS,KAAK,iBAAiB,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;wBAClF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,uBAAuB,iBAAiB,CAAC,SAAS,CAAC,qDAAqD,CAAC,CAAC;wBAC3I,OAAO,IAAI,yBAAyB,CAAC,IAAI,iBAAiB,CAAC,SAAS,CAAC,yCAAyC,EAAE,SAAS,CAAC,CAAC;qBAC9H;yBAAM;wBACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,wBAAwB,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;wBAC3F,IAAI;4BACA,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;yBAC9C;wBAAC,OAAO,EAAE,EAAE;4BACT,OAAO,EAAE,CAAC;yBACb;qBACJ;iBACJ;qBAAM;oBACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,uBAAuB,iBAAiB,CAAC,SAAS,CAAC,gEAAgE,cAAc,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;oBACjM,OAAO,IAAI,KAAK,CAAC,IAAI,iBAAiB,CAAC,SAAS,CAAC,sBAAsB,cAAc,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;iBACtH;aACJ;iBAAM;gBACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,uBAAuB,iBAAiB,CAAC,SAAS,CAAC,0CAA0C,CAAC,CAAC;gBAChI,OAAO,IAAI,sBAAsB,CAAC,IAAI,iBAAiB,CAAC,SAAS,CAAC,8BAA8B,EAAE,SAAS,CAAC,CAAC;aAChH;SACJ;IACL,CAAC;IAEO,aAAa,CAAC,SAAc;QAChC,OAAO,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ,IAAI,SAAS,IAAI,SAAS,CAAC;IAClF,CAAC;IAEO,eAAe,CAAC,KAAa;QACjC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,iCAAiC,KAAK,2BAA2B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAE5H,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,gFAAgF;QAChF,KAAK,GAAG,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAE5B,IAAI,IAAI,CAAC,gBAAgB,sCAAiC,EAAE;YACxD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,yCAAyC,KAAK,4EAA4E,CAAC,CAAC;YAC7J,OAAO;SACV;QAED,IAAI,IAAI,CAAC,gBAAgB,kCAA+B,EAAE;YACtD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,yCAAyC,KAAK,wEAAwE,CAAC,CAAC;YAC3J,MAAM,IAAI,KAAK,CAAC,iCAAiC,KAAK,qEAAqE,CAAC,CAAC;SAChI;QAED,IAAI,IAAI,CAAC,gBAAgB,wCAAkC,EAAE;YACzD,kFAAkF;YAClF,sFAAsF;YACtF,IAAI,CAAC,oBAAoB,EAAE,CAAC;SAC/B;QAED,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,uCAAuC,KAAK,IAAI,CAAC,CAAC;SACtF;aAAM;YACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,0BAA0B,CAAC,CAAC;SACtE;QAED,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;gBAC/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,0CAA0C,CAAC,IAAI,CAAC,CAAC;YACtF,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;SAC/B;QAED,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAC9B,IAAI,CAAC,gBAAgB,oCAA+B,CAAC;QAErD,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;YAChC,IAAI;gBACA,IAAI,IAAI,CAAC,OAAO,EAAE;oBACd,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;iBACvB;aACJ;YAAC,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,0BAA0B,KAAK,kBAAkB,CAAC,IAAI,CAAC,CAAC;aAC5F;SACJ;IACL,CAAC;IAEO,WAAW,CAAC,GAAW;QAC3B,oCAAoC;QACpC,IAAI,GAAG,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE;YAC7E,OAAO,GAAG,CAAC;SACd;QAED,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,mBAAmB,GAAG,IAAI,CAAC,CAAC;SAC/C;QAED,6EAA6E;QAC7E,kCAAkC;QAClC,wEAAwE;QACxE,2EAA2E;QAC3E,mGAAmG;QACnG,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QAChD,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAEhB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,gBAAgB,GAAG,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;QAClF,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAEO,oBAAoB,CAAC,GAAW;QACpC,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,YAAY,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACvE,IAAI,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;YAC/C,YAAY,IAAI,GAAG,CAAC;SACvB;QACD,YAAY,IAAI,WAAW,CAAC;QAC5B,YAAY,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAEzD,IAAI,YAAY,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE;YACjD,YAAY,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YACzC,YAAY,IAAI,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC;SAChE;QACD,OAAO,YAAY,CAAC;IACxB,CAAC;CACJ;AAED,SAAS,gBAAgB,CAAC,kBAAiD,EAAE,eAAkC;IAC3G,OAAO,CAAC,kBAAkB,IAAI,CAAC,CAAC,eAAe,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;AACjF,CAAC;AAED,eAAe;AACf,MAAM,OAAO,kBAAkB;IAO3B,YAA6B,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;QAN3C,YAAO,GAAU,EAAE,CAAC;QAEpB,eAAU,GAAY,IAAI,CAAC;QAK/B,IAAI,CAAC,iBAAiB,GAAG,IAAI,aAAa,EAAE,CAAC;QAC7C,IAAI,CAAC,gBAAgB,GAAG,IAAI,aAAa,EAAE,CAAC;QAE5C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IAC7C,CAAC;IAEM,IAAI,CAAC,IAA0B;QAClC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,IAAI,CAAC,gBAAgB,GAAG,IAAI,aAAa,EAAE,CAAC;SAC/C;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;IACzC,CAAC;IAEM,IAAI;QACP,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAEO,WAAW,CAAC,IAA0B;QAC1C,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,OAAM,CAAC,IAAI,CAAC,EAAE;YACjE,MAAM,IAAI,KAAK,CAAC,+BAA+B,OAAM,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,OAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAC1G;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxB,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,SAAS;QACnB,OAAO,IAAI,EAAE;YACT,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;YAErC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBAClB,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACvB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;iBACvD;gBAED,MAAM;aACT;YAED,IAAI,CAAC,iBAAiB,GAAG,IAAI,aAAa,EAAE,CAAC;YAE7C,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAiB,CAAC;YAC/C,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;YAElC,MAAM,IAAI,GAAG,OAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC;gBAC/C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;gBACvB,kBAAkB,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAEpD,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;YAExB,IAAI;gBACA,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACjC,eAAe,CAAC,OAAO,EAAE,CAAC;aAC7B;YAAC,OAAO,KAAK,EAAE;gBACZ,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACjC;SACJ;IACL,CAAC;IAEO,MAAM,CAAC,cAAc,CAAC,YAA2B;QACrD,MAAM,WAAW,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAClF,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;QAC3C,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE;YAC7B,MAAM,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC;YACzC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC;SAC7B;QAED,OAAO,MAAM,CAAC,MAAM,CAAC;IACzB,CAAC;CACJ;AAED,MAAM,aAAa;IAKf;QACI,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;IAC1G,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,SAAU,EAAE,CAAC;IACtB,CAAC;IAEM,MAAM,CAAC,MAAY;QACtB,IAAI,CAAC,SAAU,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;CACJ", "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { DefaultHttpClient } from \"./DefaultHttpClient\";\r\nimport { AggregateErrors, DisabledTransportError, FailedToNegotiateWithServerError, FailedToStartTransportError, HttpError, UnsupportedTransportError } from \"./Errors\";\r\nimport { HeaderNames } from \"./HeaderNames\";\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { IConnection } from \"./IConnection\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { HttpTransportType, ITransport, TransferFormat } from \"./ITransport\";\r\nimport { LongPollingTransport } from \"./LongPollingTransport\";\r\nimport { ServerSentEventsTransport } from \"./ServerSentEventsTransport\";\r\nimport { Arg, createLogger, getUserAgentHeader, Platform } from \"./Utils\";\r\nimport { WebSocketTransport } from \"./WebSocketTransport\";\r\n\r\n/** @private */\r\nconst enum ConnectionState {\r\n    Connecting = \"Connecting\",\r\n    Connected = \"Connected\",\r\n    Disconnected = \"Disconnected\",\r\n    Disconnecting = \"Disconnecting\",\r\n}\r\n\r\n/** @private */\r\nexport interface INegotiateResponse {\r\n    connectionId?: string;\r\n    connectionToken?: string;\r\n    negotiateVersion?: number;\r\n    availableTransports?: IAvailableTransport[];\r\n    url?: string;\r\n    accessToken?: string;\r\n    error?: string;\r\n}\r\n\r\n/** @private */\r\nexport interface IAvailableTransport {\r\n    transport: keyof typeof HttpTransportType;\r\n    transferFormats: (keyof typeof TransferFormat)[];\r\n}\r\n\r\nconst MAX_REDIRECTS = 100;\r\n\r\n/** @private */\r\nexport class HttpConnection implements IConnection {\r\n    private _connectionState: ConnectionState;\r\n    // connectionStarted is tracked independently from connectionState, so we can check if the\r\n    // connection ever did successfully transition from connecting to connected before disconnecting.\r\n    private _connectionStarted: boolean;\r\n    private readonly _httpClient: HttpClient;\r\n    private readonly _logger: ILogger;\r\n    private readonly _options: IHttpConnectionOptions;\r\n    // Needs to not start with _ to be available for tests\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private transport?: ITransport;\r\n    private _startInternalPromise?: Promise<void>;\r\n    private _stopPromise?: Promise<void>;\r\n    private _stopPromiseResolver: (value?: PromiseLike<void>) => void = () => {};\r\n    private _stopError?: Error;\r\n    private _accessTokenFactory?: () => string | Promise<string>;\r\n    private _sendQueue?: TransportSendQueue;\r\n\r\n    public readonly features: any = {};\r\n    public baseUrl: string;\r\n    public connectionId?: string;\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((e?: Error) => void) | null;\r\n\r\n    private readonly _negotiateVersion: number = 1;\r\n\r\n    constructor(url: string, options: IHttpConnectionOptions = {}) {\r\n        Arg.isRequired(url, \"url\");\r\n\r\n        this._logger = createLogger(options.logger);\r\n        this.baseUrl = this._resolveUrl(url);\r\n\r\n        options = options || {};\r\n        options.logMessageContent = options.logMessageContent === undefined ? false : options.logMessageContent;\r\n        if (typeof options.withCredentials === \"boolean\" || options.withCredentials === undefined) {\r\n            options.withCredentials = options.withCredentials === undefined ? true : options.withCredentials;\r\n        } else {\r\n            throw new Error(\"withCredentials option was not a 'boolean' or 'undefined' value\");\r\n        }\r\n        options.timeout = options.timeout === undefined ? 100 * 1000 : options.timeout;\r\n\r\n        let webSocketModule: any = null;\r\n        let eventSourceModule: any = null;\r\n\r\n        if (Platform.isNode && typeof require !== \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n            webSocketModule = requireFunc(\"ws\");\r\n            eventSourceModule = requireFunc(\"eventsource\");\r\n        }\r\n\r\n        if (!Platform.isNode && typeof WebSocket !== \"undefined\" && !options.WebSocket) {\r\n            options.WebSocket = WebSocket;\r\n        } else if (Platform.isNode && !options.WebSocket) {\r\n            if (webSocketModule) {\r\n                options.WebSocket = webSocketModule;\r\n            }\r\n        }\r\n\r\n        if (!Platform.isNode && typeof EventSource !== \"undefined\" && !options.EventSource) {\r\n            options.EventSource = EventSource;\r\n        } else if (Platform.isNode && !options.EventSource) {\r\n            if (typeof eventSourceModule !== \"undefined\") {\r\n                options.EventSource = eventSourceModule;\r\n            }\r\n        }\r\n\r\n        this._httpClient = options.httpClient || new DefaultHttpClient(this._logger);\r\n        this._connectionState = ConnectionState.Disconnected;\r\n        this._connectionStarted = false;\r\n        this._options = options;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n\r\n    public start(): Promise<void>;\r\n    public start(transferFormat: TransferFormat): Promise<void>;\r\n    public async start(transferFormat?: TransferFormat): Promise<void> {\r\n        transferFormat = transferFormat || TransferFormat.Binary;\r\n\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n\r\n        this._logger.log(LogLevel.Debug, `Starting connection with transfer format '${TransferFormat[transferFormat]}'.`);\r\n\r\n        if (this._connectionState !== ConnectionState.Disconnected) {\r\n            return Promise.reject(new Error(\"Cannot start an HttpConnection that is not in the 'Disconnected' state.\"));\r\n        }\r\n\r\n        this._connectionState = ConnectionState.Connecting;\r\n\r\n        this._startInternalPromise = this._startInternal(transferFormat);\r\n        await this._startInternalPromise;\r\n\r\n        // The TypeScript compiler thinks that connectionState must be Connecting here. The TypeScript compiler is wrong.\r\n        if (this._connectionState as any === ConnectionState.Disconnecting) {\r\n            // stop() was called and transitioned the client into the Disconnecting state.\r\n            const message = \"Failed to start the HttpConnection before stop() was called.\";\r\n            this._logger.log(LogLevel.Error, message);\r\n\r\n            // We cannot await stopPromise inside startInternal since stopInternal awaits the startInternalPromise.\r\n            await this._stopPromise;\r\n\r\n            return Promise.reject(new Error(message));\r\n        } else if (this._connectionState as any !== ConnectionState.Connected) {\r\n            // stop() was called and transitioned the client into the Disconnecting state.\r\n            const message = \"HttpConnection.startInternal completed gracefully but didn't enter the connection into the connected state!\";\r\n            this._logger.log(LogLevel.Error, message);\r\n            return Promise.reject(new Error(message));\r\n        }\r\n\r\n        this._connectionStarted = true;\r\n    }\r\n\r\n    public send(data: string | ArrayBuffer): Promise<void> {\r\n        if (this._connectionState !== ConnectionState.Connected) {\r\n            return Promise.reject(new Error(\"Cannot send data if the connection is not in the 'Connected' State.\"));\r\n        }\r\n\r\n        if (!this._sendQueue) {\r\n            this._sendQueue = new TransportSendQueue(this.transport!);\r\n        }\r\n\r\n        // Transport will not be null if state is connected\r\n        return this._sendQueue.send(data);\r\n    }\r\n\r\n    public async stop(error?: Error): Promise<void> {\r\n        if (this._connectionState === ConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnected state.`);\r\n            return Promise.resolve();\r\n        }\r\n\r\n        if (this._connectionState === ConnectionState.Disconnecting) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\r\n            return this._stopPromise;\r\n        }\r\n\r\n        this._connectionState = ConnectionState.Disconnecting;\r\n\r\n        this._stopPromise = new Promise((resolve) => {\r\n            // Don't complete stop() until stopConnection() completes.\r\n            this._stopPromiseResolver = resolve;\r\n        });\r\n\r\n        // stopInternal should never throw so just observe it.\r\n        await this._stopInternal(error);\r\n        await this._stopPromise;\r\n    }\r\n\r\n    private async _stopInternal(error?: Error): Promise<void> {\r\n        // Set error as soon as possible otherwise there is a race between\r\n        // the transport closing and providing an error and the error from a close message\r\n        // We would prefer the close message error.\r\n        this._stopError = error;\r\n\r\n        try {\r\n            await this._startInternalPromise;\r\n        } catch (e) {\r\n            // This exception is returned to the user as a rejected Promise from the start method.\r\n        }\r\n\r\n        // The transport's onclose will trigger stopConnection which will run our onclose event.\r\n        // The transport should always be set if currently connected. If it wasn't set, it's likely because\r\n        // stop was called during start() and start() failed.\r\n        if (this.transport) {\r\n            try {\r\n                await this.transport.stop();\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `HttpConnection.transport.stop() threw error '${e}'.`);\r\n                this._stopConnection();\r\n            }\r\n\r\n            this.transport = undefined;\r\n        } else {\r\n            this._logger.log(LogLevel.Debug, \"HttpConnection.transport is undefined in HttpConnection.stop() because start() failed.\");\r\n        }\r\n    }\r\n\r\n    private async _startInternal(transferFormat: TransferFormat): Promise<void> {\r\n        // Store the original base url and the access token factory since they may change\r\n        // as part of negotiating\r\n        let url = this.baseUrl;\r\n        this._accessTokenFactory = this._options.accessTokenFactory;\r\n\r\n        try {\r\n            if (this._options.skipNegotiation) {\r\n                if (this._options.transport === HttpTransportType.WebSockets) {\r\n                    // No need to add a connection ID in this case\r\n                    this.transport = this._constructTransport(HttpTransportType.WebSockets);\r\n                    // We should just call connect directly in this case.\r\n                    // No fallback or negotiate in this case.\r\n                    await this._startTransport(url, transferFormat);\r\n                } else {\r\n                    throw new Error(\"Negotiation can only be skipped when using the WebSocket transport directly.\");\r\n                }\r\n            } else {\r\n                let negotiateResponse: INegotiateResponse | null = null;\r\n                let redirects = 0;\r\n\r\n                do {\r\n                    negotiateResponse = await this._getNegotiationResponse(url);\r\n                    // the user tries to stop the connection when it is being started\r\n                    if (this._connectionState === ConnectionState.Disconnecting || this._connectionState === ConnectionState.Disconnected) {\r\n                        throw new Error(\"The connection was stopped during negotiation.\");\r\n                    }\r\n\r\n                    if (negotiateResponse.error) {\r\n                        throw new Error(negotiateResponse.error);\r\n                    }\r\n\r\n                    if ((negotiateResponse as any).ProtocolVersion) {\r\n                        throw new Error(\"Detected a connection attempt to an ASP.NET SignalR Server. This client only supports connecting to an ASP.NET Core SignalR Server. See https://aka.ms/signalr-core-differences for details.\");\r\n                    }\r\n\r\n                    if (negotiateResponse.url) {\r\n                        url = negotiateResponse.url;\r\n                    }\r\n\r\n                    if (negotiateResponse.accessToken) {\r\n                        // Replace the current access token factory with one that uses\r\n                        // the returned access token\r\n                        const accessToken = negotiateResponse.accessToken;\r\n                        this._accessTokenFactory = () => accessToken;\r\n                    }\r\n\r\n                    redirects++;\r\n                }\r\n                while (negotiateResponse.url && redirects < MAX_REDIRECTS);\r\n\r\n                if (redirects === MAX_REDIRECTS && negotiateResponse.url) {\r\n                    throw new Error(\"Negotiate redirection limit exceeded.\");\r\n                }\r\n\r\n                await this._createTransport(url, this._options.transport, negotiateResponse, transferFormat);\r\n            }\r\n\r\n            if (this.transport instanceof LongPollingTransport) {\r\n                this.features.inherentKeepAlive = true;\r\n            }\r\n\r\n            if (this._connectionState === ConnectionState.Connecting) {\r\n                // Ensure the connection transitions to the connected state prior to completing this.startInternalPromise.\r\n                // start() will handle the case when stop was called and startInternal exits still in the disconnecting state.\r\n                this._logger.log(LogLevel.Debug, \"The HttpConnection connected successfully.\");\r\n                this._connectionState = ConnectionState.Connected;\r\n            }\r\n\r\n            // stop() is waiting on us via this.startInternalPromise so keep this.transport around so it can clean up.\r\n            // This is the only case startInternal can exit in neither the connected nor disconnected state because stopConnection()\r\n            // will transition to the disconnected state. start() will wait for the transition using the stopPromise.\r\n        } catch (e) {\r\n            this._logger.log(LogLevel.Error, \"Failed to start the connection: \" + e);\r\n            this._connectionState = ConnectionState.Disconnected;\r\n            this.transport = undefined;\r\n\r\n            // if start fails, any active calls to stop assume that start will complete the stop promise\r\n            this._stopPromiseResolver();\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n\r\n    private async _getNegotiationResponse(url: string): Promise<INegotiateResponse> {\r\n        const headers: {[k: string]: string} = {};\r\n        if (this._accessTokenFactory) {\r\n            const token = await this._accessTokenFactory();\r\n            if (token) {\r\n                headers[HeaderNames.Authorization] = `Bearer ${token}`;\r\n            }\r\n        }\r\n\r\n        const [name, value] = getUserAgentHeader();\r\n        headers[name] = value;\r\n\r\n        const negotiateUrl = this._resolveNegotiateUrl(url);\r\n        this._logger.log(LogLevel.Debug, `Sending negotiation request: ${negotiateUrl}.`);\r\n        try {\r\n            const response = await this._httpClient.post(negotiateUrl, {\r\n                content: \"\",\r\n                headers: { ...headers, ...this._options.headers },\r\n                timeout: this._options.timeout,\r\n                withCredentials: this._options.withCredentials,\r\n            });\r\n\r\n            if (response.statusCode !== 200) {\r\n                return Promise.reject(new Error(`Unexpected status code returned from negotiate '${response.statusCode}'`));\r\n            }\r\n\r\n            const negotiateResponse = JSON.parse(response.content as string) as INegotiateResponse;\r\n            if (!negotiateResponse.negotiateVersion || negotiateResponse.negotiateVersion < 1) {\r\n                // Negotiate version 0 doesn't use connectionToken\r\n                // So we set it equal to connectionId so all our logic can use connectionToken without being aware of the negotiate version\r\n                negotiateResponse.connectionToken = negotiateResponse.connectionId;\r\n            }\r\n            return negotiateResponse;\r\n        } catch (e) {\r\n            let errorMessage = \"Failed to complete negotiation with the server: \" + e;\r\n            if (e instanceof HttpError) {\r\n                if (e.statusCode === 404) {\r\n                    errorMessage = errorMessage + \" Either this is not a SignalR endpoint or there is a proxy blocking the connection.\";\r\n                }\r\n            }\r\n            this._logger.log(LogLevel.Error, errorMessage);\r\n\r\n            return Promise.reject(new FailedToNegotiateWithServerError(errorMessage));\r\n        }\r\n    }\r\n\r\n    private _createConnectUrl(url: string, connectionToken: string | null | undefined) {\r\n        if (!connectionToken) {\r\n            return url;\r\n        }\r\n\r\n        return url + (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + `id=${connectionToken}`;\r\n    }\r\n\r\n    private async _createTransport(url: string, requestedTransport: HttpTransportType | ITransport | undefined, negotiateResponse: INegotiateResponse, requestedTransferFormat: TransferFormat): Promise<void> {\r\n        let connectUrl = this._createConnectUrl(url, negotiateResponse.connectionToken);\r\n        if (this._isITransport(requestedTransport)) {\r\n            this._logger.log(LogLevel.Debug, \"Connection was provided an instance of ITransport, using that directly.\");\r\n            this.transport = requestedTransport;\r\n            await this._startTransport(connectUrl, requestedTransferFormat);\r\n\r\n            this.connectionId = negotiateResponse.connectionId;\r\n            return;\r\n        }\r\n\r\n        const transportExceptions: any[] = [];\r\n        const transports = negotiateResponse.availableTransports || [];\r\n        let negotiate: INegotiateResponse | undefined = negotiateResponse;\r\n        for (const endpoint of transports) {\r\n            const transportOrError = this._resolveTransportOrError(endpoint, requestedTransport, requestedTransferFormat);\r\n            if (transportOrError instanceof Error) {\r\n                // Store the error and continue, we don't want to cause a re-negotiate in these cases\r\n                transportExceptions.push(`${endpoint.transport} failed:`);\r\n                transportExceptions.push(transportOrError);\r\n            } else if (this._isITransport(transportOrError)) {\r\n                this.transport = transportOrError;\r\n                if (!negotiate) {\r\n                    try {\r\n                        negotiate = await this._getNegotiationResponse(url);\r\n                    } catch (ex) {\r\n                        return Promise.reject(ex);\r\n                    }\r\n                    connectUrl = this._createConnectUrl(url, negotiate.connectionToken);\r\n                }\r\n                try {\r\n                    await this._startTransport(connectUrl, requestedTransferFormat);\r\n                    this.connectionId = negotiate.connectionId;\r\n                    return;\r\n                } catch (ex) {\r\n                    this._logger.log(LogLevel.Error, `Failed to start the transport '${endpoint.transport}': ${ex}`);\r\n                    negotiate = undefined;\r\n                    transportExceptions.push(new FailedToStartTransportError(`${endpoint.transport} failed: ${ex}`, HttpTransportType[endpoint.transport]));\r\n\r\n                    if (this._connectionState !== ConnectionState.Connecting) {\r\n                        const message = \"Failed to select transport before stop() was called.\";\r\n                        this._logger.log(LogLevel.Debug, message);\r\n                        return Promise.reject(new Error(message));\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        if (transportExceptions.length > 0) {\r\n            return Promise.reject(new AggregateErrors(`Unable to connect to the server with any of the available transports. ${transportExceptions.join(\" \")}`, transportExceptions));\r\n        }\r\n        return Promise.reject(new Error(\"None of the transports supported by the client are supported by the server.\"));\r\n    }\r\n\r\n    private _constructTransport(transport: HttpTransportType): ITransport {\r\n        switch (transport) {\r\n            case HttpTransportType.WebSockets:\r\n                if (!this._options.WebSocket) {\r\n                    throw new Error(\"'WebSocket' is not supported in your environment.\");\r\n                }\r\n                return new WebSocketTransport(this._httpClient, this._accessTokenFactory, this._logger, this._options.logMessageContent!, this._options.WebSocket, this._options.headers || {});\r\n            case HttpTransportType.ServerSentEvents:\r\n                if (!this._options.EventSource) {\r\n                    throw new Error(\"'EventSource' is not supported in your environment.\");\r\n                }\r\n                return new ServerSentEventsTransport(this._httpClient, this._accessTokenFactory, this._logger, this._options);\r\n            case HttpTransportType.LongPolling:\r\n                return new LongPollingTransport(this._httpClient, this._accessTokenFactory, this._logger, this._options);\r\n            default:\r\n                throw new Error(`Unknown transport: ${transport}.`);\r\n        }\r\n    }\r\n\r\n    private _startTransport(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        this.transport!.onreceive = this.onreceive;\r\n        this.transport!.onclose = (e) => this._stopConnection(e);\r\n        return this.transport!.connect(url, transferFormat);\r\n    }\r\n\r\n    private _resolveTransportOrError(endpoint: IAvailableTransport, requestedTransport: HttpTransportType | undefined, requestedTransferFormat: TransferFormat): ITransport | Error {\r\n        const transport = HttpTransportType[endpoint.transport];\r\n        if (transport === null || transport === undefined) {\r\n            this._logger.log(LogLevel.Debug, `Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\r\n            return new Error(`Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\r\n        } else {\r\n            if (transportMatches(requestedTransport, transport)) {\r\n                const transferFormats = endpoint.transferFormats.map((s) => TransferFormat[s]);\r\n                if (transferFormats.indexOf(requestedTransferFormat) >= 0) {\r\n                    if ((transport === HttpTransportType.WebSockets && !this._options.WebSocket) ||\r\n                        (transport === HttpTransportType.ServerSentEvents && !this._options.EventSource)) {\r\n                        this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it is not supported in your environment.'`);\r\n                        return new UnsupportedTransportError(`'${HttpTransportType[transport]}' is not supported in your environment.`, transport);\r\n                    } else {\r\n                        this._logger.log(LogLevel.Debug, `Selecting transport '${HttpTransportType[transport]}'.`);\r\n                        try {\r\n                            return this._constructTransport(transport);\r\n                        } catch (ex) {\r\n                            return ex;\r\n                        }\r\n                    }\r\n                } else {\r\n                    this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it does not support the requested transfer format '${TransferFormat[requestedTransferFormat]}'.`);\r\n                    return new Error(`'${HttpTransportType[transport]}' does not support ${TransferFormat[requestedTransferFormat]}.`);\r\n                }\r\n            } else {\r\n                this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it was disabled by the client.`);\r\n                return new DisabledTransportError(`'${HttpTransportType[transport]}' is disabled by the client.`, transport);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _isITransport(transport: any): transport is ITransport {\r\n        return transport && typeof (transport) === \"object\" && \"connect\" in transport;\r\n    }\r\n\r\n    private _stopConnection(error?: Error): void {\r\n        this._logger.log(LogLevel.Debug, `HttpConnection.stopConnection(${error}) called while in state ${this._connectionState}.`);\r\n\r\n        this.transport = undefined;\r\n\r\n        // If we have a stopError, it takes precedence over the error from the transport\r\n        error = this._stopError || error;\r\n        this._stopError = undefined;\r\n\r\n        if (this._connectionState === ConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is already in the disconnected state.`);\r\n            return;\r\n        }\r\n\r\n        if (this._connectionState === ConnectionState.Connecting) {\r\n            this._logger.log(LogLevel.Warning, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is still in the connecting state.`);\r\n            throw new Error(`HttpConnection.stopConnection(${error}) was called while the connection is still in the connecting state.`);\r\n        }\r\n\r\n        if (this._connectionState === ConnectionState.Disconnecting) {\r\n            // A call to stop() induced this call to stopConnection and needs to be completed.\r\n            // Any stop() awaiters will be scheduled to continue after the onclose callback fires.\r\n            this._stopPromiseResolver();\r\n        }\r\n\r\n        if (error) {\r\n            this._logger.log(LogLevel.Error, `Connection disconnected with error '${error}'.`);\r\n        } else {\r\n            this._logger.log(LogLevel.Information, \"Connection disconnected.\");\r\n        }\r\n\r\n        if (this._sendQueue) {\r\n            this._sendQueue.stop().catch((e) => {\r\n                this._logger.log(LogLevel.Error, `TransportSendQueue.stop() threw error '${e}'.`);\r\n            });\r\n            this._sendQueue = undefined;\r\n        }\r\n\r\n        this.connectionId = undefined;\r\n        this._connectionState = ConnectionState.Disconnected;\r\n\r\n        if (this._connectionStarted) {\r\n            this._connectionStarted = false;\r\n            try {\r\n                if (this.onclose) {\r\n                    this.onclose(error);\r\n                }\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `HttpConnection.onclose(${error}) threw error '${e}'.`);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _resolveUrl(url: string): string {\r\n        // startsWith is not supported in IE\r\n        if (url.lastIndexOf(\"https://\", 0) === 0 || url.lastIndexOf(\"http://\", 0) === 0) {\r\n            return url;\r\n        }\r\n\r\n        if (!Platform.isBrowser) {\r\n            throw new Error(`Cannot resolve '${url}'.`);\r\n        }\r\n\r\n        // Setting the url to the href propery of an anchor tag handles normalization\r\n        // for us. There are 3 main cases.\r\n        // 1. Relative path normalization e.g \"b\" -> \"http://localhost:5000/a/b\"\r\n        // 2. Absolute path normalization e.g \"/a/b\" -> \"http://localhost:5000/a/b\"\r\n        // 3. Networkpath reference normalization e.g \"//localhost:5000/a/b\" -> \"http://localhost:5000/a/b\"\r\n        const aTag = window.document.createElement(\"a\");\r\n        aTag.href = url;\r\n\r\n        this._logger.log(LogLevel.Information, `Normalizing '${url}' to '${aTag.href}'.`);\r\n        return aTag.href;\r\n    }\r\n\r\n    private _resolveNegotiateUrl(url: string): string {\r\n        const index = url.indexOf(\"?\");\r\n        let negotiateUrl = url.substring(0, index === -1 ? url.length : index);\r\n        if (negotiateUrl[negotiateUrl.length - 1] !== \"/\") {\r\n            negotiateUrl += \"/\";\r\n        }\r\n        negotiateUrl += \"negotiate\";\r\n        negotiateUrl += index === -1 ? \"\" : url.substring(index);\r\n\r\n        if (negotiateUrl.indexOf(\"negotiateVersion\") === -1) {\r\n            negotiateUrl += index === -1 ? \"?\" : \"&\";\r\n            negotiateUrl += \"negotiateVersion=\" + this._negotiateVersion;\r\n        }\r\n        return negotiateUrl;\r\n    }\r\n}\r\n\r\nfunction transportMatches(requestedTransport: HttpTransportType | undefined, actualTransport: HttpTransportType) {\r\n    return !requestedTransport || ((actualTransport & requestedTransport) !== 0);\r\n}\r\n\r\n/** @private */\r\nexport class TransportSendQueue {\r\n    private _buffer: any[] = [];\r\n    private _sendBufferedData: PromiseSource;\r\n    private _executing: boolean = true;\r\n    private _transportResult?: PromiseSource;\r\n    private _sendLoopPromise: Promise<void>;\r\n\r\n    constructor(private readonly _transport: ITransport) {\r\n        this._sendBufferedData = new PromiseSource();\r\n        this._transportResult = new PromiseSource();\r\n\r\n        this._sendLoopPromise = this._sendLoop();\r\n    }\r\n\r\n    public send(data: string | ArrayBuffer): Promise<void> {\r\n        this._bufferData(data);\r\n        if (!this._transportResult) {\r\n            this._transportResult = new PromiseSource();\r\n        }\r\n        return this._transportResult.promise;\r\n    }\r\n\r\n    public stop(): Promise<void> {\r\n        this._executing = false;\r\n        this._sendBufferedData.resolve();\r\n        return this._sendLoopPromise;\r\n    }\r\n\r\n    private _bufferData(data: string | ArrayBuffer): void {\r\n        if (this._buffer.length && typeof(this._buffer[0]) !== typeof(data)) {\r\n            throw new Error(`Expected data to be of type ${typeof(this._buffer)} but was of type ${typeof(data)}`);\r\n        }\r\n\r\n        this._buffer.push(data);\r\n        this._sendBufferedData.resolve();\r\n    }\r\n\r\n    private async _sendLoop(): Promise<void> {\r\n        while (true) {\r\n            await this._sendBufferedData.promise;\r\n\r\n            if (!this._executing) {\r\n                if (this._transportResult) {\r\n                    this._transportResult.reject(\"Connection stopped.\");\r\n                }\r\n\r\n                break;\r\n            }\r\n\r\n            this._sendBufferedData = new PromiseSource();\r\n\r\n            const transportResult = this._transportResult!;\r\n            this._transportResult = undefined;\r\n\r\n            const data = typeof(this._buffer[0]) === \"string\" ?\r\n                this._buffer.join(\"\") :\r\n                TransportSendQueue._concatBuffers(this._buffer);\r\n\r\n            this._buffer.length = 0;\r\n\r\n            try {\r\n                await this._transport.send(data);\r\n                transportResult.resolve();\r\n            } catch (error) {\r\n                transportResult.reject(error);\r\n            }\r\n        }\r\n    }\r\n\r\n    private static _concatBuffers(arrayBuffers: ArrayBuffer[]): ArrayBuffer {\r\n        const totalLength = arrayBuffers.map((b) => b.byteLength).reduce((a, b) => a + b);\r\n        const result = new Uint8Array(totalLength);\r\n        let offset = 0;\r\n        for (const item of arrayBuffers) {\r\n            result.set(new Uint8Array(item), offset);\r\n            offset += item.byteLength;\r\n        }\r\n\r\n        return result.buffer;\r\n    }\r\n}\r\n\r\nclass PromiseSource {\r\n    private _resolver?: () => void;\r\n    private _rejecter!: (reason?: any) => void;\r\n    public promise: Promise<void>;\r\n\r\n    constructor() {\r\n        this.promise = new Promise((resolve, reject) => [this._resolver, this._rejecter] = [resolve, reject]);\r\n    }\r\n\r\n    public resolve(): void {\r\n        this._resolver!();\r\n    }\r\n\r\n    public reject(reason?: any): void {\r\n        this._rejecter!(reason);\r\n    }\r\n}\r\n"]}