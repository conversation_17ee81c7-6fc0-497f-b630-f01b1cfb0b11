{"version": 3, "sources": ["webpack://signalR/webpack/universalModuleDefinition", "webpack://signalR/webpack/bootstrap", "webpack://signalR/webpack/runtime/define property getters", "webpack://signalR/webpack/runtime/global", "webpack://signalR/webpack/runtime/hasOwnProperty shorthand", "webpack://signalR/webpack/runtime/make namespace object", "webpack://signalR/src/ILogger.ts", "webpack://signalR/src/Errors.ts", "webpack://signalR/src/HttpClient.ts", "webpack://signalR/src/Loggers.ts", "webpack://signalR/src/Utils.ts", "webpack://signalR/src/FetchHttpClient.ts", "webpack://signalR/src/XhrHttpClient.ts", "webpack://signalR/src/DefaultHttpClient.ts", "webpack://signalR/src/TextMessageFormat.ts", "webpack://signalR/src/HandshakeProtocol.ts", "webpack://signalR/src/IHubProtocol.ts", "webpack://signalR/src/HubConnection.ts", "webpack://signalR/src/Subject.ts", "webpack://signalR/src/DefaultReconnectPolicy.ts", "webpack://signalR/src/HeaderNames.ts", "webpack://signalR/src/ITransport.ts", "webpack://signalR/src/AbortController.ts", "webpack://signalR/src/LongPollingTransport.ts", "webpack://signalR/src/ServerSentEventsTransport.ts", "webpack://signalR/src/WebSocketTransport.ts", "webpack://signalR/src/HttpConnection.ts", "webpack://signalR/src/JsonHubProtocol.ts", "webpack://signalR/src/HubConnectionBuilder.ts", "webpack://signalR/src/browser-index.ts"], "names": ["root", "factory", "self", "__webpack_require__", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "g", "globalThis", "this", "Function", "e", "window", "obj", "prop", "prototype", "hasOwnProperty", "call", "r", "Symbol", "toStringTag", "value", "LogLevel", "HttpError", "Error", "errorMessage", "statusCode", "trueProto", "super", "__proto__", "TimeoutError", "AbortError", "UnsupportedTransportError", "message", "transport", "errorType", "DisabledTransportError", "FailedToStartTransportError", "FailedToNegotiateWithServerError", "AggregateErrors", "innerErrors", "HttpResponse", "statusText", "content", "HttpClient", "url", "options", "send", "method", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_logLevel", "_message", "instance", "VERSION", "Arg", "val", "name", "match", "values", "Platform", "document", "<PERSON><PERSON><PERSON><PERSON>", "isWebWorker", "isReactNative", "getDataDetail", "data", "<PERSON><PERSON><PERSON><PERSON>", "detail", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "byteLength", "view", "Uint8Array", "str", "for<PERSON>ach", "num", "toString", "substr", "length", "formatA<PERSON>y<PERSON>uffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "async", "sendMessage", "logger", "transportName", "httpClient", "accessTokenFactory", "headers", "token", "getUserAgentHeader", "log", "Trace", "logMessageContent", "responseType", "response", "post", "timeout", "withCredentials", "SubjectSubscription", "subject", "observer", "_subject", "_observer", "index", "observers", "indexOf", "splice", "cancelCallback", "catch", "_", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minimumLogLevel", "_minLevel", "out", "console", "logLevel", "msg", "Date", "toISOString", "Critical", "error", "Warning", "warn", "Information", "info", "userAgentHeaderName", "isNode", "constructUserAgent", "getOsName", "getRuntimeVersion", "version", "os", "runtime", "runtimeVersion", "userAgent", "majorAndMinor", "split", "process", "platform", "versions", "node", "getErrorString", "stack", "FetchHttpClient", "_logger", "fetch", "requireFunc", "_jar", "<PERSON><PERSON><PERSON><PERSON>", "_fetchType", "bind", "getGlobalThis", "AbortController", "_abortControllerType", "request", "abortSignal", "aborted", "abortController", "<PERSON>ab<PERSON>", "abort", "timeoutId", "msTimeout", "setTimeout", "body", "cache", "credentials", "mode", "redirect", "signal", "clearTimeout", "ok", "deserializeContent", "status", "payload", "cookies", "getCookies", "c", "join", "arrayBuffer", "text", "XhrHttpClient", "Promise", "reject", "resolve", "xhr", "XMLHttpRequest", "open", "undefined", "setRequestHeader", "keys", "header", "onload", "responseText", "onerror", "ontimeout", "DefaultHttpClient", "_httpClient", "getCookieString", "TextMessageFormat", "output", "RecordSeparator", "input", "messages", "pop", "RecordSeparatorCode", "String", "fromCharCode", "HandshakeProtocol", "handshakeRequest", "write", "JSON", "stringify", "messageData", "remainingData", "binaryData", "separatorIndex", "responseLength", "apply", "Array", "slice", "buffer", "textData", "substring", "parse", "type", "MessageType", "HubConnectionState", "Subject", "item", "next", "err", "complete", "push", "HubConnection", "connection", "protocol", "reconnectPolicy", "_nextKeepAlive", "_freezeEventListener", "isRequired", "serverTimeoutInMilliseconds", "keepAliveIntervalInMilliseconds", "_protocol", "_reconnectPolicy", "_handshakeProtocol", "onreceive", "_processIncomingData", "onclose", "_connectionClosed", "_callbacks", "_methods", "_closedCallbacks", "_reconnectingCallbacks", "_reconnectedCallbacks", "_invocationId", "_receivedHandshakeResponse", "_connectionState", "Disconnected", "_connectionStarted", "_cachedPingMessage", "writeMessage", "<PERSON>", "connectionId", "baseUrl", "Reconnecting", "_startPromise", "_startWithStateTransitions", "Connecting", "Debug", "_startInternal", "addEventListener", "Connected", "_stopDuringStartError", "handshakePromise", "_handshakeResolver", "_handshake<PERSON><PERSON><PERSON><PERSON>", "start", "transferFormat", "_sendMessage", "writeHandshakeRequest", "_cleanupTimeout", "_resetTimeoutPeriod", "_resetKeepAliveInterval", "_cleanupPingTimer", "stop", "startPromise", "_stopPromise", "_stopInternal", "Disconnecting", "_reconnectDelayHandle", "_completeClose", "methodName", "args", "streams", "streamIds", "_replaceStreamingParams", "invocationDescriptor", "_createStreamInvocation", "promiseQueue", "cancelInvocation", "_createCancelInvocation", "invocationId", "then", "_sendWithProtocol", "invocationEvent", "Completion", "_launchStreams", "sendPromise", "_createInvocation", "result", "newMethod", "toLowerCase", "handlers", "removeIdx", "callback", "_processHandshakeResponse", "parseMessages", "Invocation", "_invokeClientMethod", "StreamItem", "Close", "allowReconnect", "responseMessage", "parseHandshakeResponse", "features", "inherentKeepAlive", "getTime", "_timeoutHandle", "serverTimeout", "_pingServerHandle", "nextPing", "invocationMessage", "methods", "target", "m", "arguments", "_cancelCallbacksWithError", "_reconnect", "removeEventListener", "reconnectStartTime", "now", "previousReconnectAttempts", "retryError", "nextRetryDelay", "_getNextRetryDelay", "previousRetryCount", "elapsedMilliseconds", "retryReason", "nextRetryDelayInMilliseconds", "callbacks", "nonblocking", "streamId", "subscribe", "_createCompletionMessage", "_createStreamItemMessage", "i", "argument", "_isObservable", "arg", "StreamInvocation", "id", "CancelInvocation", "DEFAULT_RETRY_DELAYS_IN_MILLISECONDS", "DefaultReconnectPolicy", "re<PERSON><PERSON><PERSON><PERSON>", "_retryD<PERSON>ys", "retryContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HttpTransportType", "TransferFormat", "Authorization", "<PERSON><PERSON>", "_isAborted", "LongPollingTransport", "_accessTokenFactory", "_pollAbort", "_options", "_running", "isIn", "_url", "Binary", "pollOptions", "_getAccessToken", "_updateHeaderToken", "pollUrl", "_closeError", "_receiving", "_poll", "pollAborted", "_raiseOnClose", "deleteOptions", "delete", "logMessage", "ServerSentEventsTransport", "encodeURIComponent", "eventSource", "opened", "Text", "EventSource", "onmessage", "_close", "onopen", "_eventSource", "close", "WebSocketTransport", "webSocketConstructor", "_logMessageContent", "_webSocketConstructor", "_headers", "webSocket", "replace", "binaryType", "_event", "_webSocket", "event", "ErrorEvent", "readyState", "OPEN", "_isCloseEvent", "<PERSON><PERSON><PERSON>", "code", "reason", "HttpConnection", "_stopPromiseResolver", "_negotiateVersion", "_resolveUrl", "webSocketModule", "eventSourceModule", "WebSocket", "_startInternalPromise", "_sendQueue", "TransportSendQueue", "_stopError", "_stopConnection", "skipNegotiation", "WebSockets", "_constructTransport", "_startTransport", "negotiateResponse", "redirects", "_getNegotiationResponse", "ProtocolVersion", "accessToken", "_createTransport", "negotiateUrl", "_resolveNegotiateUrl", "negotiateVersion", "connectionToken", "requestedTransport", "requestedTransferFormat", "connectUrl", "_createConnectUrl", "_isITransport", "transportExceptions", "transports", "availableTransports", "negotiate", "endpoint", "transportOrError", "_resolveTransportOrError", "ex", "ServerSentEvents", "LongPolling", "connect", "actualTransport", "transportMatches", "transferFormats", "map", "s", "lastIndexOf", "aTag", "createElement", "href", "_transport", "_buffer", "_executing", "_sendBufferedData", "PromiseSource", "_transportResult", "_sendLoopPromise", "_sendLoop", "_bufferData", "promise", "transportResult", "_concatBuffers", "arrayBuffers", "totalLength", "b", "reduce", "a", "offset", "set", "_resolver", "_rejecter", "JsonHubProtocol", "hubMessages", "parsedMessage", "_isInvocationMessage", "_isStreamItemMessage", "_isCompletionMessage", "_assertNotEmptyString", "LogLevelNameMapping", "trace", "debug", "information", "warning", "critical", "none", "None", "HubConnectionBuilder", "logging", "mapping", "parseLogLevel", "transportTypeOrOptions", "isNotEmpty", "httpConnectionOptions", "retryDelaysOrReconnectPolicy", "isArray", "create", "writable", "end", "module", "define", "amd"], "mappings": "AAAA,IAA2CA,EAAMC,EAAND,EASxCE,KAT8CD,EASxC,WACT,M,MCTA,IAAIE,EAAsB,CCA1B,EAAwB,CAACC,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXF,EAAoBI,EAAEF,EAAYC,KAASH,EAAoBI,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,OCJ3EH,EAAoBS,EAAI,WACvB,GAA0B,iBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOC,MAAQ,IAAIC,SAAS,cAAb,GACd,MAAOC,GACR,GAAsB,iBAAXC,OAAqB,OAAOA,QALjB,GCAxBd,EAAoBI,EAAI,CAACW,EAAKC,IAAUX,OAAOY,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFhB,EAAoBoB,EAAKnB,IACH,oBAAXoB,QAA0BA,OAAOC,aAC1CjB,OAAOC,eAAeL,EAASoB,OAAOC,YAAa,CAAEC,MAAO,WAE7DlB,OAAOC,eAAeL,EAAS,IAAc,CAAEsB,OAAO,K,ICG3CC,E,8VCFL,MAAMC,UAAkBC,MAa3B,YAAYC,EAAsBC,GAC9B,MAAMC,aAAuBZ,UAC7Ba,MAAM,GAAGH,mBAA8BC,MACvCjB,KAAKiB,WAAaA,EAIlBjB,KAAKoB,UAAYF,GAKlB,MAAMG,UAAqBN,MAS9B,YAAYC,EAAuB,uBAC/B,MAAME,aAAuBZ,UAC7Ba,MAAMH,GAINhB,KAAKoB,UAAYF,GAKlB,MAAMI,UAAmBP,MAS5B,YAAYC,EAAuB,sBAC/B,MAAME,aAAuBZ,UAC7Ba,MAAMH,GAINhB,KAAKoB,UAAYF,GAMlB,MAAMK,UAAkCR,MAgB3C,YAAYS,EAAiBC,GACzB,MAAMP,aAAuBZ,UAC7Ba,MAAMK,GACNxB,KAAKyB,UAAYA,EACjBzB,KAAK0B,UAAY,4BAIjB1B,KAAKoB,UAAYF,GAMlB,MAAMS,UAA+BZ,MAgBxC,YAAYS,EAAiBC,GACzB,MAAMP,aAAuBZ,UAC7Ba,MAAMK,GACNxB,KAAKyB,UAAYA,EACjBzB,KAAK0B,UAAY,yBAIjB1B,KAAKoB,UAAYF,GAMlB,MAAMU,UAAoCb,MAgB7C,YAAYS,EAAiBC,GACzB,MAAMP,aAAuBZ,UAC7Ba,MAAMK,GACNxB,KAAKyB,UAAYA,EACjBzB,KAAK0B,UAAY,8BAIjB1B,KAAKoB,UAAYF,GAMlB,MAAMW,UAAyCd,MAYlD,YAAYS,GACR,MAAMN,aAAuBZ,UAC7Ba,MAAMK,GACNxB,KAAK0B,UAAY,mCAIjB1B,KAAKoB,UAAYF,GAMlB,MAAMY,UAAwBf,MAajC,YAAYS,EAAiBO,GACzB,MAAMb,aAAuBZ,UAC7Ba,MAAMK,GAENxB,KAAK+B,YAAcA,EAInB/B,KAAKoB,UAAYF,GC9KlB,MAAMc,EAqCT,YACoBf,EACAgB,EACAC,GAFA,KAAAjB,aACA,KAAAgB,aACA,KAAAC,WAQjB,MAAeC,EAeX,IAAIC,EAAaC,GACpB,OAAOrC,KAAKsC,KAAK,IACVD,EACHE,OAAQ,MACRH,QAkBD,KAAKA,EAAaC,GACrB,OAAOrC,KAAKsC,KAAK,IACVD,EACHE,OAAQ,OACRH,QAkBD,OAAOA,EAAaC,GACvB,OAAOrC,KAAKsC,KAAK,IACVD,EACHE,OAAQ,SACRH,QAiBD,gBAAgBA,GACnB,MAAO,KF3Jf,SAAYvB,GAER,qBAEA,qBAEA,iCAEA,yBAEA,qBAEA,2BAEA,mBAdJ,CAAYA,MAAQ,KGFb,MAAM2B,EAIT,eAIO,IAAIC,EAAqBC,KANlB,EAAAC,SAAoB,IAAIH,ECKnC,MAAMI,EAAkB,kBAExB,MAAMC,EACF,kBAAkBC,EAAUC,GAC/B,GAAID,QACA,MAAM,IAAI/B,MAAM,QAAQgC,4BAGzB,kBAAkBD,EAAaC,GAClC,IAAKD,GAAOA,EAAIE,MAAM,SAClB,MAAM,IAAIjC,MAAM,QAAQgC,oCAIzB,YAAYD,EAAUG,EAAaF,GAEtC,KAAMD,KAAOG,GACT,MAAM,IAAIlC,MAAM,WAAWgC,YAAeD,OAM/C,MAAMI,EAEF,uBACH,MAAyB,iBAAX/C,QAAkD,iBAApBA,OAAOgD,SAIhD,yBACH,MAAuB,iBAAT/D,MAAqB,kBAAmBA,KAI1D,2BACI,MAAyB,iBAAXe,aAAkD,IAApBA,OAAOgD,SAKhD,oBACH,OAAQnD,KAAKoD,YAAcpD,KAAKqD,cAAgBrD,KAAKsD,eAKtD,SAASC,EAAcC,EAAWC,GACrC,IAAIC,EAAS,GAYb,OAXIC,EAAcH,IACdE,EAAS,yBAAyBF,EAAKI,aACnCH,IACAC,GAAU,eAYf,SAA2BF,GAC9B,MAAMK,EAAO,IAAIC,WAAWN,GAG5B,IAAIO,EAAM,GAOV,OANAF,EAAKG,SAASC,IAEVF,GAAO,KADKE,EAAM,GAAK,IAAM,KACXA,EAAIC,SAAS,UAI5BH,EAAII,OAAO,EAAGJ,EAAIK,OAAS,GAvBDC,CAAkBb,QAExB,iBAATA,IACdE,EAAS,yBAAyBF,EAAKY,SACnCX,IACAC,GAAU,eAAeF,OAG1BE,EAoBJ,SAASC,EAAcb,GAC1B,OAAOA,GAA8B,oBAAhBwB,cAChBxB,aAAewB,aAEXxB,EAAIyB,aAAwC,gBAAzBzB,EAAIyB,YAAYxB,MAIzCyB,eAAeC,EAAYC,EAAiBC,EAAuBC,EAAwBxC,EAAayC,EAC7E3C,EAA+BG,GAC7D,IAAIyC,EAAiC,GACrC,GAAID,EAAoB,CACpB,MAAME,QAAcF,IAChBE,IACAD,EAAU,CACN,cAAmB,UAAUC,MAKzC,MAAOhC,EAAMnC,GAASoE,IACtBF,EAAQ/B,GAAQnC,EAEhB8D,EAAOO,IAAIpE,EAASqE,MAAO,IAAIP,8BAA0CpB,EAAcrB,EAASG,EAAQ8C,uBAExG,MAAMC,EAAezB,EAAczB,GAAW,cAAgB,OACxDmD,QAAiBT,EAAWU,KAAKlD,EAAK,CACxCF,UACA4C,QAAS,IAAKA,KAAYzC,EAAQyC,SAClCM,eACAG,QAASlD,EAAQkD,QACjBC,gBAAiBnD,EAAQmD,kBAG7Bd,EAAOO,IAAIpE,EAASqE,MAAO,IAAIP,mDAA+DU,EAASpE,eAqBpG,MAAMwE,EAIT,YAAYC,EAAqBC,GAC7B3F,KAAK4F,EAAWF,EAChB1F,KAAK6F,EAAYF,EAGd,UACH,MAAMG,EAAgB9F,KAAK4F,EAASG,UAAUC,QAAQhG,KAAK6F,GACvDC,GAAS,GACT9F,KAAK4F,EAASG,UAAUE,OAAOH,EAAO,GAGH,IAAnC9F,KAAK4F,EAASG,UAAU3B,QAAgBpE,KAAK4F,EAASM,gBACtDlG,KAAK4F,EAASM,iBAAiBC,OAAOC,SAM3C,MAAMC,EAWT,YAAYC,GACRtG,KAAKuG,EAAYD,EACjBtG,KAAKwG,IAAMC,QAGR,IAAIC,EAAoBlF,GAC3B,GAAIkF,GAAY1G,KAAKuG,EAAW,CAC5B,MAAMI,EAAM,KAAI,IAAIC,MAAOC,kBAAkBhG,EAAS6F,OAAclF,IACpE,OAAQkF,GACJ,KAAK7F,EAASiG,SACd,KAAKjG,EAASE,MACVf,KAAKwG,IAAIO,MAAMJ,GACf,MACJ,KAAK9F,EAASmG,QACVhH,KAAKwG,IAAIS,KAAKN,GACd,MACJ,KAAK9F,EAASqG,YACVlH,KAAKwG,IAAIW,KAAKR,GACd,MACJ,QAEI3G,KAAKwG,IAAIvB,IAAI0B,MAQ1B,SAAS3B,IACZ,IAAIoC,EAAsB,uBAI1B,OAHIlE,EAASmE,SACTD,EAAsB,cAEnB,CAAEA,EAAqBE,EAAmB1E,EAAS2E,IAyDtDrE,EAASmE,OACF,SAEA,UA5D0EG,MAIlF,SAASF,EAAmBG,EAAiBC,EAAYC,EAAiBC,GAE7E,IAAIC,EAAoB,qBAExB,MAAMC,EAAgBL,EAAQM,MAAM,KAmBpC,OAlBAF,GAAa,GAAGC,EAAc,MAAMA,EAAc,KAClDD,GAAa,KAAKJ,MAGdI,GADAH,GAAa,KAAPA,EACO,GAAGA,MAEH,eAGjBG,GAAa,GAAGF,IAGZE,GADAD,EACa,KAAKA,IAEL,4BAGjBC,GAAa,IACNA,EAIG,SAASN,IACnB,IAAIrE,EAASmE,OAYT,MAAO,GAXP,OAAQW,QAAQC,UACZ,IAAK,QACD,MAAO,aACX,IAAK,SACD,MAAO,QACX,IAAK,QACD,MAAO,QACX,QACI,OAAOD,QAAQC,UAQjB,SAAST,IACnB,GAAItE,EAASmE,OACT,OAAOW,QAAQE,SAASC,KAczB,SAASC,EAAelI,GAC3B,OAAIA,EAAEmI,MACKnI,EAAEmI,MACFnI,EAAEsB,QACFtB,EAAEsB,QAEN,GAAGtB,ICnRP,MAAMoI,UAAwBnG,EAOjC,YAAmBuC,GAIf,GAHAvD,QACAnB,KAAKuI,EAAU7D,EAEM,oBAAV8D,MAAuB,CAG9B,MAAMC,EAA0D,QAGhEzI,KAAK0I,EAAO,IAAKD,EAAY,gBAAiBE,WAC9C3I,KAAK4I,EAAaH,EAAY,cAI9BzI,KAAK4I,EAAaH,EAAY,eAAZA,CAA4BzI,KAAK4I,EAAY5I,KAAK0I,QAEpE1I,KAAK4I,EAAaJ,MAAMK,KD+P7B,WAEH,GAA0B,oBAAf9I,WACP,OAAOA,WAEX,GAAoB,oBAATX,KACP,OAAOA,KAEX,GAAsB,oBAAXe,OACP,OAAOA,OAEX,QAAsB,IAAX,EAAAL,EACP,OAAO,EAAAA,EAEX,MAAM,IAAIiB,MAAM,yBC7QqB+H,IAEjC,GAA+B,oBAApBC,gBAAiC,CAGxC,MAAMN,EAA0D,QAGhEzI,KAAKgJ,EAAuBP,EAAY,yBAExCzI,KAAKgJ,EAAuBD,gBAK7B,WAAWE,GAEd,GAAIA,EAAQC,aAAeD,EAAQC,YAAYC,QAC3C,MAAM,IAAI7H,EAGd,IAAK2H,EAAQ1G,OACT,MAAM,IAAIxB,MAAM,sBAEpB,IAAKkI,EAAQ7G,IACT,MAAM,IAAIrB,MAAM,mBAGpB,MAAMqI,EAAkB,IAAIpJ,KAAKgJ,EAEjC,IAAIjC,EAEAkC,EAAQC,cACRD,EAAQC,YAAYG,QAAU,KAC1BD,EAAgBE,QAChBvC,EAAQ,IAAIzF,IAMpB,IAUI+D,EAVAkE,EAAiB,KACrB,GAAIN,EAAQ1D,QAAS,CACjB,MAAMiE,EAAYP,EAAQ1D,QAC1BgE,EAAYE,YAAW,KACnBL,EAAgBE,QAChBtJ,KAAKuI,EAAQtD,IAAIpE,EAASmG,QAAS,8BACnCD,EAAQ,IAAI1F,IACbmI,GAIP,IACInE,QAAiBrF,KAAK4I,EAAWK,EAAQ7G,IAAM,CAC3CsH,KAAMT,EAAQ/G,QACdyH,MAAO,WACPC,aAAyC,IAA5BX,EAAQzD,gBAA2B,UAAY,cAC5DV,QAAS,CACL,eAAgB,2BAChB,mBAAoB,oBACjBmE,EAAQnE,SAEfvC,OAAQ0G,EAAQ1G,OAChBsH,KAAM,OACNC,SAAU,SACVC,OAAQX,EAAgBW,SAE9B,MAAO7J,GACL,GAAI6G,EACA,MAAMA,EAMV,MAJA/G,KAAKuI,EAAQtD,IACTpE,EAASmG,QACT,4BAA4B9G,MAE1BA,E,QAEFqJ,GACAS,aAAaT,GAEbN,EAAQC,cACRD,EAAQC,YAAYG,QAAU,MAItC,IAAKhE,EAAS4E,GAAI,CACd,MAAMjJ,QAAqBkJ,EAAmB7E,EAAU,QACxD,MAAM,IAAIvE,EAAUE,GAAgBqE,EAASpD,WAAYoD,EAAS8E,QAGtE,MAAMjI,EAAUgI,EAAmB7E,EAAU4D,EAAQ7D,cAC/CgF,QAAgBlI,EAEtB,OAAO,IAAIF,EACPqD,EAAS8E,OACT9E,EAASpD,WACTmI,GAID,gBAAgBhI,GACnB,IAAIiI,EAAkB,GAKtB,OAJInH,EAASmE,QAAUrH,KAAK0I,GAExB1I,KAAK0I,EAAK4B,WAAWlI,GAAK,CAAClC,EAAGqK,IAAMF,EAAUE,EAAEC,KAAK,QAElDH,GAIf,SAASH,EAAmB7E,EAAoBD,GAC5C,IAAIlD,EACJ,OAAQkD,GACJ,IAAK,cACDlD,EAAUmD,EAASoF,cACnB,MACJ,IAAK,OACDvI,EAAUmD,EAASqF,OACnB,MACJ,IAAK,OACL,IAAK,WACL,IAAK,OACD,MAAM,IAAI3J,MAAM,GAAGqE,uBACvB,QACIlD,EAAUmD,EAASqF,OAI3B,OAAOxI,EC5JJ,MAAMyI,UAAsBxI,EAG/B,YAAmBuC,GACfvD,QACAnB,KAAKuI,EAAU7D,EAIZ,KAAKuE,GAER,OAAIA,EAAQC,aAAeD,EAAQC,YAAYC,QACpCyB,QAAQC,OAAO,IAAIvJ,GAGzB2H,EAAQ1G,OAGR0G,EAAQ7G,IAIN,IAAIwI,SAAsB,CAACE,EAASD,KACvC,MAAME,EAAM,IAAIC,eAEhBD,EAAIE,KAAKhC,EAAQ1G,OAAS0G,EAAQ7G,KAAM,GACxC2I,EAAIvF,qBAA8C0F,IAA5BjC,EAAQzD,iBAAuCyD,EAAQzD,gBAC7EuF,EAAII,iBAAiB,mBAAoB,kBAEzCJ,EAAII,iBAAiB,eAAgB,4BAErC,MAAMrG,EAAUmE,EAAQnE,QACpBA,GACApF,OAAO0L,KAAKtG,GACPd,SAASqH,IACNN,EAAII,iBAAiBE,EAAQvG,EAAQuG,OAI7CpC,EAAQ7D,eACR2F,EAAI3F,aAAe6D,EAAQ7D,cAG3B6D,EAAQC,cACRD,EAAQC,YAAYG,QAAU,KAC1B0B,EAAIzB,QACJuB,EAAO,IAAIvJ,KAIf2H,EAAQ1D,UACRwF,EAAIxF,QAAU0D,EAAQ1D,SAG1BwF,EAAIO,OAAS,KACLrC,EAAQC,cACRD,EAAQC,YAAYG,QAAU,MAG9B0B,EAAIZ,QAAU,KAAOY,EAAIZ,OAAS,IAClCW,EAAQ,IAAI9I,EAAa+I,EAAIZ,OAAQY,EAAI9I,WAAY8I,EAAI1F,UAAY0F,EAAIQ,eAEzEV,EAAO,IAAI/J,EAAUiK,EAAI1F,UAAY0F,EAAIQ,cAAgBR,EAAI9I,WAAY8I,EAAIZ,UAIrFY,EAAIS,QAAU,KACVxL,KAAKuI,EAAQtD,IAAIpE,EAASmG,QAAS,4BAA4B+D,EAAIZ,WAAWY,EAAI9I,eAClF4I,EAAO,IAAI/J,EAAUiK,EAAI9I,WAAY8I,EAAIZ,UAG7CY,EAAIU,UAAY,KACZzL,KAAKuI,EAAQtD,IAAIpE,EAASmG,QAAS,8BACnC6D,EAAO,IAAIxJ,IAGf0J,EAAIzI,KAAK2G,EAAQ/G,SAAW,OAzDrB0I,QAAQC,OAAO,IAAI9J,MAAM,oBAHzB6J,QAAQC,OAAO,IAAI9J,MAAM,wBCZrC,MAAM2K,UAA0BvJ,EAInC,YAAmBuC,GAGf,GAFAvD,QAEqB,oBAAVqH,OAAyBtF,EAASmE,OACzCrH,KAAK2L,EAAc,IAAIrD,EAAgB5D,OACpC,IAA8B,oBAAnBsG,eAGd,MAAM,IAAIjK,MAAM,+BAFhBf,KAAK2L,EAAc,IAAIhB,EAAcjG,IAOtC,KAAKuE,GAER,OAAIA,EAAQC,aAAeD,EAAQC,YAAYC,QACpCyB,QAAQC,OAAO,IAAIvJ,GAGzB2H,EAAQ1G,OAGR0G,EAAQ7G,IAINpC,KAAK2L,EAAYrJ,KAAK2G,GAHlB2B,QAAQC,OAAO,IAAI9J,MAAM,oBAHzB6J,QAAQC,OAAO,IAAI9J,MAAM,uBASjC,gBAAgBqB,GACnB,OAAOpC,KAAK2L,EAAYC,gBAAgBxJ,ICxCzC,MAAMyJ,EAIF,aAAaC,GAChB,MAAO,GAAGA,IAASD,EAAkBE,kBAGlC,aAAaC,GAChB,GAAIA,EAAMA,EAAM5H,OAAS,KAAOyH,EAAkBE,gBAC9C,MAAM,IAAIhL,MAAM,0BAGpB,MAAMkL,EAAWD,EAAMjE,MAAM8D,EAAkBE,iBAE/C,OADAE,EAASC,MACFD,GAdG,EAAAE,oBAAsB,GACtB,EAAAJ,gBAAkBK,OAAOC,aAAaR,EAAkBM,qBCYnE,MAAMG,EAEF,sBAAsBC,GACzB,OAAOV,EAAkBW,MAAMC,KAAKC,UAAUH,IAG3C,uBAAuB/I,GAC1B,IAAImJ,EACAC,EAEJ,GAAIjJ,EAAcH,GAAO,CAErB,MAAMqJ,EAAa,IAAI/I,WAAWN,GAC5BsJ,EAAiBD,EAAW7G,QAAQ6F,EAAkBM,qBAC5D,IAAwB,IAApBW,EACA,MAAM,IAAI/L,MAAM,0BAKpB,MAAMgM,EAAiBD,EAAiB,EACxCH,EAAcP,OAAOC,aAAaW,MAAM,KAAMC,MAAM3M,UAAU4M,MAAM1M,KAAKqM,EAAWK,MAAM,EAAGH,KAC7FH,EAAiBC,EAAWjJ,WAAamJ,EAAkBF,EAAWK,MAAMH,GAAgBI,OAAS,SAClG,CACH,MAAMC,EAAmB5J,EACnBsJ,EAAiBM,EAASpH,QAAQ6F,EAAkBE,iBAC1D,IAAwB,IAApBe,EACA,MAAM,IAAI/L,MAAM,0BAKpB,MAAMgM,EAAiBD,EAAiB,EACxCH,EAAcS,EAASC,UAAU,EAAGN,GACpCH,EAAiBQ,EAAShJ,OAAS2I,EAAkBK,EAASC,UAAUN,GAAkB,KAI9F,MAAMd,EAAWJ,EAAkByB,MAAMX,GACnCtH,EAAWoH,KAAKa,MAAMrB,EAAS,IACrC,GAAI5G,EAASkI,KACT,MAAM,IAAIxM,MAAM,kDAMpB,MAAO,CAAC6L,EAJ0CvH,ICvD1D,IAAYmI,ECSAC,GDTZ,SAAYD,GAER,+BAEA,+BAEA,+BAEA,2CAEA,2CAEA,mBAEA,qBAdJ,CAAYA,MAAW,KEAhB,MAAME,EAOT,cACI1N,KAAK+F,UAAY,GAGd,KAAK4H,GACR,IAAK,MAAMhI,KAAY3F,KAAK+F,UACxBJ,EAASiI,KAAKD,GAIf,MAAME,GACT,IAAK,MAAMlI,KAAY3F,KAAK+F,UACpBJ,EAASoB,OACTpB,EAASoB,MAAM8G,GAKpB,WACH,IAAK,MAAMlI,KAAY3F,KAAK+F,UACpBJ,EAASmI,UACTnI,EAASmI,WAKd,UAAUnI,GAEb,OADA3F,KAAK+F,UAAUgI,KAAKpI,GACb,IAAIF,EAAoBzF,KAAM2F,KD1B7C,SAAY8H,GAER,8BAEA,0BAEA,wBAEA,gCAEA,8BAVJ,CAAYA,MAAkB,KAcvB,MAAMO,EAmET,YAAoBC,EAAyBvJ,EAAiBwJ,EAAwBC,GAvC9E,KAAAC,EAAyB,EASzB,KAAAC,EAAuB,KAE3BrO,KAAKuI,EAAQtD,IAAIpE,EAASmG,QAAS,yNA6BnCnE,EAAIyL,WAAWL,EAAY,cAC3BpL,EAAIyL,WAAW5J,EAAQ,UACvB7B,EAAIyL,WAAWJ,EAAU,YAEzBlO,KAAKuO,4BA1FyB,IA2F9BvO,KAAKwO,gCA1F+B,KA4FpCxO,KAAKuI,EAAU7D,EACf1E,KAAKyO,EAAYP,EACjBlO,KAAKiO,WAAaA,EAClBjO,KAAK0O,EAAmBP,EACxBnO,KAAK2O,EAAqB,IAAIrC,EAE9BtM,KAAKiO,WAAWW,UAAapL,GAAcxD,KAAK6O,EAAqBrL,GACrExD,KAAKiO,WAAWa,QAAW/H,GAAkB/G,KAAK+O,EAAkBhI,GAEpE/G,KAAKgP,EAAa,GAClBhP,KAAKiP,EAAW,GAChBjP,KAAKkP,EAAmB,GACxBlP,KAAKmP,EAAyB,GAC9BnP,KAAKoP,EAAwB,GAC7BpP,KAAKqP,EAAgB,EACrBrP,KAAKsP,GAA6B,EAClCtP,KAAKuP,EAAmB9B,EAAmB+B,aAC3CxP,KAAKyP,GAAqB,EAE1BzP,KAAK0P,EAAqB1P,KAAKyO,EAAUkB,aAAa,CAAEpC,KAAMC,EAAYoC,OA/BvE,cAAc3B,EAAyBvJ,EAAiBwJ,EAAwBC,GACnF,OAAO,IAAIH,EAAcC,EAAYvJ,EAAQwJ,EAAUC,GAkC3D,YACI,OAAOnO,KAAKuP,EAMhB,mBACI,OAAOvP,KAAKiO,YAAcjO,KAAKiO,WAAW4B,cAAwB,KAItE,cACI,OAAO7P,KAAKiO,WAAW6B,SAAW,GAQtC,YAAY1N,GACR,GAAIpC,KAAKuP,IAAqB9B,EAAmB+B,cAAgBxP,KAAKuP,IAAqB9B,EAAmBsC,aAC1G,MAAM,IAAIhP,MAAM,0FAGpB,IAAKqB,EACD,MAAM,IAAIrB,MAAM,8CAGpBf,KAAKiO,WAAW6B,QAAU1N,EAOvB,QAEH,OADApC,KAAKgQ,EAAgBhQ,KAAKiQ,IACnBjQ,KAAKgQ,EAGR,UACJ,GAAIhQ,KAAKuP,IAAqB9B,EAAmB+B,aAC7C,OAAO5E,QAAQC,OAAO,IAAI9J,MAAM,0EAGpCf,KAAKuP,EAAmB9B,EAAmByC,WAC3ClQ,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,2BAEjC,UACUnQ,KAAKoQ,IAEPlN,EAASE,WAETjD,OAAOgD,SAASkN,iBAAiB,SAAUrQ,KAAKqO,GAGpDrO,KAAKuP,EAAmB9B,EAAmB6C,UAC3CtQ,KAAKyP,GAAqB,EAC1BzP,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,yCACnC,MAAOjQ,GAGL,OAFAF,KAAKuP,EAAmB9B,EAAmB+B,aAC3CxP,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,gEAAgEjQ,OAC1F0K,QAAQC,OAAO3K,IAItB,UACJF,KAAKuQ,OAAwBrF,EAC7BlL,KAAKsP,GAA6B,EAElC,MAAMkB,EAAmB,IAAI5F,SAAQ,CAACE,EAASD,KAC3C7K,KAAKyQ,EAAqB3F,EAC1B9K,KAAK0Q,EAAqB7F,WAGxB7K,KAAKiO,WAAW0C,MAAM3Q,KAAKyO,EAAUmC,gBAE3C,IACI,MAAMrE,EAA4C,CAC9C2B,SAAUlO,KAAKyO,EAAU1L,KACzB0E,QAASzH,KAAKyO,EAAUhH,SAmB5B,GAhBAzH,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,oCAE3BnQ,KAAK6Q,EAAa7Q,KAAK2O,EAAmBmC,sBAAsBvE,IAEtEvM,KAAKuI,EAAQtD,IAAIpE,EAASqG,YAAa,sBAAsBlH,KAAKyO,EAAU1L,UAG5E/C,KAAK+Q,IACL/Q,KAAKgR,IACLhR,KAAKiR,UAECT,EAKFxQ,KAAKuQ,EAKL,MAAMvQ,KAAKuQ,EAEjB,MAAOrQ,GASL,MARAF,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,oCAAoCjQ,8CAErEF,KAAK+Q,IACL/Q,KAAKkR,UAIClR,KAAKiO,WAAWkD,KAAKjR,GACrBA,GAQP,aAEH,MAAMkR,EAAepR,KAAKgQ,EAE1BhQ,KAAKqR,GAAerR,KAAKsR,WACnBtR,KAAKqR,GAEX,UAEUD,EACR,MAAOlR,KAKL,GAAc6G,GAClB,OAAI/G,KAAKuP,IAAqB9B,EAAmB+B,cAC7CxP,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,8BAA8BpJ,+DACxD6D,QAAQE,WAGf9K,KAAKuP,IAAqB9B,EAAmB8D,eAC7CvR,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,+BAA+BpJ,4EACzD/G,KAAKqR,KAGhBrR,KAAKuP,EAAmB9B,EAAmB8D,cAE3CvR,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,2BAE7BnQ,KAAKwR,IAILxR,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,iEAEjCnG,aAAahK,KAAKwR,IAClBxR,KAAKwR,QAAwBtG,EAE7BlL,KAAKyR,KACE7G,QAAQE,YAGnB9K,KAAK+Q,IACL/Q,KAAKkR,IACLlR,KAAKuQ,EAAwBxJ,GAAS,IAAIhG,MAAM,uEAKzCf,KAAKiO,WAAWkD,KAAKpK,KAUzB,OAAgB2K,KAAuBC,GAC1C,MAAOC,EAASC,GAAa7R,KAAK8R,GAAwBH,GACpDI,EAAuB/R,KAAKgS,GAAwBN,EAAYC,EAAME,GAG5E,IAAII,EAEJ,MAAMvM,EAAU,IAAIgI,EAqCpB,OApCAhI,EAAQQ,eAAiB,KACrB,MAAMgM,EAA4ClS,KAAKmS,GAAwBJ,EAAqBK,cAIpG,cAFOpS,KAAKgP,EAAW+C,EAAqBK,cAErCH,EAAaI,MAAK,IACdrS,KAAKsS,GAAkBJ,MAItClS,KAAKgP,EAAW+C,EAAqBK,cAAgB,CAACG,EAA+DxL,KAC7GA,EACArB,EAAQqB,MAAMA,GAEPwL,IAEHA,EAAgBhF,OAASC,EAAYgF,WACjCD,EAAgBxL,MAChBrB,EAAQqB,MAAM,IAAIhG,MAAMwR,EAAgBxL,QAExCrB,EAAQoI,WAGZpI,EAAQkI,KAAM2E,EAAoB,QAK9CN,EAAejS,KAAKsS,GAAkBP,GACjC5L,OAAOjG,IACJwF,EAAQqB,MAAM7G,UACPF,KAAKgP,EAAW+C,EAAqBK,iBAGpDpS,KAAKyS,GAAeb,EAASK,GAEtBvM,EAGH,EAAalE,GAEjB,OADAxB,KAAKiR,IACEjR,KAAKiO,WAAW3L,KAAKd,GAOxB,GAAkBA,GACtB,OAAOxB,KAAK6Q,EAAa7Q,KAAKyO,EAAUkB,aAAanO,IAYlD,KAAKkQ,KAAuBC,GAC/B,MAAOC,EAASC,GAAa7R,KAAK8R,GAAwBH,GACpDe,EAAc1S,KAAKsS,GAAkBtS,KAAK2S,GAAkBjB,EAAYC,GAAM,EAAME,IAI1F,OAFA7R,KAAKyS,GAAeb,EAASc,GAEtBA,EAcJ,OAAgBhB,KAAuBC,GAC1C,MAAOC,EAASC,GAAa7R,KAAK8R,GAAwBH,GACpDI,EAAuB/R,KAAK2S,GAAkBjB,EAAYC,GAAM,EAAOE,GAgC7E,OA9BU,IAAIjH,SAAa,CAACE,EAASD,KAEjC7K,KAAKgP,EAAW+C,EAAqBK,cAAiB,CAACG,EAA+DxL,KAC9GA,EACA8D,EAAO9D,GAEAwL,IAEHA,EAAgBhF,OAASC,EAAYgF,WACjCD,EAAgBxL,MAChB8D,EAAO,IAAI9J,MAAMwR,EAAgBxL,QAEjC+D,EAAQyH,EAAgBK,QAG5B/H,EAAO,IAAI9J,MAAM,4BAA4BwR,EAAgBhF,WAKzE,MAAM0E,EAAejS,KAAKsS,GAAkBP,GACvC5L,OAAOjG,IACJ2K,EAAO3K,UAEAF,KAAKgP,EAAW+C,EAAqBK,iBAGpDpS,KAAKyS,GAAeb,EAASK,MAW9B,GAAGP,EAAoBmB,GACrBnB,GAAemB,IAIpBnB,EAAaA,EAAWoB,cACnB9S,KAAKiP,EAASyC,KACf1R,KAAKiP,EAASyC,GAAc,KAIsB,IAAlD1R,KAAKiP,EAASyC,GAAY1L,QAAQ6M,IAItC7S,KAAKiP,EAASyC,GAAY3D,KAAK8E,IAkB5B,IAAInB,EAAoBnP,GAC3B,IAAKmP,EACD,OAGJA,EAAaA,EAAWoB,cACxB,MAAMC,EAAW/S,KAAKiP,EAASyC,GAC/B,GAAKqB,EAGL,GAAIxQ,EAAQ,CACR,MAAMyQ,EAAYD,EAAS/M,QAAQzD,IAChB,IAAfyQ,IACAD,EAAS9M,OAAO+M,EAAW,GACH,IAApBD,EAAS3O,eACFpE,KAAKiP,EAASyC,gBAItB1R,KAAKiP,EAASyC,GAStB,QAAQuB,GACPA,GACAjT,KAAKkP,EAAiBnB,KAAKkF,GAQ5B,eAAeA,GACdA,GACAjT,KAAKmP,EAAuBpB,KAAKkF,GAQlC,cAAcA,GACbA,GACAjT,KAAKoP,EAAsBrB,KAAKkF,GAIhC,EAAqBzP,GASzB,GARAxD,KAAK+Q,IAEA/Q,KAAKsP,IACN9L,EAAOxD,KAAKkT,GAA0B1P,GACtCxD,KAAKsP,GAA6B,GAIlC9L,EAAM,CAEN,MAAMyI,EAAWjM,KAAKyO,EAAU0E,cAAc3P,EAAMxD,KAAKuI,GAEzD,IAAK,MAAM/G,KAAWyK,EAClB,OAAQzK,EAAQ+L,MACZ,KAAKC,EAAY4F,WACbpT,KAAKqT,GAAoB7R,GACzB,MACJ,KAAKgM,EAAY8F,WACjB,KAAK9F,EAAYgF,WAAY,CACzB,MAAMS,EAAWjT,KAAKgP,EAAWxN,EAAQ4Q,cACzC,GAAIa,EAAU,CACNzR,EAAQ+L,OAASC,EAAYgF,mBACtBxS,KAAKgP,EAAWxN,EAAQ4Q,cAEnC,IACIa,EAASzR,GACX,MAAOtB,GACLF,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAO,gCAAgCqH,EAAelI,OAGxF,MAEJ,KAAKsN,EAAYoC,KAEb,MACJ,KAAKpC,EAAY+F,MAAO,CACpBvT,KAAKuI,EAAQtD,IAAIpE,EAASqG,YAAa,uCAEvC,MAAMH,EAAQvF,EAAQuF,MAAQ,IAAIhG,MAAM,sCAAwCS,EAAQuF,YAASmE,GAElE,IAA3B1J,EAAQgS,eAKRxT,KAAKiO,WAAWkD,KAAKpK,GAGrB/G,KAAKqR,GAAerR,KAAKsR,GAAcvK,GAG3C,MAEJ,QACI/G,KAAKuI,EAAQtD,IAAIpE,EAASmG,QAAS,yBAAyBxF,EAAQ+L,UAMpFvN,KAAKgR,IAGD,GAA0BxN,GAC9B,IAAIiQ,EACA7G,EAEJ,KACKA,EAAe6G,GAAmBzT,KAAK2O,EAAmB+E,uBAAuBlQ,GACpF,MAAOtD,GACL,MAAMsB,EAAU,qCAAuCtB,EACvDF,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAOS,GAEjC,MAAMuF,EAAQ,IAAIhG,MAAMS,GAExB,MADAxB,KAAK0Q,EAAmB3J,GAClBA,EAEV,GAAI0M,EAAgB1M,MAAO,CACvB,MAAMvF,EAAU,oCAAsCiS,EAAgB1M,MACtE/G,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAOS,GAEjC,MAAMuF,EAAQ,IAAIhG,MAAMS,GAExB,MADAxB,KAAK0Q,EAAmB3J,GAClBA,EAMV,OAJI/G,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,8BAGrCnQ,KAAKyQ,IACE7D,EAGH,IACA5M,KAAKiO,WAAW0F,SAASC,oBAM7B5T,KAAKoO,GAAiB,IAAIxH,MAAOiN,UAAY7T,KAAKwO,gCAElDxO,KAAKkR,KAGD,IACJ,KAAKlR,KAAKiO,WAAW0F,UAAa3T,KAAKiO,WAAW0F,SAASC,oBAEvD5T,KAAK8T,GAAiBrK,YAAW,IAAMzJ,KAAK+T,iBAAiB/T,KAAKuO,kCAGnCrD,IAA3BlL,KAAKgU,KACT,CACI,IAAIC,EAAWjU,KAAKoO,GAAiB,IAAIxH,MAAOiN,UAC5CI,EAAW,IACXA,EAAW,GAIfjU,KAAKgU,GAAoBvK,YAAWjF,UAChC,GAAIxE,KAAKuP,IAAqB9B,EAAmB6C,UAC7C,UACUtQ,KAAK6Q,EAAa7Q,KAAK0P,GAC/B,MAGE1P,KAAKkR,OAGd+C,IAMP,gBAIJjU,KAAKiO,WAAWkD,KAAK,IAAIpQ,MAAM,wEAG3B,GAAoBmT,GACxB,MAAMC,EAAUnU,KAAKiP,EAASiF,EAAkBE,OAAOtB,eACvD,GAAIqB,EAAS,CACT,IACIA,EAAQnQ,SAASqQ,GAAMA,EAAErH,MAAMhN,KAAMkU,EAAkBI,aACzD,MAAOpU,GACLF,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAO,6BAA6BmT,EAAkBE,OAAOtB,8BAA8B5S,OAGzH,GAAIgU,EAAkB9B,aAAc,CAEhC,MAAM5Q,EAAU,qFAChBxB,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAOS,GAGjCxB,KAAKqR,GAAerR,KAAKsR,GAAc,IAAIvQ,MAAMS,UAGrDxB,KAAKuI,EAAQtD,IAAIpE,EAASmG,QAAS,mCAAmCkN,EAAkBE,kBAIxF,EAAkBrN,GACtB/G,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,kCAAkCpJ,4BAAgC/G,KAAKuP,MAGxGvP,KAAKuQ,EAAwBvQ,KAAKuQ,GAAyBxJ,GAAS,IAAIhG,MAAM,iFAI1Ef,KAAKyQ,GACLzQ,KAAKyQ,IAGTzQ,KAAKuU,GAA0BxN,GAAS,IAAIhG,MAAM,uEAElDf,KAAK+Q,IACL/Q,KAAKkR,IAEDlR,KAAKuP,IAAqB9B,EAAmB8D,cAC7CvR,KAAKyR,GAAe1K,GACb/G,KAAKuP,IAAqB9B,EAAmB6C,WAAatQ,KAAK0O,EAEtE1O,KAAKwU,GAAWzN,GACT/G,KAAKuP,IAAqB9B,EAAmB6C,WACpDtQ,KAAKyR,GAAe1K,GAUpB,GAAeA,GACnB,GAAI/G,KAAKyP,EAAoB,CACzBzP,KAAKuP,EAAmB9B,EAAmB+B,aAC3CxP,KAAKyP,GAAqB,EAEtBvM,EAASE,WACTjD,OAAOgD,SAASsR,oBAAoB,SAAUzU,KAAKqO,GAGvD,IACIrO,KAAKkP,EAAiBlL,SAASuG,GAAMA,EAAEyC,MAAMhN,KAAM,CAAC+G,MACtD,MAAO7G,GACLF,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAO,0CAA0CgG,mBAAuB7G,SAKtG,SAAiB6G,GACrB,MAAM2N,EAAqB9N,KAAK+N,MAChC,IAAIC,EAA4B,EAC5BC,OAAuB3J,IAAVnE,EAAsBA,EAAQ,IAAIhG,MAAM,mDAErD+T,EAAiB9U,KAAK+U,GAAmBH,IAA6B,EAAGC,GAE7E,GAAuB,OAAnBC,EAGA,OAFA9U,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,2GACjCnQ,KAAKyR,GAAe1K,GAYxB,GARA/G,KAAKuP,EAAmB9B,EAAmBsC,aAEvChJ,EACA/G,KAAKuI,EAAQtD,IAAIpE,EAASqG,YAAa,6CAA6CH,OAEpF/G,KAAKuI,EAAQtD,IAAIpE,EAASqG,YAAa,4BAGA,IAAvClH,KAAKmP,EAAuB/K,OAAc,CAC1C,IACIpE,KAAKmP,EAAuBnL,SAASuG,GAAMA,EAAEyC,MAAMhN,KAAM,CAAC+G,MAC5D,MAAO7G,GACLF,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAO,iDAAiDgG,mBAAuB7G,OAI7G,GAAIF,KAAKuP,IAAqB9B,EAAmBsC,aAE7C,YADA/P,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,yFAKzC,KAA0B,OAAnB2E,GAAyB,CAQ5B,GAPA9U,KAAKuI,EAAQtD,IAAIpE,EAASqG,YAAa,4BAA4B0N,mBAA2CE,eAExG,IAAIlK,SAASE,IACf9K,KAAKwR,GAAwB/H,WAAWqB,EAASgK,MAErD9U,KAAKwR,QAAwBtG,EAEzBlL,KAAKuP,IAAqB9B,EAAmBsC,aAE7C,YADA/P,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,qFAIrC,IAMI,SALMnQ,KAAKoQ,IAEXpQ,KAAKuP,EAAmB9B,EAAmB6C,UAC3CtQ,KAAKuI,EAAQtD,IAAIpE,EAASqG,YAAa,2CAEG,IAAtClH,KAAKoP,EAAsBhL,OAC3B,IACIpE,KAAKoP,EAAsBpL,SAASuG,GAAMA,EAAEyC,MAAMhN,KAAM,CAACA,KAAKiO,WAAW4B,iBAC3E,MAAO3P,GACLF,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAO,uDAAuDf,KAAKiO,WAAW4B,8BAA8B3P,OAI9I,OACF,MAAOA,GAGL,GAFAF,KAAKuI,EAAQtD,IAAIpE,EAASqG,YAAa,8CAA8ChH,OAEjFF,KAAKuP,IAAqB9B,EAAmBsC,aAM7C,OALA/P,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,4BAA4BnQ,KAAKuP,oFAE9DvP,KAAKuP,IAA4B9B,EAAmB8D,eACpDvR,KAAKyR,MAKboD,EAAa3U,aAAaa,MAAQb,EAAI,IAAIa,MAAMb,EAAEgE,YAClD4Q,EAAiB9U,KAAK+U,GAAmBH,IAA6BhO,KAAK+N,MAAQD,EAAoBG,IAI/G7U,KAAKuI,EAAQtD,IAAIpE,EAASqG,YAAa,+CAA+CN,KAAK+N,MAAQD,YAA6BE,gDAEhI5U,KAAKyR,KAGD,GAAmBuD,EAA4BC,EAA6BC,GAChF,IACI,OAAOlV,KAAK0O,EAAkByG,6BAA6B,CACvDF,sBACAD,qBACAE,gBAEN,MAAOhV,GAEL,OADAF,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAO,6CAA6CiU,MAAuBC,mBAAqC/U,OACnI,MAIP,GAA0B6G,GAC9B,MAAMqO,EAAYpV,KAAKgP,EACvBhP,KAAKgP,EAAa,GAElBtP,OAAO0L,KAAKgK,GACPpR,SAASxE,IACN,MAAMyT,EAAWmC,EAAU5V,GAC3B,IACIyT,EAAS,KAAMlM,GACjB,MAAO7G,GACLF,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAO,wCAAwCgG,mBAAuBqB,EAAelI,UAKvH,IACAF,KAAKgU,KACLhK,aAAahK,KAAKgU,IAClBhU,KAAKgU,QAAoB9I,GAIzB,IACAlL,KAAK8T,IACL9J,aAAahK,KAAK8T,IAIlB,GAAkBpC,EAAoBC,EAAa0D,EAAsBxD,GAC7E,GAAIwD,EACA,OAAyB,IAArBxD,EAAUzN,OACH,CACHkQ,UAAW3C,EACXE,YACAuC,OAAQ1C,EACRnE,KAAMC,EAAY4F,YAGf,CACHkB,UAAW3C,EACXyC,OAAQ1C,EACRnE,KAAMC,EAAY4F,YAGvB,CACH,MAAMhB,EAAepS,KAAKqP,EAG1B,OAFArP,KAAKqP,IAEoB,IAArBwC,EAAUzN,OACH,CACHkQ,UAAW3C,EACXS,aAAcA,EAAalO,WAC3B2N,YACAuC,OAAQ1C,EACRnE,KAAMC,EAAY4F,YAGf,CACHkB,UAAW3C,EACXS,aAAcA,EAAalO,WAC3BkQ,OAAQ1C,EACRnE,KAAMC,EAAY4F,aAM1B,GAAexB,EAA+BK,GAClD,GAAuB,IAAnBL,EAAQxN,OAAZ,CAKK6N,IACDA,EAAerH,QAAQE,WAK3B,IAAK,MAAMwK,KAAY1D,EACnBA,EAAQ0D,GAAUC,UAAU,CACxBzH,SAAU,KACNmE,EAAeA,EAAaI,MAAK,IAAMrS,KAAKsS,GAAkBtS,KAAKwV,GAAyBF,OAEhGvO,MAAQ8G,IACJ,IAAIrM,EAEAA,EADAqM,aAAe9M,MACL8M,EAAIrM,QACPqM,GAAOA,EAAI3J,SACR2J,EAAI3J,WAEJ,gBAGd+N,EAAeA,EAAaI,MAAK,IAAMrS,KAAKsS,GAAkBtS,KAAKwV,GAAyBF,EAAU9T,OAE1GoM,KAAOD,IACHsE,EAAeA,EAAaI,MAAK,IAAMrS,KAAKsS,GAAkBtS,KAAKyV,GAAyBH,EAAU3H,UAM9G,GAAwBgE,GAC5B,MAAMC,EAAgC,GAChCC,EAAsB,GAC5B,IAAK,IAAI6D,EAAI,EAAGA,EAAI/D,EAAKvN,OAAQsR,IAAK,CAClC,MAAMC,EAAWhE,EAAK+D,GACtB,GAAI1V,KAAK4V,GAAcD,GAAW,CAC9B,MAAML,EAAWtV,KAAKqP,EACtBrP,KAAKqP,IAELuC,EAAQ0D,GAAYK,EACpB9D,EAAU9D,KAAKuH,EAASpR,YAGxByN,EAAK1L,OAAOyP,EAAG,IAIvB,MAAO,CAAC9D,EAASC,GAGb,GAAcgE,GAElB,OAAOA,GAAOA,EAAIN,WAAsC,mBAAlBM,EAAIN,UAGtC,GAAwB7D,EAAoBC,EAAaE,GAC7D,MAAMO,EAAepS,KAAKqP,EAG1B,OAFArP,KAAKqP,IAEoB,IAArBwC,EAAUzN,OACH,CACHkQ,UAAW3C,EACXS,aAAcA,EAAalO,WAC3B2N,YACAuC,OAAQ1C,EACRnE,KAAMC,EAAYsI,kBAGf,CACHxB,UAAW3C,EACXS,aAAcA,EAAalO,WAC3BkQ,OAAQ1C,EACRnE,KAAMC,EAAYsI,kBAKtB,GAAwBC,GAC5B,MAAO,CACH3D,aAAc2D,EACdxI,KAAMC,EAAYwI,kBAIlB,GAAyBD,EAAYpI,GACzC,MAAO,CACHyE,aAAc2D,EACdpI,OACAJ,KAAMC,EAAY8F,YAIlB,GAAyByC,EAAYhP,EAAa6L,GACtD,OAAI7L,EACO,CACHA,QACAqL,aAAc2D,EACdxI,KAAMC,EAAYgF,YAInB,CACHJ,aAAc2D,EACdnD,SACArF,KAAMC,EAAYgF,aEt/B9B,MAAMyD,EAAuC,CAAC,EAAG,IAAM,IAAO,IAAO,MAG9D,MAAMC,EAGT,YAAYC,GACRnW,KAAKoW,QAA+BlL,IAAhBiL,EAA4B,IAAIA,EAAa,MAAQF,EAGtE,6BAA6BI,GAChC,OAAOrW,KAAKoW,GAAaC,EAAarB,qBCdvC,MAAesB,GCEtB,IAAYC,EAYAC,EDbQ,EAAAC,cAAgB,gBAChB,EAAAC,OAAS,SCA7B,SAAYH,GAER,mBAEA,+BAEA,2CAEA,iCARJ,CAAYA,MAAiB,KAY7B,SAAYC,GAER,mBAEA,uBAJJ,CAAYA,MAAc,KCRnB,MAAM,EAAb,cACY,KAAAG,IAAsB,EACvB,KAAAtN,QAA+B,KAE/B,QACErJ,KAAK2W,KACN3W,KAAK2W,IAAa,EACd3W,KAAKqJ,SACLrJ,KAAKqJ,WAKjB,aACI,OAAOrJ,KAGX,cACI,OAAOA,KAAK2W,ICbb,MAAMC,EAoBT,YAAYhS,EAAwBC,EAAkEH,EAAiBrC,GACnHrC,KAAK2L,EAAc/G,EACnB5E,KAAK6W,GAAsBhS,EAC3B7E,KAAKuI,EAAU7D,EACf1E,KAAK8W,GAAa,IAAI,EACtB9W,KAAK+W,GAAW1U,EAEhBrC,KAAKgX,IAAW,EAEhBhX,KAAK4O,UAAY,KACjB5O,KAAK8O,QAAU,KAdnB,kBACI,OAAO9O,KAAK8W,GAAW3N,QAgBpB,cAAc/G,EAAawO,GAU9B,GATA/N,EAAIyL,WAAWlM,EAAK,OACpBS,EAAIyL,WAAWsC,EAAgB,kBAC/B/N,EAAIoU,KAAKrG,EAAgB4F,EAAgB,kBAEzCxW,KAAKkX,GAAO9U,EAEZpC,KAAKuI,EAAQtD,IAAIpE,EAASqE,MAAO,uCAG7B0L,IAAmB4F,EAAeW,QACP,oBAAnBnM,gBAA+E,iBAAtC,IAAIA,gBAAiB5F,aACtE,MAAM,IAAIrE,MAAM,8FAGpB,MAAOgC,EAAMnC,GAASoE,IAChBF,EAAU,CAAE,CAAC/B,GAAOnC,KAAUZ,KAAK+W,GAASjS,SAE5CsS,EAA2B,CAC7BlO,YAAalJ,KAAK8W,GAAW/M,OAC7BjF,UACAS,QAAS,IACTC,gBAAiBxF,KAAK+W,GAASvR,iBAG/BoL,IAAmB4F,EAAeW,SAClCC,EAAYhS,aAAe,eAG/B,MAAML,QAAc/E,KAAKqX,KACzBrX,KAAKsX,GAAmBF,EAAarS,GAIrC,MAAMwS,EAAU,GAAGnV,OAASwE,KAAK+N,QACjC3U,KAAKuI,EAAQtD,IAAIpE,EAASqE,MAAO,oCAAoCqS,MACrE,MAAMlS,QAAiBrF,KAAK2L,EAAY9L,IAAI0X,EAASH,GACzB,MAAxB/R,EAASpE,YACTjB,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAO,qDAAqDsE,EAASpE,eAG/FjB,KAAKwX,GAAc,IAAI1W,EAAUuE,EAASpD,YAAc,GAAIoD,EAASpE,YACrEjB,KAAKgX,IAAW,GAEhBhX,KAAKgX,IAAW,EAGpBhX,KAAKyX,GAAazX,KAAK0X,GAAM1X,KAAKkX,GAAME,GAGpC,WACJ,OAAIpX,KAAK6W,SACQ7W,KAAK6W,KAGf,KAGH,GAAmB5N,EAAsBlE,GACxCkE,EAAQnE,UACTmE,EAAQnE,QAAU,IAElBC,EACAkE,EAAQnE,QAAQwR,EAAYG,eAAiB,UAAU1R,IAGvDkE,EAAQnE,QAAQwR,EAAYG,uBACrBxN,EAAQnE,QAAQwR,EAAYG,eAInC,SAAYrU,EAAagV,GAC7B,IACI,KAAOpX,KAAKgX,IAAU,CAElB,MAAMjS,QAAc/E,KAAKqX,KACzBrX,KAAKsX,GAAmBF,EAAarS,GAErC,IACI,MAAMwS,EAAU,GAAGnV,OAASwE,KAAK+N,QACjC3U,KAAKuI,EAAQtD,IAAIpE,EAASqE,MAAO,oCAAoCqS,MACrE,MAAMlS,QAAiBrF,KAAK2L,EAAY9L,IAAI0X,EAASH,GAEzB,MAAxB/R,EAASpE,YACTjB,KAAKuI,EAAQtD,IAAIpE,EAASqG,YAAa,sDAEvClH,KAAKgX,IAAW,GACe,MAAxB3R,EAASpE,YAChBjB,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAO,qDAAqDsE,EAASpE,eAG/FjB,KAAKwX,GAAc,IAAI1W,EAAUuE,EAASpD,YAAc,GAAIoD,EAASpE,YACrEjB,KAAKgX,IAAW,GAGZ3R,EAASnD,SACTlC,KAAKuI,EAAQtD,IAAIpE,EAASqE,MAAO,0CAA0C3B,EAAc8B,EAASnD,QAASlC,KAAK+W,GAAS5R,uBACrHnF,KAAK4O,WACL5O,KAAK4O,UAAUvJ,EAASnD,UAI5BlC,KAAKuI,EAAQtD,IAAIpE,EAASqE,MAAO,sDAG3C,MAAOhF,GACAF,KAAKgX,GAIF9W,aAAamB,EAEbrB,KAAKuI,EAAQtD,IAAIpE,EAASqE,MAAO,uDAGjClF,KAAKwX,GAActX,EACnBF,KAAKgX,IAAW,GARpBhX,KAAKuI,EAAQtD,IAAIpE,EAASqE,MAAO,wDAAwDhF,EAAEsB,a,QAcvGxB,KAAKuI,EAAQtD,IAAIpE,EAASqE,MAAO,6CAI5BlF,KAAK2X,aACN3X,KAAK4X,MAKV,WAAWpU,GACd,OAAKxD,KAAKgX,GAGHvS,EAAYzE,KAAKuI,EAAS,cAAevI,KAAK2L,EAAa3L,KAAKkX,GAAOlX,KAAK6W,GAAqBrT,EAAMxD,KAAK+W,IAFxGnM,QAAQC,OAAO,IAAI9J,MAAM,iDAKjC,aACHf,KAAKuI,EAAQtD,IAAIpE,EAASqE,MAAO,6CAGjClF,KAAKgX,IAAW,EAChBhX,KAAK8W,GAAWxN,QAEhB,UACUtJ,KAAKyX,GAGXzX,KAAKuI,EAAQtD,IAAIpE,EAASqE,MAAO,qDAAqDlF,KAAKkX,OAE3F,MAAMpS,EAAiC,IAChC/B,EAAMnC,GAASoE,IACtBF,EAAQ/B,GAAQnC,EAEhB,MAAMiX,EAA6B,CAC/B/S,QAAS,IAAKA,KAAY9E,KAAK+W,GAASjS,SACxCS,QAASvF,KAAK+W,GAASxR,QACvBC,gBAAiBxF,KAAK+W,GAASvR,iBAE7BT,QAAc/E,KAAKqX,KACzBrX,KAAKsX,GAAmBO,EAAe9S,SACjC/E,KAAK2L,EAAYmM,OAAO9X,KAAKkX,GAAOW,GAE1C7X,KAAKuI,EAAQtD,IAAIpE,EAASqE,MAAO,gD,QAEjClF,KAAKuI,EAAQtD,IAAIpE,EAASqE,MAAO,0CAIjClF,KAAK4X,MAIL,KACJ,GAAI5X,KAAK8O,QAAS,CACd,IAAIiJ,EAAa,gDACb/X,KAAKwX,KACLO,GAAc,WAAa/X,KAAKwX,IAEpCxX,KAAKuI,EAAQtD,IAAIpE,EAASqE,MAAO6S,GACjC/X,KAAK8O,QAAQ9O,KAAKwX,MC1NvB,MAAMQ,EAWT,YAAYpT,EAAwBC,EAAkEH,EAC1FrC,GACRrC,KAAK2L,EAAc/G,EACnB5E,KAAK6W,GAAsBhS,EAC3B7E,KAAKuI,EAAU7D,EACf1E,KAAK+W,GAAW1U,EAEhBrC,KAAK4O,UAAY,KACjB5O,KAAK8O,QAAU,KAGZ,cAAc1M,EAAawO,GAU9B,GATA/N,EAAIyL,WAAWlM,EAAK,OACpBS,EAAIyL,WAAWsC,EAAgB,kBAC/B/N,EAAIoU,KAAKrG,EAAgB4F,EAAgB,kBAEzCxW,KAAKuI,EAAQtD,IAAIpE,EAASqE,MAAO,+BAGjClF,KAAKkX,GAAO9U,EAERpC,KAAK6W,GAAqB,CAC1B,MAAM9R,QAAc/E,KAAK6W,KACrB9R,IACA3C,IAAQA,EAAI4D,QAAQ,KAAO,EAAI,IAAM,KAAO,gBAAgBiS,mBAAmBlT,MAIvF,OAAO,IAAI6F,SAAc,CAACE,EAASD,KAC/B,IAMIqN,EANAC,GAAS,EACb,GAAIvH,IAAmB4F,EAAe4B,KAAtC,CAMA,GAAIlV,EAASE,WAAaF,EAASG,YAC/B6U,EAAc,IAAIlY,KAAK+W,GAASsB,YAAajW,EAAK,CAAEoD,gBAAiBxF,KAAK+W,GAASvR,sBAChF,CAEH,MAAM6E,EAAUrK,KAAK2L,EAAYC,gBAAgBxJ,GAC3C0C,EAA0B,GAChCA,EAAQ4R,OAASrM,EACjB,MAAOtH,EAAMnC,GAASoE,IACtBF,EAAQ/B,GAAQnC,EAEhBsX,EAAc,IAAIlY,KAAK+W,GAASsB,YAAajW,EAAK,CAAEoD,gBAAiBxF,KAAK+W,GAASvR,gBAAiBV,QAAS,IAAKA,KAAY9E,KAAK+W,GAASjS,WAGhJ,IACIoT,EAAYI,UAAapY,IACrB,GAAIF,KAAK4O,UACL,IACI5O,KAAKuI,EAAQtD,IAAIpE,EAASqE,MAAO,kCAAkC3B,EAAcrD,EAAEsD,KAAMxD,KAAK+W,GAAS5R,uBACvGnF,KAAK4O,UAAU1O,EAAEsD,MACnB,MAAOuD,GAEL,YADA/G,KAAKuY,GAAOxR,KAOxBmR,EAAY1M,QAAWtL,IAEfiY,EACAnY,KAAKuY,KAEL1N,EAAO,IAAI9J,MAAM,kQAMzBmX,EAAYM,OAAS,KACjBxY,KAAKuI,EAAQtD,IAAIpE,EAASqG,YAAa,oBAAoBlH,KAAKkX,MAChElX,KAAKyY,GAAeP,EACpBC,GAAS,EACTrN,KAEN,MAAO5K,GAEL,YADA2K,EAAO3K,SAlDP2K,EAAO,IAAI9J,MAAM,iFAwDtB,WAAWyC,GACd,OAAKxD,KAAKyY,GAGHhU,EAAYzE,KAAKuI,EAAS,MAAOvI,KAAK2L,EAAa3L,KAAKkX,GAAOlX,KAAK6W,GAAqBrT,EAAMxD,KAAK+W,IAFhGnM,QAAQC,OAAO,IAAI9J,MAAM,iDAKjC,OAEH,OADAf,KAAKuY,KACE3N,QAAQE,UAGX,GAAO5K,GACPF,KAAKyY,KACLzY,KAAKyY,GAAaC,QAClB1Y,KAAKyY,QAAevN,EAEhBlL,KAAK8O,SACL9O,KAAK8O,QAAQ5O,KCnHtB,MAAMyY,EAYT,YAAY/T,EAAwBC,EAAkEH,EAC1FS,EAA4ByT,EAA4C9T,GAChF9E,KAAKuI,EAAU7D,EACf1E,KAAK6W,GAAsBhS,EAC3B7E,KAAK6Y,GAAqB1T,EAC1BnF,KAAK8Y,GAAwBF,EAC7B5Y,KAAK2L,EAAc/G,EAEnB5E,KAAK4O,UAAY,KACjB5O,KAAK8O,QAAU,KACf9O,KAAK+Y,GAAWjU,EAGb,cAAc1C,EAAawO,GAM9B,GALA/N,EAAIyL,WAAWlM,EAAK,OACpBS,EAAIyL,WAAWsC,EAAgB,kBAC/B/N,EAAIoU,KAAKrG,EAAgB4F,EAAgB,kBACzCxW,KAAKuI,EAAQtD,IAAIpE,EAASqE,MAAO,sCAE7BlF,KAAK6W,GAAqB,CAC1B,MAAM9R,QAAc/E,KAAK6W,KACrB9R,IACA3C,IAAQA,EAAI4D,QAAQ,KAAO,EAAI,IAAM,KAAO,gBAAgBiS,mBAAmBlT,MAIvF,OAAO,IAAI6F,SAAc,CAACE,EAASD,KAE/B,IAAImO,EADJ5W,EAAMA,EAAI6W,QAAQ,QAAS,MAE3B,MAAM5O,EAAUrK,KAAK2L,EAAYC,gBAAgBxJ,GACjD,IAAI+V,GAAS,EAEb,GAAIjV,EAASmE,OAAQ,CACjB,MAAMvC,EAAiC,IAChC/B,EAAMnC,GAASoE,IACtBF,EAAQ/B,GAAQnC,EAEZyJ,IACAvF,EAAQwR,EAAYI,QAAU,GAAGrM,KAIrC2O,EAAY,IAAIhZ,KAAK8Y,GAAsB1W,OAAK8I,EAAW,CACvDpG,QAAS,IAAKA,KAAY9E,KAAK+Y,MAIlCC,IAEDA,EAAY,IAAIhZ,KAAK8Y,GAAsB1W,IAG3CwO,IAAmB4F,EAAeW,SAClC6B,EAAUE,WAAa,eAG3BF,EAAUR,OAAUW,IAChBnZ,KAAKuI,EAAQtD,IAAIpE,EAASqG,YAAa,0BAA0B9E,MACjEpC,KAAKoZ,GAAaJ,EAClBb,GAAS,EACTrN,KAGJkO,EAAUxN,QAAW6N,IACjB,IAAItS,EAAa,KAGbA,EADsB,oBAAfuS,YAA8BD,aAAiBC,WAC9CD,EAAMtS,MAEN,wCAGZ/G,KAAKuI,EAAQtD,IAAIpE,EAASqG,YAAa,0BAA0BH,OAGrEiS,EAAUV,UAAa9W,IAEnB,GADAxB,KAAKuI,EAAQtD,IAAIpE,EAASqE,MAAO,yCAAyC3B,EAAc/B,EAAQgC,KAAMxD,KAAK6Y,QACvG7Y,KAAK4O,UACL,IACI5O,KAAK4O,UAAUpN,EAAQgC,MACzB,MAAOuD,GAEL,YADA/G,KAAKuY,GAAOxR,KAMxBiS,EAAUlK,QAAWuK,IAGjB,GAAIlB,EACAnY,KAAKuY,GAAOc,OACT,CACH,IAAItS,EAAa,KAGbA,EADsB,oBAAfuS,YAA8BD,aAAiBC,WAC9CD,EAAMtS,MAEN,iSAMZ8D,EAAO,IAAI9J,MAAMgG,SAM1B,KAAKvD,GACR,OAAIxD,KAAKoZ,IAAcpZ,KAAKoZ,GAAWG,aAAevZ,KAAK8Y,GAAsBU,MAC7ExZ,KAAKuI,EAAQtD,IAAIpE,EAASqE,MAAO,wCAAwC3B,EAAcC,EAAMxD,KAAK6Y,QAClG7Y,KAAKoZ,GAAW9W,KAAKkB,GACdoH,QAAQE,WAGZF,QAAQC,OAAO,sCAGnB,OAOH,OANI7K,KAAKoZ,IAGLpZ,KAAKuY,QAAOrN,GAGTN,QAAQE,UAGX,GAAOuO,GAEPrZ,KAAKoZ,KAELpZ,KAAKoZ,GAAWtK,QAAU,OAC1B9O,KAAKoZ,GAAWd,UAAY,OAC5BtY,KAAKoZ,GAAW5N,QAAU,OAC1BxL,KAAKoZ,GAAWV,QAChB1Y,KAAKoZ,QAAalO,GAGtBlL,KAAKuI,EAAQtD,IAAIpE,EAASqE,MAAO,yCAC7BlF,KAAK8O,WACD9O,KAAKyZ,GAAcJ,KAA8B,IAAnBA,EAAMK,UAAqC,MAAfL,EAAMM,KAEzDN,aAAiBtY,MACxBf,KAAK8O,QAAQuK,GAEbrZ,KAAK8O,UAJL9O,KAAK8O,QAAQ,IAAI/N,MAAM,sCAAsCsY,EAAMM,SAASN,EAAMO,QAAU,yBAShG,GAAcP,GAClB,OAAOA,GAAmC,kBAAnBA,EAAMK,UAAgD,iBAAfL,EAAMM,MCtIrE,MAAME,EA0BT,YAAYzX,EAAaC,EAAkC,IhB6DxD,IAAsBqC,EgBrDrB,GArBI,KAAAoV,GAA4D,OAKpD,KAAAnG,SAAgB,GAMf,KAAAoG,GAA4B,EAGzClX,EAAIyL,WAAWlM,EAAK,OAEpBpC,KAAKuI,OhB2DM2C,KADUxG,EgB1DOrC,EAAQqC,QhB4D7B,IAAI2B,EAAcxF,EAASqG,aAGvB,OAAXxC,EACOlC,EAAWG,cAGUuI,IAA3BxG,EAAmBO,IACbP,EAGJ,IAAI2B,EAAc3B,GgBtErB1E,KAAK8P,QAAU9P,KAAKga,GAAY5X,IAEhCC,EAAUA,GAAW,IACb8C,uBAAkD+F,IAA9B7I,EAAQ8C,mBAA0C9C,EAAQ8C,kBAC/C,kBAA5B9C,EAAQmD,sBAA6D0F,IAA5B7I,EAAQmD,gBAGxD,MAAM,IAAIzE,MAAM,mEAFhBsB,EAAQmD,qBAA8C0F,IAA5B7I,EAAQmD,iBAAuCnD,EAAQmD,gBAIrFnD,EAAQkD,aAA8B2F,IAApB7I,EAAQkD,QAAwB,IAAalD,EAAQkD,QAEvE,IAAI0U,EAAuB,KACvBC,EAAyB,KAE7B,GAAIhX,EAASmE,OAA0C,CAGnD,MAAMoB,EAA0D,QAChEwR,EAAkBxR,EAAY,MAC9ByR,EAAoBzR,EAAY,eAG/BvF,EAASmE,QAA+B,oBAAd8S,WAA8B9X,EAAQ8X,UAE1DjX,EAASmE,SAAWhF,EAAQ8X,WAC/BF,IACA5X,EAAQ8X,UAAYF,GAHxB5X,EAAQ8X,UAAYA,UAOnBjX,EAASmE,QAAiC,oBAAhBgR,aAAgChW,EAAQgW,YAE5DnV,EAASmE,SAAWhF,EAAQgW,kBACF,IAAtB6B,IACP7X,EAAQgW,YAAc6B,GAH1B7X,EAAQgW,YAAcA,YAO1BrY,KAAK2L,EAActJ,EAAQuC,YAAc,IAAI8G,EAAkB1L,KAAKuI,GACpEvI,KAAKuP,EAAmB,eACxBvP,KAAKyP,GAAqB,EAC1BzP,KAAK+W,GAAW1U,EAEhBrC,KAAK4O,UAAY,KACjB5O,KAAK8O,QAAU,KAKZ,YAAY8B,GAOf,GANAA,EAAiBA,GAAkB4F,EAAeW,OAElDtU,EAAIoU,KAAKrG,EAAgB4F,EAAgB,kBAEzCxW,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,6CAA6CqG,EAAe5F,QAE/D,iBAA1B5Q,KAAKuP,EACL,OAAO3E,QAAQC,OAAO,IAAI9J,MAAM,4EASpC,GANAf,KAAKuP,EAAmB,aAExBvP,KAAKoa,GAAwBpa,KAAKoQ,EAAeQ,SAC3C5Q,KAAKoa,GAG0B,kBAAjCpa,KAAKuP,EAA2D,CAEhE,MAAM/N,EAAU,+DAMhB,OALAxB,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAOS,SAG3BxB,KAAKqR,GAEJzG,QAAQC,OAAO,IAAI9J,MAAMS,IAC7B,GAAqC,cAAjCxB,KAAKuP,EAAuD,CAEnE,MAAM/N,EAAU,8GAEhB,OADAxB,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAOS,GAC1BoJ,QAAQC,OAAO,IAAI9J,MAAMS,IAGpCxB,KAAKyP,GAAqB,EAGvB,KAAKjM,GACR,MAA8B,cAA1BxD,KAAKuP,EACE3E,QAAQC,OAAO,IAAI9J,MAAM,yEAG/Bf,KAAKqa,KACNra,KAAKqa,GAAa,IAAIC,EAAmBta,KAAKyB,YAI3CzB,KAAKqa,GAAW/X,KAAKkB,IAGzB,WAAWuD,GACd,MAA8B,iBAA1B/G,KAAKuP,GACLvP,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,+BAA+BpJ,2EACzD6D,QAAQE,WAGW,kBAA1B9K,KAAKuP,GACLvP,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,+BAA+BpJ,4EACzD/G,KAAKqR,KAGhBrR,KAAKuP,EAAmB,gBAExBvP,KAAKqR,GAAe,IAAIzG,SAASE,IAE7B9K,KAAK8Z,GAAuBhP,WAI1B9K,KAAKsR,GAAcvK,cACnB/G,KAAKqR,IAGP,SAAoBtK,GAIxB/G,KAAKua,GAAaxT,EAElB,UACU/G,KAAKoa,GACb,MAAOla,IAOT,GAAIF,KAAKyB,UAAW,CAChB,UACUzB,KAAKyB,UAAU0P,OACvB,MAAOjR,GACLF,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAO,gDAAgDb,OACjFF,KAAKwa,KAGTxa,KAAKyB,eAAYyJ,OAEjBlL,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,0FAIjC,QAAqBS,GAGzB,IAAIxO,EAAMpC,KAAK8P,QACf9P,KAAK6W,GAAsB7W,KAAK+W,GAASlS,mBAEzC,IACI,GAAI7E,KAAK+W,GAAS0D,gBAAiB,CAC/B,GAAIza,KAAK+W,GAAStV,YAAc8U,EAAkBmE,WAO9C,MAAM,IAAI3Z,MAAM,gFALhBf,KAAKyB,UAAYzB,KAAK2a,GAAoBpE,EAAkBmE,kBAGtD1a,KAAK4a,GAAgBxY,EAAKwO,OAIjC,CACH,IAAIiK,EAA+C,KAC/CC,EAAY,EAEhB,EAAG,CAGC,GAFAD,QAA0B7a,KAAK+a,GAAwB3Y,GAEzB,kBAA1BpC,KAAKuP,GAAgF,iBAA1BvP,KAAKuP,EAChE,MAAM,IAAIxO,MAAM,kDAGpB,GAAI8Z,EAAkB9T,MAClB,MAAM,IAAIhG,MAAM8Z,EAAkB9T,OAGtC,GAAK8T,EAA0BG,gBAC3B,MAAM,IAAIja,MAAM,gMAOpB,GAJI8Z,EAAkBzY,MAClBA,EAAMyY,EAAkBzY,KAGxByY,EAAkBI,YAAa,CAG/B,MAAMA,EAAcJ,EAAkBI,YACtCjb,KAAK6W,GAAsB,IAAMoE,EAGrCH,UAEGD,EAAkBzY,KAAO0Y,EAxO1B,KA0ON,GA1OM,MA0OFA,GAA+BD,EAAkBzY,IACjD,MAAM,IAAIrB,MAAM,+CAGdf,KAAKkb,GAAiB9Y,EAAKpC,KAAK+W,GAAStV,UAAWoZ,EAAmBjK,GAG7E5Q,KAAKyB,qBAAqBmV,IAC1B5W,KAAK2T,SAASC,mBAAoB,GAGR,eAA1B5T,KAAKuP,IAGLvP,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,8CACjCnQ,KAAKuP,EAAmB,aAM9B,MAAOrP,GAOL,OANAF,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAO,mCAAqCb,GACtEF,KAAKuP,EAAmB,eACxBvP,KAAKyB,eAAYyJ,EAGjBlL,KAAK8Z,KACElP,QAAQC,OAAO3K,IAItB,SAA8BkC,GAClC,MAAM0C,EAAiC,GACvC,GAAI9E,KAAK6W,GAAqB,CAC1B,MAAM9R,QAAc/E,KAAK6W,KACrB9R,IACAD,EAAQwR,EAAYG,eAAiB,UAAU1R,KAIvD,MAAOhC,EAAMnC,GAASoE,IACtBF,EAAQ/B,GAAQnC,EAEhB,MAAMua,EAAenb,KAAKob,GAAqBhZ,GAC/CpC,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,gCAAgCgL,MACjE,IACI,MAAM9V,QAAiBrF,KAAK2L,EAAYrG,KAAK6V,EAAc,CACvDjZ,QAAS,GACT4C,QAAS,IAAKA,KAAY9E,KAAK+W,GAASjS,SACxCS,QAASvF,KAAK+W,GAASxR,QACvBC,gBAAiBxF,KAAK+W,GAASvR,kBAGnC,GAA4B,MAAxBH,EAASpE,WACT,OAAO2J,QAAQC,OAAO,IAAI9J,MAAM,mDAAmDsE,EAASpE,gBAGhG,MAAM4Z,EAAoBpO,KAAKa,MAAMjI,EAASnD,SAM9C,QALK2Y,EAAkBQ,kBAAoBR,EAAkBQ,iBAAmB,KAG5ER,EAAkBS,gBAAkBT,EAAkBhL,cAEnDgL,EACT,MAAO3a,GACL,IAAIc,EAAe,mDAAqDd,EAQxE,OAPIA,aAAaY,GACQ,MAAjBZ,EAAEe,aACFD,GAA8B,uFAGtChB,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAOC,GAE1B4J,QAAQC,OAAO,IAAIhJ,EAAiCb,KAI3D,GAAkBoB,EAAakZ,GACnC,OAAKA,EAIElZ,IAA6B,IAAtBA,EAAI4D,QAAQ,KAAc,IAAM,KAAO,MAAMsV,IAHhDlZ,EAMP,SAAuBA,EAAamZ,EAAgEV,EAAuCW,GAC/I,IAAIC,EAAazb,KAAK0b,GAAkBtZ,EAAKyY,EAAkBS,iBAC/D,GAAItb,KAAK2b,GAAcJ,GAMnB,OALAvb,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,2EACjCnQ,KAAKyB,UAAY8Z,QACXvb,KAAK4a,GAAgBa,EAAYD,QAEvCxb,KAAK6P,aAAegL,EAAkBhL,cAI1C,MAAM+L,EAA6B,GAC7BC,EAAahB,EAAkBiB,qBAAuB,GAC5D,IAAIC,EAA4ClB,EAChD,IAAK,MAAMmB,KAAYH,EAAY,CAC/B,MAAMI,EAAmBjc,KAAKkc,GAAyBF,EAAUT,EAAoBC,GACrF,GAAIS,aAA4Blb,MAE5B6a,EAAoB7N,KAAK,GAAGiO,EAASva,qBACrCma,EAAoB7N,KAAKkO,QACtB,GAAIjc,KAAK2b,GAAcM,GAAmB,CAE7C,GADAjc,KAAKyB,UAAYwa,GACZF,EAAW,CACZ,IACIA,QAAkB/b,KAAK+a,GAAwB3Y,GACjD,MAAO+Z,GACL,OAAOvR,QAAQC,OAAOsR,GAE1BV,EAAazb,KAAK0b,GAAkBtZ,EAAK2Z,EAAUT,iBAEvD,IAGI,aAFMtb,KAAK4a,GAAgBa,EAAYD,QACvCxb,KAAK6P,aAAekM,EAAUlM,cAEhC,MAAOsM,GAKL,GAJAnc,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAO,kCAAkCib,EAASva,eAAe0a,KAC3FJ,OAAY7Q,EACZ0Q,EAAoB7N,KAAK,IAAInM,EAA4B,GAAGoa,EAASva,qBAAqB0a,IAAM5F,EAAkByF,EAASva,aAE7F,eAA1BzB,KAAKuP,EAAiD,CACtD,MAAM/N,EAAU,uDAEhB,OADAxB,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO3O,GAC1BoJ,QAAQC,OAAO,IAAI9J,MAAMS,OAMhD,OAAIoa,EAAoBxX,OAAS,EACtBwG,QAAQC,OAAO,IAAI/I,EAAgB,yEAAyE8Z,EAAoBpR,KAAK,OAAQoR,IAEjJhR,QAAQC,OAAO,IAAI9J,MAAM,gFAG5B,GAAoBU,GACxB,OAAQA,GACJ,KAAK8U,EAAkBmE,WACnB,IAAK1a,KAAK+W,GAASoD,UACf,MAAM,IAAIpZ,MAAM,qDAEpB,OAAO,IAAI4X,EAAmB3Y,KAAK2L,EAAa3L,KAAK6W,GAAqB7W,KAAKuI,EAASvI,KAAK+W,GAAS5R,kBAAoBnF,KAAK+W,GAASoD,UAAWna,KAAK+W,GAASjS,SAAW,IAChL,KAAKyR,EAAkB6F,iBACnB,IAAKpc,KAAK+W,GAASsB,YACf,MAAM,IAAItX,MAAM,uDAEpB,OAAO,IAAIiX,EAA0BhY,KAAK2L,EAAa3L,KAAK6W,GAAqB7W,KAAKuI,EAASvI,KAAK+W,IACxG,KAAKR,EAAkB8F,YACnB,OAAO,IAAIzF,EAAqB5W,KAAK2L,EAAa3L,KAAK6W,GAAqB7W,KAAKuI,EAASvI,KAAK+W,IACnG,QACI,MAAM,IAAIhW,MAAM,sBAAsBU,OAI1C,GAAgBW,EAAawO,GAGjC,OAFA5Q,KAAKyB,UAAWmN,UAAY5O,KAAK4O,UACjC5O,KAAKyB,UAAWqN,QAAW5O,GAAMF,KAAKwa,GAAgBta,GAC/CF,KAAKyB,UAAW6a,QAAQla,EAAKwO,GAGhC,GAAyBoL,EAA+BT,EAAmDC,GAC/G,MAAM/Z,EAAY8U,EAAkByF,EAASva,WAC7C,GAAIA,QAEA,OADAzB,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,uBAAuB6L,EAASva,0DAC1D,IAAIV,MAAM,uBAAuBib,EAASva,0DAEjD,IA0HZ,SAA0B8Z,EAAmDgB,GACzE,OAAQhB,GAAkE,IAA1CgB,EAAkBhB,GA3HtCiB,CAAiBjB,EAAoB9Z,GAqBrC,OADAzB,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,uBAAuBoG,EAAkB9U,8CACnE,IAAIE,EAAuB,IAAI4U,EAAkB9U,iCAA0CA,GAnBlG,KADwBua,EAASS,gBAAgBC,KAAKC,GAAMnG,EAAemG,KACvD3W,QAAQwV,IAA4B,GAepD,OADAxb,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,uBAAuBoG,EAAkB9U,kEAA0E+U,EAAegF,QAC5J,IAAIza,MAAM,IAAIwV,EAAkB9U,wBAAgC+U,EAAegF,OAdtF,GAAK/Z,IAAc8U,EAAkBmE,aAAe1a,KAAK+W,GAASoD,WAC7D1Y,IAAc8U,EAAkB6F,mBAAqBpc,KAAK+W,GAASsB,YAEpE,OADArY,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,uBAAuBoG,EAAkB9U,yDACnE,IAAIF,EAA0B,IAAIgV,EAAkB9U,4CAAqDA,GAEhHzB,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,wBAAwBoG,EAAkB9U,QAC3E,IACI,OAAOzB,KAAK2a,GAAoBlZ,GAClC,MAAO0a,GACL,OAAOA,GAcvB,GAAc1a,GAClB,OAAOA,GAAoC,iBAAhB,GAA4B,YAAaA,EAGhE,GAAgBsF,GASpB,GARA/G,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,iCAAiCpJ,4BAAgC/G,KAAKuP,MAEvGvP,KAAKyB,eAAYyJ,EAGjBnE,EAAQ/G,KAAKua,IAAcxT,EAC3B/G,KAAKua,QAAarP,EAEY,iBAA1BlL,KAAKuP,EAAT,CAKA,GAA8B,eAA1BvP,KAAKuP,EAEL,MADAvP,KAAKuI,EAAQtD,IAAIpE,EAASmG,QAAS,yCAAyCD,2EACtE,IAAIhG,MAAM,iCAAiCgG,wEAyBrD,GAtB8B,kBAA1B/G,KAAKuP,GAGLvP,KAAK8Z,KAGL/S,EACA/G,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAO,uCAAuCgG,OAExE/G,KAAKuI,EAAQtD,IAAIpE,EAASqG,YAAa,4BAGvClH,KAAKqa,KACLra,KAAKqa,GAAWlJ,OAAOhL,OAAOjG,IAC1BF,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAO,0CAA0Cb,UAE/EF,KAAKqa,QAAanP,GAGtBlL,KAAK6P,kBAAe3E,EACpBlL,KAAKuP,EAAmB,eAEpBvP,KAAKyP,EAAoB,CACzBzP,KAAKyP,GAAqB,EAC1B,IACQzP,KAAK8O,SACL9O,KAAK8O,QAAQ/H,GAEnB,MAAO7G,GACLF,KAAKuI,EAAQtD,IAAIpE,EAASE,MAAO,0BAA0BgG,mBAAuB7G,cAtCtFF,KAAKuI,EAAQtD,IAAIpE,EAASsP,MAAO,yCAAyCpJ,+EA2C1E,GAAY3E,GAEhB,GAAuC,IAAnCA,EAAIwa,YAAY,WAAY,IAA8C,IAAlCxa,EAAIwa,YAAY,UAAW,GACnE,OAAOxa,EAGX,IAAKc,EAASE,UACV,MAAM,IAAIrC,MAAM,mBAAmBqB,OAQvC,MAAMya,EAAO1c,OAAOgD,SAAS2Z,cAAc,KAI3C,OAHAD,EAAKE,KAAO3a,EAEZpC,KAAKuI,EAAQtD,IAAIpE,EAASqG,YAAa,gBAAgB9E,UAAYya,EAAKE,UACjEF,EAAKE,KAGR,GAAqB3a,GACzB,MAAM0D,EAAQ1D,EAAI4D,QAAQ,KAC1B,IAAImV,EAAe/Y,EAAIiL,UAAU,GAAc,IAAXvH,EAAe1D,EAAIgC,OAAS0B,GAWhE,MAV8C,MAA1CqV,EAAaA,EAAa/W,OAAS,KACnC+W,GAAgB,KAEpBA,GAAgB,YAChBA,IAA2B,IAAXrV,EAAe,GAAK1D,EAAIiL,UAAUvH,IAEA,IAA9CqV,EAAanV,QAAQ,sBACrBmV,IAA2B,IAAXrV,EAAe,IAAM,IACrCqV,GAAgB,oBAAsBnb,KAAK+Z,IAExCoB,GASR,MAAMb,EAOT,YAA6B0C,GAAA,KAAAA,KANrB,KAAAC,GAAiB,GAEjB,KAAAC,IAAsB,EAK1Bld,KAAKmd,GAAoB,IAAIC,EAC7Bpd,KAAKqd,GAAmB,IAAID,EAE5Bpd,KAAKsd,GAAmBtd,KAAKud,KAG1B,KAAK/Z,GAKR,OAJAxD,KAAKwd,GAAYha,GACZxD,KAAKqd,KACNrd,KAAKqd,GAAmB,IAAID,GAEzBpd,KAAKqd,GAAiBI,QAG1B,OAGH,OAFAzd,KAAKkd,IAAa,EAClBld,KAAKmd,GAAkBrS,UAChB9K,KAAKsd,GAGR,GAAY9Z,GAChB,GAAIxD,KAAKid,GAAQ7Y,eAAiBpE,KAAKid,GAAQ,WAAc,EACzD,MAAM,IAAIlc,MAAM,sCAAsCf,KAAY,6BAA2B,KAGjGA,KAAKid,GAAQlP,KAAKvK,GAClBxD,KAAKmd,GAAkBrS,UAGnB,WACJ,OAAa,CAGT,SAFM9K,KAAKmd,GAAkBM,SAExBzd,KAAKkd,GAAY,CACdld,KAAKqd,IACLrd,KAAKqd,GAAiBxS,OAAO,uBAGjC,MAGJ7K,KAAKmd,GAAoB,IAAIC,EAE7B,MAAMM,EAAkB1d,KAAKqd,GAC7Brd,KAAKqd,QAAmBnS,EAExB,MAAM1H,EAAmC,iBAArBxD,KAAKid,GAAQ,GAC7Bjd,KAAKid,GAAQzS,KAAK,IAClB8P,EAAmBqD,GAAe3d,KAAKid,IAE3Cjd,KAAKid,GAAQ7Y,OAAS,EAEtB,UACUpE,KAAKgd,GAAW1a,KAAKkB,GAC3Bka,EAAgB5S,UAClB,MAAO/D,GACL2W,EAAgB7S,OAAO9D,KAK3B,UAAsB6W,GAC1B,MAAMC,EAAcD,EAAalB,KAAKoB,GAAMA,EAAEla,aAAYma,QAAO,CAACC,EAAGF,IAAME,EAAIF,IACzElL,EAAS,IAAI9O,WAAW+Z,GAC9B,IAAII,EAAS,EACb,IAAK,MAAMtQ,KAAQiQ,EACfhL,EAAOsL,IAAI,IAAIpa,WAAW6J,GAAOsQ,GACjCA,GAAUtQ,EAAK/J,WAGnB,OAAOgP,EAAOzF,QAItB,MAAMiQ,EAKF,cACIpd,KAAKyd,QAAU,IAAI7S,SAAQ,CAACE,EAASD,KAAY7K,KAAKme,GAAWne,KAAKoe,IAAa,CAACtT,EAASD,KAG1F,UACH7K,KAAKme,KAGF,OAAOvE,GACV5Z,KAAKoe,GAAWxE,ICjpBjB,MAAMyE,EAAb,cAGoB,KAAAtb,KANmB,OAQnB,KAAA0E,QAAkB,EAGlB,KAAAmJ,eAAiC4F,EAAe4B,KAOzD,cAAcpM,EAAetH,GAEhC,GAAqB,iBAAVsH,EACP,MAAM,IAAIjL,MAAM,2DAGpB,IAAKiL,EACD,MAAO,GAGI,OAAXtH,IACAA,EAASlC,EAAWG,UAIxB,MAAMsJ,EAAWJ,EAAkByB,MAAMtB,GAEnCsS,EAAc,GACpB,IAAK,MAAM9c,KAAWyK,EAAU,CAC5B,MAAMsS,EAAgB9R,KAAKa,MAAM9L,GACjC,GAAkC,iBAAvB+c,EAAchR,KACrB,MAAM,IAAIxM,MAAM,oBAEpB,OAAQwd,EAAchR,MAClB,KAAKC,EAAY4F,WACbpT,KAAKwe,GAAqBD,GAC1B,MACJ,KAAK/Q,EAAY8F,WACbtT,KAAKye,GAAqBF,GAC1B,MACJ,KAAK/Q,EAAYgF,WACbxS,KAAK0e,GAAqBH,GAC1B,MACJ,KAAK/Q,EAAYoC,KAGjB,KAAKpC,EAAY+F,MAEb,MACJ,QAEI7O,EAAOO,IAAIpE,EAASqG,YAAa,yBAA2BqX,EAAchR,KAAO,cACjF,SAER+Q,EAAYvQ,KAAKwQ,GAGrB,OAAOD,EAQJ,aAAa9c,GAChB,OAAOqK,EAAkBW,MAAMC,KAAKC,UAAUlL,IAG1C,GAAqBA,GACzBxB,KAAK2e,GAAsBnd,EAAQ4S,OAAQ,gDAEdlJ,IAAzB1J,EAAQ4Q,cACRpS,KAAK2e,GAAsBnd,EAAQ4Q,aAAc,2CAIjD,GAAqB5Q,GAGzB,GAFAxB,KAAK2e,GAAsBnd,EAAQ4Q,aAAc,gDAE5BlH,IAAjB1J,EAAQmM,KACR,MAAM,IAAI5M,MAAM,2CAIhB,GAAqBS,GACzB,GAAIA,EAAQoR,QAAUpR,EAAQuF,MAC1B,MAAM,IAAIhG,MAAM,4CAGfS,EAAQoR,QAAUpR,EAAQuF,OAC3B/G,KAAK2e,GAAsBnd,EAAQuF,MAAO,2CAG9C/G,KAAK2e,GAAsBnd,EAAQ4Q,aAAc,2CAG7C,GAAsBxR,EAAYI,GACtC,GAAqB,iBAAVJ,GAAgC,KAAVA,EAC7B,MAAM,IAAIG,MAAMC,ICrG5B,MAAM4d,EAA+C,CACjDC,MAAOhe,EAASqE,MAChB4Z,MAAOje,EAASsP,MAChBhJ,KAAMtG,EAASqG,YACf6X,YAAale,EAASqG,YACtBD,KAAMpG,EAASmG,QACfgY,QAASne,EAASmG,QAClBD,MAAOlG,EAASE,MAChBke,SAAUpe,EAASiG,SACnBoY,KAAMre,EAASse,MAgBZ,MAAMC,EA0CF,iBAAiBC,GAGpB,GAFAxc,EAAIyL,WAAW+Q,EAAS,gBAoINnU,IAlILmU,EAkIHpa,IAjINjF,KAAK0E,OAAS2a,OACX,GAAuB,iBAAZA,EAAsB,CACpC,MAAM3Y,EA7DlB,SAAuB3D,GAInB,MAAMuc,EAAUV,EAAoB7b,EAAK+P,eACzC,QAAuB,IAAZwM,EACP,OAAOA,EAEP,MAAM,IAAIve,MAAM,sBAAsBgC,KAqDjBwc,CAAcF,GAC/Brf,KAAK0E,OAAS,IAAI2B,EAAcK,QAEhC1G,KAAK0E,OAAS,IAAI2B,EAAcgZ,GAGpC,OAAOrf,KA2BJ,QAAQoC,EAAaod,GAiBxB,OAhBA3c,EAAIyL,WAAWlM,EAAK,OACpBS,EAAI4c,WAAWrd,EAAK,OAEpBpC,KAAKoC,IAAMA,EAKPpC,KAAK0f,sBAD6B,iBAA3BF,EACsB,IAAKxf,KAAK0f,yBAA0BF,GAEpC,IACtBxf,KAAK0f,sBACRje,UAAW+d,GAIZxf,KAOJ,gBAAgBkO,GAInB,OAHArL,EAAIyL,WAAWJ,EAAU,YAEzBlO,KAAKkO,SAAWA,EACTlO,KAoBJ,uBAAuB2f,GAC1B,GAAI3f,KAAKmO,gBACL,MAAM,IAAIpN,MAAM,2CAWpB,OARK4e,EAEM1S,MAAM2S,QAAQD,GACrB3f,KAAKmO,gBAAkB,IAAI+H,EAAuByJ,GAElD3f,KAAKmO,gBAAkBwR,EAJvB3f,KAAKmO,gBAAkB,IAAI+H,EAOxBlW,KAOJ,QAGH,MAAM0f,EAAwB1f,KAAK0f,uBAAyB,GAS5D,QANqCxU,IAAjCwU,EAAsBhb,SAEtBgb,EAAsBhb,OAAS1E,KAAK0E,SAInC1E,KAAKoC,IACN,MAAM,IAAIrB,MAAM,4FAEpB,MAAMkN,EAAa,IAAI4L,EAAe7Z,KAAKoC,IAAKsd,GAEhD,OAAO1R,EAAc6R,OACjB5R,EACAjO,KAAK0E,QAAUlC,EAAWG,SAC1B3C,KAAKkO,UAAY,IAAImQ,EACrBre,KAAKmO,kB,OC1MZrK,WAAWxD,UAAU0F,SACtBtG,OAAOC,eAAemE,WAAWxD,UAAW,UAAW,CACnDM,MAAOqM,MAAM3M,UAAU0F,QACvB8Z,UAAU,IAGbhc,WAAWxD,UAAU4M,OACtBxN,OAAOC,eAAemE,WAAWxD,UAAW,QAAS,CAGjDM,MAAO,SAAS+P,EAAgBoP,GAAgB,OAAO,IAAIjc,WAAWmJ,MAAM3M,UAAU4M,MAAM1M,KAAKR,KAAM2Q,EAAOoP,KAC9GD,UAAU,IAGbhc,WAAWxD,UAAU0D,SACtBtE,OAAOC,eAAemE,WAAWxD,UAAW,UAAW,CACnDM,MAAOqM,MAAM3M,UAAU0D,QACvB8b,UAAU,I,O7BxBK,iBAAZxgB,SAA0C,iBAAX0gB,OACxCA,OAAO1gB,QAAUH,IACQ,mBAAX8gB,QAAyBA,OAAOC,IAC9CD,OAAO,GAAI9gB,GACe,iBAAZG,QACdA,QAAiB,QAAIH,IAErBD,EAAc,QAAIC", "file": "signalr.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"signalR\"] = factory();\n\telse\n\t\troot[\"signalR\"] = factory();\n})(self, function() {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// These values are designed to match the ASP.NET Log Levels since that's the pattern we're emulating here.\r\n/** Indicates the severity of a log message.\r\n *\r\n * Log Levels are ordered in increasing severity. So `Debug` is more severe than `Trace`, etc.\r\n */\r\nexport enum LogLevel {\r\n    /** Log level for very low severity diagnostic messages. */\r\n    Trace = 0,\r\n    /** Log level for low severity diagnostic messages. */\r\n    Debug = 1,\r\n    /** Log level for informational diagnostic messages. */\r\n    Information = 2,\r\n    /** Log level for diagnostic messages that indicate a non-fatal problem. */\r\n    Warning = 3,\r\n    /** Log level for diagnostic messages that indicate a failure in the current operation. */\r\n    Error = 4,\r\n    /** Log level for diagnostic messages that indicate a failure that will terminate the entire application. */\r\n    Critical = 5,\r\n    /** The highest possible log level. Used when configuring logging to indicate that no log messages should be emitted. */\r\n    None = 6,\r\n}\r\n\r\n/** An abstraction that provides a sink for diagnostic messages. */\r\nexport interface ILogger {\r\n    /** Called by the framework to emit a diagnostic message.\r\n     *\r\n     * @param {LogLevel} logLevel The severity level of the message.\r\n     * @param {string} message The message.\r\n     */\r\n    log(logLevel: LogLevel, message: string): void;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HttpTransportType } from \"./ITransport\";\r\n\r\n/** Error thrown when an HTTP request fails. */\r\nexport class HttpError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The HTTP status code represented by this error. */\r\n    public statusCode: number;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     * @param {number} statusCode The HTTP status code represented by this error.\r\n     */\r\n    constructor(errorMessage: string, statusCode: number) {\r\n        const trueProto = new.target.prototype;\r\n        super(`${errorMessage}: Status code '${statusCode}'`);\r\n        this.statusCode = statusCode;\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when a timeout elapses. */\r\nexport class TimeoutError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.TimeoutError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    constructor(errorMessage: string = \"A timeout occurred.\") {\r\n        const trueProto = new.target.prototype;\r\n        super(errorMessage);\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when an action is aborted. */\r\nexport class AbortError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** Constructs a new instance of {@link AbortError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    constructor(errorMessage: string = \"An abort occurred.\") {\r\n        const trueProto = new.target.prototype;\r\n        super(errorMessage);\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the selected transport is unsupported by the browser. */\r\n/** @private */\r\nexport class UnsupportedTransportError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The {@link @microsoft/signalr.HttpTransportType} this error occured on. */\r\n    public transport: HttpTransportType;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.UnsupportedTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occured on.\r\n     */\r\n    constructor(message: string, transport: HttpTransportType) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'UnsupportedTransportError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the selected transport is disabled by the browser. */\r\n/** @private */\r\nexport class DisabledTransportError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The {@link @microsoft/signalr.HttpTransportType} this error occured on. */\r\n    public transport: HttpTransportType;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.DisabledTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occured on.\r\n     */\r\n    constructor(message: string, transport: HttpTransportType) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'DisabledTransportError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the selected transport cannot be started. */\r\n/** @private */\r\nexport class FailedToStartTransportError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The {@link @microsoft/signalr.HttpTransportType} this error occured on. */\r\n    public transport: HttpTransportType;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.FailedToStartTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occured on.\r\n     */\r\n    constructor(message: string, transport: HttpTransportType) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'FailedToStartTransportError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the negotiation with the server failed to complete. */\r\n/** @private */\r\nexport class FailedToNegotiateWithServerError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.FailedToNegotiateWithServerError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     */\r\n    constructor(message: string) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.errorType = 'FailedToNegotiateWithServerError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when multiple errors have occured. */\r\n/** @private */\r\nexport class AggregateErrors extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The collection of errors this error is aggregating. */\r\n    public innerErrors: Error[];\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.AggregateErrors}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {Error[]} innerErrors The collection of errors this error is aggregating.\r\n     */\r\n    constructor(message: string, innerErrors: Error[]) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n\r\n        this.innerErrors = innerErrors;\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortSignal } from \"./AbortController\";\r\nimport { MessageHeaders } from \"./IHubProtocol\";\r\n\r\n/** Represents an HTTP request. */\r\nexport interface HttpRequest {\r\n    /** The HTTP method to use for the request. */\r\n    method?: string;\r\n\r\n    /** The URL for the request. */\r\n    url?: string;\r\n\r\n    /** The body content for the request. May be a string or an ArrayBuffer (for binary data). */\r\n    content?: string | ArrayBuffer;\r\n\r\n    /** An object describing headers to apply to the request. */\r\n    headers?: MessageHeaders;\r\n\r\n    /** The XMLHttpRequestResponseType to apply to the request. */\r\n    responseType?: XMLHttpRequestResponseType;\r\n\r\n    /** An AbortSignal that can be monitored for cancellation. */\r\n    abortSignal?: AbortSignal;\r\n\r\n    /** The time to wait for the request to complete before throwing a TimeoutError. Measured in milliseconds. */\r\n    timeout?: number;\r\n\r\n    /** This controls whether credentials such as cookies are sent in cross-site requests. */\r\n    withCredentials?: boolean;\r\n}\r\n\r\n/** Represents an HTTP response. */\r\nexport class HttpResponse {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     */\r\n    constructor(statusCode: number);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code and message.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code, message and string content.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     * @param {string} content The content of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string, content: string);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code, message and binary content.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     * @param {ArrayBuffer} content The content of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string, content: ArrayBuffer);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code, message and binary content.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     * @param {string | ArrayBuffer} content The content of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string, content: string | ArrayBuffer);\r\n    constructor(\r\n        public readonly statusCode: number,\r\n        public readonly statusText?: string,\r\n        public readonly content?: string | ArrayBuffer) {\r\n    }\r\n}\r\n\r\n/** Abstraction over an HTTP client.\r\n *\r\n * This class provides an abstraction over an HTTP client so that a different implementation can be provided on different platforms.\r\n */\r\nexport abstract class HttpClient {\r\n    /** Issues an HTTP GET request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public get(url: string): Promise<HttpResponse>;\r\n\r\n    /** Issues an HTTP GET request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @param {HttpRequest} options Additional options to configure the request. The 'url' field in this object will be overridden by the url parameter.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public get(url: string, options: HttpRequest): Promise<HttpResponse>;\r\n    public get(url: string, options?: HttpRequest): Promise<HttpResponse> {\r\n        return this.send({\r\n            ...options,\r\n            method: \"GET\",\r\n            url,\r\n        });\r\n    }\r\n\r\n    /** Issues an HTTP POST request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public post(url: string): Promise<HttpResponse>;\r\n\r\n    /** Issues an HTTP POST request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @param {HttpRequest} options Additional options to configure the request. The 'url' field in this object will be overridden by the url parameter.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public post(url: string, options: HttpRequest): Promise<HttpResponse>;\r\n    public post(url: string, options?: HttpRequest): Promise<HttpResponse> {\r\n        return this.send({\r\n            ...options,\r\n            method: \"POST\",\r\n            url,\r\n        });\r\n    }\r\n\r\n    /** Issues an HTTP DELETE request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public delete(url: string): Promise<HttpResponse>;\r\n\r\n    /** Issues an HTTP DELETE request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @param {HttpRequest} options Additional options to configure the request. The 'url' field in this object will be overridden by the url parameter.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public delete(url: string, options: HttpRequest): Promise<HttpResponse>;\r\n    public delete(url: string, options?: HttpRequest): Promise<HttpResponse> {\r\n        return this.send({\r\n            ...options,\r\n            method: \"DELETE\",\r\n            url,\r\n        });\r\n    }\r\n\r\n    /** Issues an HTTP request to the specified URL, returning a {@link Promise} that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {HttpRequest} request An {@link @microsoft/signalr.HttpRequest} describing the request to send.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an HttpResponse describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public abstract send(request: HttpRequest): Promise<HttpResponse>;\r\n\r\n    /** Gets all cookies that apply to the specified URL.\r\n     *\r\n     * @param url The URL that the cookies are valid for.\r\n     * @returns {string} A string containing all the key-value cookie pairs for the specified URL.\r\n     */\r\n    // @ts-ignore\r\n    public getCookieString(url: string): string {\r\n        return \"\";\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\n\r\n/** A logger that does nothing when log messages are sent to it. */\r\nexport class NullLogger implements ILogger {\r\n    /** The singleton instance of the {@link @microsoft/signalr.NullLogger}. */\r\n    public static instance: ILogger = new NullLogger();\r\n\r\n    private constructor() {}\r\n\r\n    /** @inheritDoc */\r\n    // eslint-disable-next-line\r\n    public log(_logLevel: LogLevel, _message: string): void {\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { IStreamSubscriber, ISubscription } from \"./Stream\";\r\nimport { Subject } from \"./Subject\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\n\r\n// Version token that will be replaced by the prepack command\r\n/** The version of the SignalR client. */\r\n\r\nexport const VERSION: string = \"0.0.0-DEV_BUILD\";\r\n/** @private */\r\nexport class Arg {\r\n    public static isRequired(val: any, name: string): void {\r\n        if (val === null || val === undefined) {\r\n            throw new Error(`The '${name}' argument is required.`);\r\n        }\r\n    }\r\n    public static isNotEmpty(val: string, name: string): void {\r\n        if (!val || val.match(/^\\s*$/)) {\r\n            throw new Error(`The '${name}' argument should not be empty.`);\r\n        }\r\n    }\r\n\r\n    public static isIn(val: any, values: any, name: string): void {\r\n        // TypeScript enums have keys for **both** the name and the value of each enum member on the type itself.\r\n        if (!(val in values)) {\r\n            throw new Error(`Unknown ${name} value: ${val}.`);\r\n        }\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport class Platform {\r\n    // react-native has a window but no document so we should check both\r\n    public static get isBrowser(): boolean {\r\n        return typeof window === \"object\" && typeof window.document === \"object\";\r\n    }\r\n\r\n    // WebWorkers don't have a window object so the isBrowser check would fail\r\n    public static get isWebWorker(): boolean {\r\n        return typeof self === \"object\" && \"importScripts\" in self;\r\n    }\r\n\r\n    // react-native has a window but no document\r\n    static get isReactNative(): boolean {\r\n        return typeof window === \"object\" && typeof window.document === \"undefined\";\r\n    }\r\n\r\n    // Node apps shouldn't have a window object, but WebWorkers don't either\r\n    // so we need to check for both WebWorker and window\r\n    public static get isNode(): boolean {\r\n        return !this.isBrowser && !this.isWebWorker && !this.isReactNative;\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport function getDataDetail(data: any, includeContent: boolean): string {\r\n    let detail = \"\";\r\n    if (isArrayBuffer(data)) {\r\n        detail = `Binary data of length ${data.byteLength}`;\r\n        if (includeContent) {\r\n            detail += `. Content: '${formatArrayBuffer(data)}'`;\r\n        }\r\n    } else if (typeof data === \"string\") {\r\n        detail = `String data of length ${data.length}`;\r\n        if (includeContent) {\r\n            detail += `. Content: '${data}'`;\r\n        }\r\n    }\r\n    return detail;\r\n}\r\n\r\n/** @private */\r\nexport function formatArrayBuffer(data: ArrayBuffer): string {\r\n    const view = new Uint8Array(data);\r\n\r\n    // Uint8Array.map only supports returning another Uint8Array?\r\n    let str = \"\";\r\n    view.forEach((num) => {\r\n        const pad = num < 16 ? \"0\" : \"\";\r\n        str += `0x${pad}${num.toString(16)} `;\r\n    });\r\n\r\n    // Trim of trailing space.\r\n    return str.substr(0, str.length - 1);\r\n}\r\n\r\n// Also in signalr-protocol-msgpack/Utils.ts\r\n/** @private */\r\nexport function isArrayBuffer(val: any): val is ArrayBuffer {\r\n    return val && typeof ArrayBuffer !== \"undefined\" &&\r\n        (val instanceof ArrayBuffer ||\r\n            // Sometimes we get an ArrayBuffer that doesn't satisfy instanceof\r\n            (val.constructor && val.constructor.name === \"ArrayBuffer\"));\r\n}\r\n\r\n/** @private */\r\nexport async function sendMessage(logger: ILogger, transportName: string, httpClient: HttpClient, url: string, accessTokenFactory: (() => string | Promise<string>) | undefined,\r\n                                  content: string | ArrayBuffer, options: IHttpConnectionOptions): Promise<void> {\r\n    let headers: {[k: string]: string} = {};\r\n    if (accessTokenFactory) {\r\n        const token = await accessTokenFactory();\r\n        if (token) {\r\n            headers = {\r\n                [\"Authorization\"]: `Bearer ${token}`,\r\n            };\r\n        }\r\n    }\r\n\r\n    const [name, value] = getUserAgentHeader();\r\n    headers[name] = value;\r\n\r\n    logger.log(LogLevel.Trace, `(${transportName} transport) sending data. ${getDataDetail(content, options.logMessageContent!)}.`);\r\n\r\n    const responseType = isArrayBuffer(content) ? \"arraybuffer\" : \"text\";\r\n    const response = await httpClient.post(url, {\r\n        content,\r\n        headers: { ...headers, ...options.headers},\r\n        responseType,\r\n        timeout: options.timeout,\r\n        withCredentials: options.withCredentials,\r\n    });\r\n\r\n    logger.log(LogLevel.Trace, `(${transportName} transport) request complete. Response status: ${response.statusCode}.`);\r\n}\r\n\r\n/** @private */\r\nexport function createLogger(logger?: ILogger | LogLevel): ILogger {\r\n    if (logger === undefined) {\r\n        return new ConsoleLogger(LogLevel.Information);\r\n    }\r\n\r\n    if (logger === null) {\r\n        return NullLogger.instance;\r\n    }\r\n\r\n    if ((logger as ILogger).log !== undefined) {\r\n        return logger as ILogger;\r\n    }\r\n\r\n    return new ConsoleLogger(logger as LogLevel);\r\n}\r\n\r\n/** @private */\r\nexport class SubjectSubscription<T> implements ISubscription<T> {\r\n    private _subject: Subject<T>;\r\n    private _observer: IStreamSubscriber<T>;\r\n\r\n    constructor(subject: Subject<T>, observer: IStreamSubscriber<T>) {\r\n        this._subject = subject;\r\n        this._observer = observer;\r\n    }\r\n\r\n    public dispose(): void {\r\n        const index: number = this._subject.observers.indexOf(this._observer);\r\n        if (index > -1) {\r\n            this._subject.observers.splice(index, 1);\r\n        }\r\n\r\n        if (this._subject.observers.length === 0 && this._subject.cancelCallback) {\r\n            this._subject.cancelCallback().catch((_) => { });\r\n        }\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport class ConsoleLogger implements ILogger {\r\n    private readonly _minLevel: LogLevel;\r\n\r\n    // Public for testing purposes.\r\n    public out: {\r\n        error(message: any): void,\r\n        warn(message: any): void,\r\n        info(message: any): void,\r\n        log(message: any): void,\r\n    };\r\n\r\n    constructor(minimumLogLevel: LogLevel) {\r\n        this._minLevel = minimumLogLevel;\r\n        this.out = console;\r\n    }\r\n\r\n    public log(logLevel: LogLevel, message: string): void {\r\n        if (logLevel >= this._minLevel) {\r\n            const msg = `[${new Date().toISOString()}] ${LogLevel[logLevel]}: ${message}`;\r\n            switch (logLevel) {\r\n                case LogLevel.Critical:\r\n                case LogLevel.Error:\r\n                    this.out.error(msg);\r\n                    break;\r\n                case LogLevel.Warning:\r\n                    this.out.warn(msg);\r\n                    break;\r\n                case LogLevel.Information:\r\n                    this.out.info(msg);\r\n                    break;\r\n                default:\r\n                    // console.debug only goes to attached debuggers in Node, so we use console.log for Trace and Debug\r\n                    this.out.log(msg);\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport function getUserAgentHeader(): [string, string] {\r\n    let userAgentHeaderName = \"X-SignalR-User-Agent\";\r\n    if (Platform.isNode) {\r\n        userAgentHeaderName = \"User-Agent\";\r\n    }\r\n    return [ userAgentHeaderName, constructUserAgent(VERSION, getOsName(), getRuntime(), getRuntimeVersion()) ];\r\n}\r\n\r\n/** @private */\r\nexport function constructUserAgent(version: string, os: string, runtime: string, runtimeVersion: string | undefined): string {\r\n    // Microsoft SignalR/[Version] ([Detailed Version]; [Operating System]; [Runtime]; [Runtime Version])\r\n    let userAgent: string = \"Microsoft SignalR/\";\r\n\r\n    const majorAndMinor = version.split(\".\");\r\n    userAgent += `${majorAndMinor[0]}.${majorAndMinor[1]}`;\r\n    userAgent += ` (${version}; `;\r\n\r\n    if (os && os !== \"\") {\r\n        userAgent += `${os}; `;\r\n    } else {\r\n        userAgent += \"Unknown OS; \";\r\n    }\r\n\r\n    userAgent += `${runtime}`;\r\n\r\n    if (runtimeVersion) {\r\n        userAgent += `; ${runtimeVersion}`;\r\n    } else {\r\n        userAgent += \"; Unknown Runtime Version\";\r\n    }\r\n\r\n    userAgent += \")\";\r\n    return userAgent;\r\n}\r\n\r\n// eslint-disable-next-line spaced-comment\r\n/*#__PURE__*/ function getOsName(): string {\r\n    if (Platform.isNode) {\r\n        switch (process.platform) {\r\n            case \"win32\":\r\n                return \"Windows NT\";\r\n            case \"darwin\":\r\n                return \"macOS\";\r\n            case \"linux\":\r\n                return \"Linux\";\r\n            default:\r\n                return process.platform;\r\n        }\r\n    } else {\r\n        return \"\";\r\n    }\r\n}\r\n\r\n// eslint-disable-next-line spaced-comment\r\n/*#__PURE__*/ function getRuntimeVersion(): string | undefined {\r\n    if (Platform.isNode) {\r\n        return process.versions.node;\r\n    }\r\n    return undefined;\r\n}\r\n\r\nfunction getRuntime(): string {\r\n    if (Platform.isNode) {\r\n        return \"NodeJS\";\r\n    } else {\r\n        return \"Browser\";\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport function getErrorString(e: any): string {\r\n    if (e.stack) {\r\n        return e.stack;\r\n    } else if (e.message) {\r\n        return e.message;\r\n    }\r\n    return `${e}`;\r\n}\r\n\r\n/** @private */\r\nexport function getGlobalThis(): unknown {\r\n    // globalThis is semi-new and not available in Node until v12\r\n    if (typeof globalThis !== \"undefined\") {\r\n        return globalThis;\r\n    }\r\n    if (typeof self !== \"undefined\") {\r\n        return self;\r\n    }\r\n    if (typeof window !== \"undefined\") {\r\n        return window;\r\n    }\r\n    if (typeof global !== \"undefined\") {\r\n        return global;\r\n    }\r\n    throw new Error(\"could not find global\");\r\n}", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// @ts-ignore: This will be removed from built files and is here to make the types available during dev work\r\nimport { CookieJar } from \"@types/tough-cookie\";\r\n\r\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { Platform, getGlobalThis } from \"./Utils\";\r\n\r\nexport class FetchHttpClient extends HttpClient {\r\n    private readonly _abortControllerType: { prototype: AbortController, new(): AbortController };\r\n    private readonly _fetchType: (input: RequestInfo, init?: RequestInit) => Promise<Response>;\r\n    private readonly _jar?: CookieJar;\r\n\r\n    private readonly _logger: ILogger;\r\n\r\n    public constructor(logger: ILogger) {\r\n        super();\r\n        this._logger = logger;\r\n\r\n        if (typeof fetch === \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: T<PERSON> doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n\r\n            // Cookies aren't automatically handled in Node so we need to add a CookieJar to preserve cookies across requests\r\n            this._jar = new (requireFunc(\"tough-cookie\")).CookieJar();\r\n            this._fetchType = requireFunc(\"node-fetch\");\r\n\r\n            // node-fetch doesn't have a nice API for getting and setting cookies\r\n            // fetch-cookie will wrap a fetch implementation with a default CookieJar or a provided one\r\n            this._fetchType = requireFunc(\"fetch-cookie\")(this._fetchType, this._jar);\r\n        } else {\r\n            this._fetchType = fetch.bind(getGlobalThis());\r\n        }\r\n        if (typeof AbortController === \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n\r\n            // Node needs EventListener methods on AbortController which our custom polyfill doesn't provide\r\n            this._abortControllerType = requireFunc(\"abort-controller\");\r\n        } else {\r\n            this._abortControllerType = AbortController;\r\n        }\r\n    }\r\n\r\n    /** @inheritDoc */\r\n    public async send(request: HttpRequest): Promise<HttpResponse> {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            throw new AbortError();\r\n        }\r\n\r\n        if (!request.method) {\r\n            throw new Error(\"No method defined.\");\r\n        }\r\n        if (!request.url) {\r\n            throw new Error(\"No url defined.\");\r\n        }\r\n\r\n        const abortController = new this._abortControllerType();\r\n\r\n        let error: any;\r\n        // Hook our abortSignal into the abort controller\r\n        if (request.abortSignal) {\r\n            request.abortSignal.onabort = () => {\r\n                abortController.abort();\r\n                error = new AbortError();\r\n            };\r\n        }\r\n\r\n        // If a timeout has been passed in, setup a timeout to call abort\r\n        // Type needs to be any to fit window.setTimeout and NodeJS.setTimeout\r\n        let timeoutId: any = null;\r\n        if (request.timeout) {\r\n            const msTimeout = request.timeout!;\r\n            timeoutId = setTimeout(() => {\r\n                abortController.abort();\r\n                this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\r\n                error = new TimeoutError();\r\n            }, msTimeout);\r\n        }\r\n\r\n        let response: Response;\r\n        try {\r\n            response = await this._fetchType(request.url!, {\r\n                body: request.content!,\r\n                cache: \"no-cache\",\r\n                credentials: request.withCredentials === true ? \"include\" : \"same-origin\",\r\n                headers: {\r\n                    \"Content-Type\": \"text/plain;charset=UTF-8\",\r\n                    \"X-Requested-With\": \"XMLHttpRequest\",\r\n                    ...request.headers,\r\n                },\r\n                method: request.method!,\r\n                mode: \"cors\",\r\n                redirect: \"follow\",\r\n                signal: abortController.signal,\r\n            });\r\n        } catch (e) {\r\n            if (error) {\r\n                throw error;\r\n            }\r\n            this._logger.log(\r\n                LogLevel.Warning,\r\n                `Error from HTTP request. ${e}.`,\r\n            );\r\n            throw e;\r\n        } finally {\r\n            if (timeoutId) {\r\n                clearTimeout(timeoutId);\r\n            }\r\n            if (request.abortSignal) {\r\n                request.abortSignal.onabort = null;\r\n            }\r\n        }\r\n\r\n        if (!response.ok) {\r\n            const errorMessage = await deserializeContent(response, \"text\") as string;\r\n            throw new HttpError(errorMessage || response.statusText, response.status);\r\n        }\r\n\r\n        const content = deserializeContent(response, request.responseType);\r\n        const payload = await content;\r\n\r\n        return new HttpResponse(\r\n            response.status,\r\n            response.statusText,\r\n            payload,\r\n        );\r\n    }\r\n\r\n    public getCookieString(url: string): string {\r\n        let cookies: string = \"\";\r\n        if (Platform.isNode && this._jar) {\r\n            // @ts-ignore: unused variable\r\n            this._jar.getCookies(url, (e, c) => cookies = c.join(\"; \"));\r\n        }\r\n        return cookies;\r\n    }\r\n}\r\n\r\nfunction deserializeContent(response: Response, responseType?: XMLHttpRequestResponseType): Promise<string | ArrayBuffer> {\r\n    let content;\r\n    switch (responseType) {\r\n        case \"arraybuffer\":\r\n            content = response.arrayBuffer();\r\n            break;\r\n        case \"text\":\r\n            content = response.text();\r\n            break;\r\n        case \"blob\":\r\n        case \"document\":\r\n        case \"json\":\r\n            throw new Error(`${responseType} is not supported.`);\r\n        default:\r\n            content = response.text();\r\n            break;\r\n    }\r\n\r\n    return content;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\n\r\nexport class XhrHttpClient extends HttpClient {\r\n    private readonly _logger: ILogger;\r\n\r\n    public constructor(logger: ILogger) {\r\n        super();\r\n        this._logger = logger;\r\n    }\r\n\r\n    /** @inheritDoc */\r\n    public send(request: HttpRequest): Promise<HttpResponse> {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            return Promise.reject(new AbortError());\r\n        }\r\n\r\n        if (!request.method) {\r\n            return Promise.reject(new Error(\"No method defined.\"));\r\n        }\r\n        if (!request.url) {\r\n            return Promise.reject(new Error(\"No url defined.\"));\r\n        }\r\n\r\n        return new Promise<HttpResponse>((resolve, reject) => {\r\n            const xhr = new XMLHttpRequest();\r\n\r\n            xhr.open(request.method!, request.url!, true);\r\n            xhr.withCredentials = request.withCredentials === undefined ? true : request.withCredentials;\r\n            xhr.setRequestHeader(\"X-Requested-With\", \"XMLHttpRequest\");\r\n            // Explicitly setting the Content-Type header for React Native on Android platform.\r\n            xhr.setRequestHeader(\"Content-Type\", \"text/plain;charset=UTF-8\");\r\n\r\n            const headers = request.headers;\r\n            if (headers) {\r\n                Object.keys(headers)\r\n                    .forEach((header) => {\r\n                        xhr.setRequestHeader(header, headers[header]);\r\n                    });\r\n            }\r\n\r\n            if (request.responseType) {\r\n                xhr.responseType = request.responseType;\r\n            }\r\n\r\n            if (request.abortSignal) {\r\n                request.abortSignal.onabort = () => {\r\n                    xhr.abort();\r\n                    reject(new AbortError());\r\n                };\r\n            }\r\n\r\n            if (request.timeout) {\r\n                xhr.timeout = request.timeout;\r\n            }\r\n\r\n            xhr.onload = () => {\r\n                if (request.abortSignal) {\r\n                    request.abortSignal.onabort = null;\r\n                }\r\n\r\n                if (xhr.status >= 200 && xhr.status < 300) {\r\n                    resolve(new HttpResponse(xhr.status, xhr.statusText, xhr.response || xhr.responseText));\r\n                } else {\r\n                    reject(new HttpError(xhr.response || xhr.responseText || xhr.statusText, xhr.status));\r\n                }\r\n            };\r\n\r\n            xhr.onerror = () => {\r\n                this._logger.log(LogLevel.Warning, `Error from HTTP request. ${xhr.status}: ${xhr.statusText}.`);\r\n                reject(new HttpError(xhr.statusText, xhr.status));\r\n            };\r\n\r\n            xhr.ontimeout = () => {\r\n                this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\r\n                reject(new TimeoutError());\r\n            };\r\n\r\n            xhr.send(request.content || \"\");\r\n        });\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortError } from \"./Errors\";\r\nimport { FetchHttpClient } from \"./FetchHttpClient\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nimport { ILogger } from \"./ILogger\";\r\nimport { Platform } from \"./Utils\";\r\nimport { XhrHttpClient } from \"./XhrHttpClient\";\r\n\r\n/** Default implementation of {@link @microsoft/signalr.HttpClient}. */\r\nexport class DefaultHttpClient extends HttpClient {\r\n    private readonly _httpClient: HttpClient;\r\n\r\n    /** Creates a new instance of the {@link @microsoft/signalr.DefaultHttpClient}, using the provided {@link @microsoft/signalr.ILogger} to log messages. */\r\n    public constructor(logger: ILogger) {\r\n        super();\r\n\r\n        if (typeof fetch !== \"undefined\" || Platform.isNode) {\r\n            this._httpClient = new FetchHttpClient(logger);\r\n        } else if (typeof XMLHttpRequest !== \"undefined\") {\r\n            this._httpClient = new XhrHttpClient(logger);\r\n        } else {\r\n            throw new Error(\"No usable HttpClient found.\");\r\n        }\r\n    }\r\n\r\n    /** @inheritDoc */\r\n    public send(request: HttpRequest): Promise<HttpResponse> {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            return Promise.reject(new AbortError());\r\n        }\r\n\r\n        if (!request.method) {\r\n            return Promise.reject(new Error(\"No method defined.\"));\r\n        }\r\n        if (!request.url) {\r\n            return Promise.reject(new Error(\"No url defined.\"));\r\n        }\r\n\r\n        return this._httpClient.send(request);\r\n    }\r\n\r\n    public getCookieString(url: string): string {\r\n        return this._httpClient.getCookieString(url);\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Not exported from index\r\n/** @private */\r\nexport class TextMessageFormat {\r\n    public static RecordSeparatorCode = 0x1e;\r\n    public static RecordSeparator = String.fromCharCode(TextMessageFormat.RecordSeparatorCode);\r\n\r\n    public static write(output: string): string {\r\n        return `${output}${TextMessageFormat.RecordSeparator}`;\r\n    }\r\n\r\n    public static parse(input: string): string[] {\r\n        if (input[input.length - 1] !== TextMessageFormat.RecordSeparator) {\r\n            throw new Error(\"Message is incomplete.\");\r\n        }\r\n\r\n        const messages = input.split(TextMessageFormat.RecordSeparator);\r\n        messages.pop();\r\n        return messages;\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { TextMessageFormat } from \"./TextMessageFormat\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\n\r\n/** @private */\r\nexport interface HandshakeRequestMessage {\r\n    readonly protocol: string;\r\n    readonly version: number;\r\n}\r\n\r\n/** @private */\r\nexport interface HandshakeResponseMessage {\r\n    readonly error: string;\r\n    readonly minorVersion: number;\r\n}\r\n\r\n/** @private */\r\nexport class HandshakeProtocol {\r\n    // Handshake request is always JSON\r\n    public writeHandshakeRequest(handshakeRequest: HandshakeRequestMessage): string {\r\n        return TextMessageFormat.write(JSON.stringify(handshakeRequest));\r\n    }\r\n\r\n    public parseHandshakeResponse(data: any): [any, HandshakeResponseMessage] {\r\n        let messageData: string;\r\n        let remainingData: any;\r\n\r\n        if (isArrayBuffer(data)) {\r\n            // Format is binary but still need to read JSON text from handshake response\r\n            const binaryData = new Uint8Array(data);\r\n            const separatorIndex = binaryData.indexOf(TextMessageFormat.RecordSeparatorCode);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            const responseLength = separatorIndex + 1;\r\n            messageData = String.fromCharCode.apply(null, Array.prototype.slice.call(binaryData.slice(0, responseLength)));\r\n            remainingData = (binaryData.byteLength > responseLength) ? binaryData.slice(responseLength).buffer : null;\r\n        } else {\r\n            const textData: string = data;\r\n            const separatorIndex = textData.indexOf(TextMessageFormat.RecordSeparator);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            const responseLength = separatorIndex + 1;\r\n            messageData = textData.substring(0, responseLength);\r\n            remainingData = (textData.length > responseLength) ? textData.substring(responseLength) : null;\r\n        }\r\n\r\n        // At this point we should have just the single handshake message\r\n        const messages = TextMessageFormat.parse(messageData);\r\n        const response = JSON.parse(messages[0]);\r\n        if (response.type) {\r\n            throw new Error(\"Expected a handshake response from the server.\");\r\n        }\r\n        const responseMessage: HandshakeResponseMessage = response;\r\n\r\n        // multiple messages could have arrived with handshake\r\n        // return additional data to be parsed as usual, or null if all parsed\r\n        return [remainingData, responseMessage];\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { ILogger } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\n\r\n/** Defines the type of a Hub Message. */\r\nexport enum MessageType {\r\n    /** Indicates the message is an Invocation message and implements the {@link @microsoft/signalr.InvocationMessage} interface. */\r\n    Invocation = 1,\r\n    /** Indicates the message is a StreamItem message and implements the {@link @microsoft/signalr.StreamItemMessage} interface. */\r\n    StreamItem = 2,\r\n    /** Indicates the message is a Completion message and implements the {@link @microsoft/signalr.CompletionMessage} interface. */\r\n    Completion = 3,\r\n    /** Indicates the message is a Stream Invocation message and implements the {@link @microsoft/signalr.StreamInvocationMessage} interface. */\r\n    StreamInvocation = 4,\r\n    /** Indicates the message is a Cancel Invocation message and implements the {@link @microsoft/signalr.CancelInvocationMessage} interface. */\r\n    CancelInvocation = 5,\r\n    /** Indicates the message is a Ping message and implements the {@link @microsoft/signalr.PingMessage} interface. */\r\n    Ping = 6,\r\n    /** Indicates the message is a Close message and implements the {@link @microsoft/signalr.CloseMessage} interface. */\r\n    Close = 7,\r\n}\r\n\r\n/** Defines a dictionary of string keys and string values representing headers attached to a Hub message. */\r\nexport interface MessageHeaders {\r\n    /** Gets or sets the header with the specified key. */\r\n    [key: string]: string;\r\n}\r\n\r\n/** Union type of all known Hub messages. */\r\nexport type HubMessage =\r\n    InvocationMessage |\r\n    StreamInvocationMessage |\r\n    StreamItemMessage |\r\n    CompletionMessage |\r\n    CancelInvocationMessage |\r\n    PingMessage |\r\n    CloseMessage;\r\n\r\n/** Defines properties common to all Hub messages. */\r\nexport interface HubMessageBase {\r\n    /** A {@link @microsoft/signalr.MessageType} value indicating the type of this message. */\r\n    readonly type: MessageType;\r\n}\r\n\r\n/** Defines properties common to all Hub messages relating to a specific invocation. */\r\nexport interface HubInvocationMessage extends HubMessageBase {\r\n    /** A {@link @microsoft/signalr.MessageHeaders} dictionary containing headers attached to the message. */\r\n    readonly headers?: MessageHeaders;\r\n    /** The ID of the invocation relating to this message.\r\n     *\r\n     * This is expected to be present for {@link @microsoft/signalr.StreamInvocationMessage} and {@link @microsoft/signalr.CompletionMessage}. It may\r\n     * be 'undefined' for an {@link @microsoft/signalr.InvocationMessage} if the sender does not expect a response.\r\n     */\r\n    readonly invocationId?: string;\r\n}\r\n\r\n/** A hub message representing a non-streaming invocation. */\r\nexport interface InvocationMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Invocation;\r\n    /** The target method name. */\r\n    readonly target: string;\r\n    /** The target method arguments. */\r\n    readonly arguments: any[];\r\n    /** The target methods stream IDs. */\r\n    readonly streamIds?: string[];\r\n}\r\n\r\n/** A hub message representing a streaming invocation. */\r\nexport interface StreamInvocationMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.StreamInvocation;\r\n\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n    /** The target method name. */\r\n    readonly target: string;\r\n    /** The target method arguments. */\r\n    readonly arguments: any[];\r\n    /** The target methods stream IDs. */\r\n    readonly streamIds?: string[];\r\n}\r\n\r\n/** A hub message representing a single item produced as part of a result stream. */\r\nexport interface StreamItemMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.StreamItem;\r\n\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n\r\n    /** The item produced by the server. */\r\n    readonly item?: any;\r\n}\r\n\r\n/** A hub message representing the result of an invocation. */\r\nexport interface CompletionMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Completion;\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n    /** The error produced by the invocation, if any.\r\n     *\r\n     * Either {@link @microsoft/signalr.CompletionMessage.error} or {@link @microsoft/signalr.CompletionMessage.result} must be defined, but not both.\r\n     */\r\n    readonly error?: string;\r\n    /** The result produced by the invocation, if any.\r\n     *\r\n     * Either {@link @microsoft/signalr.CompletionMessage.error} or {@link @microsoft/signalr.CompletionMessage.result} must be defined, but not both.\r\n     */\r\n    readonly result?: any;\r\n}\r\n\r\n/** A hub message indicating that the sender is still active. */\r\nexport interface PingMessage extends HubMessageBase {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Ping;\r\n}\r\n\r\n/** A hub message indicating that the sender is closing the connection.\r\n *\r\n * If {@link @microsoft/signalr.CloseMessage.error} is defined, the sender is closing the connection due to an error.\r\n */\r\nexport interface CloseMessage extends HubMessageBase {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Close;\r\n    /** The error that triggered the close, if any.\r\n     *\r\n     * If this property is undefined, the connection was closed normally and without error.\r\n     */\r\n    readonly error?: string;\r\n\r\n    /** If true, clients with automatic reconnects enabled should attempt to reconnect after receiving the CloseMessage. Otherwise, they should not. */\r\n    readonly allowReconnect?: boolean;\r\n}\r\n\r\n/** A hub message sent to request that a streaming invocation be canceled. */\r\nexport interface CancelInvocationMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.CancelInvocation;\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n}\r\n\r\n/** A protocol abstraction for communicating with SignalR Hubs.  */\r\nexport interface IHubProtocol {\r\n    /** The name of the protocol. This is used by SignalR to resolve the protocol between the client and server. */\r\n    readonly name: string;\r\n    /** The version of the protocol. */\r\n    readonly version: number;\r\n    /** The {@link @microsoft/signalr.TransferFormat} of the protocol. */\r\n    readonly transferFormat: TransferFormat;\r\n\r\n    /** Creates an array of {@link @microsoft/signalr.HubMessage} objects from the specified serialized representation.\r\n     *\r\n     * If {@link @microsoft/signalr.IHubProtocol.transferFormat} is 'Text', the `input` parameter must be a string, otherwise it must be an ArrayBuffer.\r\n     *\r\n     * @param {string | ArrayBuffer} input A string or ArrayBuffer containing the serialized representation.\r\n     * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n     */\r\n    parseMessages(input: string | ArrayBuffer, logger: ILogger): HubMessage[];\r\n\r\n    /** Writes the specified {@link @microsoft/signalr.HubMessage} to a string or ArrayBuffer and returns it.\r\n     *\r\n     * If {@link @microsoft/signalr.IHubProtocol.transferFormat} is 'Text', the result of this method will be a string, otherwise it will be an ArrayBuffer.\r\n     *\r\n     * @param {HubMessage} message The message to write.\r\n     * @returns {string | ArrayBuffer} A string or ArrayBuffer containing the serialized representation of the message.\r\n     */\r\n    writeMessage(message: HubMessage): string | ArrayBuffer;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HandshakeProtocol, HandshakeRequestMessage, HandshakeResponseMessage } from \"./HandshakeProtocol\";\r\nimport { IConnection } from \"./IConnection\";\r\nimport { CancelInvocationMessage, CompletionMessage, IHubProtocol, InvocationMessage, MessageType, StreamInvocationMessage, StreamItemMessage } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { IRetryPolicy } from \"./IRetryPolicy\";\r\nimport { IStreamResult } from \"./Stream\";\r\nimport { Subject } from \"./Subject\";\r\nimport { Arg, getErrorString, Platform } from \"./Utils\";\r\n\r\nconst DEFAULT_TIMEOUT_IN_MS: number = 30 * 1000;\r\nconst DEFAULT_PING_INTERVAL_IN_MS: number = 15 * 1000;\r\n\r\n/** Describes the current state of the {@link HubConnection} to the server. */\r\nexport enum HubConnectionState {\r\n    /** The hub connection is disconnected. */\r\n    Disconnected = \"Disconnected\",\r\n    /** The hub connection is connecting. */\r\n    Connecting = \"Connecting\",\r\n    /** The hub connection is connected. */\r\n    Connected = \"Connected\",\r\n    /** The hub connection is disconnecting. */\r\n    Disconnecting = \"Disconnecting\",\r\n    /** The hub connection is reconnecting. */\r\n    Reconnecting = \"Reconnecting\",\r\n}\r\n\r\n/** Represents a connection to a SignalR Hub. */\r\nexport class HubConnection {\r\n    private readonly _cachedPingMessage: string | ArrayBuffer;\r\n    // Needs to not start with _ for tests\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private readonly connection: IConnection;\r\n    private readonly _logger: ILogger;\r\n    private readonly _reconnectPolicy?: IRetryPolicy;\r\n    private _protocol: IHubProtocol;\r\n    private _handshakeProtocol: HandshakeProtocol;\r\n    private _callbacks: { [invocationId: string]: (invocationEvent: StreamItemMessage | CompletionMessage | null, error?: Error) => void };\r\n    private _methods: { [name: string]: ((...args: any[]) => void)[] };\r\n    private _invocationId: number;\r\n\r\n    private _closedCallbacks: ((error?: Error) => void)[];\r\n    private _reconnectingCallbacks: ((error?: Error) => void)[];\r\n    private _reconnectedCallbacks: ((connectionId?: string) => void)[];\r\n\r\n    private _receivedHandshakeResponse: boolean;\r\n    private _handshakeResolver!: (value?: PromiseLike<{}>) => void;\r\n    private _handshakeRejecter!: (reason?: any) => void;\r\n    private _stopDuringStartError?: Error;\r\n\r\n    private _connectionState: HubConnectionState;\r\n    // connectionStarted is tracked independently from connectionState, so we can check if the\r\n    // connection ever did successfully transition from connecting to connected before disconnecting.\r\n    private _connectionStarted: boolean;\r\n    private _startPromise?: Promise<void>;\r\n    private _stopPromise?: Promise<void>;\r\n    private _nextKeepAlive: number = 0;\r\n\r\n    // The type of these a) doesn't matter and b) varies when building in browser and node contexts\r\n    // Since we're building the WebPack bundle directly from the TypeScript, this matters (previously\r\n    // we built the bundle from the compiled JavaScript).\r\n    private _reconnectDelayHandle?: any;\r\n    private _timeoutHandle?: any;\r\n    private _pingServerHandle?: any;\r\n\r\n    private _freezeEventListener = () =>\r\n    {\r\n        this._logger.log(LogLevel.Warning, \"The page is being frozen, this will likely lead to the connection being closed and messages being lost. For more information see the docs at https://docs.microsoft.com/aspnet/core/signalr/javascript-client#bsleep\");\r\n    };\r\n\r\n    /** The server timeout in milliseconds.\r\n     *\r\n     * If this timeout elapses without receiving any messages from the server, the connection will be terminated with an error.\r\n     * The default timeout value is 30,000 milliseconds (30 seconds).\r\n     */\r\n    public serverTimeoutInMilliseconds: number;\r\n\r\n    /** Default interval at which to ping the server.\r\n     *\r\n     * The default value is 15,000 milliseconds (15 seconds).\r\n     * Allows the server to detect hard disconnects (like when a client unplugs their computer).\r\n     * The ping will happen at most as often as the server pings.\r\n     * If the server pings every 5 seconds, a value lower than 5 will ping every 5 seconds.\r\n     */\r\n    public keepAliveIntervalInMilliseconds: number;\r\n\r\n    /** @internal */\r\n    // Using a public static factory method means we can have a private constructor and an _internal_\r\n    // create method that can be used by HubConnectionBuilder. An \"internal\" constructor would just\r\n    // be stripped away and the '.d.ts' file would have no constructor, which is interpreted as a\r\n    // public parameter-less constructor.\r\n    public static create(connection: IConnection, logger: ILogger, protocol: IHubProtocol, reconnectPolicy?: IRetryPolicy): HubConnection {\r\n        return new HubConnection(connection, logger, protocol, reconnectPolicy);\r\n    }\r\n\r\n    private constructor(connection: IConnection, logger: ILogger, protocol: IHubProtocol, reconnectPolicy?: IRetryPolicy) {\r\n        Arg.isRequired(connection, \"connection\");\r\n        Arg.isRequired(logger, \"logger\");\r\n        Arg.isRequired(protocol, \"protocol\");\r\n\r\n        this.serverTimeoutInMilliseconds = DEFAULT_TIMEOUT_IN_MS;\r\n        this.keepAliveIntervalInMilliseconds = DEFAULT_PING_INTERVAL_IN_MS;\r\n\r\n        this._logger = logger;\r\n        this._protocol = protocol;\r\n        this.connection = connection;\r\n        this._reconnectPolicy = reconnectPolicy;\r\n        this._handshakeProtocol = new HandshakeProtocol();\r\n\r\n        this.connection.onreceive = (data: any) => this._processIncomingData(data);\r\n        this.connection.onclose = (error?: Error) => this._connectionClosed(error);\r\n\r\n        this._callbacks = {};\r\n        this._methods = {};\r\n        this._closedCallbacks = [];\r\n        this._reconnectingCallbacks = [];\r\n        this._reconnectedCallbacks = [];\r\n        this._invocationId = 0;\r\n        this._receivedHandshakeResponse = false;\r\n        this._connectionState = HubConnectionState.Disconnected;\r\n        this._connectionStarted = false;\r\n\r\n        this._cachedPingMessage = this._protocol.writeMessage({ type: MessageType.Ping });\r\n    }\r\n\r\n    /** Indicates the state of the {@link HubConnection} to the server. */\r\n    get state(): HubConnectionState {\r\n        return this._connectionState;\r\n    }\r\n\r\n    /** Represents the connection id of the {@link HubConnection} on the server. The connection id will be null when the connection is either\r\n     *  in the disconnected state or if the negotiation step was skipped.\r\n     */\r\n    get connectionId(): string | null {\r\n        return this.connection ? (this.connection.connectionId || null) : null;\r\n    }\r\n\r\n    /** Indicates the url of the {@link HubConnection} to the server. */\r\n    get baseUrl(): string {\r\n        return this.connection.baseUrl || \"\";\r\n    }\r\n\r\n    /**\r\n     * Sets a new url for the HubConnection. Note that the url can only be changed when the connection is in either the Disconnected or\r\n     * Reconnecting states.\r\n     * @param {string} url The url to connect to.\r\n     */\r\n    set baseUrl(url: string) {\r\n        if (this._connectionState !== HubConnectionState.Disconnected && this._connectionState !== HubConnectionState.Reconnecting) {\r\n            throw new Error(\"The HubConnection must be in the Disconnected or Reconnecting state to change the url.\");\r\n        }\r\n\r\n        if (!url) {\r\n            throw new Error(\"The HubConnection url must be a valid url.\");\r\n        }\r\n\r\n        this.connection.baseUrl = url;\r\n    }\r\n\r\n    /** Starts the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully established, or rejects with an error.\r\n     */\r\n    public start(): Promise<void> {\r\n        this._startPromise = this._startWithStateTransitions();\r\n        return this._startPromise;\r\n    }\r\n\r\n    private async _startWithStateTransitions(): Promise<void> {\r\n        if (this._connectionState !== HubConnectionState.Disconnected) {\r\n            return Promise.reject(new Error(\"Cannot start a HubConnection that is not in the 'Disconnected' state.\"));\r\n        }\r\n\r\n        this._connectionState = HubConnectionState.Connecting;\r\n        this._logger.log(LogLevel.Debug, \"Starting HubConnection.\");\r\n\r\n        try {\r\n            await this._startInternal();\r\n\r\n            if (Platform.isBrowser) {\r\n                // Log when the browser freezes the tab so users know why their connection unexpectedly stopped working\r\n                window.document.addEventListener(\"freeze\", this._freezeEventListener);\r\n            }\r\n\r\n            this._connectionState = HubConnectionState.Connected;\r\n            this._connectionStarted = true;\r\n            this._logger.log(LogLevel.Debug, \"HubConnection connected successfully.\");\r\n        } catch (e) {\r\n            this._connectionState = HubConnectionState.Disconnected;\r\n            this._logger.log(LogLevel.Debug, `HubConnection failed to start successfully because of error '${e}'.`);\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n\r\n    private async _startInternal() {\r\n        this._stopDuringStartError = undefined;\r\n        this._receivedHandshakeResponse = false;\r\n        // Set up the promise before any connection is (re)started otherwise it could race with received messages\r\n        const handshakePromise = new Promise((resolve, reject) => {\r\n            this._handshakeResolver = resolve;\r\n            this._handshakeRejecter = reject;\r\n        });\r\n\r\n        await this.connection.start(this._protocol.transferFormat);\r\n\r\n        try {\r\n            const handshakeRequest: HandshakeRequestMessage = {\r\n                protocol: this._protocol.name,\r\n                version: this._protocol.version,\r\n            };\r\n\r\n            this._logger.log(LogLevel.Debug, \"Sending handshake request.\");\r\n\r\n            await this._sendMessage(this._handshakeProtocol.writeHandshakeRequest(handshakeRequest));\r\n\r\n            this._logger.log(LogLevel.Information, `Using HubProtocol '${this._protocol.name}'.`);\r\n\r\n            // defensively cleanup timeout in case we receive a message from the server before we finish start\r\n            this._cleanupTimeout();\r\n            this._resetTimeoutPeriod();\r\n            this._resetKeepAliveInterval();\r\n\r\n            await handshakePromise;\r\n\r\n            // It's important to check the stopDuringStartError instead of just relying on the handshakePromise\r\n            // being rejected on close, because this continuation can run after both the handshake completed successfully\r\n            // and the connection was closed.\r\n            if (this._stopDuringStartError) {\r\n                // It's important to throw instead of returning a rejected promise, because we don't want to allow any state\r\n                // transitions to occur between now and the calling code observing the exceptions. Returning a rejected promise\r\n                // will cause the calling continuation to get scheduled to run later.\r\n                // eslint-disable-next-line @typescript-eslint/no-throw-literal\r\n                throw this._stopDuringStartError;\r\n            }\r\n        } catch (e) {\r\n            this._logger.log(LogLevel.Debug, `Hub handshake failed with error '${e}' during start(). Stopping HubConnection.`);\r\n\r\n            this._cleanupTimeout();\r\n            this._cleanupPingTimer();\r\n\r\n            // HttpConnection.stop() should not complete until after the onclose callback is invoked.\r\n            // This will transition the HubConnection to the disconnected state before HttpConnection.stop() completes.\r\n            await this.connection.stop(e);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /** Stops the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully terminated, or rejects with an error.\r\n     */\r\n    public async stop(): Promise<void> {\r\n        // Capture the start promise before the connection might be restarted in an onclose callback.\r\n        const startPromise = this._startPromise;\r\n\r\n        this._stopPromise = this._stopInternal();\r\n        await this._stopPromise;\r\n\r\n        try {\r\n            // Awaiting undefined continues immediately\r\n            await startPromise;\r\n        } catch (e) {\r\n            // This exception is returned to the user as a rejected Promise from the start method.\r\n        }\r\n    }\r\n\r\n    private _stopInternal(error?: Error): Promise<void> {\r\n        if (this._connectionState === HubConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HubConnection.stop(${error}) ignored because it is already in the disconnected state.`);\r\n            return Promise.resolve();\r\n        }\r\n\r\n        if (this._connectionState === HubConnectionState.Disconnecting) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\r\n            return this._stopPromise!;\r\n        }\r\n\r\n        this._connectionState = HubConnectionState.Disconnecting;\r\n\r\n        this._logger.log(LogLevel.Debug, \"Stopping HubConnection.\");\r\n\r\n        if (this._reconnectDelayHandle) {\r\n            // We're in a reconnect delay which means the underlying connection is currently already stopped.\r\n            // Just clear the handle to stop the reconnect loop (which no one is waiting on thankfully) and\r\n            // fire the onclose callbacks.\r\n            this._logger.log(LogLevel.Debug, \"Connection stopped during reconnect delay. Done reconnecting.\");\r\n\r\n            clearTimeout(this._reconnectDelayHandle);\r\n            this._reconnectDelayHandle = undefined;\r\n\r\n            this._completeClose();\r\n            return Promise.resolve();\r\n        }\r\n\r\n        this._cleanupTimeout();\r\n        this._cleanupPingTimer();\r\n        this._stopDuringStartError = error || new Error(\"The connection was stopped before the hub handshake could complete.\");\r\n\r\n        // HttpConnection.stop() should not complete until after either HttpConnection.start() fails\r\n        // or the onclose callback is invoked. The onclose callback will transition the HubConnection\r\n        // to the disconnected state if need be before HttpConnection.stop() completes.\r\n        return this.connection.stop(error);\r\n    }\r\n\r\n    /** Invokes a streaming hub method on the server using the specified name and arguments.\r\n     *\r\n     * @typeparam T The type of the items returned by the server.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {IStreamResult<T>} An object that yields results from the server as they are received.\r\n     */\r\n    public stream<T = any>(methodName: string, ...args: any[]): IStreamResult<T> {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const invocationDescriptor = this._createStreamInvocation(methodName, args, streamIds);\r\n\r\n        // eslint-disable-next-line prefer-const\r\n        let promiseQueue: Promise<void>;\r\n\r\n        const subject = new Subject<T>();\r\n        subject.cancelCallback = () => {\r\n            const cancelInvocation: CancelInvocationMessage = this._createCancelInvocation(invocationDescriptor.invocationId);\r\n\r\n            delete this._callbacks[invocationDescriptor.invocationId];\r\n\r\n            return promiseQueue.then(() => {\r\n                return this._sendWithProtocol(cancelInvocation);\r\n            });\r\n        };\r\n\r\n        this._callbacks[invocationDescriptor.invocationId] = (invocationEvent: CompletionMessage | StreamItemMessage | null, error?: Error) => {\r\n            if (error) {\r\n                subject.error(error);\r\n                return;\r\n            } else if (invocationEvent) {\r\n                // invocationEvent will not be null when an error is not passed to the callback\r\n                if (invocationEvent.type === MessageType.Completion) {\r\n                    if (invocationEvent.error) {\r\n                        subject.error(new Error(invocationEvent.error));\r\n                    } else {\r\n                        subject.complete();\r\n                    }\r\n                } else {\r\n                    subject.next((invocationEvent.item) as T);\r\n                }\r\n            }\r\n        };\r\n\r\n        promiseQueue = this._sendWithProtocol(invocationDescriptor)\r\n            .catch((e) => {\r\n                subject.error(e);\r\n                delete this._callbacks[invocationDescriptor.invocationId];\r\n            });\r\n\r\n        this._launchStreams(streams, promiseQueue);\r\n\r\n        return subject;\r\n    }\r\n\r\n    private _sendMessage(message: any) {\r\n        this._resetKeepAliveInterval();\r\n        return this.connection.send(message);\r\n    }\r\n\r\n    /**\r\n     * Sends a js object to the server.\r\n     * @param message The js object to serialize and send.\r\n     */\r\n    private _sendWithProtocol(message: any) {\r\n        return this._sendMessage(this._protocol.writeMessage(message));\r\n    }\r\n\r\n    /** Invokes a hub method on the server using the specified name and arguments. Does not wait for a response from the receiver.\r\n     *\r\n     * The Promise returned by this method resolves when the client has sent the invocation to the server. The server may still\r\n     * be processing the invocation.\r\n     *\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<void>} A Promise that resolves when the invocation has been successfully sent, or rejects with an error.\r\n     */\r\n    public send(methodName: string, ...args: any[]): Promise<void> {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const sendPromise = this._sendWithProtocol(this._createInvocation(methodName, args, true, streamIds));\r\n\r\n        this._launchStreams(streams, sendPromise);\r\n\r\n        return sendPromise;\r\n    }\r\n\r\n    /** Invokes a hub method on the server using the specified name and arguments.\r\n     *\r\n     * The Promise returned by this method resolves when the server indicates it has finished invoking the method. When the promise\r\n     * resolves, the server has finished invoking the method. If the server method returns a result, it is produced as the result of\r\n     * resolving the Promise.\r\n     *\r\n     * @typeparam T The expected return type.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<T>} A Promise that resolves with the result of the server method (if any), or rejects with an error.\r\n     */\r\n    public invoke<T = any>(methodName: string, ...args: any[]): Promise<T> {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const invocationDescriptor = this._createInvocation(methodName, args, false, streamIds);\r\n\r\n        const p = new Promise<any>((resolve, reject) => {\r\n            // invocationId will always have a value for a non-blocking invocation\r\n            this._callbacks[invocationDescriptor.invocationId!] = (invocationEvent: StreamItemMessage | CompletionMessage | null, error?: Error) => {\r\n                if (error) {\r\n                    reject(error);\r\n                    return;\r\n                } else if (invocationEvent) {\r\n                    // invocationEvent will not be null when an error is not passed to the callback\r\n                    if (invocationEvent.type === MessageType.Completion) {\r\n                        if (invocationEvent.error) {\r\n                            reject(new Error(invocationEvent.error));\r\n                        } else {\r\n                            resolve(invocationEvent.result);\r\n                        }\r\n                    } else {\r\n                        reject(new Error(`Unexpected message type: ${invocationEvent.type}`));\r\n                    }\r\n                }\r\n            };\r\n\r\n            const promiseQueue = this._sendWithProtocol(invocationDescriptor)\r\n                .catch((e) => {\r\n                    reject(e);\r\n                    // invocationId will always have a value for a non-blocking invocation\r\n                    delete this._callbacks[invocationDescriptor.invocationId!];\r\n                });\r\n\r\n            this._launchStreams(streams, promiseQueue);\r\n        });\r\n\r\n        return p;\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the hub method with the specified method name is invoked.\r\n     *\r\n     * @param {string} methodName The name of the hub method to define.\r\n     * @param {Function} newMethod The handler that will be raised when the hub method is invoked.\r\n     */\r\n    public on(methodName: string, newMethod: (...args: any[]) => void): void {\r\n        if (!methodName || !newMethod) {\r\n            return;\r\n        }\r\n\r\n        methodName = methodName.toLowerCase();\r\n        if (!this._methods[methodName]) {\r\n            this._methods[methodName] = [];\r\n        }\r\n\r\n        // Preventing adding the same handler multiple times.\r\n        if (this._methods[methodName].indexOf(newMethod) !== -1) {\r\n            return;\r\n        }\r\n\r\n        this._methods[methodName].push(newMethod);\r\n    }\r\n\r\n    /** Removes all handlers for the specified hub method.\r\n     *\r\n     * @param {string} methodName The name of the method to remove handlers for.\r\n     */\r\n    public off(methodName: string): void;\r\n\r\n    /** Removes the specified handler for the specified hub method.\r\n     *\r\n     * You must pass the exact same Function instance as was previously passed to {@link @microsoft/signalr.HubConnection.on}. Passing a different instance (even if the function\r\n     * body is the same) will not remove the handler.\r\n     *\r\n     * @param {string} methodName The name of the method to remove handlers for.\r\n     * @param {Function} method The handler to remove. This must be the same Function instance as the one passed to {@link @microsoft/signalr.HubConnection.on}.\r\n     */\r\n    public off(methodName: string, method: (...args: any[]) => void): void;\r\n    public off(methodName: string, method?: (...args: any[]) => void): void {\r\n        if (!methodName) {\r\n            return;\r\n        }\r\n\r\n        methodName = methodName.toLowerCase();\r\n        const handlers = this._methods[methodName];\r\n        if (!handlers) {\r\n            return;\r\n        }\r\n        if (method) {\r\n            const removeIdx = handlers.indexOf(method);\r\n            if (removeIdx !== -1) {\r\n                handlers.splice(removeIdx, 1);\r\n                if (handlers.length === 0) {\r\n                    delete this._methods[methodName];\r\n                }\r\n            }\r\n        } else {\r\n            delete this._methods[methodName];\r\n        }\r\n\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the connection is closed.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection is closed. Optionally receives a single argument containing the error that caused the connection to close (if any).\r\n     */\r\n    public onclose(callback: (error?: Error) => void): void {\r\n        if (callback) {\r\n            this._closedCallbacks.push(callback);\r\n        }\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the connection starts reconnecting.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection starts reconnecting. Optionally receives a single argument containing the error that caused the connection to start reconnecting (if any).\r\n     */\r\n    public onreconnecting(callback: (error?: Error) => void): void {\r\n        if (callback) {\r\n            this._reconnectingCallbacks.push(callback);\r\n        }\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the connection successfully reconnects.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection successfully reconnects.\r\n     */\r\n    public onreconnected(callback: (connectionId?: string) => void): void {\r\n        if (callback) {\r\n            this._reconnectedCallbacks.push(callback);\r\n        }\r\n    }\r\n\r\n    private _processIncomingData(data: any) {\r\n        this._cleanupTimeout();\r\n\r\n        if (!this._receivedHandshakeResponse) {\r\n            data = this._processHandshakeResponse(data);\r\n            this._receivedHandshakeResponse = true;\r\n        }\r\n\r\n        // Data may have all been read when processing handshake response\r\n        if (data) {\r\n            // Parse the messages\r\n            const messages = this._protocol.parseMessages(data, this._logger);\r\n\r\n            for (const message of messages) {\r\n                switch (message.type) {\r\n                    case MessageType.Invocation:\r\n                        this._invokeClientMethod(message);\r\n                        break;\r\n                    case MessageType.StreamItem:\r\n                    case MessageType.Completion: {\r\n                        const callback = this._callbacks[message.invocationId];\r\n                        if (callback) {\r\n                            if (message.type === MessageType.Completion) {\r\n                                delete this._callbacks[message.invocationId];\r\n                            }\r\n                            try {\r\n                                callback(message);\r\n                            } catch (e) {\r\n                                this._logger.log(LogLevel.Error, `Stream callback threw error: ${getErrorString(e)}`);\r\n                            }\r\n                        }\r\n                        break;\r\n                    }\r\n                    case MessageType.Ping:\r\n                        // Don't care about pings\r\n                        break;\r\n                    case MessageType.Close: {\r\n                        this._logger.log(LogLevel.Information, \"Close message received from server.\");\r\n\r\n                        const error = message.error ? new Error(\"Server returned an error on close: \" + message.error) : undefined;\r\n\r\n                        if (message.allowReconnect === true) {\r\n                            // It feels wrong not to await connection.stop() here, but processIncomingData is called as part of an onreceive callback which is not async,\r\n                            // this is already the behavior for serverTimeout(), and HttpConnection.Stop() should catch and log all possible exceptions.\r\n\r\n                            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n                            this.connection.stop(error);\r\n                        } else {\r\n                            // We cannot await stopInternal() here, but subsequent calls to stop() will await this if stopInternal() is still ongoing.\r\n                            this._stopPromise = this._stopInternal(error);\r\n                        }\r\n\r\n                        break;\r\n                    }\r\n                    default:\r\n                        this._logger.log(LogLevel.Warning, `Invalid message type: ${message.type}.`);\r\n                        break;\r\n                }\r\n            }\r\n        }\r\n\r\n        this._resetTimeoutPeriod();\r\n    }\r\n\r\n    private _processHandshakeResponse(data: any): any {\r\n        let responseMessage: HandshakeResponseMessage;\r\n        let remainingData: any;\r\n\r\n        try {\r\n            [remainingData, responseMessage] = this._handshakeProtocol.parseHandshakeResponse(data);\r\n        } catch (e) {\r\n            const message = \"Error parsing handshake response: \" + e;\r\n            this._logger.log(LogLevel.Error, message);\r\n\r\n            const error = new Error(message);\r\n            this._handshakeRejecter(error);\r\n            throw error;\r\n        }\r\n        if (responseMessage.error) {\r\n            const message = \"Server returned handshake error: \" + responseMessage.error;\r\n            this._logger.log(LogLevel.Error, message);\r\n\r\n            const error = new Error(message);\r\n            this._handshakeRejecter(error);\r\n            throw error;\r\n        } else {\r\n            this._logger.log(LogLevel.Debug, \"Server handshake complete.\");\r\n        }\r\n\r\n        this._handshakeResolver();\r\n        return remainingData;\r\n    }\r\n\r\n    private _resetKeepAliveInterval() {\r\n        if (this.connection.features.inherentKeepAlive) {\r\n            return;\r\n        }\r\n\r\n        // Set the time we want the next keep alive to be sent\r\n        // Timer will be setup on next message receive\r\n        this._nextKeepAlive = new Date().getTime() + this.keepAliveIntervalInMilliseconds;\r\n\r\n        this._cleanupPingTimer();\r\n    }\r\n\r\n    private _resetTimeoutPeriod() {\r\n        if (!this.connection.features || !this.connection.features.inherentKeepAlive) {\r\n            // Set the timeout timer\r\n            this._timeoutHandle = setTimeout(() => this.serverTimeout(), this.serverTimeoutInMilliseconds);\r\n\r\n            // Set keepAlive timer if there isn't one\r\n            if (this._pingServerHandle === undefined)\r\n            {\r\n                let nextPing = this._nextKeepAlive - new Date().getTime();\r\n                if (nextPing < 0) {\r\n                    nextPing = 0;\r\n                }\r\n\r\n                // The timer needs to be set from a networking callback to avoid Chrome timer throttling from causing timers to run once a minute\r\n                this._pingServerHandle = setTimeout(async () => {\r\n                    if (this._connectionState === HubConnectionState.Connected) {\r\n                        try {\r\n                            await this._sendMessage(this._cachedPingMessage);\r\n                        } catch {\r\n                            // We don't care about the error. It should be seen elsewhere in the client.\r\n                            // The connection is probably in a bad or closed state now, cleanup the timer so it stops triggering\r\n                            this._cleanupPingTimer();\r\n                        }\r\n                    }\r\n                }, nextPing);\r\n            }\r\n        }\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private serverTimeout() {\r\n        // The server hasn't talked to us in a while. It doesn't like us anymore ... :(\r\n        // Terminate the connection, but we don't need to wait on the promise. This could trigger reconnecting.\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this.connection.stop(new Error(\"Server timeout elapsed without receiving a message from the server.\"));\r\n    }\r\n\r\n    private _invokeClientMethod(invocationMessage: InvocationMessage) {\r\n        const methods = this._methods[invocationMessage.target.toLowerCase()];\r\n        if (methods) {\r\n            try {\r\n                methods.forEach((m) => m.apply(this, invocationMessage.arguments));\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `A callback for the method ${invocationMessage.target.toLowerCase()} threw error '${e}'.`);\r\n            }\r\n\r\n            if (invocationMessage.invocationId) {\r\n                // This is not supported in v1. So we return an error to avoid blocking the server waiting for the response.\r\n                const message = \"Server requested a response, which is not supported in this version of the client.\";\r\n                this._logger.log(LogLevel.Error, message);\r\n\r\n                // We don't want to wait on the stop itself.\r\n                this._stopPromise = this._stopInternal(new Error(message));\r\n            }\r\n        } else {\r\n            this._logger.log(LogLevel.Warning, `No client method with the name '${invocationMessage.target}' found.`);\r\n        }\r\n    }\r\n\r\n    private _connectionClosed(error?: Error) {\r\n        this._logger.log(LogLevel.Debug, `HubConnection.connectionClosed(${error}) called while in state ${this._connectionState}.`);\r\n\r\n        // Triggering this.handshakeRejecter is insufficient because it could already be resolved without the continuation having run yet.\r\n        this._stopDuringStartError = this._stopDuringStartError || error || new Error(\"The underlying connection was closed before the hub handshake could complete.\");\r\n\r\n        // If the handshake is in progress, start will be waiting for the handshake promise, so we complete it.\r\n        // If it has already completed, this should just noop.\r\n        if (this._handshakeResolver) {\r\n            this._handshakeResolver();\r\n        }\r\n\r\n        this._cancelCallbacksWithError(error || new Error(\"Invocation canceled due to the underlying connection being closed.\"));\r\n\r\n        this._cleanupTimeout();\r\n        this._cleanupPingTimer();\r\n\r\n        if (this._connectionState === HubConnectionState.Disconnecting) {\r\n            this._completeClose(error);\r\n        } else if (this._connectionState === HubConnectionState.Connected && this._reconnectPolicy) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._reconnect(error);\r\n        } else if (this._connectionState === HubConnectionState.Connected) {\r\n            this._completeClose(error);\r\n        }\r\n\r\n        // If none of the above if conditions were true were called the HubConnection must be in either:\r\n        // 1. The Connecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail it.\r\n        // 2. The Reconnecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail the current reconnect attempt\r\n        //    and potentially continue the reconnect() loop.\r\n        // 3. The Disconnected state in which case we're already done.\r\n    }\r\n\r\n    private _completeClose(error?: Error) {\r\n        if (this._connectionStarted) {\r\n            this._connectionState = HubConnectionState.Disconnected;\r\n            this._connectionStarted = false;\r\n\r\n            if (Platform.isBrowser) {\r\n                window.document.removeEventListener(\"freeze\", this._freezeEventListener);\r\n            }\r\n\r\n            try {\r\n                this._closedCallbacks.forEach((c) => c.apply(this, [error]));\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `An onclose callback called with error '${error}' threw error '${e}'.`);\r\n            }\r\n        }\r\n    }\r\n\r\n    private async _reconnect(error?: Error) {\r\n        const reconnectStartTime = Date.now();\r\n        let previousReconnectAttempts = 0;\r\n        let retryError = error !== undefined ? error : new Error(\"Attempting to reconnect due to a unknown error.\");\r\n\r\n        let nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, 0, retryError);\r\n\r\n        if (nextRetryDelay === null) {\r\n            this._logger.log(LogLevel.Debug, \"Connection not reconnecting because the IRetryPolicy returned null on the first reconnect attempt.\");\r\n            this._completeClose(error);\r\n            return;\r\n        }\r\n\r\n        this._connectionState = HubConnectionState.Reconnecting;\r\n\r\n        if (error) {\r\n            this._logger.log(LogLevel.Information, `Connection reconnecting because of error '${error}'.`);\r\n        } else {\r\n            this._logger.log(LogLevel.Information, \"Connection reconnecting.\");\r\n        }\r\n\r\n        if (this._reconnectingCallbacks.length !== 0) {\r\n            try {\r\n                this._reconnectingCallbacks.forEach((c) => c.apply(this, [error]));\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `An onreconnecting callback called with error '${error}' threw error '${e}'.`);\r\n            }\r\n\r\n            // Exit early if an onreconnecting callback called connection.stop().\r\n            if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                this._logger.log(LogLevel.Debug, \"Connection left the reconnecting state in onreconnecting callback. Done reconnecting.\");\r\n                return;\r\n            }\r\n        }\r\n\r\n        while (nextRetryDelay !== null) {\r\n            this._logger.log(LogLevel.Information, `Reconnect attempt number ${previousReconnectAttempts} will start in ${nextRetryDelay} ms.`);\r\n\r\n            await new Promise((resolve) => {\r\n                this._reconnectDelayHandle = setTimeout(resolve, nextRetryDelay!);\r\n            });\r\n            this._reconnectDelayHandle = undefined;\r\n\r\n            if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                this._logger.log(LogLevel.Debug, \"Connection left the reconnecting state during reconnect delay. Done reconnecting.\");\r\n                return;\r\n            }\r\n\r\n            try {\r\n                await this._startInternal();\r\n\r\n                this._connectionState = HubConnectionState.Connected;\r\n                this._logger.log(LogLevel.Information, \"HubConnection reconnected successfully.\");\r\n\r\n                if (this._reconnectedCallbacks.length !== 0) {\r\n                    try {\r\n                        this._reconnectedCallbacks.forEach((c) => c.apply(this, [this.connection.connectionId]));\r\n                    } catch (e) {\r\n                        this._logger.log(LogLevel.Error, `An onreconnected callback called with connectionId '${this.connection.connectionId}; threw error '${e}'.`);\r\n                    }\r\n                }\r\n\r\n                return;\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Information, `Reconnect attempt failed because of error '${e}'.`);\r\n\r\n                if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                    this._logger.log(LogLevel.Debug, `Connection moved to the '${this._connectionState}' from the reconnecting state during reconnect attempt. Done reconnecting.`);\r\n                    // The TypeScript compiler thinks that connectionState must be Connected here. The TypeScript compiler is wrong.\r\n                    if (this._connectionState as any === HubConnectionState.Disconnecting) {\r\n                        this._completeClose();\r\n                    }\r\n                    return;\r\n                }\r\n\r\n                retryError = e instanceof Error ? e : new Error(e.toString());\r\n                nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, Date.now() - reconnectStartTime, retryError);\r\n            }\r\n        }\r\n\r\n        this._logger.log(LogLevel.Information, `Reconnect retries have been exhausted after ${Date.now() - reconnectStartTime} ms and ${previousReconnectAttempts} failed attempts. Connection disconnecting.`);\r\n\r\n        this._completeClose();\r\n    }\r\n\r\n    private _getNextRetryDelay(previousRetryCount: number, elapsedMilliseconds: number, retryReason: Error) {\r\n        try {\r\n            return this._reconnectPolicy!.nextRetryDelayInMilliseconds({\r\n                elapsedMilliseconds,\r\n                previousRetryCount,\r\n                retryReason,\r\n            });\r\n        } catch (e) {\r\n            this._logger.log(LogLevel.Error, `IRetryPolicy.nextRetryDelayInMilliseconds(${previousRetryCount}, ${elapsedMilliseconds}) threw error '${e}'.`);\r\n            return null;\r\n        }\r\n    }\r\n\r\n    private _cancelCallbacksWithError(error: Error) {\r\n        const callbacks = this._callbacks;\r\n        this._callbacks = {};\r\n\r\n        Object.keys(callbacks)\r\n            .forEach((key) => {\r\n                const callback = callbacks[key];\r\n                try {\r\n                    callback(null, error);\r\n                } catch (e) {\r\n                    this._logger.log(LogLevel.Error, `Stream 'error' callback called with '${error}' threw error: ${getErrorString(e)}`);\r\n                }\r\n            });\r\n    }\r\n\r\n    private _cleanupPingTimer(): void {\r\n        if (this._pingServerHandle) {\r\n            clearTimeout(this._pingServerHandle);\r\n            this._pingServerHandle = undefined;\r\n        }\r\n    }\r\n\r\n    private _cleanupTimeout(): void {\r\n        if (this._timeoutHandle) {\r\n            clearTimeout(this._timeoutHandle);\r\n        }\r\n    }\r\n\r\n    private _createInvocation(methodName: string, args: any[], nonblocking: boolean, streamIds: string[]): InvocationMessage {\r\n        if (nonblocking) {\r\n            if (streamIds.length !== 0) {\r\n                return {\r\n                    arguments: args,\r\n                    streamIds,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            } else {\r\n                return {\r\n                    arguments: args,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n        } else {\r\n            const invocationId = this._invocationId;\r\n            this._invocationId++;\r\n\r\n            if (streamIds.length !== 0) {\r\n                return {\r\n                    arguments: args,\r\n                    invocationId: invocationId.toString(),\r\n                    streamIds,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            } else {\r\n                return {\r\n                    arguments: args,\r\n                    invocationId: invocationId.toString(),\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n        }\r\n    }\r\n\r\n    private _launchStreams(streams: IStreamResult<any>[], promiseQueue: Promise<void>): void {\r\n        if (streams.length === 0) {\r\n            return;\r\n        }\r\n\r\n        // Synchronize stream data so they arrive in-order on the server\r\n        if (!promiseQueue) {\r\n            promiseQueue = Promise.resolve();\r\n        }\r\n\r\n        // We want to iterate over the keys, since the keys are the stream ids\r\n        // eslint-disable-next-line guard-for-in\r\n        for (const streamId in streams) {\r\n            streams[streamId].subscribe({\r\n                complete: () => {\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId)));\r\n                },\r\n                error: (err) => {\r\n                    let message: string;\r\n                    if (err instanceof Error) {\r\n                        message = err.message;\r\n                    } else if (err && err.toString) {\r\n                        message = err.toString();\r\n                    } else {\r\n                        message = \"Unknown error\";\r\n                    }\r\n\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId, message)));\r\n                },\r\n                next: (item) => {\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createStreamItemMessage(streamId, item)));\r\n                },\r\n            });\r\n        }\r\n    }\r\n\r\n    private _replaceStreamingParams(args: any[]): [IStreamResult<any>[], string[]] {\r\n        const streams: IStreamResult<any>[] = [];\r\n        const streamIds: string[] = [];\r\n        for (let i = 0; i < args.length; i++) {\r\n            const argument = args[i];\r\n            if (this._isObservable(argument)) {\r\n                const streamId = this._invocationId;\r\n                this._invocationId++;\r\n                // Store the stream for later use\r\n                streams[streamId] = argument;\r\n                streamIds.push(streamId.toString());\r\n\r\n                // remove stream from args\r\n                args.splice(i, 1);\r\n            }\r\n        }\r\n\r\n        return [streams, streamIds];\r\n    }\r\n\r\n    private _isObservable(arg: any): arg is IStreamResult<any> {\r\n        // This allows other stream implementations to just work (like rxjs)\r\n        return arg && arg.subscribe && typeof arg.subscribe === \"function\";\r\n    }\r\n\r\n    private _createStreamInvocation(methodName: string, args: any[], streamIds: string[]): StreamInvocationMessage {\r\n        const invocationId = this._invocationId;\r\n        this._invocationId++;\r\n\r\n        if (streamIds.length !== 0) {\r\n            return {\r\n                arguments: args,\r\n                invocationId: invocationId.toString(),\r\n                streamIds,\r\n                target: methodName,\r\n                type: MessageType.StreamInvocation,\r\n            };\r\n        } else {\r\n            return {\r\n                arguments: args,\r\n                invocationId: invocationId.toString(),\r\n                target: methodName,\r\n                type: MessageType.StreamInvocation,\r\n            };\r\n        }\r\n    }\r\n\r\n    private _createCancelInvocation(id: string): CancelInvocationMessage {\r\n        return {\r\n            invocationId: id,\r\n            type: MessageType.CancelInvocation,\r\n        };\r\n    }\r\n\r\n    private _createStreamItemMessage(id: string, item: any): StreamItemMessage {\r\n        return {\r\n            invocationId: id,\r\n            item,\r\n            type: MessageType.StreamItem,\r\n        };\r\n    }\r\n\r\n    private _createCompletionMessage(id: string, error?: any, result?: any): CompletionMessage {\r\n        if (error) {\r\n            return {\r\n                error,\r\n                invocationId: id,\r\n                type: MessageType.Completion,\r\n            };\r\n        }\r\n\r\n        return {\r\n            invocationId: id,\r\n            result,\r\n            type: MessageType.Completion,\r\n        };\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { IStreamResult, IStreamSubscriber, ISubscription } from \"./Stream\";\r\nimport { SubjectSubscription } from \"./Utils\";\r\n\r\n/** Stream implementation to stream items to the server. */\r\nexport class Subject<T> implements IStreamResult<T> {\r\n    /** @internal */\r\n    public observers: IStreamSubscriber<T>[];\r\n\r\n    /** @internal */\r\n    public cancelCallback?: () => Promise<void>;\r\n\r\n    constructor() {\r\n        this.observers = [];\r\n    }\r\n\r\n    public next(item: T): void {\r\n        for (const observer of this.observers) {\r\n            observer.next(item);\r\n        }\r\n    }\r\n\r\n    public error(err: any): void {\r\n        for (const observer of this.observers) {\r\n            if (observer.error) {\r\n                observer.error(err);\r\n            }\r\n        }\r\n    }\r\n\r\n    public complete(): void {\r\n        for (const observer of this.observers) {\r\n            if (observer.complete) {\r\n                observer.complete();\r\n            }\r\n        }\r\n    }\r\n\r\n    public subscribe(observer: IStreamSubscriber<T>): ISubscription<T> {\r\n        this.observers.push(observer);\r\n        return new SubjectSubscription(this, observer);\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { IRetryPolicy, RetryContext } from \"./IRetryPolicy\";\r\n\r\n// 0, 2, 10, 30 second delays before reconnect attempts.\r\nconst DEFAULT_RETRY_DELAYS_IN_MILLISECONDS = [0, 2000, 10000, 30000, null];\r\n\r\n/** @private */\r\nexport class DefaultReconnectPolicy implements IRetryPolicy {\r\n    private readonly _retryDelays: (number | null)[];\r\n\r\n    constructor(retryDelays?: number[]) {\r\n        this._retryDelays = retryDelays !== undefined ? [...retryDelays, null] : DEFAULT_RETRY_DELAYS_IN_MILLISECONDS;\r\n    }\r\n\r\n    public nextRetryDelayInMilliseconds(retryContext: RetryContext): number | null {\r\n        return this._retryDelays[retryContext.previousRetryCount];\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nexport abstract class HeaderNames {\r\n    static readonly Authorization = \"Authorization\";\r\n    static readonly Cookie = \"<PERSON>ie\";\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// This will be treated as a bit flag in the future, so we keep it using power-of-two values.\r\n/** Specifies a specific HTTP transport type. */\r\nexport enum HttpTransportType {\r\n    /** Specifies no transport preference. */\r\n    None = 0,\r\n    /** Specifies the WebSockets transport. */\r\n    WebSockets = 1,\r\n    /** Specifies the Server-Sent Events transport. */\r\n    ServerSentEvents = 2,\r\n    /** Specifies the Long Polling transport. */\r\n    LongPolling = 4,\r\n}\r\n\r\n/** Specifies the transfer format for a connection. */\r\nexport enum TransferFormat {\r\n    /** Specifies that only text data will be transmitted over the connection. */\r\n    Text = 1,\r\n    /** Specifies that binary data will be transmitted over the connection. */\r\n    Binary = 2,\r\n}\r\n\r\n/** An abstraction over the behavior of transports. This is designed to support the framework and not intended for use by applications. */\r\nexport interface ITransport {\r\n    connect(url: string, transferFormat: TransferFormat): Promise<void>;\r\n    send(data: any): Promise<void>;\r\n    stop(): Promise<void>;\r\n    onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    onclose: ((error?: Error) => void) | null;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Rough polyfill of https://developer.mozilla.org/en-US/docs/Web/API/AbortController\r\n// We don't actually ever use the API being polyfilled, we always use the polyfill because\r\n// it's a very new API right now.\r\n\r\n// Not exported from index.\r\n/** @private */\r\nexport class AbortController implements AbortSignal {\r\n    private _isAborted: boolean = false;\r\n    public onabort: (() => void) | null = null;\r\n\r\n    public abort(): void {\r\n        if (!this._isAborted) {\r\n            this._isAborted = true;\r\n            if (this.onabort) {\r\n                this.onabort();\r\n            }\r\n        }\r\n    }\r\n\r\n    get signal(): AbortSignal {\r\n        return this;\r\n    }\r\n\r\n    get aborted(): boolean {\r\n        return this._isAborted;\r\n    }\r\n}\r\n\r\n/** Represents a signal that can be monitored to determine if a request has been aborted. */\r\nexport interface AbortSignal {\r\n    /** Indicates if the request has been aborted. */\r\n    aborted: boolean;\r\n    /** Set this to a handler that will be invoked when the request is aborted. */\r\n    onabort: (() => void) | null;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortController } from \"./AbortController\";\r\nimport { HttpError, TimeoutError } from \"./Errors\";\r\nimport { HeaderNames } from \"./HeaderNames\";\r\nimport { HttpClient, HttpRequest } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { ITransport, TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, sendMessage } from \"./Utils\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\n\r\n// Not exported from 'index', this type is internal.\r\n/** @private */\r\nexport class LongPollingTransport implements ITransport {\r\n    private readonly _httpClient: HttpClient;\r\n    private readonly _accessTokenFactory: (() => string | Promise<string>) | undefined;\r\n    private readonly _logger: ILogger;\r\n    private readonly _options: IHttpConnectionOptions;\r\n    private readonly _pollAbort: AbortController;\r\n\r\n    private _url?: string;\r\n    private _running: boolean;\r\n    private _receiving?: Promise<void>;\r\n    private _closeError?: Error;\r\n\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((error?: Error) => void) | null;\r\n\r\n    // This is an internal type, not exported from 'index' so this is really just internal.\r\n    public get pollAborted(): boolean {\r\n        return this._pollAbort.aborted;\r\n    }\r\n\r\n    constructor(httpClient: HttpClient, accessTokenFactory: (() => string | Promise<string>) | undefined, logger: ILogger, options: IHttpConnectionOptions) {\r\n        this._httpClient = httpClient;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n        this._logger = logger;\r\n        this._pollAbort = new AbortController();\r\n        this._options = options;\r\n\r\n        this._running = false;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n\r\n    public async connect(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n\r\n        this._url = url;\r\n\r\n        this._logger.log(LogLevel.Trace, \"(LongPolling transport) Connecting.\");\r\n\r\n        // Allow binary format on Node and Browsers that support binary content (indicated by the presence of responseType property)\r\n        if (transferFormat === TransferFormat.Binary &&\r\n            (typeof XMLHttpRequest !== \"undefined\" && typeof new XMLHttpRequest().responseType !== \"string\")) {\r\n            throw new Error(\"Binary protocols over XmlHttpRequest not implementing advanced features are not supported.\");\r\n        }\r\n\r\n        const [name, value] = getUserAgentHeader();\r\n        const headers = { [name]: value, ...this._options.headers };\r\n\r\n        const pollOptions: HttpRequest = {\r\n            abortSignal: this._pollAbort.signal,\r\n            headers,\r\n            timeout: 100000,\r\n            withCredentials: this._options.withCredentials,\r\n        };\r\n\r\n        if (transferFormat === TransferFormat.Binary) {\r\n            pollOptions.responseType = \"arraybuffer\";\r\n        }\r\n\r\n        const token = await this._getAccessToken();\r\n        this._updateHeaderToken(pollOptions, token);\r\n\r\n        // Make initial long polling request\r\n        // Server uses first long polling request to finish initializing connection and it returns without data\r\n        const pollUrl = `${url}&_=${Date.now()}`;\r\n        this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\r\n        const response = await this._httpClient.get(pollUrl, pollOptions);\r\n        if (response.statusCode !== 200) {\r\n            this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\r\n\r\n            // Mark running as false so that the poll immediately ends and runs the close logic\r\n            this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\r\n            this._running = false;\r\n        } else {\r\n            this._running = true;\r\n        }\r\n\r\n        this._receiving = this._poll(this._url, pollOptions);\r\n    }\r\n\r\n    private async _getAccessToken(): Promise<string | null> {\r\n        if (this._accessTokenFactory) {\r\n            return await this._accessTokenFactory();\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private _updateHeaderToken(request: HttpRequest, token: string | null) {\r\n        if (!request.headers) {\r\n            request.headers = {};\r\n        }\r\n        if (token) {\r\n            request.headers[HeaderNames.Authorization] = `Bearer ${token}`;\r\n            return;\r\n        }\r\n        if (request.headers[HeaderNames.Authorization]) {\r\n            delete request.headers[HeaderNames.Authorization];\r\n        }\r\n    }\r\n\r\n    private async _poll(url: string, pollOptions: HttpRequest): Promise<void> {\r\n        try {\r\n            while (this._running) {\r\n                // We have to get the access token on each poll, in case it changes\r\n                const token = await this._getAccessToken();\r\n                this._updateHeaderToken(pollOptions, token);\r\n\r\n                try {\r\n                    const pollUrl = `${url}&_=${Date.now()}`;\r\n                    this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\r\n                    const response = await this._httpClient.get(pollUrl, pollOptions);\r\n\r\n                    if (response.statusCode === 204) {\r\n                        this._logger.log(LogLevel.Information, \"(LongPolling transport) Poll terminated by server.\");\r\n\r\n                        this._running = false;\r\n                    } else if (response.statusCode !== 200) {\r\n                        this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\r\n\r\n                        // Unexpected status code\r\n                        this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\r\n                        this._running = false;\r\n                    } else {\r\n                        // Process the response\r\n                        if (response.content) {\r\n                            this._logger.log(LogLevel.Trace, `(LongPolling transport) data received. ${getDataDetail(response.content, this._options.logMessageContent!)}.`);\r\n                            if (this.onreceive) {\r\n                                this.onreceive(response.content);\r\n                            }\r\n                        } else {\r\n                            // This is another way timeout manifest.\r\n                            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                        }\r\n                    }\r\n                } catch (e) {\r\n                    if (!this._running) {\r\n                        // Log but disregard errors that occur after stopping\r\n                        this._logger.log(LogLevel.Trace, `(LongPolling transport) Poll errored after shutdown: ${e.message}`);\r\n                    } else {\r\n                        if (e instanceof TimeoutError) {\r\n                            // Ignore timeouts and reissue the poll.\r\n                            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                        } else {\r\n                            // Close the connection with the error as the result.\r\n                            this._closeError = e;\r\n                            this._running = false;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        } finally {\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Polling complete.\");\r\n\r\n            // We will reach here with pollAborted==false when the server returned a response causing the transport to stop.\r\n            // If pollAborted==true then client initiated the stop and the stop method will raise the close event after DELETE is sent.\r\n            if (!this.pollAborted) {\r\n                this._raiseOnClose();\r\n            }\r\n        }\r\n    }\r\n\r\n    public async send(data: any): Promise<void> {\r\n        if (!this._running) {\r\n            return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\r\n        }\r\n        return sendMessage(this._logger, \"LongPolling\", this._httpClient, this._url!, this._accessTokenFactory, data, this._options);\r\n    }\r\n\r\n    public async stop(): Promise<void> {\r\n        this._logger.log(LogLevel.Trace, \"(LongPolling transport) Stopping polling.\");\r\n\r\n        // Tell receiving loop to stop, abort any current request, and then wait for it to finish\r\n        this._running = false;\r\n        this._pollAbort.abort();\r\n\r\n        try {\r\n            await this._receiving;\r\n\r\n            // Send DELETE to clean up long polling on the server\r\n            this._logger.log(LogLevel.Trace, `(LongPolling transport) sending DELETE request to ${this._url}.`);\r\n\r\n            const headers: {[k: string]: string} = {};\r\n            const [name, value] = getUserAgentHeader();\r\n            headers[name] = value;\r\n\r\n            const deleteOptions: HttpRequest = {\r\n                headers: { ...headers, ...this._options.headers },\r\n                timeout: this._options.timeout,\r\n                withCredentials: this._options.withCredentials,\r\n            };\r\n            const token = await this._getAccessToken();\r\n            this._updateHeaderToken(deleteOptions, token);\r\n            await this._httpClient.delete(this._url!, deleteOptions);\r\n\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) DELETE request sent.\");\r\n        } finally {\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Stop finished.\");\r\n\r\n            // Raise close event here instead of in polling\r\n            // It needs to happen after the DELETE request is sent\r\n            this._raiseOnClose();\r\n        }\r\n    }\r\n\r\n    private _raiseOnClose() {\r\n        if (this.onclose) {\r\n            let logMessage = \"(LongPolling transport) Firing onclose event.\";\r\n            if (this._closeError) {\r\n                logMessage += \" Error: \" + this._closeError;\r\n            }\r\n            this._logger.log(LogLevel.Trace, logMessage);\r\n            this.onclose(this._closeError);\r\n        }\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { MessageHeaders } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { ITransport, TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, Platform, sendMessage } from \"./Utils\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\n\r\n/** @private */\r\nexport class ServerSentEventsTransport implements ITransport {\r\n    private readonly _httpClient: HttpClient;\r\n    private readonly _accessTokenFactory: (() => string | Promise<string>) | undefined;\r\n    private readonly _logger: ILogger;\r\n    private readonly _options: IHttpConnectionOptions;\r\n    private _eventSource?: EventSource;\r\n    private _url?: string;\r\n\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((error?: Error) => void) | null;\r\n\r\n    constructor(httpClient: HttpClient, accessTokenFactory: (() => string | Promise<string>) | undefined, logger: ILogger,\r\n                options: IHttpConnectionOptions) {\r\n        this._httpClient = httpClient;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n        this._logger = logger;\r\n        this._options = options;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n\r\n    public async connect(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n\r\n        this._logger.log(LogLevel.Trace, \"(SSE transport) Connecting.\");\r\n\r\n        // set url before accessTokenFactory because this.url is only for send and we set the auth header instead of the query string for send\r\n        this._url = url;\r\n\r\n        if (this._accessTokenFactory) {\r\n            const token = await this._accessTokenFactory();\r\n            if (token) {\r\n                url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(token)}`;\r\n            }\r\n        }\r\n\r\n        return new Promise<void>((resolve, reject) => {\r\n            let opened = false;\r\n            if (transferFormat !== TransferFormat.Text) {\r\n                reject(new Error(\"The Server-Sent Events transport only supports the 'Text' transfer format\"));\r\n                return;\r\n            }\r\n\r\n            let eventSource: EventSource;\r\n            if (Platform.isBrowser || Platform.isWebWorker) {\r\n                eventSource = new this._options.EventSource!(url, { withCredentials: this._options.withCredentials });\r\n            } else {\r\n                // Non-browser passes cookies via the dictionary\r\n                const cookies = this._httpClient.getCookieString(url);\r\n                const headers: MessageHeaders = {};\r\n                headers.Cookie = cookies;\r\n                const [name, value] = getUserAgentHeader();\r\n                headers[name] = value;\r\n\r\n                eventSource = new this._options.EventSource!(url, { withCredentials: this._options.withCredentials, headers: { ...headers, ...this._options.headers} } as EventSourceInit);\r\n            }\r\n\r\n            try {\r\n                eventSource.onmessage = (e: MessageEvent) => {\r\n                    if (this.onreceive) {\r\n                        try {\r\n                            this._logger.log(LogLevel.Trace, `(SSE transport) data received. ${getDataDetail(e.data, this._options.logMessageContent!)}.`);\r\n                            this.onreceive(e.data);\r\n                        } catch (error) {\r\n                            this._close(error);\r\n                            return;\r\n                        }\r\n                    }\r\n                };\r\n\r\n                // @ts-ignore: not using event on purpose\r\n                eventSource.onerror = (e: Event) => {\r\n                    // EventSource doesn't give any useful information about server side closes.\r\n                    if (opened) {\r\n                        this._close();\r\n                    } else {\r\n                        reject(new Error(\"EventSource failed to connect. The connection could not be found on the server,\"\r\n                        + \" either the connection ID is not present on the server, or a proxy is refusing/buffering the connection.\"\r\n                        + \" If you have multiple servers check that sticky sessions are enabled.\"));\r\n                    }\r\n                };\r\n\r\n                eventSource.onopen = () => {\r\n                    this._logger.log(LogLevel.Information, `SSE connected to ${this._url}`);\r\n                    this._eventSource = eventSource;\r\n                    opened = true;\r\n                    resolve();\r\n                };\r\n            } catch (e) {\r\n                reject(e);\r\n                return;\r\n            }\r\n        });\r\n    }\r\n\r\n    public async send(data: any): Promise<void> {\r\n        if (!this._eventSource) {\r\n            return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\r\n        }\r\n        return sendMessage(this._logger, \"SSE\", this._httpClient, this._url!, this._accessTokenFactory, data, this._options);\r\n    }\r\n\r\n    public stop(): Promise<void> {\r\n        this._close();\r\n        return Promise.resolve();\r\n    }\r\n\r\n    private _close(e?: Error) {\r\n        if (this._eventSource) {\r\n            this._eventSource.close();\r\n            this._eventSource = undefined;\r\n\r\n            if (this.onclose) {\r\n                this.onclose(e);\r\n            }\r\n        }\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HeaderNames } from \"./HeaderNames\";\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { MessageHeaders } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { ITransport, TransferFormat } from \"./ITransport\";\r\nimport { WebSocketConstructor } from \"./Polyfills\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, Platform } from \"./Utils\";\r\n\r\n/** @private */\r\nexport class WebSocketTransport implements ITransport {\r\n    private readonly _logger: ILogger;\r\n    private readonly _accessTokenFactory: (() => string | Promise<string>) | undefined;\r\n    private readonly _logMessageContent: boolean;\r\n    private readonly _webSocketConstructor: WebSocketConstructor;\r\n    private readonly _httpClient: HttpClient;\r\n    private _webSocket?: WebSocket;\r\n    private _headers: MessageHeaders;\r\n\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((error?: Error) => void) | null;\r\n\r\n    constructor(httpClient: HttpClient, accessTokenFactory: (() => string | Promise<string>) | undefined, logger: ILogger,\r\n                logMessageContent: boolean, webSocketConstructor: WebSocketConstructor, headers: MessageHeaders) {\r\n        this._logger = logger;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n        this._logMessageContent = logMessageContent;\r\n        this._webSocketConstructor = webSocketConstructor;\r\n        this._httpClient = httpClient;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n        this._headers = headers;\r\n    }\r\n\r\n    public async connect(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n        this._logger.log(LogLevel.Trace, \"(WebSockets transport) Connecting.\");\r\n\r\n        if (this._accessTokenFactory) {\r\n            const token = await this._accessTokenFactory();\r\n            if (token) {\r\n                url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(token)}`;\r\n            }\r\n        }\r\n\r\n        return new Promise<void>((resolve, reject) => {\r\n            url = url.replace(/^http/, \"ws\");\r\n            let webSocket: WebSocket | undefined;\r\n            const cookies = this._httpClient.getCookieString(url);\r\n            let opened = false;\r\n\r\n            if (Platform.isNode) {\r\n                const headers: {[k: string]: string} = {};\r\n                const [name, value] = getUserAgentHeader();\r\n                headers[name] = value;\r\n\r\n                if (cookies) {\r\n                    headers[HeaderNames.Cookie] = `${cookies}`;\r\n                }\r\n\r\n                // Only pass headers when in non-browser environments\r\n                webSocket = new this._webSocketConstructor(url, undefined, {\r\n                    headers: { ...headers, ...this._headers },\r\n                });\r\n            }\r\n\r\n            if (!webSocket) {\r\n                // Chrome is not happy with passing 'undefined' as protocol\r\n                webSocket = new this._webSocketConstructor(url);\r\n            }\r\n\r\n            if (transferFormat === TransferFormat.Binary) {\r\n                webSocket.binaryType = \"arraybuffer\";\r\n            }\r\n\r\n            webSocket.onopen = (_event: Event) => {\r\n                this._logger.log(LogLevel.Information, `WebSocket connected to ${url}.`);\r\n                this._webSocket = webSocket;\r\n                opened = true;\r\n                resolve();\r\n            };\r\n\r\n            webSocket.onerror = (event: Event) => {\r\n                let error: any = null;\r\n                // ErrorEvent is a browser only type we need to check if the type exists before using it\r\n                if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\r\n                    error = event.error;\r\n                } else {\r\n                    error = \"There was an error with the transport\";\r\n                }\r\n\r\n                this._logger.log(LogLevel.Information, `(WebSockets transport) ${error}.`);\r\n            };\r\n\r\n            webSocket.onmessage = (message: MessageEvent) => {\r\n                this._logger.log(LogLevel.Trace, `(WebSockets transport) data received. ${getDataDetail(message.data, this._logMessageContent)}.`);\r\n                if (this.onreceive) {\r\n                    try {\r\n                        this.onreceive(message.data);\r\n                    } catch (error) {\r\n                        this._close(error);\r\n                        return;\r\n                    }\r\n                }\r\n            };\r\n\r\n            webSocket.onclose = (event: CloseEvent) => {\r\n                // Don't call close handler if connection was never established\r\n                // We'll reject the connect call instead\r\n                if (opened) {\r\n                    this._close(event);\r\n                } else {\r\n                    let error: any = null;\r\n                    // ErrorEvent is a browser only type we need to check if the type exists before using it\r\n                    if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\r\n                        error = event.error;\r\n                    } else {\r\n                        error = \"WebSocket failed to connect. The connection could not be found on the server,\"\r\n                        + \" either the endpoint may not be a SignalR endpoint,\"\r\n                        + \" the connection ID is not present on the server, or there is a proxy blocking WebSockets.\"\r\n                        + \" If you have multiple servers check that sticky sessions are enabled.\";\r\n                    }\r\n\r\n                    reject(new Error(error));\r\n                }\r\n            };\r\n        });\r\n    }\r\n\r\n    public send(data: any): Promise<void> {\r\n        if (this._webSocket && this._webSocket.readyState === this._webSocketConstructor.OPEN) {\r\n            this._logger.log(LogLevel.Trace, `(WebSockets transport) sending data. ${getDataDetail(data, this._logMessageContent)}.`);\r\n            this._webSocket.send(data);\r\n            return Promise.resolve();\r\n        }\r\n\r\n        return Promise.reject(\"WebSocket is not in the OPEN state\");\r\n    }\r\n\r\n    public stop(): Promise<void> {\r\n        if (this._webSocket) {\r\n            // Manually invoke onclose callback inline so we know the HttpConnection was closed properly before returning\r\n            // This also solves an issue where websocket.onclose could take 18+ seconds to trigger during network disconnects\r\n            this._close(undefined);\r\n        }\r\n\r\n        return Promise.resolve();\r\n    }\r\n\r\n    private _close(event?: CloseEvent | Error): void {\r\n        // webSocket will be null if the transport did not start successfully\r\n        if (this._webSocket) {\r\n            // Clear websocket handlers because we are considering the socket closed now\r\n            this._webSocket.onclose = () => {};\r\n            this._webSocket.onmessage = () => {};\r\n            this._webSocket.onerror = () => {};\r\n            this._webSocket.close();\r\n            this._webSocket = undefined;\r\n        }\r\n\r\n        this._logger.log(LogLevel.Trace, \"(WebSockets transport) socket closed.\");\r\n        if (this.onclose) {\r\n            if (this._isCloseEvent(event) && (event.wasClean === false || event.code !== 1000)) {\r\n                this.onclose(new Error(`WebSocket closed with status code: ${event.code} (${event.reason || \"no reason given\"}).`));\r\n            } else if (event instanceof Error) {\r\n                this.onclose(event);\r\n            } else {\r\n                this.onclose();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _isCloseEvent(event?: any): event is CloseEvent {\r\n        return event && typeof event.wasClean === \"boolean\" && typeof event.code === \"number\";\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { DefaultHttpClient } from \"./DefaultHttpClient\";\r\nimport { AggregateErrors, DisabledTransportError, FailedToNegotiateWithServerError, FailedToStartTransportError, HttpError, UnsupportedTransportError } from \"./Errors\";\r\nimport { HeaderNames } from \"./HeaderNames\";\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { IConnection } from \"./IConnection\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { HttpTransportType, ITransport, TransferFormat } from \"./ITransport\";\r\nimport { LongPollingTransport } from \"./LongPollingTransport\";\r\nimport { ServerSentEventsTransport } from \"./ServerSentEventsTransport\";\r\nimport { Arg, createLogger, getUserAgentHeader, Platform } from \"./Utils\";\r\nimport { WebSocketTransport } from \"./WebSocketTransport\";\r\n\r\n/** @private */\r\nconst enum ConnectionState {\r\n    Connecting = \"Connecting\",\r\n    Connected = \"Connected\",\r\n    Disconnected = \"Disconnected\",\r\n    Disconnecting = \"Disconnecting\",\r\n}\r\n\r\n/** @private */\r\nexport interface INegotiateResponse {\r\n    connectionId?: string;\r\n    connectionToken?: string;\r\n    negotiateVersion?: number;\r\n    availableTransports?: IAvailableTransport[];\r\n    url?: string;\r\n    accessToken?: string;\r\n    error?: string;\r\n}\r\n\r\n/** @private */\r\nexport interface IAvailableTransport {\r\n    transport: keyof typeof HttpTransportType;\r\n    transferFormats: (keyof typeof TransferFormat)[];\r\n}\r\n\r\nconst MAX_REDIRECTS = 100;\r\n\r\n/** @private */\r\nexport class HttpConnection implements IConnection {\r\n    private _connectionState: ConnectionState;\r\n    // connectionStarted is tracked independently from connectionState, so we can check if the\r\n    // connection ever did successfully transition from connecting to connected before disconnecting.\r\n    private _connectionStarted: boolean;\r\n    private readonly _httpClient: HttpClient;\r\n    private readonly _logger: ILogger;\r\n    private readonly _options: IHttpConnectionOptions;\r\n    // Needs to not start with _ to be available for tests\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private transport?: ITransport;\r\n    private _startInternalPromise?: Promise<void>;\r\n    private _stopPromise?: Promise<void>;\r\n    private _stopPromiseResolver: (value?: PromiseLike<void>) => void = () => {};\r\n    private _stopError?: Error;\r\n    private _accessTokenFactory?: () => string | Promise<string>;\r\n    private _sendQueue?: TransportSendQueue;\r\n\r\n    public readonly features: any = {};\r\n    public baseUrl: string;\r\n    public connectionId?: string;\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((e?: Error) => void) | null;\r\n\r\n    private readonly _negotiateVersion: number = 1;\r\n\r\n    constructor(url: string, options: IHttpConnectionOptions = {}) {\r\n        Arg.isRequired(url, \"url\");\r\n\r\n        this._logger = createLogger(options.logger);\r\n        this.baseUrl = this._resolveUrl(url);\r\n\r\n        options = options || {};\r\n        options.logMessageContent = options.logMessageContent === undefined ? false : options.logMessageContent;\r\n        if (typeof options.withCredentials === \"boolean\" || options.withCredentials === undefined) {\r\n            options.withCredentials = options.withCredentials === undefined ? true : options.withCredentials;\r\n        } else {\r\n            throw new Error(\"withCredentials option was not a 'boolean' or 'undefined' value\");\r\n        }\r\n        options.timeout = options.timeout === undefined ? 100 * 1000 : options.timeout;\r\n\r\n        let webSocketModule: any = null;\r\n        let eventSourceModule: any = null;\r\n\r\n        if (Platform.isNode && typeof require !== \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n            webSocketModule = requireFunc(\"ws\");\r\n            eventSourceModule = requireFunc(\"eventsource\");\r\n        }\r\n\r\n        if (!Platform.isNode && typeof WebSocket !== \"undefined\" && !options.WebSocket) {\r\n            options.WebSocket = WebSocket;\r\n        } else if (Platform.isNode && !options.WebSocket) {\r\n            if (webSocketModule) {\r\n                options.WebSocket = webSocketModule;\r\n            }\r\n        }\r\n\r\n        if (!Platform.isNode && typeof EventSource !== \"undefined\" && !options.EventSource) {\r\n            options.EventSource = EventSource;\r\n        } else if (Platform.isNode && !options.EventSource) {\r\n            if (typeof eventSourceModule !== \"undefined\") {\r\n                options.EventSource = eventSourceModule;\r\n            }\r\n        }\r\n\r\n        this._httpClient = options.httpClient || new DefaultHttpClient(this._logger);\r\n        this._connectionState = ConnectionState.Disconnected;\r\n        this._connectionStarted = false;\r\n        this._options = options;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n\r\n    public start(): Promise<void>;\r\n    public start(transferFormat: TransferFormat): Promise<void>;\r\n    public async start(transferFormat?: TransferFormat): Promise<void> {\r\n        transferFormat = transferFormat || TransferFormat.Binary;\r\n\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n\r\n        this._logger.log(LogLevel.Debug, `Starting connection with transfer format '${TransferFormat[transferFormat]}'.`);\r\n\r\n        if (this._connectionState !== ConnectionState.Disconnected) {\r\n            return Promise.reject(new Error(\"Cannot start an HttpConnection that is not in the 'Disconnected' state.\"));\r\n        }\r\n\r\n        this._connectionState = ConnectionState.Connecting;\r\n\r\n        this._startInternalPromise = this._startInternal(transferFormat);\r\n        await this._startInternalPromise;\r\n\r\n        // The TypeScript compiler thinks that connectionState must be Connecting here. The TypeScript compiler is wrong.\r\n        if (this._connectionState as any === ConnectionState.Disconnecting) {\r\n            // stop() was called and transitioned the client into the Disconnecting state.\r\n            const message = \"Failed to start the HttpConnection before stop() was called.\";\r\n            this._logger.log(LogLevel.Error, message);\r\n\r\n            // We cannot await stopPromise inside startInternal since stopInternal awaits the startInternalPromise.\r\n            await this._stopPromise;\r\n\r\n            return Promise.reject(new Error(message));\r\n        } else if (this._connectionState as any !== ConnectionState.Connected) {\r\n            // stop() was called and transitioned the client into the Disconnecting state.\r\n            const message = \"HttpConnection.startInternal completed gracefully but didn't enter the connection into the connected state!\";\r\n            this._logger.log(LogLevel.Error, message);\r\n            return Promise.reject(new Error(message));\r\n        }\r\n\r\n        this._connectionStarted = true;\r\n    }\r\n\r\n    public send(data: string | ArrayBuffer): Promise<void> {\r\n        if (this._connectionState !== ConnectionState.Connected) {\r\n            return Promise.reject(new Error(\"Cannot send data if the connection is not in the 'Connected' State.\"));\r\n        }\r\n\r\n        if (!this._sendQueue) {\r\n            this._sendQueue = new TransportSendQueue(this.transport!);\r\n        }\r\n\r\n        // Transport will not be null if state is connected\r\n        return this._sendQueue.send(data);\r\n    }\r\n\r\n    public async stop(error?: Error): Promise<void> {\r\n        if (this._connectionState === ConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnected state.`);\r\n            return Promise.resolve();\r\n        }\r\n\r\n        if (this._connectionState === ConnectionState.Disconnecting) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\r\n            return this._stopPromise;\r\n        }\r\n\r\n        this._connectionState = ConnectionState.Disconnecting;\r\n\r\n        this._stopPromise = new Promise((resolve) => {\r\n            // Don't complete stop() until stopConnection() completes.\r\n            this._stopPromiseResolver = resolve;\r\n        });\r\n\r\n        // stopInternal should never throw so just observe it.\r\n        await this._stopInternal(error);\r\n        await this._stopPromise;\r\n    }\r\n\r\n    private async _stopInternal(error?: Error): Promise<void> {\r\n        // Set error as soon as possible otherwise there is a race between\r\n        // the transport closing and providing an error and the error from a close message\r\n        // We would prefer the close message error.\r\n        this._stopError = error;\r\n\r\n        try {\r\n            await this._startInternalPromise;\r\n        } catch (e) {\r\n            // This exception is returned to the user as a rejected Promise from the start method.\r\n        }\r\n\r\n        // The transport's onclose will trigger stopConnection which will run our onclose event.\r\n        // The transport should always be set if currently connected. If it wasn't set, it's likely because\r\n        // stop was called during start() and start() failed.\r\n        if (this.transport) {\r\n            try {\r\n                await this.transport.stop();\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `HttpConnection.transport.stop() threw error '${e}'.`);\r\n                this._stopConnection();\r\n            }\r\n\r\n            this.transport = undefined;\r\n        } else {\r\n            this._logger.log(LogLevel.Debug, \"HttpConnection.transport is undefined in HttpConnection.stop() because start() failed.\");\r\n        }\r\n    }\r\n\r\n    private async _startInternal(transferFormat: TransferFormat): Promise<void> {\r\n        // Store the original base url and the access token factory since they may change\r\n        // as part of negotiating\r\n        let url = this.baseUrl;\r\n        this._accessTokenFactory = this._options.accessTokenFactory;\r\n\r\n        try {\r\n            if (this._options.skipNegotiation) {\r\n                if (this._options.transport === HttpTransportType.WebSockets) {\r\n                    // No need to add a connection ID in this case\r\n                    this.transport = this._constructTransport(HttpTransportType.WebSockets);\r\n                    // We should just call connect directly in this case.\r\n                    // No fallback or negotiate in this case.\r\n                    await this._startTransport(url, transferFormat);\r\n                } else {\r\n                    throw new Error(\"Negotiation can only be skipped when using the WebSocket transport directly.\");\r\n                }\r\n            } else {\r\n                let negotiateResponse: INegotiateResponse | null = null;\r\n                let redirects = 0;\r\n\r\n                do {\r\n                    negotiateResponse = await this._getNegotiationResponse(url);\r\n                    // the user tries to stop the connection when it is being started\r\n                    if (this._connectionState === ConnectionState.Disconnecting || this._connectionState === ConnectionState.Disconnected) {\r\n                        throw new Error(\"The connection was stopped during negotiation.\");\r\n                    }\r\n\r\n                    if (negotiateResponse.error) {\r\n                        throw new Error(negotiateResponse.error);\r\n                    }\r\n\r\n                    if ((negotiateResponse as any).ProtocolVersion) {\r\n                        throw new Error(\"Detected a connection attempt to an ASP.NET SignalR Server. This client only supports connecting to an ASP.NET Core SignalR Server. See https://aka.ms/signalr-core-differences for details.\");\r\n                    }\r\n\r\n                    if (negotiateResponse.url) {\r\n                        url = negotiateResponse.url;\r\n                    }\r\n\r\n                    if (negotiateResponse.accessToken) {\r\n                        // Replace the current access token factory with one that uses\r\n                        // the returned access token\r\n                        const accessToken = negotiateResponse.accessToken;\r\n                        this._accessTokenFactory = () => accessToken;\r\n                    }\r\n\r\n                    redirects++;\r\n                }\r\n                while (negotiateResponse.url && redirects < MAX_REDIRECTS);\r\n\r\n                if (redirects === MAX_REDIRECTS && negotiateResponse.url) {\r\n                    throw new Error(\"Negotiate redirection limit exceeded.\");\r\n                }\r\n\r\n                await this._createTransport(url, this._options.transport, negotiateResponse, transferFormat);\r\n            }\r\n\r\n            if (this.transport instanceof LongPollingTransport) {\r\n                this.features.inherentKeepAlive = true;\r\n            }\r\n\r\n            if (this._connectionState === ConnectionState.Connecting) {\r\n                // Ensure the connection transitions to the connected state prior to completing this.startInternalPromise.\r\n                // start() will handle the case when stop was called and startInternal exits still in the disconnecting state.\r\n                this._logger.log(LogLevel.Debug, \"The HttpConnection connected successfully.\");\r\n                this._connectionState = ConnectionState.Connected;\r\n            }\r\n\r\n            // stop() is waiting on us via this.startInternalPromise so keep this.transport around so it can clean up.\r\n            // This is the only case startInternal can exit in neither the connected nor disconnected state because stopConnection()\r\n            // will transition to the disconnected state. start() will wait for the transition using the stopPromise.\r\n        } catch (e) {\r\n            this._logger.log(LogLevel.Error, \"Failed to start the connection: \" + e);\r\n            this._connectionState = ConnectionState.Disconnected;\r\n            this.transport = undefined;\r\n\r\n            // if start fails, any active calls to stop assume that start will complete the stop promise\r\n            this._stopPromiseResolver();\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n\r\n    private async _getNegotiationResponse(url: string): Promise<INegotiateResponse> {\r\n        const headers: {[k: string]: string} = {};\r\n        if (this._accessTokenFactory) {\r\n            const token = await this._accessTokenFactory();\r\n            if (token) {\r\n                headers[HeaderNames.Authorization] = `Bearer ${token}`;\r\n            }\r\n        }\r\n\r\n        const [name, value] = getUserAgentHeader();\r\n        headers[name] = value;\r\n\r\n        const negotiateUrl = this._resolveNegotiateUrl(url);\r\n        this._logger.log(LogLevel.Debug, `Sending negotiation request: ${negotiateUrl}.`);\r\n        try {\r\n            const response = await this._httpClient.post(negotiateUrl, {\r\n                content: \"\",\r\n                headers: { ...headers, ...this._options.headers },\r\n                timeout: this._options.timeout,\r\n                withCredentials: this._options.withCredentials,\r\n            });\r\n\r\n            if (response.statusCode !== 200) {\r\n                return Promise.reject(new Error(`Unexpected status code returned from negotiate '${response.statusCode}'`));\r\n            }\r\n\r\n            const negotiateResponse = JSON.parse(response.content as string) as INegotiateResponse;\r\n            if (!negotiateResponse.negotiateVersion || negotiateResponse.negotiateVersion < 1) {\r\n                // Negotiate version 0 doesn't use connectionToken\r\n                // So we set it equal to connectionId so all our logic can use connectionToken without being aware of the negotiate version\r\n                negotiateResponse.connectionToken = negotiateResponse.connectionId;\r\n            }\r\n            return negotiateResponse;\r\n        } catch (e) {\r\n            let errorMessage = \"Failed to complete negotiation with the server: \" + e;\r\n            if (e instanceof HttpError) {\r\n                if (e.statusCode === 404) {\r\n                    errorMessage = errorMessage + \" Either this is not a SignalR endpoint or there is a proxy blocking the connection.\";\r\n                }\r\n            }\r\n            this._logger.log(LogLevel.Error, errorMessage);\r\n\r\n            return Promise.reject(new FailedToNegotiateWithServerError(errorMessage));\r\n        }\r\n    }\r\n\r\n    private _createConnectUrl(url: string, connectionToken: string | null | undefined) {\r\n        if (!connectionToken) {\r\n            return url;\r\n        }\r\n\r\n        return url + (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + `id=${connectionToken}`;\r\n    }\r\n\r\n    private async _createTransport(url: string, requestedTransport: HttpTransportType | ITransport | undefined, negotiateResponse: INegotiateResponse, requestedTransferFormat: TransferFormat): Promise<void> {\r\n        let connectUrl = this._createConnectUrl(url, negotiateResponse.connectionToken);\r\n        if (this._isITransport(requestedTransport)) {\r\n            this._logger.log(LogLevel.Debug, \"Connection was provided an instance of ITransport, using that directly.\");\r\n            this.transport = requestedTransport;\r\n            await this._startTransport(connectUrl, requestedTransferFormat);\r\n\r\n            this.connectionId = negotiateResponse.connectionId;\r\n            return;\r\n        }\r\n\r\n        const transportExceptions: any[] = [];\r\n        const transports = negotiateResponse.availableTransports || [];\r\n        let negotiate: INegotiateResponse | undefined = negotiateResponse;\r\n        for (const endpoint of transports) {\r\n            const transportOrError = this._resolveTransportOrError(endpoint, requestedTransport, requestedTransferFormat);\r\n            if (transportOrError instanceof Error) {\r\n                // Store the error and continue, we don't want to cause a re-negotiate in these cases\r\n                transportExceptions.push(`${endpoint.transport} failed:`);\r\n                transportExceptions.push(transportOrError);\r\n            } else if (this._isITransport(transportOrError)) {\r\n                this.transport = transportOrError;\r\n                if (!negotiate) {\r\n                    try {\r\n                        negotiate = await this._getNegotiationResponse(url);\r\n                    } catch (ex) {\r\n                        return Promise.reject(ex);\r\n                    }\r\n                    connectUrl = this._createConnectUrl(url, negotiate.connectionToken);\r\n                }\r\n                try {\r\n                    await this._startTransport(connectUrl, requestedTransferFormat);\r\n                    this.connectionId = negotiate.connectionId;\r\n                    return;\r\n                } catch (ex) {\r\n                    this._logger.log(LogLevel.Error, `Failed to start the transport '${endpoint.transport}': ${ex}`);\r\n                    negotiate = undefined;\r\n                    transportExceptions.push(new FailedToStartTransportError(`${endpoint.transport} failed: ${ex}`, HttpTransportType[endpoint.transport]));\r\n\r\n                    if (this._connectionState !== ConnectionState.Connecting) {\r\n                        const message = \"Failed to select transport before stop() was called.\";\r\n                        this._logger.log(LogLevel.Debug, message);\r\n                        return Promise.reject(new Error(message));\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        if (transportExceptions.length > 0) {\r\n            return Promise.reject(new AggregateErrors(`Unable to connect to the server with any of the available transports. ${transportExceptions.join(\" \")}`, transportExceptions));\r\n        }\r\n        return Promise.reject(new Error(\"None of the transports supported by the client are supported by the server.\"));\r\n    }\r\n\r\n    private _constructTransport(transport: HttpTransportType): ITransport {\r\n        switch (transport) {\r\n            case HttpTransportType.WebSockets:\r\n                if (!this._options.WebSocket) {\r\n                    throw new Error(\"'WebSocket' is not supported in your environment.\");\r\n                }\r\n                return new WebSocketTransport(this._httpClient, this._accessTokenFactory, this._logger, this._options.logMessageContent!, this._options.WebSocket, this._options.headers || {});\r\n            case HttpTransportType.ServerSentEvents:\r\n                if (!this._options.EventSource) {\r\n                    throw new Error(\"'EventSource' is not supported in your environment.\");\r\n                }\r\n                return new ServerSentEventsTransport(this._httpClient, this._accessTokenFactory, this._logger, this._options);\r\n            case HttpTransportType.LongPolling:\r\n                return new LongPollingTransport(this._httpClient, this._accessTokenFactory, this._logger, this._options);\r\n            default:\r\n                throw new Error(`Unknown transport: ${transport}.`);\r\n        }\r\n    }\r\n\r\n    private _startTransport(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        this.transport!.onreceive = this.onreceive;\r\n        this.transport!.onclose = (e) => this._stopConnection(e);\r\n        return this.transport!.connect(url, transferFormat);\r\n    }\r\n\r\n    private _resolveTransportOrError(endpoint: IAvailableTransport, requestedTransport: HttpTransportType | undefined, requestedTransferFormat: TransferFormat): ITransport | Error {\r\n        const transport = HttpTransportType[endpoint.transport];\r\n        if (transport === null || transport === undefined) {\r\n            this._logger.log(LogLevel.Debug, `Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\r\n            return new Error(`Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\r\n        } else {\r\n            if (transportMatches(requestedTransport, transport)) {\r\n                const transferFormats = endpoint.transferFormats.map((s) => TransferFormat[s]);\r\n                if (transferFormats.indexOf(requestedTransferFormat) >= 0) {\r\n                    if ((transport === HttpTransportType.WebSockets && !this._options.WebSocket) ||\r\n                        (transport === HttpTransportType.ServerSentEvents && !this._options.EventSource)) {\r\n                        this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it is not supported in your environment.'`);\r\n                        return new UnsupportedTransportError(`'${HttpTransportType[transport]}' is not supported in your environment.`, transport);\r\n                    } else {\r\n                        this._logger.log(LogLevel.Debug, `Selecting transport '${HttpTransportType[transport]}'.`);\r\n                        try {\r\n                            return this._constructTransport(transport);\r\n                        } catch (ex) {\r\n                            return ex;\r\n                        }\r\n                    }\r\n                } else {\r\n                    this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it does not support the requested transfer format '${TransferFormat[requestedTransferFormat]}'.`);\r\n                    return new Error(`'${HttpTransportType[transport]}' does not support ${TransferFormat[requestedTransferFormat]}.`);\r\n                }\r\n            } else {\r\n                this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it was disabled by the client.`);\r\n                return new DisabledTransportError(`'${HttpTransportType[transport]}' is disabled by the client.`, transport);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _isITransport(transport: any): transport is ITransport {\r\n        return transport && typeof (transport) === \"object\" && \"connect\" in transport;\r\n    }\r\n\r\n    private _stopConnection(error?: Error): void {\r\n        this._logger.log(LogLevel.Debug, `HttpConnection.stopConnection(${error}) called while in state ${this._connectionState}.`);\r\n\r\n        this.transport = undefined;\r\n\r\n        // If we have a stopError, it takes precedence over the error from the transport\r\n        error = this._stopError || error;\r\n        this._stopError = undefined;\r\n\r\n        if (this._connectionState === ConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is already in the disconnected state.`);\r\n            return;\r\n        }\r\n\r\n        if (this._connectionState === ConnectionState.Connecting) {\r\n            this._logger.log(LogLevel.Warning, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is still in the connecting state.`);\r\n            throw new Error(`HttpConnection.stopConnection(${error}) was called while the connection is still in the connecting state.`);\r\n        }\r\n\r\n        if (this._connectionState === ConnectionState.Disconnecting) {\r\n            // A call to stop() induced this call to stopConnection and needs to be completed.\r\n            // Any stop() awaiters will be scheduled to continue after the onclose callback fires.\r\n            this._stopPromiseResolver();\r\n        }\r\n\r\n        if (error) {\r\n            this._logger.log(LogLevel.Error, `Connection disconnected with error '${error}'.`);\r\n        } else {\r\n            this._logger.log(LogLevel.Information, \"Connection disconnected.\");\r\n        }\r\n\r\n        if (this._sendQueue) {\r\n            this._sendQueue.stop().catch((e) => {\r\n                this._logger.log(LogLevel.Error, `TransportSendQueue.stop() threw error '${e}'.`);\r\n            });\r\n            this._sendQueue = undefined;\r\n        }\r\n\r\n        this.connectionId = undefined;\r\n        this._connectionState = ConnectionState.Disconnected;\r\n\r\n        if (this._connectionStarted) {\r\n            this._connectionStarted = false;\r\n            try {\r\n                if (this.onclose) {\r\n                    this.onclose(error);\r\n                }\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `HttpConnection.onclose(${error}) threw error '${e}'.`);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _resolveUrl(url: string): string {\r\n        // startsWith is not supported in IE\r\n        if (url.lastIndexOf(\"https://\", 0) === 0 || url.lastIndexOf(\"http://\", 0) === 0) {\r\n            return url;\r\n        }\r\n\r\n        if (!Platform.isBrowser) {\r\n            throw new Error(`Cannot resolve '${url}'.`);\r\n        }\r\n\r\n        // Setting the url to the href propery of an anchor tag handles normalization\r\n        // for us. There are 3 main cases.\r\n        // 1. Relative path normalization e.g \"b\" -> \"http://localhost:5000/a/b\"\r\n        // 2. Absolute path normalization e.g \"/a/b\" -> \"http://localhost:5000/a/b\"\r\n        // 3. Networkpath reference normalization e.g \"//localhost:5000/a/b\" -> \"http://localhost:5000/a/b\"\r\n        const aTag = window.document.createElement(\"a\");\r\n        aTag.href = url;\r\n\r\n        this._logger.log(LogLevel.Information, `Normalizing '${url}' to '${aTag.href}'.`);\r\n        return aTag.href;\r\n    }\r\n\r\n    private _resolveNegotiateUrl(url: string): string {\r\n        const index = url.indexOf(\"?\");\r\n        let negotiateUrl = url.substring(0, index === -1 ? url.length : index);\r\n        if (negotiateUrl[negotiateUrl.length - 1] !== \"/\") {\r\n            negotiateUrl += \"/\";\r\n        }\r\n        negotiateUrl += \"negotiate\";\r\n        negotiateUrl += index === -1 ? \"\" : url.substring(index);\r\n\r\n        if (negotiateUrl.indexOf(\"negotiateVersion\") === -1) {\r\n            negotiateUrl += index === -1 ? \"?\" : \"&\";\r\n            negotiateUrl += \"negotiateVersion=\" + this._negotiateVersion;\r\n        }\r\n        return negotiateUrl;\r\n    }\r\n}\r\n\r\nfunction transportMatches(requestedTransport: HttpTransportType | undefined, actualTransport: HttpTransportType) {\r\n    return !requestedTransport || ((actualTransport & requestedTransport) !== 0);\r\n}\r\n\r\n/** @private */\r\nexport class TransportSendQueue {\r\n    private _buffer: any[] = [];\r\n    private _sendBufferedData: PromiseSource;\r\n    private _executing: boolean = true;\r\n    private _transportResult?: PromiseSource;\r\n    private _sendLoopPromise: Promise<void>;\r\n\r\n    constructor(private readonly _transport: ITransport) {\r\n        this._sendBufferedData = new PromiseSource();\r\n        this._transportResult = new PromiseSource();\r\n\r\n        this._sendLoopPromise = this._sendLoop();\r\n    }\r\n\r\n    public send(data: string | ArrayBuffer): Promise<void> {\r\n        this._bufferData(data);\r\n        if (!this._transportResult) {\r\n            this._transportResult = new PromiseSource();\r\n        }\r\n        return this._transportResult.promise;\r\n    }\r\n\r\n    public stop(): Promise<void> {\r\n        this._executing = false;\r\n        this._sendBufferedData.resolve();\r\n        return this._sendLoopPromise;\r\n    }\r\n\r\n    private _bufferData(data: string | ArrayBuffer): void {\r\n        if (this._buffer.length && typeof(this._buffer[0]) !== typeof(data)) {\r\n            throw new Error(`Expected data to be of type ${typeof(this._buffer)} but was of type ${typeof(data)}`);\r\n        }\r\n\r\n        this._buffer.push(data);\r\n        this._sendBufferedData.resolve();\r\n    }\r\n\r\n    private async _sendLoop(): Promise<void> {\r\n        while (true) {\r\n            await this._sendBufferedData.promise;\r\n\r\n            if (!this._executing) {\r\n                if (this._transportResult) {\r\n                    this._transportResult.reject(\"Connection stopped.\");\r\n                }\r\n\r\n                break;\r\n            }\r\n\r\n            this._sendBufferedData = new PromiseSource();\r\n\r\n            const transportResult = this._transportResult!;\r\n            this._transportResult = undefined;\r\n\r\n            const data = typeof(this._buffer[0]) === \"string\" ?\r\n                this._buffer.join(\"\") :\r\n                TransportSendQueue._concatBuffers(this._buffer);\r\n\r\n            this._buffer.length = 0;\r\n\r\n            try {\r\n                await this._transport.send(data);\r\n                transportResult.resolve();\r\n            } catch (error) {\r\n                transportResult.reject(error);\r\n            }\r\n        }\r\n    }\r\n\r\n    private static _concatBuffers(arrayBuffers: ArrayBuffer[]): ArrayBuffer {\r\n        const totalLength = arrayBuffers.map((b) => b.byteLength).reduce((a, b) => a + b);\r\n        const result = new Uint8Array(totalLength);\r\n        let offset = 0;\r\n        for (const item of arrayBuffers) {\r\n            result.set(new Uint8Array(item), offset);\r\n            offset += item.byteLength;\r\n        }\r\n\r\n        return result.buffer;\r\n    }\r\n}\r\n\r\nclass PromiseSource {\r\n    private _resolver?: () => void;\r\n    private _rejecter!: (reason?: any) => void;\r\n    public promise: Promise<void>;\r\n\r\n    constructor() {\r\n        this.promise = new Promise((resolve, reject) => [this._resolver, this._rejecter] = [resolve, reject]);\r\n    }\r\n\r\n    public resolve(): void {\r\n        this._resolver!();\r\n    }\r\n\r\n    public reject(reason?: any): void {\r\n        this._rejecter!(reason);\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { CompletionMessage, HubMessage, IHubProtocol, InvocationMessage, MessageType, StreamItemMessage } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { TextMessageFormat } from \"./TextMessageFormat\";\r\n\r\nconst JSON_HUB_PROTOCOL_NAME: string = \"json\";\r\n\r\n/** Implements the JSON Hub Protocol. */\r\nexport class JsonHubProtocol implements IHubProtocol {\r\n\r\n    /** @inheritDoc */\r\n    public readonly name: string = JSON_HUB_PROTOCOL_NAME;\r\n    /** @inheritDoc */\r\n    public readonly version: number = 1;\r\n\r\n    /** @inheritDoc */\r\n    public readonly transferFormat: TransferFormat = TransferFormat.Text;\r\n\r\n    /** Creates an array of {@link @microsoft/signalr.HubMessage} objects from the specified serialized representation.\r\n     *\r\n     * @param {string} input A string containing the serialized representation.\r\n     * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n     */\r\n    public parseMessages(input: string, logger: ILogger): HubMessage[] {\r\n        // The interface does allow \"ArrayBuffer\" to be passed in, but this implementation does not. So let's throw a useful error.\r\n        if (typeof input !== \"string\") {\r\n            throw new Error(\"Invalid input for JSON hub protocol. Expected a string.\");\r\n        }\r\n\r\n        if (!input) {\r\n            return [];\r\n        }\r\n\r\n        if (logger === null) {\r\n            logger = NullLogger.instance;\r\n        }\r\n\r\n        // Parse the messages\r\n        const messages = TextMessageFormat.parse(input);\r\n\r\n        const hubMessages = [];\r\n        for (const message of messages) {\r\n            const parsedMessage = JSON.parse(message) as HubMessage;\r\n            if (typeof parsedMessage.type !== \"number\") {\r\n                throw new Error(\"Invalid payload.\");\r\n            }\r\n            switch (parsedMessage.type) {\r\n                case MessageType.Invocation:\r\n                    this._isInvocationMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.StreamItem:\r\n                    this._isStreamItemMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Completion:\r\n                    this._isCompletionMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Ping:\r\n                    // Single value, no need to validate\r\n                    break;\r\n                case MessageType.Close:\r\n                    // All optional values, no need to validate\r\n                    break;\r\n                default:\r\n                    // Future protocol changes can add message types, old clients can ignore them\r\n                    logger.log(LogLevel.Information, \"Unknown message type '\" + parsedMessage.type + \"' ignored.\");\r\n                    continue;\r\n            }\r\n            hubMessages.push(parsedMessage);\r\n        }\r\n\r\n        return hubMessages;\r\n    }\r\n\r\n    /** Writes the specified {@link @microsoft/signalr.HubMessage} to a string and returns it.\r\n     *\r\n     * @param {HubMessage} message The message to write.\r\n     * @returns {string} A string containing the serialized representation of the message.\r\n     */\r\n    public writeMessage(message: HubMessage): string {\r\n        return TextMessageFormat.write(JSON.stringify(message));\r\n    }\r\n\r\n    private _isInvocationMessage(message: InvocationMessage): void {\r\n        this._assertNotEmptyString(message.target, \"Invalid payload for Invocation message.\");\r\n\r\n        if (message.invocationId !== undefined) {\r\n            this._assertNotEmptyString(message.invocationId, \"Invalid payload for Invocation message.\");\r\n        }\r\n    }\r\n\r\n    private _isStreamItemMessage(message: StreamItemMessage): void {\r\n        this._assertNotEmptyString(message.invocationId, \"Invalid payload for StreamItem message.\");\r\n\r\n        if (message.item === undefined) {\r\n            throw new Error(\"Invalid payload for StreamItem message.\");\r\n        }\r\n    }\r\n\r\n    private _isCompletionMessage(message: CompletionMessage): void {\r\n        if (message.result && message.error) {\r\n            throw new Error(\"Invalid payload for Completion message.\");\r\n        }\r\n\r\n        if (!message.result && message.error) {\r\n            this._assertNotEmptyString(message.error, \"Invalid payload for Completion message.\");\r\n        }\r\n\r\n        this._assertNotEmptyString(message.invocationId, \"Invalid payload for Completion message.\");\r\n    }\r\n\r\n    private _assertNotEmptyString(value: any, errorMessage: string): void {\r\n        if (typeof value !== \"string\" || value === \"\") {\r\n            throw new Error(errorMessage);\r\n        }\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { DefaultReconnectPolicy } from \"./DefaultReconnectPolicy\";\r\nimport { HttpConnection } from \"./HttpConnection\";\r\nimport { HubConnection } from \"./HubConnection\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\nimport { IHubProtocol } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { IRetryPolicy } from \"./IRetryPolicy\";\r\nimport { HttpTransportType } from \"./ITransport\";\r\nimport { JsonHubProtocol } from \"./JsonHubProtocol\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { Arg, ConsoleLogger } from \"./Utils\";\r\n\r\nconst LogLevelNameMapping: {[k: string]: LogLevel} = {\r\n    trace: LogLevel.Trace,\r\n    debug: LogLevel.Debug,\r\n    info: LogLevel.Information,\r\n    information: LogLevel.Information,\r\n    warn: LogLevel.Warning,\r\n    warning: LogLevel.Warning,\r\n    error: LogLevel.Error,\r\n    critical: LogLevel.Critical,\r\n    none: LogLevel.None,\r\n};\r\n\r\nfunction parseLogLevel(name: string): LogLevel {\r\n    // Case-insensitive matching via lower-casing\r\n    // Yes, I know case-folding is a complicated problem in Unicode, but we only support\r\n    // the ASCII strings defined in LogLevelNameMapping anyway, so it's fine -anurse.\r\n    const mapping = LogLevelNameMapping[name.toLowerCase()];\r\n    if (typeof mapping !== \"undefined\") {\r\n        return mapping;\r\n    } else {\r\n        throw new Error(`Unknown log level: ${name}`);\r\n    }\r\n}\r\n\r\n/** A builder for configuring {@link @microsoft/signalr.HubConnection} instances. */\r\nexport class HubConnectionBuilder {\r\n    /** @internal */\r\n    public protocol?: IHubProtocol;\r\n    /** @internal */\r\n    public httpConnectionOptions?: IHttpConnectionOptions;\r\n    /** @internal */\r\n    public url?: string;\r\n    /** @internal */\r\n    public logger?: ILogger;\r\n\r\n    /** If defined, this indicates the client should automatically attempt to reconnect if the connection is lost. */\r\n    /** @internal */\r\n    public reconnectPolicy?: IRetryPolicy;\r\n\r\n    /** Configures console logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {LogLevel} logLevel The minimum level of messages to log. Anything at this level, or a more severe level, will be logged.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public configureLogging(logLevel: LogLevel): HubConnectionBuilder;\r\n\r\n    /** Configures custom logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {ILogger} logger An object implementing the {@link @microsoft/signalr.ILogger} interface, which will be used to write all log messages.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public configureLogging(logger: ILogger): HubConnectionBuilder;\r\n\r\n    /** Configures custom logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {string} logLevel A string representing a LogLevel setting a minimum level of messages to log.\r\n     *    See {@link https://docs.microsoft.com/aspnet/core/signalr/configuration#configure-logging|the documentation for client logging configuration} for more details.\r\n     */\r\n    public configureLogging(logLevel: string): HubConnectionBuilder;\r\n\r\n    /** Configures custom logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {LogLevel | string | ILogger} logging A {@link @microsoft/signalr.LogLevel}, a string representing a LogLevel, or an object implementing the {@link @microsoft/signalr.ILogger} interface.\r\n     *    See {@link https://docs.microsoft.com/aspnet/core/signalr/configuration#configure-logging|the documentation for client logging configuration} for more details.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public configureLogging(logging: LogLevel | string | ILogger): HubConnectionBuilder;\r\n    public configureLogging(logging: LogLevel | string | ILogger): HubConnectionBuilder {\r\n        Arg.isRequired(logging, \"logging\");\r\n\r\n        if (isLogger(logging)) {\r\n            this.logger = logging;\r\n        } else if (typeof logging === \"string\") {\r\n            const logLevel = parseLogLevel(logging);\r\n            this.logger = new ConsoleLogger(logLevel);\r\n        } else {\r\n            this.logger = new ConsoleLogger(logging);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use HTTP-based transports to connect to the specified URL.\r\n     *\r\n     * The transport will be selected automatically based on what the server and client support.\r\n     *\r\n     * @param {string} url The URL the connection will use.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withUrl(url: string): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use the specified HTTP-based transport to connect to the specified URL.\r\n     *\r\n     * @param {string} url The URL the connection will use.\r\n     * @param {HttpTransportType} transportType The specific transport to use.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withUrl(url: string, transportType: HttpTransportType): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use HTTP-based transports to connect to the specified URL.\r\n     *\r\n     * @param {string} url The URL the connection will use.\r\n     * @param {IHttpConnectionOptions} options An options object used to configure the connection.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withUrl(url: string, options: IHttpConnectionOptions): HubConnectionBuilder;\r\n    public withUrl(url: string, transportTypeOrOptions?: IHttpConnectionOptions | HttpTransportType): HubConnectionBuilder {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isNotEmpty(url, \"url\");\r\n\r\n        this.url = url;\r\n\r\n        // Flow-typing knows where it's at. Since HttpTransportType is a number and IHttpConnectionOptions is guaranteed\r\n        // to be an object, we know (as does TypeScript) this comparison is all we need to figure out which overload was called.\r\n        if (typeof transportTypeOrOptions === \"object\") {\r\n            this.httpConnectionOptions = { ...this.httpConnectionOptions, ...transportTypeOrOptions };\r\n        } else {\r\n            this.httpConnectionOptions = {\r\n                ...this.httpConnectionOptions,\r\n                transport: transportTypeOrOptions,\r\n            };\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use the specified Hub Protocol.\r\n     *\r\n     * @param {IHubProtocol} protocol The {@link @microsoft/signalr.IHubProtocol} implementation to use.\r\n     */\r\n    public withHubProtocol(protocol: IHubProtocol): HubConnectionBuilder {\r\n        Arg.isRequired(protocol, \"protocol\");\r\n\r\n        this.protocol = protocol;\r\n        return this;\r\n    }\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to automatically attempt to reconnect if the connection is lost.\r\n     * By default, the client will wait 0, 2, 10 and 30 seconds respectively before trying up to 4 reconnect attempts.\r\n     */\r\n    public withAutomaticReconnect(): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to automatically attempt to reconnect if the connection is lost.\r\n     *\r\n     * @param {number[]} retryDelays An array containing the delays in milliseconds before trying each reconnect attempt.\r\n     * The length of the array represents how many failed reconnect attempts it takes before the client will stop attempting to reconnect.\r\n     */\r\n    public withAutomaticReconnect(retryDelays: number[]): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to automatically attempt to reconnect if the connection is lost.\r\n     *\r\n     * @param {IRetryPolicy} reconnectPolicy An {@link @microsoft/signalR.IRetryPolicy} that controls the timing and number of reconnect attempts.\r\n     */\r\n    public withAutomaticReconnect(reconnectPolicy: IRetryPolicy): HubConnectionBuilder;\r\n    public withAutomaticReconnect(retryDelaysOrReconnectPolicy?: number[] | IRetryPolicy): HubConnectionBuilder {\r\n        if (this.reconnectPolicy) {\r\n            throw new Error(\"A reconnectPolicy has already been set.\");\r\n        }\r\n\r\n        if (!retryDelaysOrReconnectPolicy) {\r\n            this.reconnectPolicy = new DefaultReconnectPolicy();\r\n        } else if (Array.isArray(retryDelaysOrReconnectPolicy)) {\r\n            this.reconnectPolicy = new DefaultReconnectPolicy(retryDelaysOrReconnectPolicy);\r\n        } else {\r\n            this.reconnectPolicy = retryDelaysOrReconnectPolicy;\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Creates a {@link @microsoft/signalr.HubConnection} from the configuration options specified in this builder.\r\n     *\r\n     * @returns {HubConnection} The configured {@link @microsoft/signalr.HubConnection}.\r\n     */\r\n    public build(): HubConnection {\r\n        // If httpConnectionOptions has a logger, use it. Otherwise, override it with the one\r\n        // provided to configureLogger\r\n        const httpConnectionOptions = this.httpConnectionOptions || {};\r\n\r\n        // If it's 'null', the user **explicitly** asked for null, don't mess with it.\r\n        if (httpConnectionOptions.logger === undefined) {\r\n            // If our logger is undefined or null, that's OK, the HttpConnection constructor will handle it.\r\n            httpConnectionOptions.logger = this.logger;\r\n        }\r\n\r\n        // Now create the connection\r\n        if (!this.url) {\r\n            throw new Error(\"The 'HubConnectionBuilder.withUrl' method must be called before building the connection.\");\r\n        }\r\n        const connection = new HttpConnection(this.url, httpConnectionOptions);\r\n\r\n        return HubConnection.create(\r\n            connection,\r\n            this.logger || NullLogger.instance,\r\n            this.protocol || new JsonHubProtocol(),\r\n            this.reconnectPolicy);\r\n    }\r\n}\r\n\r\nfunction isLogger(logger: any): logger is ILogger {\r\n    return logger.log !== undefined;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// This is where we add any polyfills we'll need for the browser. It is the entry module for browser-specific builds.\r\n\r\n// Copy from Array.prototype into Uint8Array to polyfill on IE. It's OK because the implementations of indexOf and slice use properties\r\n// that exist on Uint8Array with the same name, and JavaScript is magic.\r\n// We make them 'writable' because the Buffer polyfill messes with it as well.\r\nif (!Uint8Array.prototype.indexOf) {\r\n    Object.defineProperty(Uint8Array.prototype, \"indexOf\", {\r\n        value: Array.prototype.indexOf,\r\n        writable: true,\r\n    });\r\n}\r\nif (!Uint8Array.prototype.slice) {\r\n    Object.defineProperty(Uint8Array.prototype, \"slice\", {\r\n        // wrap the slice in Uint8Array so it looks like a Uint8Array.slice call\r\n        // eslint-disable-next-line object-shorthand\r\n        value: function(start?: number, end?: number) { return new Uint8Array(Array.prototype.slice.call(this, start, end)); },\r\n        writable: true,\r\n    });\r\n}\r\nif (!Uint8Array.prototype.forEach) {\r\n    Object.defineProperty(Uint8Array.prototype, \"forEach\", {\r\n        value: Array.prototype.forEach,\r\n        writable: true,\r\n    });\r\n}\r\n\r\nexport * from \"./index\";\r\n"], "sourceRoot": ""}