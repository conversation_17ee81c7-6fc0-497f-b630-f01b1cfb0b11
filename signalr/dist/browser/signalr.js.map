{"version": 3, "sources": ["webpack://signalR/webpack/universalModuleDefinition", "webpack://signalR/webpack/bootstrap", "webpack://signalR/webpack/runtime/define property getters", "webpack://signalR/webpack/runtime/global", "webpack://signalR/webpack/runtime/hasOwnProperty shorthand", "webpack://signalR/webpack/runtime/make namespace object", "webpack://signalR/src/Errors.ts", "webpack://signalR/src/HttpClient.ts", "webpack://signalR/src/ILogger.ts", "webpack://signalR/src/Loggers.ts", "webpack://signalR/src/Utils.ts", "webpack://signalR/src/FetchHttpClient.ts", "webpack://signalR/src/XhrHttpClient.ts", "webpack://signalR/src/DefaultHttpClient.ts", "webpack://signalR/src/TextMessageFormat.ts", "webpack://signalR/src/HandshakeProtocol.ts", "webpack://signalR/src/IHubProtocol.ts", "webpack://signalR/src/Subject.ts", "webpack://signalR/src/HubConnection.ts", "webpack://signalR/src/DefaultReconnectPolicy.ts", "webpack://signalR/src/HeaderNames.ts", "webpack://signalR/src/ITransport.ts", "webpack://signalR/src/AbortController.ts", "webpack://signalR/src/LongPollingTransport.ts", "webpack://signalR/src/ServerSentEventsTransport.ts", "webpack://signalR/src/WebSocketTransport.ts", "webpack://signalR/src/HttpConnection.ts", "webpack://signalR/src/JsonHubProtocol.ts", "webpack://signalR/src/HubConnectionBuilder.ts", "webpack://signalR/src/index.ts", "webpack://signalR/src/browser-index.ts"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;UCVA;UACA;;;;;WCDA;WACA;WACA;WACA;WACA,wCAAwC,yCAAyC;WACjF;WACA;WACA,E;;;;;WCPA;WACA;WACA;WACA;WACA,EAAE;WACF;WACA;WACA,CAAC,I;;;;;WCPD,wF;;;;;WCAA;WACA;WACA;WACA,sDAAsD,kBAAkB;WACxE;WACA,+CAA+C,cAAc;WAC7D,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACNA,gEAAgE;AAChE,uEAAuE;AAIvE,+CAA+C;AACxC,MAAM,SAAU,SAAQ,KAAK;IAQhC;;;;OAIG;IACH,YAAY,YAAoB,EAAE,UAAkB;QAChD,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QACvC,KAAK,CAAC,GAAG,YAAY,kBAAkB,UAAU,GAAG,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,0CAA0C;QAC1C,8EAA8E;QAC9E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;CACJ;AAED,2CAA2C;AACpC,MAAM,YAAa,SAAQ,KAAK;IAKnC;;;OAGG;IACH,YAAY,eAAuB,qBAAqB;QACpD,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QACvC,KAAK,CAAC,YAAY,CAAC,CAAC;QAEpB,0CAA0C;QAC1C,8EAA8E;QAC9E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;CACJ;AAED,8CAA8C;AACvC,MAAM,UAAW,SAAQ,KAAK;IAKjC;;;OAGG;IACH,YAAY,eAAuB,oBAAoB;QACnD,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QACvC,KAAK,CAAC,YAAY,CAAC,CAAC;QAEpB,0CAA0C;QAC1C,8EAA8E;QAC9E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;CACJ;AAED,8EAA8E;AAC9E,eAAe;AACR,MAAM,yBAA0B,SAAQ,KAAK;IAWhD;;;;OAIG;IACH,YAAY,OAAe,EAAE,SAA4B;QACrD,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QACvC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,2BAA2B,CAAC;QAE7C,0CAA0C;QAC1C,8EAA8E;QAC9E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;CACJ;AAED,2EAA2E;AAC3E,eAAe;AACR,MAAM,sBAAuB,SAAQ,KAAK;IAW7C;;;;OAIG;IACH,YAAY,OAAe,EAAE,SAA4B;QACrD,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QACvC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,wBAAwB,CAAC;QAE1C,0CAA0C;QAC1C,8EAA8E;QAC9E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;CACJ;AAED,kEAAkE;AAClE,eAAe;AACR,MAAM,2BAA4B,SAAQ,KAAK;IAWlD;;;;OAIG;IACH,YAAY,OAAe,EAAE,SAA4B;QACrD,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QACvC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,6BAA6B,CAAC;QAE/C,0CAA0C;QAC1C,8EAA8E;QAC9E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;CACJ;AAED,4EAA4E;AAC5E,eAAe;AACR,MAAM,gCAAiC,SAAQ,KAAK;IAQvD;;;OAGG;IACH,YAAY,OAAe;QACvB,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QACvC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,SAAS,GAAG,kCAAkC,CAAC;QAEpD,0CAA0C;QAC1C,8EAA8E;QAC9E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;CACJ;AAED,sDAAsD;AACtD,eAAe;AACR,MAAM,eAAgB,SAAQ,KAAK;IAQtC;;;;OAIG;IACH,YAAY,OAAe,EAAE,WAAoB;QAC7C,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QACvC,KAAK,CAAC,OAAO,CAAC,CAAC;QAEf,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAE/B,0CAA0C;QAC1C,8EAA8E;QAC9E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;CACJ;;;AClND,gEAAgE;AAChE,uEAAuE;AAgCvE,mCAAmC;AAC5B,MAAM,YAAY;IAqCrB,YACoB,UAAkB,EAClB,UAAmB,EACnB,OAA8B;QAF9B,eAAU,GAAV,UAAU,CAAQ;QAClB,eAAU,GAAV,UAAU,CAAS;QACnB,YAAO,GAAP,OAAO,CAAuB;IAClD,CAAC;CACJ;AAED;;;GAGG;AACI,MAAe,UAAU;IAerB,GAAG,CAAC,GAAW,EAAE,OAAqB;QACzC,OAAO,IAAI,CAAC,IAAI,CAAC;YACb,GAAG,OAAO;YACV,MAAM,EAAE,KAAK;YACb,GAAG;SACN,CAAC,CAAC;IACP,CAAC;IAgBM,IAAI,CAAC,GAAW,EAAE,OAAqB;QAC1C,OAAO,IAAI,CAAC,IAAI,CAAC;YACb,GAAG,OAAO;YACV,MAAM,EAAE,MAAM;YACd,GAAG;SACN,CAAC,CAAC;IACP,CAAC;IAgBM,MAAM,CAAC,GAAW,EAAE,OAAqB;QAC5C,OAAO,IAAI,CAAC,IAAI,CAAC;YACb,GAAG,OAAO;YACV,MAAM,EAAE,QAAQ;YAChB,GAAG;SACN,CAAC,CAAC;IACP,CAAC;IASD;;;;OAIG;IACH,aAAa;IACN,eAAe,CAAC,GAAW;QAC9B,OAAO,EAAE,CAAC;IACd,CAAC;CACJ;;;ACrKD,gEAAgE;AAChE,uEAAuE;AAEvE,2GAA2G;AAC3G;;;GAGG;AACH,IAAY,QAeX;AAfD,WAAY,QAAQ;IAChB,2DAA2D;IAC3D,yCAAS;IACT,sDAAsD;IACtD,yCAAS;IACT,uDAAuD;IACvD,qDAAe;IACf,2EAA2E;IAC3E,6CAAW;IACX,0FAA0F;IAC1F,yCAAS;IACT,4GAA4G;IAC5G,+CAAY;IACZ,wHAAwH;IACxH,uCAAQ;AACZ,CAAC,EAfW,QAAQ,KAAR,QAAQ,QAenB;;;ACvBD,gEAAgE;AAChE,uEAAuE;AAIvE,mEAAmE;AAC5D,MAAM,UAAU;IAInB,gBAAuB,CAAC;IAExB,kBAAkB;IAClB,2BAA2B;IACpB,GAAG,CAAC,SAAmB,EAAE,QAAgB;IAChD,CAAC;;AARD,2EAA2E;AAC7D,mBAAQ,GAAY,IAAI,UAAU,EAAE,CAAC;;;ACRvD,gEAAgE;AAChE,uEAAuE;AAGzB;AACP;AAKvC,6DAA6D;AAC7D,yCAAyC;AAElC,MAAM,OAAO,GAAW,iBAAiB,CAAC;AACjD,eAAe;AACR,MAAM,GAAG;IACL,MAAM,CAAC,UAAU,CAAC,GAAQ,EAAE,IAAY;QAC3C,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;YACnC,MAAM,IAAI,KAAK,CAAC,QAAQ,IAAI,yBAAyB,CAAC,CAAC;SAC1D;IACL,CAAC;IACM,MAAM,CAAC,UAAU,CAAC,GAAW,EAAE,IAAY;QAC9C,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,QAAQ,IAAI,iCAAiC,CAAC,CAAC;SAClE;IACL,CAAC;IAEM,MAAM,CAAC,IAAI,CAAC,GAAQ,EAAE,MAAW,EAAE,IAAY;QAClD,yGAAyG;QACzG,IAAI,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,WAAW,IAAI,WAAW,GAAG,GAAG,CAAC,CAAC;SACrD;IACL,CAAC;CACJ;AAED,eAAe;AACR,MAAM,QAAQ;IACjB,oEAAoE;IAC7D,MAAM,KAAK,SAAS;QACvB,OAAO,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ,CAAC;IAC7E,CAAC;IAED,0EAA0E;IACnE,MAAM,KAAK,WAAW;QACzB,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,eAAe,IAAI,IAAI,CAAC;IAC/D,CAAC;IAED,4CAA4C;IAC5C,MAAM,KAAK,aAAa;QACpB,OAAO,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,WAAW,CAAC;IAChF,CAAC;IAED,wEAAwE;IACxE,oDAAoD;IAC7C,MAAM,KAAK,MAAM;QACpB,OAAO,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;IACvE,CAAC;CACJ;AAED,eAAe;AACR,SAAS,aAAa,CAAC,IAAS,EAAE,cAAuB;IAC5D,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE;QACrB,MAAM,GAAG,yBAAyB,IAAI,CAAC,UAAU,EAAE,CAAC;QACpD,IAAI,cAAc,EAAE;YAChB,MAAM,IAAI,eAAe,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC;SACvD;KACJ;SAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QACjC,MAAM,GAAG,yBAAyB,IAAI,CAAC,MAAM,EAAE,CAAC;QAChD,IAAI,cAAc,EAAE;YAChB,MAAM,IAAI,eAAe,IAAI,GAAG,CAAC;SACpC;KACJ;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,eAAe;AACR,SAAS,iBAAiB,CAAC,IAAiB;IAC/C,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;IAElC,6DAA6D;IAC7D,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;QACjB,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAChC,GAAG,IAAI,KAAK,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,0BAA0B;IAC1B,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACzC,CAAC;AAED,4CAA4C;AAC5C,eAAe;AACR,SAAS,aAAa,CAAC,GAAQ;IAClC,OAAO,GAAG,IAAI,OAAO,WAAW,KAAK,WAAW;QAC5C,CAAC,GAAG,YAAY,WAAW;YACvB,kEAAkE;YAClE,CAAC,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,WAAW,CAAC,IAAI,KAAK,aAAa,CAAC,CAAC,CAAC;AACzE,CAAC;AAED,eAAe;AACR,KAAK,UAAU,WAAW,CAAC,MAAe,EAAE,aAAqB,EAAE,UAAsB,EAAE,GAAW,EAAE,kBAAgE,EAC7I,OAA6B,EAAE,OAA+B;IAC5F,IAAI,OAAO,GAA0B,EAAE,CAAC;IACxC,IAAI,kBAAkB,EAAE;QACpB,MAAM,KAAK,GAAG,MAAM,kBAAkB,EAAE,CAAC;QACzC,IAAI,KAAK,EAAE;YACP,OAAO,GAAG;gBACN,CAAC,eAAe,CAAC,EAAE,UAAU,KAAK,EAAE;aACvC,CAAC;SACL;KACJ;IAED,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,kBAAkB,EAAE,CAAC;IAC3C,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;IAEtB,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,aAAa,6BAA6B,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,iBAAkB,CAAC,GAAG,CAAC,CAAC;IAEhI,MAAM,YAAY,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC;IACrE,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE;QACxC,OAAO;QACP,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,GAAG,OAAO,CAAC,OAAO,EAAC;QAC1C,YAAY;QACZ,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,eAAe,EAAE,OAAO,CAAC,eAAe;KAC3C,CAAC,CAAC;IAEH,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,aAAa,kDAAkD,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC;AAC1H,CAAC;AAED,eAAe;AACR,SAAS,YAAY,CAAC,MAA2B;IACpD,IAAI,MAAM,KAAK,SAAS,EAAE;QACtB,OAAO,IAAI,aAAa,CAAC,oBAAoB,CAAC,CAAC;KAClD;IAED,IAAI,MAAM,KAAK,IAAI,EAAE;QACjB,OAAO,mBAAmB,CAAC;KAC9B;IAED,IAAK,MAAkB,CAAC,GAAG,KAAK,SAAS,EAAE;QACvC,OAAO,MAAiB,CAAC;KAC5B;IAED,OAAO,IAAI,aAAa,CAAC,MAAkB,CAAC,CAAC;AACjD,CAAC;AAED,eAAe;AACR,MAAM,mBAAmB;IAI5B,YAAY,OAAmB,EAAE,QAA8B;QAC3D,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC9B,CAAC;IAEM,OAAO;QACV,MAAM,KAAK,GAAW,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACtE,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACZ,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC5C;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;YACtE,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;SACpD;IACL,CAAC;CACJ;AAED,eAAe;AACR,MAAM,aAAa;IAWtB,YAAY,eAAyB;QACjC,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;QACjC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC;IACvB,CAAC;IAEM,GAAG,CAAC,QAAkB,EAAE,OAAe;QAC1C,IAAI,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YAC5B,MAAM,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,KAAK,QAAQ,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE,CAAC;YAC9E,QAAQ,QAAQ,EAAE;gBACd,KAAK,iBAAiB,CAAC;gBACvB,KAAK,cAAc;oBACf,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBACpB,MAAM;gBACV,KAAK,gBAAgB;oBACjB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACnB,MAAM;gBACV,KAAK,oBAAoB;oBACrB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACnB,MAAM;gBACV;oBACI,mGAAmG;oBACnG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBAClB,MAAM;aACb;SACJ;IACL,CAAC;CACJ;AAED,eAAe;AACR,SAAS,kBAAkB;IAC9B,IAAI,mBAAmB,GAAG,sBAAsB,CAAC;IACjD,IAAI,QAAQ,CAAC,MAAM,EAAE;QACjB,mBAAmB,GAAG,YAAY,CAAC;KACtC;IACD,OAAO,CAAE,mBAAmB,EAAE,kBAAkB,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,EAAE,EAAE,iBAAiB,EAAE,CAAC,CAAE,CAAC;AAChH,CAAC;AAED,eAAe;AACR,SAAS,kBAAkB,CAAC,OAAe,EAAE,EAAU,EAAE,OAAe,EAAE,cAAkC;IAC/G,qGAAqG;IACrG,IAAI,SAAS,GAAW,oBAAoB,CAAC;IAE7C,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACzC,SAAS,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;IACvD,SAAS,IAAI,KAAK,OAAO,IAAI,CAAC;IAE9B,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;QACjB,SAAS,IAAI,GAAG,EAAE,IAAI,CAAC;KAC1B;SAAM;QACH,SAAS,IAAI,cAAc,CAAC;KAC/B;IAED,SAAS,IAAI,GAAG,OAAO,EAAE,CAAC;IAE1B,IAAI,cAAc,EAAE;QAChB,SAAS,IAAI,KAAK,cAAc,EAAE,CAAC;KACtC;SAAM;QACH,SAAS,IAAI,2BAA2B,CAAC;KAC5C;IAED,SAAS,IAAI,GAAG,CAAC;IACjB,OAAO,SAAS,CAAC;AACrB,CAAC;AAED,0CAA0C;AAC1C,aAAa,CAAC,SAAS,SAAS;IAC5B,IAAI,QAAQ,CAAC,MAAM,EAAE;QACjB,QAAQ,OAAO,CAAC,QAAQ,EAAE;YACtB,KAAK,OAAO;gBACR,OAAO,YAAY,CAAC;YACxB,KAAK,QAAQ;gBACT,OAAO,OAAO,CAAC;YACnB,KAAK,OAAO;gBACR,OAAO,OAAO,CAAC;YACnB;gBACI,OAAO,OAAO,CAAC,QAAQ,CAAC;SAC/B;KACJ;SAAM;QACH,OAAO,EAAE,CAAC;KACb;AACL,CAAC;AAED,0CAA0C;AAC1C,aAAa,CAAC,SAAS,iBAAiB;IACpC,IAAI,QAAQ,CAAC,MAAM,EAAE;QACjB,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;KAChC;IACD,OAAO,SAAS,CAAC;AACrB,CAAC;AAED,SAAS,UAAU;IACf,IAAI,QAAQ,CAAC,MAAM,EAAE;QACjB,OAAO,QAAQ,CAAC;KACnB;SAAM;QACH,OAAO,SAAS,CAAC;KACpB;AACL,CAAC;AAED,eAAe;AACR,SAAS,cAAc,CAAC,CAAM;IACjC,IAAI,CAAC,CAAC,KAAK,EAAE;QACT,OAAO,CAAC,CAAC,KAAK,CAAC;KAClB;SAAM,IAAI,CAAC,CAAC,OAAO,EAAE;QAClB,OAAO,CAAC,CAAC,OAAO,CAAC;KACpB;IACD,OAAO,GAAG,CAAC,EAAE,CAAC;AAClB,CAAC;AAED,eAAe;AACR,SAAS,aAAa;IACzB,6DAA6D;IAC7D,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;QACnC,OAAO,UAAU,CAAC;KACrB;IACD,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;QAC7B,OAAO,IAAI,CAAC;KACf;IACD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QAC/B,OAAO,MAAM,CAAC;KACjB;IACD,IAAI,OAAO,qBAAM,KAAK,WAAW,EAAE;QAC/B,OAAO,qBAAM,CAAC;KACjB;IACD,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;AAC7C,CAAC;;;ACjTD,gEAAgE;AAChE,uEAAuE;AAKR;AACM;AACvB;AACI;AAE3C,MAAM,eAAgB,SAAQ,UAAU;IAO3C,YAAmB,MAAe;QAC9B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;YAC9B,oFAAoF;YACpF,gDAAgD;YAChD,MAAM,WAAW,GAAG,KAAyC,CAAC,CAAC,CAAC,OAAuB,CAAC,CAAC,CAAC,CAAO,CAAC;YAElG,iHAAiH;YACjH,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;YAC1D,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC;YAE5C,qEAAqE;YACrE,2FAA2F;YAC3F,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;SAC7E;aAAM;YACH,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;SACjD;QACD,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE;YACxC,oFAAoF;YACpF,gDAAgD;YAChD,MAAM,WAAW,GAAG,KAAyC,CAAC,CAAC,CAAC,OAAuB,CAAC,CAAC,CAAC,CAAO,CAAC;YAElG,gGAAgG;YAChG,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC,kBAAkB,CAAC,CAAC;SAC/D;aAAM;YACH,IAAI,CAAC,oBAAoB,GAAG,eAAe,CAAC;SAC/C;IACL,CAAC;IAED,kBAAkB;IACX,KAAK,CAAC,IAAI,CAAC,OAAoB;QAClC,wDAAwD;QACxD,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE;YACpD,MAAM,IAAI,UAAU,EAAE,CAAC;SAC1B;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;SACzC;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;SACtC;QAED,MAAM,eAAe,GAAG,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAExD,IAAI,KAAU,CAAC;QACf,iDAAiD;QACjD,IAAI,OAAO,CAAC,WAAW,EAAE;YACrB,OAAO,CAAC,WAAW,CAAC,OAAO,GAAG,GAAG,EAAE;gBAC/B,eAAe,CAAC,KAAK,EAAE,CAAC;gBACxB,KAAK,GAAG,IAAI,UAAU,EAAE,CAAC;YAC7B,CAAC,CAAC;SACL;QAED,iEAAiE;QACjE,sEAAsE;QACtE,IAAI,SAAS,GAAQ,IAAI,CAAC;QAC1B,IAAI,OAAO,CAAC,OAAO,EAAE;YACjB,MAAM,SAAS,GAAG,OAAO,CAAC,OAAQ,CAAC;YACnC,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBACxB,eAAe,CAAC,KAAK,EAAE,CAAC;gBACxB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,4BAA4B,CAAC,CAAC;gBACjE,KAAK,GAAG,IAAI,YAAY,EAAE,CAAC;YAC/B,CAAC,EAAE,SAAS,CAAC,CAAC;SACjB;QAED,IAAI,QAAkB,CAAC;QACvB,IAAI;YACA,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAI,EAAE;gBAC3C,IAAI,EAAE,OAAO,CAAC,OAAQ;gBACtB,KAAK,EAAE,UAAU;gBACjB,WAAW,EAAE,OAAO,CAAC,eAAe,KAAK,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa;gBACzE,OAAO,EAAE;oBACL,cAAc,EAAE,0BAA0B;oBAC1C,kBAAkB,EAAE,gBAAgB;oBACpC,GAAG,OAAO,CAAC,OAAO;iBACrB;gBACD,MAAM,EAAE,OAAO,CAAC,MAAO;gBACvB,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,QAAQ;gBAClB,MAAM,EAAE,eAAe,CAAC,MAAM;aACjC,CAAC,CAAC;SACN;QAAC,OAAO,CAAC,EAAE;YACR,IAAI,KAAK,EAAE;gBACP,MAAM,KAAK,CAAC;aACf;YACD,IAAI,CAAC,OAAO,CAAC,GAAG,CACZ,gBAAgB,EAChB,4BAA4B,CAAC,GAAG,CACnC,CAAC;YACF,MAAM,CAAC,CAAC;SACX;gBAAS;YACN,IAAI,SAAS,EAAE;gBACX,YAAY,CAAC,SAAS,CAAC,CAAC;aAC3B;YACD,IAAI,OAAO,CAAC,WAAW,EAAE;gBACrB,OAAO,CAAC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC;aACtC;SACJ;QAED,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;YACd,MAAM,YAAY,GAAG,MAAM,kBAAkB,CAAC,QAAQ,EAAE,MAAM,CAAW,CAAC;YAC1E,MAAM,IAAI,SAAS,CAAC,YAAY,IAAI,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;SAC7E;QAED,MAAM,OAAO,GAAG,kBAAkB,CAAC,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;QACnE,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC;QAE9B,OAAO,IAAI,YAAY,CACnB,QAAQ,CAAC,MAAM,EACf,QAAQ,CAAC,UAAU,EACnB,OAAO,CACV,CAAC;IACN,CAAC;IAEM,eAAe,CAAC,GAAW;QAC9B,IAAI,OAAO,GAAW,EAAE,CAAC;QACzB,IAAI,eAAe,IAAI,IAAI,CAAC,IAAI,EAAE;YAC9B,8BAA8B;YAC9B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SAC/D;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;CACJ;AAED,SAAS,kBAAkB,CAAC,QAAkB,EAAE,YAAyC;IACrF,IAAI,OAAO,CAAC;IACZ,QAAQ,YAAY,EAAE;QAClB,KAAK,aAAa;YACd,OAAO,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM;QACV,KAAK,MAAM;YACP,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC1B,MAAM;QACV,KAAK,MAAM,CAAC;QACZ,KAAK,UAAU,CAAC;QAChB,KAAK,MAAM;YACP,MAAM,IAAI,KAAK,CAAC,GAAG,YAAY,oBAAoB,CAAC,CAAC;QACzD;YACI,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC1B,MAAM;KACb;IAED,OAAO,OAAO,CAAC;AACnB,CAAC;;;ACpKD,gEAAgE;AAChE,uEAAuE;AAER;AACM;AACvB;AAEvC,MAAM,aAAc,SAAQ,UAAU;IAGzC,YAAmB,MAAe;QAC9B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED,kBAAkB;IACX,IAAI,CAAC,OAAoB;QAC5B,wDAAwD;QACxD,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE;YACpD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,UAAU,EAAE,CAAC,CAAC;SAC3C;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACjB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;SAC1D;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YACd,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;SACvD;QAED,OAAO,IAAI,OAAO,CAAe,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACjD,MAAM,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;YAEjC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAO,EAAE,OAAO,CAAC,GAAI,EAAE,IAAI,CAAC,CAAC;YAC9C,GAAG,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;YAC7F,GAAG,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;YAC3D,mFAAmF;YACnF,GAAG,CAAC,gBAAgB,CAAC,cAAc,EAAE,0BAA0B,CAAC,CAAC;YAEjE,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAChC,IAAI,OAAO,EAAE;gBACT,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;qBACf,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;oBAChB,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;gBAClD,CAAC,CAAC,CAAC;aACV;YAED,IAAI,OAAO,CAAC,YAAY,EAAE;gBACtB,GAAG,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;aAC3C;YAED,IAAI,OAAO,CAAC,WAAW,EAAE;gBACrB,OAAO,CAAC,WAAW,CAAC,OAAO,GAAG,GAAG,EAAE;oBAC/B,GAAG,CAAC,KAAK,EAAE,CAAC;oBACZ,MAAM,CAAC,IAAI,UAAU,EAAE,CAAC,CAAC;gBAC7B,CAAC,CAAC;aACL;YAED,IAAI,OAAO,CAAC,OAAO,EAAE;gBACjB,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;aACjC;YAED,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;gBACd,IAAI,OAAO,CAAC,WAAW,EAAE;oBACrB,OAAO,CAAC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC;iBACtC;gBAED,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;oBACvC,OAAO,CAAC,IAAI,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;iBAC3F;qBAAM;oBACH,MAAM,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,YAAY,IAAI,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;iBACzF;YACL,CAAC,CAAC;YAEF,GAAG,CAAC,OAAO,GAAG,GAAG,EAAE;gBACf,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,4BAA4B,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC;gBACjG,MAAM,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;YACtD,CAAC,CAAC;YAEF,GAAG,CAAC,SAAS,GAAG,GAAG,EAAE;gBACjB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,4BAA4B,CAAC,CAAC;gBACjE,MAAM,CAAC,IAAI,YAAY,EAAE,CAAC,CAAC;YAC/B,CAAC,CAAC;YAEF,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACP,CAAC;CACJ;;;ACtFD,gEAAgE;AAChE,uEAAuE;AAEjC;AACc;AACiB;AAElC;AACa;AAEhD,uEAAuE;AAChE,MAAM,iBAAkB,SAAQ,UAAU;IAG7C,yJAAyJ;IACzJ,YAAmB,MAAe;QAC9B,KAAK,EAAE,CAAC;QAER,IAAI,OAAO,KAAK,KAAK,WAAW,IAAI,eAAe,EAAE;YACjD,IAAI,CAAC,WAAW,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;SAClD;aAAM,IAAI,OAAO,cAAc,KAAK,WAAW,EAAE;YAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;SAChD;aAAM;YACH,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAClD;IACL,CAAC;IAED,kBAAkB;IACX,IAAI,CAAC,OAAoB;QAC5B,wDAAwD;QACxD,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE;YACpD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,UAAU,EAAE,CAAC,CAAC;SAC3C;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACjB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;SAC1D;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YACd,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;SACvD;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAEM,eAAe,CAAC,GAAW;QAC9B,OAAO,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;IACjD,CAAC;CACJ;;;AC/CD,gEAAgE;AAChE,uEAAuE;AAEvE,0BAA0B;AAC1B,eAAe;AACR,MAAM,iBAAiB;IAInB,MAAM,CAAC,KAAK,CAAC,MAAc;QAC9B,OAAO,GAAG,MAAM,GAAG,iBAAiB,CAAC,eAAe,EAAE,CAAC;IAC3D,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,KAAa;QAC7B,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,iBAAiB,CAAC,eAAe,EAAE;YAC/D,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;SAC7C;QAED,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;QAChE,QAAQ,CAAC,GAAG,EAAE,CAAC;QACf,OAAO,QAAQ,CAAC;IACpB,CAAC;;AAfa,qCAAmB,GAAG,IAAI,CAAC;AAC3B,iCAAe,GAAG,MAAM,CAAC,YAAY,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;;;ACP/F,gEAAgE;AAChE,uEAAuE;AAEf;AAChB;AAcxC,eAAe;AACR,MAAM,iBAAiB;IAC1B,mCAAmC;IAC5B,qBAAqB,CAAC,gBAAyC;QAClE,OAAO,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACrE,CAAC;IAEM,sBAAsB,CAAC,IAAS;QACnC,IAAI,WAAmB,CAAC;QACxB,IAAI,aAAkB,CAAC;QAEvB,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE;YACrB,4EAA4E;YAC5E,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,qCAAqC,CAAC,CAAC;YACjF,IAAI,cAAc,KAAK,CAAC,CAAC,EAAE;gBACvB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;aAC7C;YAED,iDAAiD;YACjD,gDAAgD;YAChD,MAAM,cAAc,GAAG,cAAc,GAAG,CAAC,CAAC;YAC1C,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC;YAC/G,aAAa,GAAG,CAAC,UAAU,CAAC,UAAU,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;SAC7G;aAAM;YACH,MAAM,QAAQ,GAAW,IAAI,CAAC;YAC9B,MAAM,cAAc,GAAG,QAAQ,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;YAC3E,IAAI,cAAc,KAAK,CAAC,CAAC,EAAE;gBACvB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;aAC7C;YAED,iDAAiD;YACjD,gDAAgD;YAChD,MAAM,cAAc,GAAG,cAAc,GAAG,CAAC,CAAC;YAC1C,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;YACpD,aAAa,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;SAClG;QAED,iEAAiE;QACjE,MAAM,QAAQ,GAAG,uBAAuB,CAAC,WAAW,CAAC,CAAC;QACtD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,IAAI,QAAQ,CAAC,IAAI,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;SACrE;QACD,MAAM,eAAe,GAA6B,QAAQ,CAAC;QAE3D,sDAAsD;QACtD,sEAAsE;QACtE,OAAO,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;IAC5C,CAAC;CACJ;;;ACpED,gEAAgE;AAChE,uEAAuE;AAKvE,yCAAyC;AACzC,IAAY,WAeX;AAfD,WAAY,WAAW;IACnB,gIAAgI;IAChI,yDAAc;IACd,+HAA+H;IAC/H,yDAAc;IACd,+HAA+H;IAC/H,yDAAc;IACd,4IAA4I;IAC5I,qEAAoB;IACpB,4IAA4I;IAC5I,qEAAoB;IACpB,mHAAmH;IACnH,6CAAQ;IACR,qHAAqH;IACrH,+CAAS;AACb,CAAC,EAfW,WAAW,KAAX,WAAW,QAetB;;;ACtBD,gEAAgE;AAChE,uEAAuE;AAGzB;AAE9C,2DAA2D;AACpD,MAAM,OAAO;IAOhB;QACI,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;IACxB,CAAC;IAEM,IAAI,CAAC,IAAO;QACf,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YACnC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACvB;IACL,CAAC;IAEM,KAAK,CAAC,GAAQ;QACjB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YACnC,IAAI,QAAQ,CAAC,KAAK,EAAE;gBAChB,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aACvB;SACJ;IACL,CAAC;IAEM,QAAQ;QACX,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YACnC,IAAI,QAAQ,CAAC,QAAQ,EAAE;gBACnB,QAAQ,CAAC,QAAQ,EAAE,CAAC;aACvB;SACJ;IACL,CAAC;IAEM,SAAS,CAAC,QAA8B;QAC3C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9B,OAAO,IAAI,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACnD,CAAC;CACJ;;;AC5CD,gEAAgE;AAChE,uEAAuE;AAEoC;AAE2D;AACxH;AAGV;AACoB;AAExD,MAAM,qBAAqB,GAAW,EAAE,GAAG,IAAI,CAAC;AAChD,MAAM,2BAA2B,GAAW,EAAE,GAAG,IAAI,CAAC;AAEtD,8EAA8E;AAC9E,IAAY,kBAWX;AAXD,WAAY,kBAAkB;IAC1B,0CAA0C;IAC1C,mDAA6B;IAC7B,wCAAwC;IACxC,+CAAyB;IACzB,uCAAuC;IACvC,6CAAuB;IACvB,2CAA2C;IAC3C,qDAA+B;IAC/B,0CAA0C;IAC1C,mDAA6B;AACjC,CAAC,EAXW,kBAAkB,KAAlB,kBAAkB,QAW7B;AAED,gDAAgD;AACzC,MAAM,aAAa;IAmEtB,YAAoB,UAAuB,EAAE,MAAe,EAAE,QAAsB,EAAE,eAA8B;QAvC5G,mBAAc,GAAW,CAAC,CAAC;QAS3B,yBAAoB,GAAG,GAAG,EAAE;YAEhC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,sNAAsN,CAAC,CAAC;QAC/P,CAAC,CAAC;QA4BE,cAAc,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QACzC,cAAc,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QACjC,cAAc,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAErC,IAAI,CAAC,2BAA2B,GAAG,qBAAqB,CAAC;QACzD,IAAI,CAAC,+BAA+B,GAAG,2BAA2B,CAAC;QAEnE,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,kBAAkB,GAAG,IAAI,iBAAiB,EAAE,CAAC;QAElD,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC3E,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,CAAC,KAAa,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAE3E,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,sBAAsB,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC;QAChC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC;QACxC,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,CAAC,YAAY,CAAC;QACxD,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAEhC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IACtF,CAAC;IArCD,gBAAgB;IAChB,iGAAiG;IACjG,+FAA+F;IAC/F,6FAA6F;IAC7F,qCAAqC;IAC9B,MAAM,CAAC,MAAM,CAAC,UAAuB,EAAE,MAAe,EAAE,QAAsB,EAAE,eAA8B;QACjH,OAAO,IAAI,aAAa,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;IAC5E,CAAC;IAgCD,sEAAsE;IACtE,IAAI,KAAK;QACL,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC3E,CAAC;IAED,oEAAoE;IACpE,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,EAAE,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACH,IAAI,OAAO,CAAC,GAAW;QACnB,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,YAAY,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,YAAY,EAAE;YACxH,MAAM,IAAI,KAAK,CAAC,wFAAwF,CAAC,CAAC;SAC7G;QAED,IAAI,CAAC,GAAG,EAAE;YACN,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;SACjE;QAED,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,GAAG,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;QACvD,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAEO,KAAK,CAAC,0BAA0B;QACpC,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,YAAY,EAAE;YAC3D,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC,CAAC;SAC7G;QAED,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,CAAC,UAAU,CAAC;QACtD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,yBAAyB,CAAC,CAAC;QAE5D,IAAI;YACA,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAE5B,IAAI,kBAAkB,EAAE;gBACpB,uGAAuG;gBACvG,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;aACzE;YAED,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,CAAC,SAAS,CAAC;YACrD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,uCAAuC,CAAC,CAAC;SAC7E;QAAC,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,CAAC,YAAY,CAAC;YACxD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,gEAAgE,CAAC,IAAI,CAAC,CAAC;YACxG,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SAC5B;IACL,CAAC;IAEO,KAAK,CAAC,cAAc;QACxB,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC;QACvC,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC;QACxC,yGAAyG;QACzG,MAAM,gBAAgB,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrD,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;YAClC,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAE3D,IAAI;YACA,MAAM,gBAAgB,GAA4B;gBAC9C,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;gBAC7B,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO;aAClC,CAAC;YAEF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,4BAA4B,CAAC,CAAC;YAE/D,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAEzF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,sBAAsB,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,CAAC;YAEtF,kGAAkG;YAClG,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAE/B,MAAM,gBAAgB,CAAC;YAEvB,mGAAmG;YACnG,6GAA6G;YAC7G,iCAAiC;YACjC,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC5B,4GAA4G;gBAC5G,+GAA+G;gBAC/G,qEAAqE;gBACrE,+DAA+D;gBAC/D,MAAM,IAAI,CAAC,qBAAqB,CAAC;aACpC;SACJ;QAAC,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,oCAAoC,CAAC,2CAA2C,CAAC,CAAC;YAEnH,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEzB,yFAAyF;YACzF,2GAA2G;YAC3G,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,CAAC,CAAC;SACX;IACL,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,IAAI;QACb,6FAA6F;QAC7F,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;QAExC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACzC,MAAM,IAAI,CAAC,YAAY,CAAC;QAExB,IAAI;YACA,2CAA2C;YAC3C,MAAM,YAAY,CAAC;SACtB;QAAC,OAAO,CAAC,EAAE;YACR,sFAAsF;SACzF;IACL,CAAC;IAEO,aAAa,CAAC,KAAa;QAC/B,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,YAAY,EAAE;YAC3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,8BAA8B,KAAK,4DAA4D,CAAC,CAAC;YAClI,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,aAAa,EAAE;YAC5D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,+BAA+B,KAAK,yEAAyE,CAAC,CAAC;YAChJ,OAAO,IAAI,CAAC,YAAa,CAAC;SAC7B;QAED,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,CAAC,aAAa,CAAC;QAEzD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,yBAAyB,CAAC,CAAC;QAE5D,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,iGAAiG;YACjG,+FAA+F;YAC/F,8BAA8B;YAC9B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,+DAA+D,CAAC,CAAC;YAElG,YAAY,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACzC,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC;YAEvC,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,qBAAqB,GAAG,KAAK,IAAI,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;QAEvH,4FAA4F;QAC5F,6FAA6F;QAC7F,+EAA+E;QAC/E,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAU,UAAkB,EAAE,GAAG,IAAW;QACrD,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QAChE,MAAM,oBAAoB,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QAEvF,wCAAwC;QACxC,IAAI,YAA2B,CAAC;QAEhC,MAAM,OAAO,GAAG,IAAI,OAAO,EAAK,CAAC;QACjC,OAAO,CAAC,cAAc,GAAG,GAAG,EAAE;YAC1B,MAAM,gBAAgB,GAA4B,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAElH,OAAO,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;YAE1D,OAAO,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE;gBAC1B,OAAO,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,YAAY,CAAC,GAAG,CAAC,eAA6D,EAAE,KAAa,EAAE,EAAE;YAClI,IAAI,KAAK,EAAE;gBACP,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACrB,OAAO;aACV;iBAAM,IAAI,eAAe,EAAE;gBACxB,+EAA+E;gBAC/E,IAAI,eAAe,CAAC,IAAI,KAAK,sBAAsB,EAAE;oBACjD,IAAI,eAAe,CAAC,KAAK,EAAE;wBACvB,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;qBACnD;yBAAM;wBACH,OAAO,CAAC,QAAQ,EAAE,CAAC;qBACtB;iBACJ;qBAAM;oBACH,OAAO,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,IAAI,CAAM,CAAC,CAAC;iBAC7C;aACJ;QACL,CAAC,CAAC;QAEF,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC;aACtD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;YACT,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACjB,OAAO,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEP,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAE3C,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,YAAY,CAAC,OAAY;QAC7B,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzC,CAAC;IAED;;;OAGG;IACK,iBAAiB,CAAC,OAAY;QAClC,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;IACnE,CAAC;IAED;;;;;;;;OAQG;IACI,IAAI,CAAC,UAAkB,EAAE,GAAG,IAAW;QAC1C,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QAChE,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;QAEtG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAE1C,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;;;;;;;;;OAUG;IACI,MAAM,CAAU,UAAkB,EAAE,GAAG,IAAW;QACrD,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QAChE,MAAM,oBAAoB,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;QAExF,MAAM,CAAC,GAAG,IAAI,OAAO,CAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3C,sEAAsE;YACtE,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,YAAa,CAAC,GAAG,CAAC,eAA6D,EAAE,KAAa,EAAE,EAAE;gBACnI,IAAI,KAAK,EAAE;oBACP,MAAM,CAAC,KAAK,CAAC,CAAC;oBACd,OAAO;iBACV;qBAAM,IAAI,eAAe,EAAE;oBACxB,+EAA+E;oBAC/E,IAAI,eAAe,CAAC,IAAI,KAAK,sBAAsB,EAAE;wBACjD,IAAI,eAAe,CAAC,KAAK,EAAE;4BACvB,MAAM,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;yBAC5C;6BAAM;4BACH,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;yBACnC;qBACJ;yBAAM;wBACH,MAAM,CAAC,IAAI,KAAK,CAAC,4BAA4B,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;qBACzE;iBACJ;YACL,CAAC,CAAC;YAEF,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC;iBAC5D,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;gBACT,MAAM,CAAC,CAAC,CAAC,CAAC;gBACV,sEAAsE;gBACtE,OAAO,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,YAAa,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC;YAEP,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;;OAIG;IACI,EAAE,CAAC,UAAkB,EAAE,SAAmC;QAC7D,IAAI,CAAC,UAAU,IAAI,CAAC,SAAS,EAAE;YAC3B,OAAO;SACV;QAED,UAAU,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;QACtC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;YAC5B,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;SAClC;QAED,qDAAqD;QACrD,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;YACrD,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC9C,CAAC;IAiBM,GAAG,CAAC,UAAkB,EAAE,MAAiC;QAC5D,IAAI,CAAC,UAAU,EAAE;YACb,OAAO;SACV;QAED,UAAU,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;QACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC3C,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO;SACV;QACD,IAAI,MAAM,EAAE;YACR,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC3C,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;gBAClB,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;gBAC9B,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;oBACvB,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;iBACpC;aACJ;SACJ;aAAM;YACH,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SACpC;IAEL,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,QAAiC;QAC5C,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACxC;IACL,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,QAAiC;QACnD,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC9C;IACL,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,QAAyC;QAC1D,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC7C;IACL,CAAC;IAEO,oBAAoB,CAAC,IAAS;QAClC,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE;YAClC,IAAI,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;YAC5C,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;SAC1C;QAED,iEAAiE;QACjE,IAAI,IAAI,EAAE;YACN,qBAAqB;YACrB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAElE,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;gBAC5B,QAAQ,OAAO,CAAC,IAAI,EAAE;oBAClB,KAAK,sBAAsB;wBACvB,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;wBAClC,MAAM;oBACV,KAAK,sBAAsB,CAAC;oBAC5B,KAAK,sBAAsB,CAAC,CAAC;wBACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;wBACvD,IAAI,QAAQ,EAAE;4BACV,IAAI,OAAO,CAAC,IAAI,KAAK,sBAAsB,EAAE;gCACzC,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;6BAChD;4BACD,IAAI;gCACA,QAAQ,CAAC,OAAO,CAAC,CAAC;6BACrB;4BAAC,OAAO,CAAC,EAAE;gCACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,gCAAgC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;6BACzF;yBACJ;wBACD,MAAM;qBACT;oBACD,KAAK,gBAAgB;wBACjB,yBAAyB;wBACzB,MAAM;oBACV,KAAK,iBAAiB,CAAC,CAAC;wBACpB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,qCAAqC,CAAC,CAAC;wBAE9E,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,qCAAqC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;wBAE3G,IAAI,OAAO,CAAC,cAAc,KAAK,IAAI,EAAE;4BACjC,6IAA6I;4BAC7I,4HAA4H;4BAE5H,mEAAmE;4BACnE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;yBAC/B;6BAAM;4BACH,0HAA0H;4BAC1H,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;yBACjD;wBAED,MAAM;qBACT;oBACD;wBACI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,yBAAyB,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC;wBAC7E,MAAM;iBACb;aACJ;SACJ;QAED,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAEO,yBAAyB,CAAC,IAAS;QACvC,IAAI,eAAyC,CAAC;QAC9C,IAAI,aAAkB,CAAC;QAEvB,IAAI;YACA,CAAC,aAAa,EAAE,eAAe,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;SAC3F;QAAC,OAAO,CAAC,EAAE;YACR,MAAM,OAAO,GAAG,oCAAoC,GAAG,CAAC,CAAC;YACzD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YAE1C,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;YACjC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC/B,MAAM,KAAK,CAAC;SACf;QACD,IAAI,eAAe,CAAC,KAAK,EAAE;YACvB,MAAM,OAAO,GAAG,mCAAmC,GAAG,eAAe,CAAC,KAAK,CAAC;YAC5E,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YAE1C,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;YACjC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC/B,MAAM,KAAK,CAAC;SACf;aAAM;YACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,4BAA4B,CAAC,CAAC;SAClE;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,OAAO,aAAa,CAAC;IACzB,CAAC;IAEO,uBAAuB;QAC3B,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,iBAAiB,EAAE;YAC5C,OAAO;SACV;QAED,sDAAsD;QACtD,8CAA8C;QAC9C,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,+BAA+B,CAAC;QAElF,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAEO,mBAAmB;QACvB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,iBAAiB,EAAE;YAC1E,wBAAwB;YACxB,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAE/F,yCAAyC;YACzC,IAAI,IAAI,CAAC,iBAAiB,KAAK,SAAS,EACxC;gBACI,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;gBAC1D,IAAI,QAAQ,GAAG,CAAC,EAAE;oBACd,QAAQ,GAAG,CAAC,CAAC;iBAChB;gBAED,iIAAiI;gBACjI,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE;oBAC3C,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,SAAS,EAAE;wBACxD,IAAI;4BACA,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;yBACpD;wBAAC,MAAM;4BACJ,4EAA4E;4BAC5E,oGAAoG;4BACpG,IAAI,CAAC,iBAAiB,EAAE,CAAC;yBAC5B;qBACJ;gBACL,CAAC,EAAE,QAAQ,CAAC,CAAC;aAChB;SACJ;IACL,CAAC;IAED,gEAAgE;IACxD,aAAa;QACjB,+EAA+E;QAC/E,uGAAuG;QACvG,mEAAmE;QACnE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC,CAAC;IAC3G,CAAC;IAEO,mBAAmB,CAAC,iBAAoC;QAC5D,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;QACtE,IAAI,OAAO,EAAE;YACT,IAAI;gBACA,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC;aACtE;YAAC,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,6BAA6B,iBAAiB,CAAC,MAAM,CAAC,WAAW,EAAE,iBAAiB,CAAC,IAAI,CAAC,CAAC;aAC/H;YAED,IAAI,iBAAiB,CAAC,YAAY,EAAE;gBAChC,4GAA4G;gBAC5G,MAAM,OAAO,GAAG,oFAAoF,CAAC;gBACrG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;gBAE1C,4CAA4C;gBAC5C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;aAC9D;SACJ;aAAM;YACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,mCAAmC,iBAAiB,CAAC,MAAM,UAAU,CAAC,CAAC;SAC7G;IACL,CAAC;IAEO,iBAAiB,CAAC,KAAa;QACnC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,kCAAkC,KAAK,2BAA2B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAE7H,kIAAkI;QAClI,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,KAAK,IAAI,IAAI,KAAK,CAAC,+EAA+E,CAAC,CAAC;QAE/J,uGAAuG;QACvG,sDAAsD;QACtD,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC7B;QAED,IAAI,CAAC,yBAAyB,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC,CAAC;QAEzH,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,aAAa,EAAE;YAC5D,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;SAC9B;aAAM,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,SAAS,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACxF,mEAAmE;YACnE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SAC1B;aAAM,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,SAAS,EAAE;YAC/D,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;SAC9B;QAED,gGAAgG;QAChG,sHAAsH;QACtH,kJAAkJ;QAClJ,oDAAoD;QACpD,8DAA8D;IAClE,CAAC;IAEO,cAAc,CAAC,KAAa;QAChC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,CAAC,YAAY,CAAC;YACxD,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;YAEhC,IAAI,kBAAkB,EAAE;gBACpB,MAAM,CAAC,QAAQ,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;aAC5E;YAED,IAAI;gBACA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aAChE;YAAC,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,0CAA0C,KAAK,kBAAkB,CAAC,IAAI,CAAC,CAAC;aAC5G;SACJ;IACL,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,KAAa;QAClC,MAAM,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACtC,IAAI,yBAAyB,GAAG,CAAC,CAAC;QAClC,IAAI,UAAU,GAAG,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QAE5G,IAAI,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,yBAAyB,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;QAEzF,IAAI,cAAc,KAAK,IAAI,EAAE;YACzB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,oGAAoG,CAAC,CAAC;YACvI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC3B,OAAO;SACV;QAED,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,CAAC,YAAY,CAAC;QAExD,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,6CAA6C,KAAK,IAAI,CAAC,CAAC;SAClG;aAAM;YACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,0BAA0B,CAAC,CAAC;SACtE;QAED,IAAI,IAAI,CAAC,sBAAsB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1C,IAAI;gBACA,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACtE;YAAC,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,iDAAiD,KAAK,kBAAkB,CAAC,IAAI,CAAC,CAAC;aACnH;YAED,qEAAqE;YACrE,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,YAAY,EAAE;gBAC3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,uFAAuF,CAAC,CAAC;gBAC1H,OAAO;aACV;SACJ;QAED,OAAO,cAAc,KAAK,IAAI,EAAE;YAC5B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,4BAA4B,yBAAyB,kBAAkB,cAAc,MAAM,CAAC,CAAC;YAEpI,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC1B,IAAI,CAAC,qBAAqB,GAAG,UAAU,CAAC,OAAO,EAAE,cAAe,CAAC,CAAC;YACtE,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC;YAEvC,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,YAAY,EAAE;gBAC3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,mFAAmF,CAAC,CAAC;gBACtH,OAAO;aACV;YAED,IAAI;gBACA,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;gBAE5B,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,CAAC,SAAS,CAAC;gBACrD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,yCAAyC,CAAC,CAAC;gBAElF,IAAI,IAAI,CAAC,qBAAqB,CAAC,MAAM,KAAK,CAAC,EAAE;oBACzC,IAAI;wBACA,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;qBAC5F;oBAAC,OAAO,CAAC,EAAE;wBACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,uDAAuD,IAAI,CAAC,UAAU,CAAC,YAAY,kBAAkB,CAAC,IAAI,CAAC,CAAC;qBAChJ;iBACJ;gBAED,OAAO;aACV;YAAC,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,8CAA8C,CAAC,IAAI,CAAC,CAAC;gBAE5F,IAAI,IAAI,CAAC,gBAAgB,KAAK,kBAAkB,CAAC,YAAY,EAAE;oBAC3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,4BAA4B,IAAI,CAAC,gBAAgB,4EAA4E,CAAC,CAAC;oBAChK,gHAAgH;oBAChH,IAAI,IAAI,CAAC,gBAAuB,KAAK,kBAAkB,CAAC,aAAa,EAAE;wBACnE,IAAI,CAAC,cAAc,EAAE,CAAC;qBACzB;oBACD,OAAO;iBACV;gBAED,UAAU,GAAG,CAAC,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC9D,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,yBAAyB,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,kBAAkB,EAAE,UAAU,CAAC,CAAC;aACtH;SACJ;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,+CAA+C,IAAI,CAAC,GAAG,EAAE,GAAG,kBAAkB,WAAW,yBAAyB,6CAA6C,CAAC,CAAC;QAExM,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAEO,kBAAkB,CAAC,kBAA0B,EAAE,mBAA2B,EAAE,WAAkB;QAClG,IAAI;YACA,OAAO,IAAI,CAAC,gBAAiB,CAAC,4BAA4B,CAAC;gBACvD,mBAAmB;gBACnB,kBAAkB;gBAClB,WAAW;aACd,CAAC,CAAC;SACN;QAAC,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,6CAA6C,kBAAkB,KAAK,mBAAmB,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACjJ,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IAEO,yBAAyB,CAAC,KAAY;QAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QAErB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;aACjB,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YACb,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;YAChC,IAAI;gBACA,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;aACzB;YAAC,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,wCAAwC,KAAK,kBAAkB,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;aACxH;QACL,CAAC,CAAC,CAAC;IACX,CAAC;IAEO,iBAAiB;QACrB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACrC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;SACtC;IACL,CAAC;IAEO,eAAe;QACnB,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACrC;IACL,CAAC;IAEO,iBAAiB,CAAC,UAAkB,EAAE,IAAW,EAAE,WAAoB,EAAE,SAAmB;QAChG,IAAI,WAAW,EAAE;YACb,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBACxB,OAAO;oBACH,SAAS,EAAE,IAAI;oBACf,SAAS;oBACT,MAAM,EAAE,UAAU;oBAClB,IAAI,EAAE,sBAAsB;iBAC/B,CAAC;aACL;iBAAM;gBACH,OAAO;oBACH,SAAS,EAAE,IAAI;oBACf,MAAM,EAAE,UAAU;oBAClB,IAAI,EAAE,sBAAsB;iBAC/B,CAAC;aACL;SACJ;aAAM;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;YACxC,IAAI,CAAC,aAAa,EAAE,CAAC;YAErB,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBACxB,OAAO;oBACH,SAAS,EAAE,IAAI;oBACf,YAAY,EAAE,YAAY,CAAC,QAAQ,EAAE;oBACrC,SAAS;oBACT,MAAM,EAAE,UAAU;oBAClB,IAAI,EAAE,sBAAsB;iBAC/B,CAAC;aACL;iBAAM;gBACH,OAAO;oBACH,SAAS,EAAE,IAAI;oBACf,YAAY,EAAE,YAAY,CAAC,QAAQ,EAAE;oBACrC,MAAM,EAAE,UAAU;oBAClB,IAAI,EAAE,sBAAsB;iBAC/B,CAAC;aACL;SACJ;IACL,CAAC;IAEO,cAAc,CAAC,OAA6B,EAAE,YAA2B;QAC7E,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,OAAO;SACV;QAED,gEAAgE;QAChE,IAAI,CAAC,YAAY,EAAE;YACf,YAAY,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;SACpC;QAED,sEAAsE;QACtE,wCAAwC;QACxC,KAAK,MAAM,QAAQ,IAAI,OAAO,EAAE;YAC5B,OAAO,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC;gBACxB,QAAQ,EAAE,GAAG,EAAE;oBACX,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC5G,CAAC;gBACD,KAAK,EAAE,CAAC,GAAG,EAAE,EAAE;oBACX,IAAI,OAAe,CAAC;oBACpB,IAAI,GAAG,YAAY,KAAK,EAAE;wBACtB,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;qBACzB;yBAAM,IAAI,GAAG,IAAI,GAAG,CAAC,QAAQ,EAAE;wBAC5B,OAAO,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;qBAC5B;yBAAM;wBACH,OAAO,GAAG,eAAe,CAAC;qBAC7B;oBAED,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;gBACrH,CAAC;gBACD,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE;oBACX,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;gBAClH,CAAC;aACJ,CAAC,CAAC;SACN;IACL,CAAC;IAEO,uBAAuB,CAAC,IAAW;QACvC,MAAM,OAAO,GAAyB,EAAE,CAAC;QACzC,MAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACzB,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;gBAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC;gBACpC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,iCAAiC;gBACjC,OAAO,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;gBAC7B,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAEpC,0BAA0B;gBAC1B,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACrB;SACJ;QAED,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IAChC,CAAC;IAEO,aAAa,CAAC,GAAQ;QAC1B,oEAAoE;QACpE,OAAO,GAAG,IAAI,GAAG,CAAC,SAAS,IAAI,OAAO,GAAG,CAAC,SAAS,KAAK,UAAU,CAAC;IACvE,CAAC;IAEO,uBAAuB,CAAC,UAAkB,EAAE,IAAW,EAAE,SAAmB;QAChF,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;QACxC,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,OAAO;gBACH,SAAS,EAAE,IAAI;gBACf,YAAY,EAAE,YAAY,CAAC,QAAQ,EAAE;gBACrC,SAAS;gBACT,MAAM,EAAE,UAAU;gBAClB,IAAI,EAAE,4BAA4B;aACrC,CAAC;SACL;aAAM;YACH,OAAO;gBACH,SAAS,EAAE,IAAI;gBACf,YAAY,EAAE,YAAY,CAAC,QAAQ,EAAE;gBACrC,MAAM,EAAE,UAAU;gBAClB,IAAI,EAAE,4BAA4B;aACrC,CAAC;SACL;IACL,CAAC;IAEO,uBAAuB,CAAC,EAAU;QACtC,OAAO;YACH,YAAY,EAAE,EAAE;YAChB,IAAI,EAAE,4BAA4B;SACrC,CAAC;IACN,CAAC;IAEO,wBAAwB,CAAC,EAAU,EAAE,IAAS;QAClD,OAAO;YACH,YAAY,EAAE,EAAE;YAChB,IAAI;YACJ,IAAI,EAAE,sBAAsB;SAC/B,CAAC;IACN,CAAC;IAEO,wBAAwB,CAAC,EAAU,EAAE,KAAW,EAAE,MAAY;QAClE,IAAI,KAAK,EAAE;YACP,OAAO;gBACH,KAAK;gBACL,YAAY,EAAE,EAAE;gBAChB,IAAI,EAAE,sBAAsB;aAC/B,CAAC;SACL;QAED,OAAO;YACH,YAAY,EAAE,EAAE;YAChB,MAAM;YACN,IAAI,EAAE,sBAAsB;SAC/B,CAAC;IACN,CAAC;CACJ;;;AC//BD,gEAAgE;AAChE,uEAAuE;AAIvE,wDAAwD;AACxD,MAAM,oCAAoC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AAE3E,eAAe;AACR,MAAM,sBAAsB;IAG/B,YAAY,WAAsB;QAC9B,IAAI,CAAC,YAAY,GAAG,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,oCAAoC,CAAC;IAClH,CAAC;IAEM,4BAA4B,CAAC,YAA0B;QAC1D,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;IAC9D,CAAC;CACJ;;;ACnBD,gEAAgE;AAChE,uEAAuE;AAEhE,MAAe,WAAW;;AACb,yBAAa,GAAG,eAAe,CAAC;AAChC,kBAAM,GAAG,QAAQ,CAAC;;;ACLtC,gEAAgE;AAChE,uEAAuE;AAEvE,6FAA6F;AAC7F,gDAAgD;AAChD,IAAY,iBASX;AATD,WAAY,iBAAiB;IACzB,yCAAyC;IACzC,yDAAQ;IACR,0CAA0C;IAC1C,qEAAc;IACd,kDAAkD;IAClD,iFAAoB;IACpB,4CAA4C;IAC5C,uEAAe;AACnB,CAAC,EATW,iBAAiB,KAAjB,iBAAiB,QAS5B;AAED,sDAAsD;AACtD,IAAY,cAKX;AALD,WAAY,cAAc;IACtB,6EAA6E;IAC7E,mDAAQ;IACR,0EAA0E;IAC1E,uDAAU;AACd,CAAC,EALW,cAAc,KAAd,cAAc,QAKzB;;;ACtBD,gEAAgE;AAChE,uEAAuE;AAEvE,qFAAqF;AACrF,0FAA0F;AAC1F,iCAAiC;AAEjC,2BAA2B;AAC3B,eAAe;AACR,MAAM,+BAAe;IAA5B;QACY,eAAU,GAAY,KAAK,CAAC;QAC7B,YAAO,GAAwB,IAAI,CAAC;IAkB/C,CAAC;IAhBU,KAAK;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,IAAI,CAAC,OAAO,EAAE,CAAC;aAClB;SACJ;IACL,CAAC;IAED,IAAI,MAAM;QACN,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;CACJ;;;AC7BD,gEAAgE;AAChE,uEAAuE;AAEnB;AACD;AACP;AAEE;AACY;AACoB;AAG9E,oDAAoD;AACpD,eAAe;AACR,MAAM,oBAAoB;IAoB7B,YAAY,UAAsB,EAAE,kBAAgE,EAAE,MAAe,EAAE,OAA+B;QAClJ,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAC9C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,IAAI,+BAAe,EAAE,CAAC;QACxC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IAhBD,uFAAuF;IACvF,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;IACnC,CAAC;IAeM,KAAK,CAAC,OAAO,CAAC,GAAW,EAAE,cAA8B;QAC5D,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC3B,cAAc,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;QACjD,QAAQ,CAAC,cAAc,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;QAE3D,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAEhB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,qCAAqC,CAAC,CAAC;QAExE,4HAA4H;QAC5H,IAAI,cAAc,KAAK,qBAAqB;YACxC,CAAC,OAAO,cAAc,KAAK,WAAW,IAAI,OAAO,IAAI,cAAc,EAAE,CAAC,YAAY,KAAK,QAAQ,CAAC,EAAE;YAClG,MAAM,IAAI,KAAK,CAAC,4FAA4F,CAAC,CAAC;SACjH;QAED,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,kBAAkB,EAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QAE5D,MAAM,WAAW,GAAgB;YAC7B,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM;YACnC,OAAO;YACP,OAAO,EAAE,MAAM;YACf,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe;SACjD,CAAC;QAEF,IAAI,cAAc,KAAK,qBAAqB,EAAE;YAC1C,WAAW,CAAC,YAAY,GAAG,aAAa,CAAC;SAC5C;QAED,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3C,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAE5C,oCAAoC;QACpC,uGAAuG;QACvG,MAAM,OAAO,GAAG,GAAG,GAAG,MAAM,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QACzC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,oCAAoC,OAAO,GAAG,CAAC,CAAC;QACjF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAClE,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE;YAC7B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,qDAAqD,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC;YAE9G,mFAAmF;YACnF,IAAI,CAAC,WAAW,GAAG,IAAI,SAAS,CAAC,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;YACjF,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;SACzB;aAAM;YACH,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACxB;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IACzD,CAAC;IAEO,KAAK,CAAC,eAAe;QACzB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,OAAO,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;SAC3C;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,kBAAkB,CAAC,OAAoB,EAAE,KAAoB;QACjE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YAClB,OAAO,CAAC,OAAO,GAAG,EAAE,CAAC;SACxB;QACD,IAAI,KAAK,EAAE;YACP,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,GAAG,UAAU,KAAK,EAAE,CAAC;YAC/D,OAAO;SACV;QACD,IAAI,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,EAAE;YAC5C,OAAO,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;SACrD;IACL,CAAC;IAEO,KAAK,CAAC,KAAK,CAAC,GAAW,EAAE,WAAwB;QACrD,IAAI;YACA,OAAO,IAAI,CAAC,QAAQ,EAAE;gBAClB,mEAAmE;gBACnE,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC3C,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;gBAE5C,IAAI;oBACA,MAAM,OAAO,GAAG,GAAG,GAAG,MAAM,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;oBACzC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,oCAAoC,OAAO,GAAG,CAAC,CAAC;oBACjF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;oBAElE,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE;wBAC7B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,oDAAoD,CAAC,CAAC;wBAE7F,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;qBACzB;yBAAM,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE;wBACpC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,qDAAqD,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC;wBAE9G,yBAAyB;wBACzB,IAAI,CAAC,WAAW,GAAG,IAAI,SAAS,CAAC,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;wBACjF,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;qBACzB;yBAAM;wBACH,uBAAuB;wBACvB,IAAI,QAAQ,CAAC,OAAO,EAAE;4BAClB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,0CAA0C,aAAa,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,iBAAkB,CAAC,GAAG,CAAC,CAAC;4BACjJ,IAAI,IAAI,CAAC,SAAS,EAAE;gCAChB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;6BACpC;yBACJ;6BAAM;4BACH,wCAAwC;4BACxC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,oDAAoD,CAAC,CAAC;yBAC1F;qBACJ;iBACJ;gBAAC,OAAO,CAAC,EAAE;oBACR,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;wBAChB,qDAAqD;wBACrD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,wDAAwD,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;qBACzG;yBAAM;wBACH,IAAI,CAAC,YAAY,YAAY,EAAE;4BAC3B,wCAAwC;4BACxC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,oDAAoD,CAAC,CAAC;yBAC1F;6BAAM;4BACH,qDAAqD;4BACrD,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;4BACrB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;yBACzB;qBACJ;iBACJ;aACJ;SACJ;gBAAS;YACN,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,2CAA2C,CAAC,CAAC;YAE9E,gHAAgH;YAChH,2HAA2H;YAC3H,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACnB,IAAI,CAAC,aAAa,EAAE,CAAC;aACxB;SACJ;IACL,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,IAAS;QACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC,CAAC;SACpF;QACD,OAAO,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAK,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjI,CAAC;IAEM,KAAK,CAAC,IAAI;QACb,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,2CAA2C,CAAC,CAAC;QAE9E,yFAAyF;QACzF,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QAExB,IAAI;YACA,MAAM,IAAI,CAAC,UAAU,CAAC;YAEtB,qDAAqD;YACrD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,qDAAqD,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;YAEpG,MAAM,OAAO,GAA0B,EAAE,CAAC;YAC1C,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,kBAAkB,EAAE,CAAC;YAC3C,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;YAEtB,MAAM,aAAa,GAAgB;gBAC/B,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;gBACjD,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO;gBAC9B,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe;aACjD,CAAC;YACF,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YAC3C,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,IAAK,EAAE,aAAa,CAAC,CAAC;YAEzD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,8CAA8C,CAAC,CAAC;SACpF;gBAAS;YACN,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,wCAAwC,CAAC,CAAC;YAE3E,+CAA+C;YAC/C,sDAAsD;YACtD,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;IACL,CAAC;IAEO,aAAa;QACjB,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,UAAU,GAAG,+CAA+C,CAAC;YACjE,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,UAAU,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;aAC/C;YACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;YAC7C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAClC;IACL,CAAC;CACJ;;;ACxOD,gEAAgE;AAChE,uEAAuE;AAIzB;AACY;AAC8B;AAGxF,eAAe;AACR,MAAM,yBAAyB;IAWlC,YAAY,UAAsB,EAAE,kBAAgE,EAAE,MAAe,EACzG,OAA+B;QACvC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAC9C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,GAAW,EAAE,cAA8B;QAC5D,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC3B,cAAc,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;QACjD,QAAQ,CAAC,cAAc,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;QAE3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,6BAA6B,CAAC,CAAC;QAEhE,sIAAsI;QACtI,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAEhB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC/C,IAAI,KAAK,EAAE;gBACP,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,gBAAgB,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;aAC3F;SACJ;QAED,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACzC,IAAI,MAAM,GAAG,KAAK,CAAC;YACnB,IAAI,cAAc,KAAK,mBAAmB,EAAE;gBACxC,MAAM,CAAC,IAAI,KAAK,CAAC,2EAA2E,CAAC,CAAC,CAAC;gBAC/F,OAAO;aACV;YAED,IAAI,WAAwB,CAAC;YAC7B,IAAI,kBAAkB,IAAI,oBAAoB,EAAE;gBAC5C,WAAW,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAY,CAAC,GAAG,EAAE,EAAE,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,CAAC;aACzG;iBAAM;gBACH,gDAAgD;gBAChD,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;gBACtD,MAAM,OAAO,GAAmB,EAAE,CAAC;gBACnC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC;gBACzB,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,kBAAkB,EAAE,CAAC;gBAC3C,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;gBAEtB,WAAW,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAY,CAAC,GAAG,EAAE,EAAE,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAC,EAAqB,CAAC,CAAC;aAC9K;YAED,IAAI;gBACA,WAAW,CAAC,SAAS,GAAG,CAAC,CAAe,EAAE,EAAE;oBACxC,IAAI,IAAI,CAAC,SAAS,EAAE;wBAChB,IAAI;4BACA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,kCAAkC,aAAa,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,iBAAkB,CAAC,GAAG,CAAC,CAAC;4BAC/H,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;yBAC1B;wBAAC,OAAO,KAAK,EAAE;4BACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;4BACnB,OAAO;yBACV;qBACJ;gBACL,CAAC,CAAC;gBAEF,yCAAyC;gBACzC,WAAW,CAAC,OAAO,GAAG,CAAC,CAAQ,EAAE,EAAE;oBAC/B,4EAA4E;oBAC5E,IAAI,MAAM,EAAE;wBACR,IAAI,CAAC,MAAM,EAAE,CAAC;qBACjB;yBAAM;wBACH,MAAM,CAAC,IAAI,KAAK,CAAC,iFAAiF;8BAChG,0GAA0G;8BAC1G,uEAAuE,CAAC,CAAC,CAAC;qBAC/E;gBACL,CAAC,CAAC;gBAEF,WAAW,CAAC,MAAM,GAAG,GAAG,EAAE;oBACtB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,oBAAoB,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;oBACxE,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;oBAChC,MAAM,GAAG,IAAI,CAAC;oBACd,OAAO,EAAE,CAAC;gBACd,CAAC,CAAC;aACL;YAAC,OAAO,CAAC,EAAE;gBACR,MAAM,CAAC,CAAC,CAAC,CAAC;gBACV,OAAO;aACV;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,IAAS;QACvB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC,CAAC;SACpF;QACD,OAAO,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAK,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACzH,CAAC;IAEM,IAAI;QACP,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAEO,MAAM,CAAC,CAAS;QACpB,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;YAC1B,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;YAE9B,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;aACnB;SACJ;IACL,CAAC;CACJ;;;ACnID,gEAAgE;AAChE,uEAAuE;AAE3B;AAGE;AACY;AAEiB;AAE3E,eAAe;AACR,MAAM,kBAAkB;IAY3B,YAAY,UAAsB,EAAE,kBAAgE,EAAE,MAAe,EACzG,iBAA0B,EAAE,oBAA0C,EAAE,OAAuB;QACvG,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAC9C,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,qBAAqB,GAAG,oBAAoB,CAAC;QAClD,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAE9B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC5B,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,GAAW,EAAE,cAA8B;QAC5D,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC3B,cAAc,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;QACjD,QAAQ,CAAC,cAAc,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;QAC3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,oCAAoC,CAAC,CAAC;QAEvE,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC/C,IAAI,KAAK,EAAE;gBACP,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,gBAAgB,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;aAC3F;SACJ;QAED,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACzC,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACjC,IAAI,SAAgC,CAAC;YACrC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YACtD,IAAI,MAAM,GAAG,KAAK,CAAC;YAEnB,IAAI,eAAe,EAAE;gBACjB,MAAM,OAAO,GAA0B,EAAE,CAAC;gBAC1C,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,kBAAkB,EAAE,CAAC;gBAC3C,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;gBAEtB,IAAI,OAAO,EAAE;oBACT,OAAO,CAAC,kBAAkB,CAAC,GAAG,GAAG,OAAO,EAAE,CAAC;iBAC9C;gBAED,qDAAqD;gBACrD,SAAS,GAAG,IAAI,IAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,SAAS,EAAE;oBACvD,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE;iBAC5C,CAAC,CAAC;aACN;YAED,IAAI,CAAC,SAAS,EAAE;gBACZ,2DAA2D;gBAC3D,SAAS,GAAG,IAAI,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;aACnD;YAED,IAAI,cAAc,KAAK,qBAAqB,EAAE;gBAC1C,SAAS,CAAC,UAAU,GAAG,aAAa,CAAC;aACxC;YAED,SAAS,CAAC,MAAM,GAAG,CAAC,MAAa,EAAE,EAAE;gBACjC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,0BAA0B,GAAG,GAAG,CAAC,CAAC;gBACzE,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;gBAC5B,MAAM,GAAG,IAAI,CAAC;gBACd,OAAO,EAAE,CAAC;YACd,CAAC,CAAC;YAEF,SAAS,CAAC,OAAO,GAAG,CAAC,KAAY,EAAE,EAAE;gBACjC,IAAI,KAAK,GAAQ,IAAI,CAAC;gBACtB,wFAAwF;gBACxF,IAAI,OAAO,UAAU,KAAK,WAAW,IAAI,KAAK,YAAY,UAAU,EAAE;oBAClE,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;iBACvB;qBAAM;oBACH,KAAK,GAAG,uCAAuC,CAAC;iBACnD;gBAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,0BAA0B,KAAK,GAAG,CAAC,CAAC;YAC/E,CAAC,CAAC;YAEF,SAAS,CAAC,SAAS,GAAG,CAAC,OAAqB,EAAE,EAAE;gBAC5C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,yCAAyC,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;gBACnI,IAAI,IAAI,CAAC,SAAS,EAAE;oBAChB,IAAI;wBACA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;qBAChC;oBAAC,OAAO,KAAK,EAAE;wBACZ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;wBACnB,OAAO;qBACV;iBACJ;YACL,CAAC,CAAC;YAEF,SAAS,CAAC,OAAO,GAAG,CAAC,KAAiB,EAAE,EAAE;gBACtC,+DAA+D;gBAC/D,wCAAwC;gBACxC,IAAI,MAAM,EAAE;oBACR,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;iBACtB;qBAAM;oBACH,IAAI,KAAK,GAAQ,IAAI,CAAC;oBACtB,wFAAwF;oBACxF,IAAI,OAAO,UAAU,KAAK,WAAW,IAAI,KAAK,YAAY,UAAU,EAAE;wBAClE,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;qBACvB;yBAAM;wBACH,KAAK,GAAG,+EAA+E;8BACrF,qDAAqD;8BACrD,2FAA2F;8BAC3F,uEAAuE,CAAC;qBAC7E;oBAED,MAAM,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;iBAC5B;YACL,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,IAAI,CAAC,IAAS;QACjB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,KAAK,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE;YACnF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,wCAAwC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;YAC1H,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3B,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;QAED,OAAO,OAAO,CAAC,MAAM,CAAC,oCAAoC,CAAC,CAAC;IAChE,CAAC;IAEM,IAAI;QACP,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,6GAA6G;YAC7G,iHAAiH;YACjH,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SAC1B;QAED,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAEO,MAAM,CAAC,KAA0B;QACrC,qEAAqE;QACrE,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,4EAA4E;YAC5E,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;YACnC,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;YACrC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;YACnC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YACxB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;SAC/B;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,uCAAuC,CAAC,CAAC;QAC1E,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE;gBAChF,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,sCAAsC,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,MAAM,IAAI,iBAAiB,IAAI,CAAC,CAAC,CAAC;aACvH;iBAAM,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC/B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aACvB;iBAAM;gBACH,IAAI,CAAC,OAAO,EAAE,CAAC;aAClB;SACJ;IACL,CAAC;IAEO,aAAa,CAAC,KAAW;QAC7B,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,QAAQ,KAAK,SAAS,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC;IAC1F,CAAC;CACJ;;;ACpLD,gEAAgE;AAChE,uEAAuE;AAEf;AACgH;AAC5H;AAIE;AAC+B;AACf;AACU;AACE;AAChB;AA2B1D,MAAM,aAAa,GAAG,GAAG,CAAC;AAE1B,eAAe;AACR,MAAM,cAAc;IA0BvB,YAAY,GAAW,EAAE,UAAkC,EAAE;QAbrD,yBAAoB,GAAwC,GAAG,EAAE,GAAE,CAAC,CAAC;QAK7D,aAAQ,GAAQ,EAAE,CAAC;QAMlB,sBAAiB,GAAW,CAAC,CAAC;QAG3C,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAE3B,IAAI,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAErC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,OAAO,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC;QACxG,IAAI,OAAO,OAAO,CAAC,eAAe,KAAK,SAAS,IAAI,OAAO,CAAC,eAAe,KAAK,SAAS,EAAE;YACvF,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;SACpG;aAAM;YACH,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;SACtF;QACD,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;QAE/E,IAAI,eAAe,GAAQ,IAAI,CAAC;QAChC,IAAI,iBAAiB,GAAQ,IAAI,CAAC;QAElC,IAAI,eAAe,IAAI,UAAc,KAAK,WAAW,EAAE;YACnD,oFAAoF;YACpF,gDAAgD;YAChD,MAAM,WAAW,GAAG,KAAyC,CAAC,CAAC,CAAC,OAAuB,CAAC,CAAC,CAAC,CAAO,CAAC;YAClG,eAAe,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YACpC,iBAAiB,GAAG,WAAW,CAAC,aAAa,CAAC,CAAC;SAClD;QAED,IAAI,CAAC,eAAe,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YAC5E,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;SACjC;aAAM,IAAI,eAAe,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YAC9C,IAAI,eAAe,EAAE;gBACjB,OAAO,CAAC,SAAS,GAAG,eAAe,CAAC;aACvC;SACJ;QAED,IAAI,CAAC,eAAe,IAAI,OAAO,WAAW,KAAK,WAAW,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAChF,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;SACrC;aAAM,IAAI,eAAe,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAChD,IAAI,OAAO,iBAAiB,KAAK,WAAW,EAAE;gBAC1C,OAAO,CAAC,WAAW,GAAG,iBAAiB,CAAC;aAC3C;SACJ;QAED,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7E,IAAI,CAAC,gBAAgB,oCAA+B,CAAC;QACrD,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IAIM,KAAK,CAAC,KAAK,CAAC,cAA+B;QAC9C,cAAc,GAAG,cAAc,IAAI,qBAAqB,CAAC;QAEzD,QAAQ,CAAC,cAAc,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;QAE3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,6CAA6C,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAElH,IAAI,IAAI,CAAC,gBAAgB,sCAAiC,EAAE;YACxD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC,CAAC;SAC/G;QAED,IAAI,CAAC,gBAAgB,gCAA6B,CAAC;QAEnD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;QACjE,MAAM,IAAI,CAAC,qBAAqB,CAAC;QAEjC,iHAAiH;QACjH,IAAI,IAAI,CAAC,gBAAuB,wCAAkC,EAAE;YAChE,8EAA8E;YAC9E,MAAM,OAAO,GAAG,8DAA8D,CAAC;YAC/E,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YAE1C,uGAAuG;YACvG,MAAM,IAAI,CAAC,YAAY,CAAC;YAExB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;SAC7C;aAAM,IAAI,IAAI,CAAC,gBAAuB,gCAA8B,EAAE;YACnE,8EAA8E;YAC9E,MAAM,OAAO,GAAG,6GAA6G,CAAC;YAC9H,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YAC1C,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;SAC7C;QAED,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACnC,CAAC;IAEM,IAAI,CAAC,IAA0B;QAClC,IAAI,IAAI,CAAC,gBAAgB,gCAA8B,EAAE;YACrD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC,CAAC;SAC3G;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,UAAU,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,SAAU,CAAC,CAAC;SAC7D;QAED,mDAAmD;QACnD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,KAAa;QAC3B,IAAI,IAAI,CAAC,gBAAgB,sCAAiC,EAAE;YACxD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,+BAA+B,KAAK,wEAAwE,CAAC,CAAC;YAC/I,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;QAED,IAAI,IAAI,CAAC,gBAAgB,wCAAkC,EAAE;YACzD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,+BAA+B,KAAK,yEAAyE,CAAC,CAAC;YAChJ,OAAO,IAAI,CAAC,YAAY,CAAC;SAC5B;QAED,IAAI,CAAC,gBAAgB,sCAAgC,CAAC;QAEtD,IAAI,CAAC,YAAY,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACxC,0DAA0D;YAC1D,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,sDAAsD;QACtD,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAChC,MAAM,IAAI,CAAC,YAAY,CAAC;IAC5B,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,KAAa;QACrC,kEAAkE;QAClE,kFAAkF;QAClF,2CAA2C;QAC3C,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,IAAI;YACA,MAAM,IAAI,CAAC,qBAAqB,CAAC;SACpC;QAAC,OAAO,CAAC,EAAE;YACR,sFAAsF;SACzF;QAED,wFAAwF;QACxF,mGAAmG;QACnG,qDAAqD;QACrD,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI;gBACA,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;aAC/B;YAAC,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,gDAAgD,CAAC,IAAI,CAAC,CAAC;gBACxF,IAAI,CAAC,eAAe,EAAE,CAAC;aAC1B;YAED,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;SAC9B;aAAM;YACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,wFAAwF,CAAC,CAAC;SAC9H;IACL,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,cAA8B;QACvD,iFAAiF;QACjF,yBAAyB;QACzB,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;QACvB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC;QAE5D,IAAI;YACA,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE;gBAC/B,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,KAAK,4BAA4B,EAAE;oBAC1D,8CAA8C;oBAC9C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,4BAA4B,CAAC,CAAC;oBACxE,qDAAqD;oBACrD,yCAAyC;oBACzC,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;iBACnD;qBAAM;oBACH,MAAM,IAAI,KAAK,CAAC,8EAA8E,CAAC,CAAC;iBACnG;aACJ;iBAAM;gBACH,IAAI,iBAAiB,GAA8B,IAAI,CAAC;gBACxD,IAAI,SAAS,GAAG,CAAC,CAAC;gBAElB,GAAG;oBACC,iBAAiB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;oBAC5D,iEAAiE;oBACjE,IAAI,IAAI,CAAC,gBAAgB,wCAAkC,IAAI,IAAI,CAAC,gBAAgB,sCAAiC,EAAE;wBACnH,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;qBACrE;oBAED,IAAI,iBAAiB,CAAC,KAAK,EAAE;wBACzB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;qBAC5C;oBAED,IAAK,iBAAyB,CAAC,eAAe,EAAE;wBAC5C,MAAM,IAAI,KAAK,CAAC,8LAA8L,CAAC,CAAC;qBACnN;oBAED,IAAI,iBAAiB,CAAC,GAAG,EAAE;wBACvB,GAAG,GAAG,iBAAiB,CAAC,GAAG,CAAC;qBAC/B;oBAED,IAAI,iBAAiB,CAAC,WAAW,EAAE;wBAC/B,8DAA8D;wBAC9D,4BAA4B;wBAC5B,MAAM,WAAW,GAAG,iBAAiB,CAAC,WAAW,CAAC;wBAClD,IAAI,CAAC,mBAAmB,GAAG,GAAG,EAAE,CAAC,WAAW,CAAC;qBAChD;oBAED,SAAS,EAAE,CAAC;iBACf,QACM,iBAAiB,CAAC,GAAG,IAAI,SAAS,GAAG,aAAa,EAAE;gBAE3D,IAAI,SAAS,KAAK,aAAa,IAAI,iBAAiB,CAAC,GAAG,EAAE;oBACtD,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;iBAC5D;gBAED,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,iBAAiB,EAAE,cAAc,CAAC,CAAC;aAChG;YAED,IAAI,IAAI,CAAC,SAAS,YAAY,oBAAoB,EAAE;gBAChD,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC;aAC1C;YAED,IAAI,IAAI,CAAC,gBAAgB,kCAA+B,EAAE;gBACtD,0GAA0G;gBAC1G,8GAA8G;gBAC9G,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,4CAA4C,CAAC,CAAC;gBAC/E,IAAI,CAAC,gBAAgB,8BAA4B,CAAC;aACrD;YAED,0GAA0G;YAC1G,wHAAwH;YACxH,yGAAyG;SAC5G;QAAC,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,kCAAkC,GAAG,CAAC,CAAC,CAAC;YACzE,IAAI,CAAC,gBAAgB,oCAA+B,CAAC;YACrD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;YAE3B,4FAA4F;YAC5F,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SAC5B;IACL,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,GAAW;QAC7C,MAAM,OAAO,GAA0B,EAAE,CAAC;QAC1C,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC/C,IAAI,KAAK,EAAE;gBACP,OAAO,CAAC,yBAAyB,CAAC,GAAG,UAAU,KAAK,EAAE,CAAC;aAC1D;SACJ;QAED,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,kBAAkB,EAAE,CAAC;QAC3C,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAEtB,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,gCAAgC,YAAY,GAAG,CAAC,CAAC;QAClF,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE;gBACvD,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;gBACjD,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO;gBAC9B,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe;aACjD,CAAC,CAAC;YAEH,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE;gBAC7B,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,mDAAmD,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;aAC/G;YAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAiB,CAAuB,CAAC;YACvF,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,IAAI,iBAAiB,CAAC,gBAAgB,GAAG,CAAC,EAAE;gBAC/E,kDAAkD;gBAClD,2HAA2H;gBAC3H,iBAAiB,CAAC,eAAe,GAAG,iBAAiB,CAAC,YAAY,CAAC;aACtE;YACD,OAAO,iBAAiB,CAAC;SAC5B;QAAC,OAAO,CAAC,EAAE;YACR,IAAI,YAAY,GAAG,kDAAkD,GAAG,CAAC,CAAC;YAC1E,IAAI,CAAC,YAAY,SAAS,EAAE;gBACxB,IAAI,CAAC,CAAC,UAAU,KAAK,GAAG,EAAE;oBACtB,YAAY,GAAG,YAAY,GAAG,qFAAqF,CAAC;iBACvH;aACJ;YACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YAE/C,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,gCAAgC,CAAC,YAAY,CAAC,CAAC,CAAC;SAC7E;IACL,CAAC;IAEO,iBAAiB,CAAC,GAAW,EAAE,eAA0C;QAC7E,IAAI,CAAC,eAAe,EAAE;YAClB,OAAO,GAAG,CAAC;SACd;QAED,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,eAAe,EAAE,CAAC;IACjF,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,GAAW,EAAE,kBAA8D,EAAE,iBAAqC,EAAE,uBAAuC;QACtL,IAAI,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,iBAAiB,CAAC,eAAe,CAAC,CAAC;QAChF,IAAI,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,EAAE;YACxC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,yEAAyE,CAAC,CAAC;YAC5G,IAAI,CAAC,SAAS,GAAG,kBAAkB,CAAC;YACpC,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,uBAAuB,CAAC,CAAC;YAEhE,IAAI,CAAC,YAAY,GAAG,iBAAiB,CAAC,YAAY,CAAC;YACnD,OAAO;SACV;QAED,MAAM,mBAAmB,GAAU,EAAE,CAAC;QACtC,MAAM,UAAU,GAAG,iBAAiB,CAAC,mBAAmB,IAAI,EAAE,CAAC;QAC/D,IAAI,SAAS,GAAmC,iBAAiB,CAAC;QAClE,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE;YAC/B,MAAM,gBAAgB,GAAG,IAAI,CAAC,wBAAwB,CAAC,QAAQ,EAAE,kBAAkB,EAAE,uBAAuB,CAAC,CAAC;YAC9G,IAAI,gBAAgB,YAAY,KAAK,EAAE;gBACnC,qFAAqF;gBACrF,mBAAmB,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,SAAS,UAAU,CAAC,CAAC;gBAC1D,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aAC9C;iBAAM,IAAI,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,EAAE;gBAC7C,IAAI,CAAC,SAAS,GAAG,gBAAgB,CAAC;gBAClC,IAAI,CAAC,SAAS,EAAE;oBACZ,IAAI;wBACA,SAAS,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;qBACvD;oBAAC,OAAO,EAAE,EAAE;wBACT,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;qBAC7B;oBACD,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,SAAS,CAAC,eAAe,CAAC,CAAC;iBACvE;gBACD,IAAI;oBACA,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,uBAAuB,CAAC,CAAC;oBAChE,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;oBAC3C,OAAO;iBACV;gBAAC,OAAO,EAAE,EAAE;oBACT,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,kCAAkC,QAAQ,CAAC,SAAS,MAAM,EAAE,EAAE,CAAC,CAAC;oBACjG,SAAS,GAAG,SAAS,CAAC;oBACtB,mBAAmB,CAAC,IAAI,CAAC,IAAI,2BAA2B,CAAC,GAAG,QAAQ,CAAC,SAAS,YAAY,EAAE,EAAE,EAAE,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAExI,IAAI,IAAI,CAAC,gBAAgB,kCAA+B,EAAE;wBACtD,MAAM,OAAO,GAAG,sDAAsD,CAAC;wBACvE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;wBAC1C,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;qBAC7C;iBACJ;aACJ;SACJ;QAED,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;YAChC,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,eAAe,CAAC,yEAAyE,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,mBAAmB,CAAC,CAAC,CAAC;SAC7K;QACD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAC,CAAC;IACpH,CAAC;IAEO,mBAAmB,CAAC,SAA4B;QACpD,QAAQ,SAAS,EAAE;YACf,KAAK,4BAA4B;gBAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;oBAC1B,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;iBACxE;gBACD,OAAO,IAAI,kBAAkB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,iBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;YACpL,KAAK,kCAAkC;gBACnC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;oBAC5B,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;iBAC1E;gBACD,OAAO,IAAI,yBAAyB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClH,KAAK,6BAA6B;gBAC9B,OAAO,IAAI,oBAAoB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7G;gBACI,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,GAAG,CAAC,CAAC;SAC3D;IACL,CAAC;IAEO,eAAe,CAAC,GAAW,EAAE,cAA8B;QAC/D,IAAI,CAAC,SAAU,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC3C,IAAI,CAAC,SAAU,CAAC,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC,SAAU,CAAC,OAAO,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;IACxD,CAAC;IAEO,wBAAwB,CAAC,QAA6B,EAAE,kBAAiD,EAAE,uBAAuC;QACtJ,MAAM,SAAS,GAAG,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACxD,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,SAAS,EAAE;YAC/C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,uBAAuB,QAAQ,CAAC,SAAS,+CAA+C,CAAC,CAAC;YAC3H,OAAO,IAAI,KAAK,CAAC,uBAAuB,QAAQ,CAAC,SAAS,+CAA+C,CAAC,CAAC;SAC9G;aAAM;YACH,IAAI,gBAAgB,CAAC,kBAAkB,EAAE,SAAS,CAAC,EAAE;gBACjD,MAAM,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/E,IAAI,eAAe,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE;oBACvD,IAAI,CAAC,SAAS,KAAK,4BAA4B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;wBACxE,CAAC,SAAS,KAAK,kCAAkC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;wBAClF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,uBAAuB,iBAAiB,CAAC,SAAS,CAAC,qDAAqD,CAAC,CAAC;wBAC3I,OAAO,IAAI,yBAAyB,CAAC,IAAI,iBAAiB,CAAC,SAAS,CAAC,yCAAyC,EAAE,SAAS,CAAC,CAAC;qBAC9H;yBAAM;wBACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,wBAAwB,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;wBAC3F,IAAI;4BACA,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;yBAC9C;wBAAC,OAAO,EAAE,EAAE;4BACT,OAAO,EAAE,CAAC;yBACb;qBACJ;iBACJ;qBAAM;oBACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,uBAAuB,iBAAiB,CAAC,SAAS,CAAC,gEAAgE,cAAc,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;oBACjM,OAAO,IAAI,KAAK,CAAC,IAAI,iBAAiB,CAAC,SAAS,CAAC,sBAAsB,cAAc,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;iBACtH;aACJ;iBAAM;gBACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,uBAAuB,iBAAiB,CAAC,SAAS,CAAC,0CAA0C,CAAC,CAAC;gBAChI,OAAO,IAAI,sBAAsB,CAAC,IAAI,iBAAiB,CAAC,SAAS,CAAC,8BAA8B,EAAE,SAAS,CAAC,CAAC;aAChH;SACJ;IACL,CAAC;IAEO,aAAa,CAAC,SAAc;QAChC,OAAO,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ,IAAI,SAAS,IAAI,SAAS,CAAC;IAClF,CAAC;IAEO,eAAe,CAAC,KAAa;QACjC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,iCAAiC,KAAK,2BAA2B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAE5H,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,gFAAgF;QAChF,KAAK,GAAG,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAE5B,IAAI,IAAI,CAAC,gBAAgB,sCAAiC,EAAE;YACxD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,yCAAyC,KAAK,4EAA4E,CAAC,CAAC;YAC7J,OAAO;SACV;QAED,IAAI,IAAI,CAAC,gBAAgB,kCAA+B,EAAE;YACtD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,yCAAyC,KAAK,wEAAwE,CAAC,CAAC;YAC3J,MAAM,IAAI,KAAK,CAAC,iCAAiC,KAAK,qEAAqE,CAAC,CAAC;SAChI;QAED,IAAI,IAAI,CAAC,gBAAgB,wCAAkC,EAAE;YACzD,kFAAkF;YAClF,sFAAsF;YACtF,IAAI,CAAC,oBAAoB,EAAE,CAAC;SAC/B;QAED,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,uCAAuC,KAAK,IAAI,CAAC,CAAC;SACtF;aAAM;YACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,0BAA0B,CAAC,CAAC;SACtE;QAED,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;gBAC/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,0CAA0C,CAAC,IAAI,CAAC,CAAC;YACtF,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;SAC/B;QAED,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAC9B,IAAI,CAAC,gBAAgB,oCAA+B,CAAC;QAErD,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;YAChC,IAAI;gBACA,IAAI,IAAI,CAAC,OAAO,EAAE;oBACd,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;iBACvB;aACJ;YAAC,OAAO,CAAC,EAAE;gBACR,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,0BAA0B,KAAK,kBAAkB,CAAC,IAAI,CAAC,CAAC;aAC5F;SACJ;IACL,CAAC;IAEO,WAAW,CAAC,GAAW;QAC3B,oCAAoC;QACpC,IAAI,GAAG,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE;YAC7E,OAAO,GAAG,CAAC;SACd;QAED,IAAI,CAAC,kBAAkB,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,mBAAmB,GAAG,IAAI,CAAC,CAAC;SAC/C;QAED,6EAA6E;QAC7E,kCAAkC;QAClC,wEAAwE;QACxE,2EAA2E;QAC3E,mGAAmG;QACnG,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QAChD,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAEhB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,gBAAgB,GAAG,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;QAClF,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAEO,oBAAoB,CAAC,GAAW;QACpC,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,YAAY,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACvE,IAAI,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;YAC/C,YAAY,IAAI,GAAG,CAAC;SACvB;QACD,YAAY,IAAI,WAAW,CAAC;QAC5B,YAAY,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAEzD,IAAI,YAAY,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE;YACjD,YAAY,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YACzC,YAAY,IAAI,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC;SAChE;QACD,OAAO,YAAY,CAAC;IACxB,CAAC;CACJ;AAED,SAAS,gBAAgB,CAAC,kBAAiD,EAAE,eAAkC;IAC3G,OAAO,CAAC,kBAAkB,IAAI,CAAC,CAAC,eAAe,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;AACjF,CAAC;AAED,eAAe;AACR,MAAM,kBAAkB;IAO3B,YAA6B,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;QAN3C,YAAO,GAAU,EAAE,CAAC;QAEpB,eAAU,GAAY,IAAI,CAAC;QAK/B,IAAI,CAAC,iBAAiB,GAAG,IAAI,aAAa,EAAE,CAAC;QAC7C,IAAI,CAAC,gBAAgB,GAAG,IAAI,aAAa,EAAE,CAAC;QAE5C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IAC7C,CAAC;IAEM,IAAI,CAAC,IAA0B;QAClC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,IAAI,CAAC,gBAAgB,GAAG,IAAI,aAAa,EAAE,CAAC;SAC/C;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;IACzC,CAAC;IAEM,IAAI;QACP,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAEO,WAAW,CAAC,IAA0B;QAC1C,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,OAAM,CAAC,IAAI,CAAC,EAAE;YACjE,MAAM,IAAI,KAAK,CAAC,+BAA+B,OAAM,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,OAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAC1G;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxB,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;IACrC,CAAC;IAEO,KAAK,CAAC,SAAS;QACnB,OAAO,IAAI,EAAE;YACT,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;YAErC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBAClB,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACvB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;iBACvD;gBAED,MAAM;aACT;YAED,IAAI,CAAC,iBAAiB,GAAG,IAAI,aAAa,EAAE,CAAC;YAE7C,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAiB,CAAC;YAC/C,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;YAElC,MAAM,IAAI,GAAG,OAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC;gBAC/C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;gBACvB,kBAAkB,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAEpD,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;YAExB,IAAI;gBACA,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACjC,eAAe,CAAC,OAAO,EAAE,CAAC;aAC7B;YAAC,OAAO,KAAK,EAAE;gBACZ,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACjC;SACJ;IACL,CAAC;IAEO,MAAM,CAAC,cAAc,CAAC,YAA2B;QACrD,MAAM,WAAW,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAClF,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;QAC3C,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE;YAC7B,MAAM,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC;YACzC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC;SAC7B;QAED,OAAO,MAAM,CAAC,MAAM,CAAC;IACzB,CAAC;CACJ;AAED,MAAM,aAAa;IAKf;QACI,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;IAC1G,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,SAAU,EAAE,CAAC;IACtB,CAAC;IAEM,MAAM,CAAC,MAAY;QACtB,IAAI,CAAC,SAAU,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;CACJ;;;AC/pBD,gEAAgE;AAChE,uEAAuE;AAEyD;AAClF;AACA;AACP;AACiB;AAExD,MAAM,sBAAsB,GAAW,MAAM,CAAC;AAE9C,wCAAwC;AACjC,MAAM,eAAe;IAA5B;QAEI,kBAAkB;QACF,SAAI,GAAW,sBAAsB,CAAC;QACtD,kBAAkB;QACF,YAAO,GAAW,CAAC,CAAC;QAEpC,kBAAkB;QACF,mBAAc,GAAmB,mBAAmB,CAAC;IAmGzE,CAAC;IAjGG;;;;OAIG;IACI,aAAa,CAAC,KAAa,EAAE,MAAe;QAC/C,2HAA2H;QAC3H,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;SAC9E;QAED,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,EAAE,CAAC;SACb;QAED,IAAI,MAAM,KAAK,IAAI,EAAE;YACjB,MAAM,GAAG,mBAAmB,CAAC;SAChC;QAED,qBAAqB;QACrB,MAAM,QAAQ,GAAG,uBAAuB,CAAC,KAAK,CAAC,CAAC;QAEhD,MAAM,WAAW,GAAG,EAAE,CAAC;QACvB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;YAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAe,CAAC;YACxD,IAAI,OAAO,aAAa,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACxC,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;aACvC;YACD,QAAQ,aAAa,CAAC,IAAI,EAAE;gBACxB,KAAK,sBAAsB;oBACvB,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;oBACzC,MAAM;gBACV,KAAK,sBAAsB;oBACvB,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;oBACzC,MAAM;gBACV,KAAK,sBAAsB;oBACvB,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;oBACzC,MAAM;gBACV,KAAK,gBAAgB;oBACjB,oCAAoC;oBACpC,MAAM;gBACV,KAAK,iBAAiB;oBAClB,2CAA2C;oBAC3C,MAAM;gBACV;oBACI,6EAA6E;oBAC7E,MAAM,CAAC,GAAG,CAAC,oBAAoB,EAAE,wBAAwB,GAAG,aAAa,CAAC,IAAI,GAAG,YAAY,CAAC,CAAC;oBAC/F,SAAS;aAChB;YACD,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACnC;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,OAAmB;QACnC,OAAO,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;IAC5D,CAAC;IAEO,oBAAoB,CAAC,OAA0B;QACnD,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,MAAM,EAAE,yCAAyC,CAAC,CAAC;QAEtF,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS,EAAE;YACpC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,YAAY,EAAE,yCAAyC,CAAC,CAAC;SAC/F;IACL,CAAC;IAEO,oBAAoB,CAAC,OAA0B;QACnD,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,YAAY,EAAE,yCAAyC,CAAC,CAAC;QAE5F,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC9D;IACL,CAAC;IAEO,oBAAoB,CAAC,OAA0B;QACnD,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,KAAK,EAAE;YACjC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC9D;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,KAAK,EAAE;YAClC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,KAAK,EAAE,yCAAyC,CAAC,CAAC;SACxF;QAED,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,YAAY,EAAE,yCAAyC,CAAC,CAAC;IAChG,CAAC;IAEO,qBAAqB,CAAC,KAAU,EAAE,YAAoB;QAC1D,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,EAAE,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;SACjC;IACL,CAAC;CACJ;;;ACvHD,gEAAgE;AAChE,uEAAuE;AAEL;AAChB;AACF;AAGF;AAGM;AACb;AACM;AAE7C,MAAM,mBAAmB,GAA4B;IACjD,KAAK,EAAE,cAAc;IACrB,KAAK,EAAE,cAAc;IACrB,IAAI,EAAE,oBAAoB;IAC1B,WAAW,EAAE,oBAAoB;IACjC,IAAI,EAAE,gBAAgB;IACtB,OAAO,EAAE,gBAAgB;IACzB,KAAK,EAAE,cAAc;IACrB,QAAQ,EAAE,iBAAiB;IAC3B,IAAI,EAAE,aAAa;CACtB,CAAC;AAEF,SAAS,aAAa,CAAC,IAAY;IAC/B,6CAA6C;IAC7C,oFAAoF;IACpF,iFAAiF;IACjF,MAAM,OAAO,GAAG,mBAAmB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;IACxD,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;QAChC,OAAO,OAAO,CAAC;KAClB;SAAM;QACH,MAAM,IAAI,KAAK,CAAC,sBAAsB,IAAI,EAAE,CAAC,CAAC;KACjD;AACL,CAAC;AAED,oFAAoF;AAC7E,MAAM,oBAAoB;IA0CtB,gBAAgB,CAAC,OAAoC;QACxD,cAAc,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAEnC,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE;YACnB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;SACzB;aAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YACpC,MAAM,QAAQ,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;YACxC,IAAI,CAAC,MAAM,GAAG,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC;SAC7C;aAAM;YACH,IAAI,CAAC,MAAM,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC;SAC5C;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IA0BM,OAAO,CAAC,GAAW,EAAE,sBAAmE;QAC3F,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC3B,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAE3B,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QAEf,gHAAgH;QAChH,wHAAwH;QACxH,IAAI,OAAO,sBAAsB,KAAK,QAAQ,EAAE;YAC5C,IAAI,CAAC,qBAAqB,GAAG,EAAE,GAAG,IAAI,CAAC,qBAAqB,EAAE,GAAG,sBAAsB,EAAE,CAAC;SAC7F;aAAM;YACH,IAAI,CAAC,qBAAqB,GAAG;gBACzB,GAAG,IAAI,CAAC,qBAAqB;gBAC7B,SAAS,EAAE,sBAAsB;aACpC,CAAC;SACL;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,QAAsB;QACzC,cAAc,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAErC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAmBM,sBAAsB,CAAC,4BAAsD;QAChF,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC9D;QAED,IAAI,CAAC,4BAA4B,EAAE;YAC/B,IAAI,CAAC,eAAe,GAAG,IAAI,sBAAsB,EAAE,CAAC;SACvD;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,4BAA4B,CAAC,EAAE;YACpD,IAAI,CAAC,eAAe,GAAG,IAAI,sBAAsB,CAAC,4BAA4B,CAAC,CAAC;SACnF;aAAM;YACH,IAAI,CAAC,eAAe,GAAG,4BAA4B,CAAC;SACvD;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,qFAAqF;QACrF,8BAA8B;QAC9B,MAAM,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,EAAE,CAAC;QAE/D,8EAA8E;QAC9E,IAAI,qBAAqB,CAAC,MAAM,KAAK,SAAS,EAAE;YAC5C,gGAAgG;YAChG,qBAAqB,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;SAC9C;QAED,4BAA4B;QAC5B,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,0FAA0F,CAAC,CAAC;SAC/G;QACD,MAAM,UAAU,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,qBAAqB,CAAC,CAAC;QAEvE,OAAO,oBAAoB,CACvB,UAAU,EACV,IAAI,CAAC,MAAM,IAAI,mBAAmB,EAClC,IAAI,CAAC,QAAQ,IAAI,IAAI,eAAe,EAAE,EACtC,IAAI,CAAC,eAAe,CAAC,CAAC;IAC9B,CAAC;CACJ;AAED,SAAS,QAAQ,CAAC,MAAW;IACzB,OAAO,MAAM,CAAC,GAAG,KAAK,SAAS,CAAC;AACpC,CAAC;;;ACxND,gEAAgE;AAChE,uEAAuE;AAIR;AACM;AACb;AAEY;AACN;AAE+B;AAC/C;AAC+B;AAEtC;AACa;AAChB;AAEF;;;ACpBlC,gEAAgE;AAChE,uEAAuE;AAEvE,qHAAqH;AAErH,uIAAuI;AACvI,wEAAwE;AACxE,8EAA8E;AAC9E,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,EAAE;IAC/B,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,EAAE;QACnD,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,OAAO;QAC9B,QAAQ,EAAE,IAAI;KACjB,CAAC,CAAC;CACN;AACD,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE;IAC7B,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE;QACjD,wEAAwE;QACxE,4CAA4C;QAC5C,KAAK,EAAE,UAAS,KAAc,EAAE,GAAY,IAAI,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACtH,QAAQ,EAAE,IAAI;KACjB,CAAC,CAAC;CACN;AACD,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,EAAE;IAC/B,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,EAAE;QACnD,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,OAAO;QAC9B,QAAQ,EAAE,IAAI;KACjB,CAAC,CAAC;CACN;AAEuB", "file": "signalr.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"signalR\"] = factory();\n\telse\n\t\troot[\"signalR\"] = factory();\n})(self, function() {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HttpTransportType } from \"./ITransport\";\r\n\r\n/** Error thrown when an HTTP request fails. */\r\nexport class HttpError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The HTTP status code represented by this error. */\r\n    public statusCode: number;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     * @param {number} statusCode The HTTP status code represented by this error.\r\n     */\r\n    constructor(errorMessage: string, statusCode: number) {\r\n        const trueProto = new.target.prototype;\r\n        super(`${errorMessage}: Status code '${statusCode}'`);\r\n        this.statusCode = statusCode;\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when a timeout elapses. */\r\nexport class TimeoutError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.TimeoutError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    constructor(errorMessage: string = \"A timeout occurred.\") {\r\n        const trueProto = new.target.prototype;\r\n        super(errorMessage);\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when an action is aborted. */\r\nexport class AbortError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** Constructs a new instance of {@link AbortError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    constructor(errorMessage: string = \"An abort occurred.\") {\r\n        const trueProto = new.target.prototype;\r\n        super(errorMessage);\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the selected transport is unsupported by the browser. */\r\n/** @private */\r\nexport class UnsupportedTransportError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The {@link @microsoft/signalr.HttpTransportType} this error occured on. */\r\n    public transport: HttpTransportType;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.UnsupportedTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occured on.\r\n     */\r\n    constructor(message: string, transport: HttpTransportType) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'UnsupportedTransportError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the selected transport is disabled by the browser. */\r\n/** @private */\r\nexport class DisabledTransportError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The {@link @microsoft/signalr.HttpTransportType} this error occured on. */\r\n    public transport: HttpTransportType;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.DisabledTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occured on.\r\n     */\r\n    constructor(message: string, transport: HttpTransportType) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'DisabledTransportError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the selected transport cannot be started. */\r\n/** @private */\r\nexport class FailedToStartTransportError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The {@link @microsoft/signalr.HttpTransportType} this error occured on. */\r\n    public transport: HttpTransportType;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.FailedToStartTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occured on.\r\n     */\r\n    constructor(message: string, transport: HttpTransportType) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'FailedToStartTransportError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the negotiation with the server failed to complete. */\r\n/** @private */\r\nexport class FailedToNegotiateWithServerError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.FailedToNegotiateWithServerError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     */\r\n    constructor(message: string) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.errorType = 'FailedToNegotiateWithServerError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when multiple errors have occured. */\r\n/** @private */\r\nexport class AggregateErrors extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The collection of errors this error is aggregating. */\r\n    public innerErrors: Error[];\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.AggregateErrors}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {Error[]} innerErrors The collection of errors this error is aggregating.\r\n     */\r\n    constructor(message: string, innerErrors: Error[]) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n\r\n        this.innerErrors = innerErrors;\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortSignal } from \"./AbortController\";\r\nimport { MessageHeaders } from \"./IHubProtocol\";\r\n\r\n/** Represents an HTTP request. */\r\nexport interface HttpRequest {\r\n    /** The HTTP method to use for the request. */\r\n    method?: string;\r\n\r\n    /** The URL for the request. */\r\n    url?: string;\r\n\r\n    /** The body content for the request. May be a string or an ArrayBuffer (for binary data). */\r\n    content?: string | ArrayBuffer;\r\n\r\n    /** An object describing headers to apply to the request. */\r\n    headers?: MessageHeaders;\r\n\r\n    /** The XMLHttpRequestResponseType to apply to the request. */\r\n    responseType?: XMLHttpRequestResponseType;\r\n\r\n    /** An AbortSignal that can be monitored for cancellation. */\r\n    abortSignal?: AbortSignal;\r\n\r\n    /** The time to wait for the request to complete before throwing a TimeoutError. Measured in milliseconds. */\r\n    timeout?: number;\r\n\r\n    /** This controls whether credentials such as cookies are sent in cross-site requests. */\r\n    withCredentials?: boolean;\r\n}\r\n\r\n/** Represents an HTTP response. */\r\nexport class HttpResponse {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     */\r\n    constructor(statusCode: number);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code and message.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code, message and string content.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     * @param {string} content The content of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string, content: string);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code, message and binary content.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     * @param {ArrayBuffer} content The content of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string, content: ArrayBuffer);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code, message and binary content.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     * @param {string | ArrayBuffer} content The content of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string, content: string | ArrayBuffer);\r\n    constructor(\r\n        public readonly statusCode: number,\r\n        public readonly statusText?: string,\r\n        public readonly content?: string | ArrayBuffer) {\r\n    }\r\n}\r\n\r\n/** Abstraction over an HTTP client.\r\n *\r\n * This class provides an abstraction over an HTTP client so that a different implementation can be provided on different platforms.\r\n */\r\nexport abstract class HttpClient {\r\n    /** Issues an HTTP GET request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public get(url: string): Promise<HttpResponse>;\r\n\r\n    /** Issues an HTTP GET request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @param {HttpRequest} options Additional options to configure the request. The 'url' field in this object will be overridden by the url parameter.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public get(url: string, options: HttpRequest): Promise<HttpResponse>;\r\n    public get(url: string, options?: HttpRequest): Promise<HttpResponse> {\r\n        return this.send({\r\n            ...options,\r\n            method: \"GET\",\r\n            url,\r\n        });\r\n    }\r\n\r\n    /** Issues an HTTP POST request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public post(url: string): Promise<HttpResponse>;\r\n\r\n    /** Issues an HTTP POST request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @param {HttpRequest} options Additional options to configure the request. The 'url' field in this object will be overridden by the url parameter.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public post(url: string, options: HttpRequest): Promise<HttpResponse>;\r\n    public post(url: string, options?: HttpRequest): Promise<HttpResponse> {\r\n        return this.send({\r\n            ...options,\r\n            method: \"POST\",\r\n            url,\r\n        });\r\n    }\r\n\r\n    /** Issues an HTTP DELETE request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public delete(url: string): Promise<HttpResponse>;\r\n\r\n    /** Issues an HTTP DELETE request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @param {HttpRequest} options Additional options to configure the request. The 'url' field in this object will be overridden by the url parameter.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public delete(url: string, options: HttpRequest): Promise<HttpResponse>;\r\n    public delete(url: string, options?: HttpRequest): Promise<HttpResponse> {\r\n        return this.send({\r\n            ...options,\r\n            method: \"DELETE\",\r\n            url,\r\n        });\r\n    }\r\n\r\n    /** Issues an HTTP request to the specified URL, returning a {@link Promise} that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {HttpRequest} request An {@link @microsoft/signalr.HttpRequest} describing the request to send.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an HttpResponse describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public abstract send(request: HttpRequest): Promise<HttpResponse>;\r\n\r\n    /** Gets all cookies that apply to the specified URL.\r\n     *\r\n     * @param url The URL that the cookies are valid for.\r\n     * @returns {string} A string containing all the key-value cookie pairs for the specified URL.\r\n     */\r\n    // @ts-ignore\r\n    public getCookieString(url: string): string {\r\n        return \"\";\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// These values are designed to match the ASP.NET Log Levels since that's the pattern we're emulating here.\r\n/** Indicates the severity of a log message.\r\n *\r\n * Log Levels are ordered in increasing severity. So `Debug` is more severe than `Trace`, etc.\r\n */\r\nexport enum LogLevel {\r\n    /** Log level for very low severity diagnostic messages. */\r\n    Trace = 0,\r\n    /** Log level for low severity diagnostic messages. */\r\n    Debug = 1,\r\n    /** Log level for informational diagnostic messages. */\r\n    Information = 2,\r\n    /** Log level for diagnostic messages that indicate a non-fatal problem. */\r\n    Warning = 3,\r\n    /** Log level for diagnostic messages that indicate a failure in the current operation. */\r\n    Error = 4,\r\n    /** Log level for diagnostic messages that indicate a failure that will terminate the entire application. */\r\n    Critical = 5,\r\n    /** The highest possible log level. Used when configuring logging to indicate that no log messages should be emitted. */\r\n    None = 6,\r\n}\r\n\r\n/** An abstraction that provides a sink for diagnostic messages. */\r\nexport interface ILogger {\r\n    /** Called by the framework to emit a diagnostic message.\r\n     *\r\n     * @param {LogLevel} logLevel The severity level of the message.\r\n     * @param {string} message The message.\r\n     */\r\n    log(logLevel: LogLevel, message: string): void;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\n\r\n/** A logger that does nothing when log messages are sent to it. */\r\nexport class NullLogger implements ILogger {\r\n    /** The singleton instance of the {@link @microsoft/signalr.NullLogger}. */\r\n    public static instance: ILogger = new NullLogger();\r\n\r\n    private constructor() {}\r\n\r\n    /** @inheritDoc */\r\n    // eslint-disable-next-line\r\n    public log(_logLevel: LogLevel, _message: string): void {\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { IStreamSubscriber, ISubscription } from \"./Stream\";\r\nimport { Subject } from \"./Subject\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\n\r\n// Version token that will be replaced by the prepack command\r\n/** The version of the SignalR client. */\r\n\r\nexport const VERSION: string = \"0.0.0-DEV_BUILD\";\r\n/** @private */\r\nexport class Arg {\r\n    public static isRequired(val: any, name: string): void {\r\n        if (val === null || val === undefined) {\r\n            throw new Error(`The '${name}' argument is required.`);\r\n        }\r\n    }\r\n    public static isNotEmpty(val: string, name: string): void {\r\n        if (!val || val.match(/^\\s*$/)) {\r\n            throw new Error(`The '${name}' argument should not be empty.`);\r\n        }\r\n    }\r\n\r\n    public static isIn(val: any, values: any, name: string): void {\r\n        // TypeScript enums have keys for **both** the name and the value of each enum member on the type itself.\r\n        if (!(val in values)) {\r\n            throw new Error(`Unknown ${name} value: ${val}.`);\r\n        }\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport class Platform {\r\n    // react-native has a window but no document so we should check both\r\n    public static get isBrowser(): boolean {\r\n        return typeof window === \"object\" && typeof window.document === \"object\";\r\n    }\r\n\r\n    // WebWorkers don't have a window object so the isBrowser check would fail\r\n    public static get isWebWorker(): boolean {\r\n        return typeof self === \"object\" && \"importScripts\" in self;\r\n    }\r\n\r\n    // react-native has a window but no document\r\n    static get isReactNative(): boolean {\r\n        return typeof window === \"object\" && typeof window.document === \"undefined\";\r\n    }\r\n\r\n    // Node apps shouldn't have a window object, but WebWorkers don't either\r\n    // so we need to check for both WebWorker and window\r\n    public static get isNode(): boolean {\r\n        return !this.isBrowser && !this.isWebWorker && !this.isReactNative;\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport function getDataDetail(data: any, includeContent: boolean): string {\r\n    let detail = \"\";\r\n    if (isArrayBuffer(data)) {\r\n        detail = `Binary data of length ${data.byteLength}`;\r\n        if (includeContent) {\r\n            detail += `. Content: '${formatArrayBuffer(data)}'`;\r\n        }\r\n    } else if (typeof data === \"string\") {\r\n        detail = `String data of length ${data.length}`;\r\n        if (includeContent) {\r\n            detail += `. Content: '${data}'`;\r\n        }\r\n    }\r\n    return detail;\r\n}\r\n\r\n/** @private */\r\nexport function formatArrayBuffer(data: ArrayBuffer): string {\r\n    const view = new Uint8Array(data);\r\n\r\n    // Uint8Array.map only supports returning another Uint8Array?\r\n    let str = \"\";\r\n    view.forEach((num) => {\r\n        const pad = num < 16 ? \"0\" : \"\";\r\n        str += `0x${pad}${num.toString(16)} `;\r\n    });\r\n\r\n    // Trim of trailing space.\r\n    return str.substr(0, str.length - 1);\r\n}\r\n\r\n// Also in signalr-protocol-msgpack/Utils.ts\r\n/** @private */\r\nexport function isArrayBuffer(val: any): val is ArrayBuffer {\r\n    return val && typeof ArrayBuffer !== \"undefined\" &&\r\n        (val instanceof ArrayBuffer ||\r\n            // Sometimes we get an ArrayBuffer that doesn't satisfy instanceof\r\n            (val.constructor && val.constructor.name === \"ArrayBuffer\"));\r\n}\r\n\r\n/** @private */\r\nexport async function sendMessage(logger: ILogger, transportName: string, httpClient: HttpClient, url: string, accessTokenFactory: (() => string | Promise<string>) | undefined,\r\n                                  content: string | ArrayBuffer, options: IHttpConnectionOptions): Promise<void> {\r\n    let headers: {[k: string]: string} = {};\r\n    if (accessTokenFactory) {\r\n        const token = await accessTokenFactory();\r\n        if (token) {\r\n            headers = {\r\n                [\"Authorization\"]: `Bearer ${token}`,\r\n            };\r\n        }\r\n    }\r\n\r\n    const [name, value] = getUserAgentHeader();\r\n    headers[name] = value;\r\n\r\n    logger.log(LogLevel.Trace, `(${transportName} transport) sending data. ${getDataDetail(content, options.logMessageContent!)}.`);\r\n\r\n    const responseType = isArrayBuffer(content) ? \"arraybuffer\" : \"text\";\r\n    const response = await httpClient.post(url, {\r\n        content,\r\n        headers: { ...headers, ...options.headers},\r\n        responseType,\r\n        timeout: options.timeout,\r\n        withCredentials: options.withCredentials,\r\n    });\r\n\r\n    logger.log(LogLevel.Trace, `(${transportName} transport) request complete. Response status: ${response.statusCode}.`);\r\n}\r\n\r\n/** @private */\r\nexport function createLogger(logger?: ILogger | LogLevel): ILogger {\r\n    if (logger === undefined) {\r\n        return new ConsoleLogger(LogLevel.Information);\r\n    }\r\n\r\n    if (logger === null) {\r\n        return NullLogger.instance;\r\n    }\r\n\r\n    if ((logger as ILogger).log !== undefined) {\r\n        return logger as ILogger;\r\n    }\r\n\r\n    return new ConsoleLogger(logger as LogLevel);\r\n}\r\n\r\n/** @private */\r\nexport class SubjectSubscription<T> implements ISubscription<T> {\r\n    private _subject: Subject<T>;\r\n    private _observer: IStreamSubscriber<T>;\r\n\r\n    constructor(subject: Subject<T>, observer: IStreamSubscriber<T>) {\r\n        this._subject = subject;\r\n        this._observer = observer;\r\n    }\r\n\r\n    public dispose(): void {\r\n        const index: number = this._subject.observers.indexOf(this._observer);\r\n        if (index > -1) {\r\n            this._subject.observers.splice(index, 1);\r\n        }\r\n\r\n        if (this._subject.observers.length === 0 && this._subject.cancelCallback) {\r\n            this._subject.cancelCallback().catch((_) => { });\r\n        }\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport class ConsoleLogger implements ILogger {\r\n    private readonly _minLevel: LogLevel;\r\n\r\n    // Public for testing purposes.\r\n    public out: {\r\n        error(message: any): void,\r\n        warn(message: any): void,\r\n        info(message: any): void,\r\n        log(message: any): void,\r\n    };\r\n\r\n    constructor(minimumLogLevel: LogLevel) {\r\n        this._minLevel = minimumLogLevel;\r\n        this.out = console;\r\n    }\r\n\r\n    public log(logLevel: LogLevel, message: string): void {\r\n        if (logLevel >= this._minLevel) {\r\n            const msg = `[${new Date().toISOString()}] ${LogLevel[logLevel]}: ${message}`;\r\n            switch (logLevel) {\r\n                case LogLevel.Critical:\r\n                case LogLevel.Error:\r\n                    this.out.error(msg);\r\n                    break;\r\n                case LogLevel.Warning:\r\n                    this.out.warn(msg);\r\n                    break;\r\n                case LogLevel.Information:\r\n                    this.out.info(msg);\r\n                    break;\r\n                default:\r\n                    // console.debug only goes to attached debuggers in Node, so we use console.log for Trace and Debug\r\n                    this.out.log(msg);\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport function getUserAgentHeader(): [string, string] {\r\n    let userAgentHeaderName = \"X-SignalR-User-Agent\";\r\n    if (Platform.isNode) {\r\n        userAgentHeaderName = \"User-Agent\";\r\n    }\r\n    return [ userAgentHeaderName, constructUserAgent(VERSION, getOsName(), getRuntime(), getRuntimeVersion()) ];\r\n}\r\n\r\n/** @private */\r\nexport function constructUserAgent(version: string, os: string, runtime: string, runtimeVersion: string | undefined): string {\r\n    // Microsoft SignalR/[Version] ([Detailed Version]; [Operating System]; [Runtime]; [Runtime Version])\r\n    let userAgent: string = \"Microsoft SignalR/\";\r\n\r\n    const majorAndMinor = version.split(\".\");\r\n    userAgent += `${majorAndMinor[0]}.${majorAndMinor[1]}`;\r\n    userAgent += ` (${version}; `;\r\n\r\n    if (os && os !== \"\") {\r\n        userAgent += `${os}; `;\r\n    } else {\r\n        userAgent += \"Unknown OS; \";\r\n    }\r\n\r\n    userAgent += `${runtime}`;\r\n\r\n    if (runtimeVersion) {\r\n        userAgent += `; ${runtimeVersion}`;\r\n    } else {\r\n        userAgent += \"; Unknown Runtime Version\";\r\n    }\r\n\r\n    userAgent += \")\";\r\n    return userAgent;\r\n}\r\n\r\n// eslint-disable-next-line spaced-comment\r\n/*#__PURE__*/ function getOsName(): string {\r\n    if (Platform.isNode) {\r\n        switch (process.platform) {\r\n            case \"win32\":\r\n                return \"Windows NT\";\r\n            case \"darwin\":\r\n                return \"macOS\";\r\n            case \"linux\":\r\n                return \"Linux\";\r\n            default:\r\n                return process.platform;\r\n        }\r\n    } else {\r\n        return \"\";\r\n    }\r\n}\r\n\r\n// eslint-disable-next-line spaced-comment\r\n/*#__PURE__*/ function getRuntimeVersion(): string | undefined {\r\n    if (Platform.isNode) {\r\n        return process.versions.node;\r\n    }\r\n    return undefined;\r\n}\r\n\r\nfunction getRuntime(): string {\r\n    if (Platform.isNode) {\r\n        return \"NodeJS\";\r\n    } else {\r\n        return \"Browser\";\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport function getErrorString(e: any): string {\r\n    if (e.stack) {\r\n        return e.stack;\r\n    } else if (e.message) {\r\n        return e.message;\r\n    }\r\n    return `${e}`;\r\n}\r\n\r\n/** @private */\r\nexport function getGlobalThis(): unknown {\r\n    // globalThis is semi-new and not available in Node until v12\r\n    if (typeof globalThis !== \"undefined\") {\r\n        return globalThis;\r\n    }\r\n    if (typeof self !== \"undefined\") {\r\n        return self;\r\n    }\r\n    if (typeof window !== \"undefined\") {\r\n        return window;\r\n    }\r\n    if (typeof global !== \"undefined\") {\r\n        return global;\r\n    }\r\n    throw new Error(\"could not find global\");\r\n}", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// @ts-ignore: This will be removed from built files and is here to make the types available during dev work\r\nimport { CookieJar } from \"@types/tough-cookie\";\r\n\r\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { Platform, getGlobalThis } from \"./Utils\";\r\n\r\nexport class FetchHttpClient extends HttpClient {\r\n    private readonly _abortControllerType: { prototype: AbortController, new(): AbortController };\r\n    private readonly _fetchType: (input: RequestInfo, init?: RequestInit) => Promise<Response>;\r\n    private readonly _jar?: CookieJar;\r\n\r\n    private readonly _logger: ILogger;\r\n\r\n    public constructor(logger: ILogger) {\r\n        super();\r\n        this._logger = logger;\r\n\r\n        if (typeof fetch === \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: T<PERSON> doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n\r\n            // Cookies aren't automatically handled in Node so we need to add a CookieJar to preserve cookies across requests\r\n            this._jar = new (requireFunc(\"tough-cookie\")).CookieJar();\r\n            this._fetchType = requireFunc(\"node-fetch\");\r\n\r\n            // node-fetch doesn't have a nice API for getting and setting cookies\r\n            // fetch-cookie will wrap a fetch implementation with a default CookieJar or a provided one\r\n            this._fetchType = requireFunc(\"fetch-cookie\")(this._fetchType, this._jar);\r\n        } else {\r\n            this._fetchType = fetch.bind(getGlobalThis());\r\n        }\r\n        if (typeof AbortController === \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n\r\n            // Node needs EventListener methods on AbortController which our custom polyfill doesn't provide\r\n            this._abortControllerType = requireFunc(\"abort-controller\");\r\n        } else {\r\n            this._abortControllerType = AbortController;\r\n        }\r\n    }\r\n\r\n    /** @inheritDoc */\r\n    public async send(request: HttpRequest): Promise<HttpResponse> {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            throw new AbortError();\r\n        }\r\n\r\n        if (!request.method) {\r\n            throw new Error(\"No method defined.\");\r\n        }\r\n        if (!request.url) {\r\n            throw new Error(\"No url defined.\");\r\n        }\r\n\r\n        const abortController = new this._abortControllerType();\r\n\r\n        let error: any;\r\n        // Hook our abortSignal into the abort controller\r\n        if (request.abortSignal) {\r\n            request.abortSignal.onabort = () => {\r\n                abortController.abort();\r\n                error = new AbortError();\r\n            };\r\n        }\r\n\r\n        // If a timeout has been passed in, setup a timeout to call abort\r\n        // Type needs to be any to fit window.setTimeout and NodeJS.setTimeout\r\n        let timeoutId: any = null;\r\n        if (request.timeout) {\r\n            const msTimeout = request.timeout!;\r\n            timeoutId = setTimeout(() => {\r\n                abortController.abort();\r\n                this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\r\n                error = new TimeoutError();\r\n            }, msTimeout);\r\n        }\r\n\r\n        let response: Response;\r\n        try {\r\n            response = await this._fetchType(request.url!, {\r\n                body: request.content!,\r\n                cache: \"no-cache\",\r\n                credentials: request.withCredentials === true ? \"include\" : \"same-origin\",\r\n                headers: {\r\n                    \"Content-Type\": \"text/plain;charset=UTF-8\",\r\n                    \"X-Requested-With\": \"XMLHttpRequest\",\r\n                    ...request.headers,\r\n                },\r\n                method: request.method!,\r\n                mode: \"cors\",\r\n                redirect: \"follow\",\r\n                signal: abortController.signal,\r\n            });\r\n        } catch (e) {\r\n            if (error) {\r\n                throw error;\r\n            }\r\n            this._logger.log(\r\n                LogLevel.Warning,\r\n                `Error from HTTP request. ${e}.`,\r\n            );\r\n            throw e;\r\n        } finally {\r\n            if (timeoutId) {\r\n                clearTimeout(timeoutId);\r\n            }\r\n            if (request.abortSignal) {\r\n                request.abortSignal.onabort = null;\r\n            }\r\n        }\r\n\r\n        if (!response.ok) {\r\n            const errorMessage = await deserializeContent(response, \"text\") as string;\r\n            throw new HttpError(errorMessage || response.statusText, response.status);\r\n        }\r\n\r\n        const content = deserializeContent(response, request.responseType);\r\n        const payload = await content;\r\n\r\n        return new HttpResponse(\r\n            response.status,\r\n            response.statusText,\r\n            payload,\r\n        );\r\n    }\r\n\r\n    public getCookieString(url: string): string {\r\n        let cookies: string = \"\";\r\n        if (Platform.isNode && this._jar) {\r\n            // @ts-ignore: unused variable\r\n            this._jar.getCookies(url, (e, c) => cookies = c.join(\"; \"));\r\n        }\r\n        return cookies;\r\n    }\r\n}\r\n\r\nfunction deserializeContent(response: Response, responseType?: XMLHttpRequestResponseType): Promise<string | ArrayBuffer> {\r\n    let content;\r\n    switch (responseType) {\r\n        case \"arraybuffer\":\r\n            content = response.arrayBuffer();\r\n            break;\r\n        case \"text\":\r\n            content = response.text();\r\n            break;\r\n        case \"blob\":\r\n        case \"document\":\r\n        case \"json\":\r\n            throw new Error(`${responseType} is not supported.`);\r\n        default:\r\n            content = response.text();\r\n            break;\r\n    }\r\n\r\n    return content;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\n\r\nexport class XhrHttpClient extends HttpClient {\r\n    private readonly _logger: ILogger;\r\n\r\n    public constructor(logger: ILogger) {\r\n        super();\r\n        this._logger = logger;\r\n    }\r\n\r\n    /** @inheritDoc */\r\n    public send(request: HttpRequest): Promise<HttpResponse> {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            return Promise.reject(new AbortError());\r\n        }\r\n\r\n        if (!request.method) {\r\n            return Promise.reject(new Error(\"No method defined.\"));\r\n        }\r\n        if (!request.url) {\r\n            return Promise.reject(new Error(\"No url defined.\"));\r\n        }\r\n\r\n        return new Promise<HttpResponse>((resolve, reject) => {\r\n            const xhr = new XMLHttpRequest();\r\n\r\n            xhr.open(request.method!, request.url!, true);\r\n            xhr.withCredentials = request.withCredentials === undefined ? true : request.withCredentials;\r\n            xhr.setRequestHeader(\"X-Requested-With\", \"XMLHttpRequest\");\r\n            // Explicitly setting the Content-Type header for React Native on Android platform.\r\n            xhr.setRequestHeader(\"Content-Type\", \"text/plain;charset=UTF-8\");\r\n\r\n            const headers = request.headers;\r\n            if (headers) {\r\n                Object.keys(headers)\r\n                    .forEach((header) => {\r\n                        xhr.setRequestHeader(header, headers[header]);\r\n                    });\r\n            }\r\n\r\n            if (request.responseType) {\r\n                xhr.responseType = request.responseType;\r\n            }\r\n\r\n            if (request.abortSignal) {\r\n                request.abortSignal.onabort = () => {\r\n                    xhr.abort();\r\n                    reject(new AbortError());\r\n                };\r\n            }\r\n\r\n            if (request.timeout) {\r\n                xhr.timeout = request.timeout;\r\n            }\r\n\r\n            xhr.onload = () => {\r\n                if (request.abortSignal) {\r\n                    request.abortSignal.onabort = null;\r\n                }\r\n\r\n                if (xhr.status >= 200 && xhr.status < 300) {\r\n                    resolve(new HttpResponse(xhr.status, xhr.statusText, xhr.response || xhr.responseText));\r\n                } else {\r\n                    reject(new HttpError(xhr.response || xhr.responseText || xhr.statusText, xhr.status));\r\n                }\r\n            };\r\n\r\n            xhr.onerror = () => {\r\n                this._logger.log(LogLevel.Warning, `Error from HTTP request. ${xhr.status}: ${xhr.statusText}.`);\r\n                reject(new HttpError(xhr.statusText, xhr.status));\r\n            };\r\n\r\n            xhr.ontimeout = () => {\r\n                this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\r\n                reject(new TimeoutError());\r\n            };\r\n\r\n            xhr.send(request.content || \"\");\r\n        });\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortError } from \"./Errors\";\r\nimport { FetchHttpClient } from \"./FetchHttpClient\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nimport { ILogger } from \"./ILogger\";\r\nimport { Platform } from \"./Utils\";\r\nimport { XhrHttpClient } from \"./XhrHttpClient\";\r\n\r\n/** Default implementation of {@link @microsoft/signalr.HttpClient}. */\r\nexport class DefaultHttpClient extends HttpClient {\r\n    private readonly _httpClient: HttpClient;\r\n\r\n    /** Creates a new instance of the {@link @microsoft/signalr.DefaultHttpClient}, using the provided {@link @microsoft/signalr.ILogger} to log messages. */\r\n    public constructor(logger: ILogger) {\r\n        super();\r\n\r\n        if (typeof fetch !== \"undefined\" || Platform.isNode) {\r\n            this._httpClient = new FetchHttpClient(logger);\r\n        } else if (typeof XMLHttpRequest !== \"undefined\") {\r\n            this._httpClient = new XhrHttpClient(logger);\r\n        } else {\r\n            throw new Error(\"No usable HttpClient found.\");\r\n        }\r\n    }\r\n\r\n    /** @inheritDoc */\r\n    public send(request: HttpRequest): Promise<HttpResponse> {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            return Promise.reject(new AbortError());\r\n        }\r\n\r\n        if (!request.method) {\r\n            return Promise.reject(new Error(\"No method defined.\"));\r\n        }\r\n        if (!request.url) {\r\n            return Promise.reject(new Error(\"No url defined.\"));\r\n        }\r\n\r\n        return this._httpClient.send(request);\r\n    }\r\n\r\n    public getCookieString(url: string): string {\r\n        return this._httpClient.getCookieString(url);\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Not exported from index\r\n/** @private */\r\nexport class TextMessageFormat {\r\n    public static RecordSeparatorCode = 0x1e;\r\n    public static RecordSeparator = String.fromCharCode(TextMessageFormat.RecordSeparatorCode);\r\n\r\n    public static write(output: string): string {\r\n        return `${output}${TextMessageFormat.RecordSeparator}`;\r\n    }\r\n\r\n    public static parse(input: string): string[] {\r\n        if (input[input.length - 1] !== TextMessageFormat.RecordSeparator) {\r\n            throw new Error(\"Message is incomplete.\");\r\n        }\r\n\r\n        const messages = input.split(TextMessageFormat.RecordSeparator);\r\n        messages.pop();\r\n        return messages;\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { TextMessageFormat } from \"./TextMessageFormat\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\n\r\n/** @private */\r\nexport interface HandshakeRequestMessage {\r\n    readonly protocol: string;\r\n    readonly version: number;\r\n}\r\n\r\n/** @private */\r\nexport interface HandshakeResponseMessage {\r\n    readonly error: string;\r\n    readonly minorVersion: number;\r\n}\r\n\r\n/** @private */\r\nexport class HandshakeProtocol {\r\n    // Handshake request is always JSON\r\n    public writeHandshakeRequest(handshakeRequest: HandshakeRequestMessage): string {\r\n        return TextMessageFormat.write(JSON.stringify(handshakeRequest));\r\n    }\r\n\r\n    public parseHandshakeResponse(data: any): [any, HandshakeResponseMessage] {\r\n        let messageData: string;\r\n        let remainingData: any;\r\n\r\n        if (isArrayBuffer(data)) {\r\n            // Format is binary but still need to read JSON text from handshake response\r\n            const binaryData = new Uint8Array(data);\r\n            const separatorIndex = binaryData.indexOf(TextMessageFormat.RecordSeparatorCode);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            const responseLength = separatorIndex + 1;\r\n            messageData = String.fromCharCode.apply(null, Array.prototype.slice.call(binaryData.slice(0, responseLength)));\r\n            remainingData = (binaryData.byteLength > responseLength) ? binaryData.slice(responseLength).buffer : null;\r\n        } else {\r\n            const textData: string = data;\r\n            const separatorIndex = textData.indexOf(TextMessageFormat.RecordSeparator);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            const responseLength = separatorIndex + 1;\r\n            messageData = textData.substring(0, responseLength);\r\n            remainingData = (textData.length > responseLength) ? textData.substring(responseLength) : null;\r\n        }\r\n\r\n        // At this point we should have just the single handshake message\r\n        const messages = TextMessageFormat.parse(messageData);\r\n        const response = JSON.parse(messages[0]);\r\n        if (response.type) {\r\n            throw new Error(\"Expected a handshake response from the server.\");\r\n        }\r\n        const responseMessage: HandshakeResponseMessage = response;\r\n\r\n        // multiple messages could have arrived with handshake\r\n        // return additional data to be parsed as usual, or null if all parsed\r\n        return [remainingData, responseMessage];\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { ILogger } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\n\r\n/** Defines the type of a Hub Message. */\r\nexport enum MessageType {\r\n    /** Indicates the message is an Invocation message and implements the {@link @microsoft/signalr.InvocationMessage} interface. */\r\n    Invocation = 1,\r\n    /** Indicates the message is a StreamItem message and implements the {@link @microsoft/signalr.StreamItemMessage} interface. */\r\n    StreamItem = 2,\r\n    /** Indicates the message is a Completion message and implements the {@link @microsoft/signalr.CompletionMessage} interface. */\r\n    Completion = 3,\r\n    /** Indicates the message is a Stream Invocation message and implements the {@link @microsoft/signalr.StreamInvocationMessage} interface. */\r\n    StreamInvocation = 4,\r\n    /** Indicates the message is a Cancel Invocation message and implements the {@link @microsoft/signalr.CancelInvocationMessage} interface. */\r\n    CancelInvocation = 5,\r\n    /** Indicates the message is a Ping message and implements the {@link @microsoft/signalr.PingMessage} interface. */\r\n    Ping = 6,\r\n    /** Indicates the message is a Close message and implements the {@link @microsoft/signalr.CloseMessage} interface. */\r\n    Close = 7,\r\n}\r\n\r\n/** Defines a dictionary of string keys and string values representing headers attached to a Hub message. */\r\nexport interface MessageHeaders {\r\n    /** Gets or sets the header with the specified key. */\r\n    [key: string]: string;\r\n}\r\n\r\n/** Union type of all known Hub messages. */\r\nexport type HubMessage =\r\n    InvocationMessage |\r\n    StreamInvocationMessage |\r\n    StreamItemMessage |\r\n    CompletionMessage |\r\n    CancelInvocationMessage |\r\n    PingMessage |\r\n    CloseMessage;\r\n\r\n/** Defines properties common to all Hub messages. */\r\nexport interface HubMessageBase {\r\n    /** A {@link @microsoft/signalr.MessageType} value indicating the type of this message. */\r\n    readonly type: MessageType;\r\n}\r\n\r\n/** Defines properties common to all Hub messages relating to a specific invocation. */\r\nexport interface HubInvocationMessage extends HubMessageBase {\r\n    /** A {@link @microsoft/signalr.MessageHeaders} dictionary containing headers attached to the message. */\r\n    readonly headers?: MessageHeaders;\r\n    /** The ID of the invocation relating to this message.\r\n     *\r\n     * This is expected to be present for {@link @microsoft/signalr.StreamInvocationMessage} and {@link @microsoft/signalr.CompletionMessage}. It may\r\n     * be 'undefined' for an {@link @microsoft/signalr.InvocationMessage} if the sender does not expect a response.\r\n     */\r\n    readonly invocationId?: string;\r\n}\r\n\r\n/** A hub message representing a non-streaming invocation. */\r\nexport interface InvocationMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Invocation;\r\n    /** The target method name. */\r\n    readonly target: string;\r\n    /** The target method arguments. */\r\n    readonly arguments: any[];\r\n    /** The target methods stream IDs. */\r\n    readonly streamIds?: string[];\r\n}\r\n\r\n/** A hub message representing a streaming invocation. */\r\nexport interface StreamInvocationMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.StreamInvocation;\r\n\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n    /** The target method name. */\r\n    readonly target: string;\r\n    /** The target method arguments. */\r\n    readonly arguments: any[];\r\n    /** The target methods stream IDs. */\r\n    readonly streamIds?: string[];\r\n}\r\n\r\n/** A hub message representing a single item produced as part of a result stream. */\r\nexport interface StreamItemMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.StreamItem;\r\n\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n\r\n    /** The item produced by the server. */\r\n    readonly item?: any;\r\n}\r\n\r\n/** A hub message representing the result of an invocation. */\r\nexport interface CompletionMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Completion;\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n    /** The error produced by the invocation, if any.\r\n     *\r\n     * Either {@link @microsoft/signalr.CompletionMessage.error} or {@link @microsoft/signalr.CompletionMessage.result} must be defined, but not both.\r\n     */\r\n    readonly error?: string;\r\n    /** The result produced by the invocation, if any.\r\n     *\r\n     * Either {@link @microsoft/signalr.CompletionMessage.error} or {@link @microsoft/signalr.CompletionMessage.result} must be defined, but not both.\r\n     */\r\n    readonly result?: any;\r\n}\r\n\r\n/** A hub message indicating that the sender is still active. */\r\nexport interface PingMessage extends HubMessageBase {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Ping;\r\n}\r\n\r\n/** A hub message indicating that the sender is closing the connection.\r\n *\r\n * If {@link @microsoft/signalr.CloseMessage.error} is defined, the sender is closing the connection due to an error.\r\n */\r\nexport interface CloseMessage extends HubMessageBase {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Close;\r\n    /** The error that triggered the close, if any.\r\n     *\r\n     * If this property is undefined, the connection was closed normally and without error.\r\n     */\r\n    readonly error?: string;\r\n\r\n    /** If true, clients with automatic reconnects enabled should attempt to reconnect after receiving the CloseMessage. Otherwise, they should not. */\r\n    readonly allowReconnect?: boolean;\r\n}\r\n\r\n/** A hub message sent to request that a streaming invocation be canceled. */\r\nexport interface CancelInvocationMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.CancelInvocation;\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n}\r\n\r\n/** A protocol abstraction for communicating with SignalR Hubs.  */\r\nexport interface IHubProtocol {\r\n    /** The name of the protocol. This is used by SignalR to resolve the protocol between the client and server. */\r\n    readonly name: string;\r\n    /** The version of the protocol. */\r\n    readonly version: number;\r\n    /** The {@link @microsoft/signalr.TransferFormat} of the protocol. */\r\n    readonly transferFormat: TransferFormat;\r\n\r\n    /** Creates an array of {@link @microsoft/signalr.HubMessage} objects from the specified serialized representation.\r\n     *\r\n     * If {@link @microsoft/signalr.IHubProtocol.transferFormat} is 'Text', the `input` parameter must be a string, otherwise it must be an ArrayBuffer.\r\n     *\r\n     * @param {string | ArrayBuffer} input A string or ArrayBuffer containing the serialized representation.\r\n     * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n     */\r\n    parseMessages(input: string | ArrayBuffer, logger: ILogger): HubMessage[];\r\n\r\n    /** Writes the specified {@link @microsoft/signalr.HubMessage} to a string or ArrayBuffer and returns it.\r\n     *\r\n     * If {@link @microsoft/signalr.IHubProtocol.transferFormat} is 'Text', the result of this method will be a string, otherwise it will be an ArrayBuffer.\r\n     *\r\n     * @param {HubMessage} message The message to write.\r\n     * @returns {string | ArrayBuffer} A string or ArrayBuffer containing the serialized representation of the message.\r\n     */\r\n    writeMessage(message: HubMessage): string | ArrayBuffer;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { IStreamResult, IStreamSubscriber, ISubscription } from \"./Stream\";\r\nimport { SubjectSubscription } from \"./Utils\";\r\n\r\n/** Stream implementation to stream items to the server. */\r\nexport class Subject<T> implements IStreamResult<T> {\r\n    /** @internal */\r\n    public observers: IStreamSubscriber<T>[];\r\n\r\n    /** @internal */\r\n    public cancelCallback?: () => Promise<void>;\r\n\r\n    constructor() {\r\n        this.observers = [];\r\n    }\r\n\r\n    public next(item: T): void {\r\n        for (const observer of this.observers) {\r\n            observer.next(item);\r\n        }\r\n    }\r\n\r\n    public error(err: any): void {\r\n        for (const observer of this.observers) {\r\n            if (observer.error) {\r\n                observer.error(err);\r\n            }\r\n        }\r\n    }\r\n\r\n    public complete(): void {\r\n        for (const observer of this.observers) {\r\n            if (observer.complete) {\r\n                observer.complete();\r\n            }\r\n        }\r\n    }\r\n\r\n    public subscribe(observer: IStreamSubscriber<T>): ISubscription<T> {\r\n        this.observers.push(observer);\r\n        return new SubjectSubscription(this, observer);\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HandshakeProtocol, HandshakeRequestMessage, HandshakeResponseMessage } from \"./HandshakeProtocol\";\r\nimport { IConnection } from \"./IConnection\";\r\nimport { CancelInvocationMessage, CompletionMessage, IHubProtocol, InvocationMessage, MessageType, StreamInvocationMessage, StreamItemMessage } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { IRetryPolicy } from \"./IRetryPolicy\";\r\nimport { IStreamResult } from \"./Stream\";\r\nimport { Subject } from \"./Subject\";\r\nimport { Arg, getErrorString, Platform } from \"./Utils\";\r\n\r\nconst DEFAULT_TIMEOUT_IN_MS: number = 30 * 1000;\r\nconst DEFAULT_PING_INTERVAL_IN_MS: number = 15 * 1000;\r\n\r\n/** Describes the current state of the {@link HubConnection} to the server. */\r\nexport enum HubConnectionState {\r\n    /** The hub connection is disconnected. */\r\n    Disconnected = \"Disconnected\",\r\n    /** The hub connection is connecting. */\r\n    Connecting = \"Connecting\",\r\n    /** The hub connection is connected. */\r\n    Connected = \"Connected\",\r\n    /** The hub connection is disconnecting. */\r\n    Disconnecting = \"Disconnecting\",\r\n    /** The hub connection is reconnecting. */\r\n    Reconnecting = \"Reconnecting\",\r\n}\r\n\r\n/** Represents a connection to a SignalR Hub. */\r\nexport class HubConnection {\r\n    private readonly _cachedPingMessage: string | ArrayBuffer;\r\n    // Needs to not start with _ for tests\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private readonly connection: IConnection;\r\n    private readonly _logger: ILogger;\r\n    private readonly _reconnectPolicy?: IRetryPolicy;\r\n    private _protocol: IHubProtocol;\r\n    private _handshakeProtocol: HandshakeProtocol;\r\n    private _callbacks: { [invocationId: string]: (invocationEvent: StreamItemMessage | CompletionMessage | null, error?: Error) => void };\r\n    private _methods: { [name: string]: ((...args: any[]) => void)[] };\r\n    private _invocationId: number;\r\n\r\n    private _closedCallbacks: ((error?: Error) => void)[];\r\n    private _reconnectingCallbacks: ((error?: Error) => void)[];\r\n    private _reconnectedCallbacks: ((connectionId?: string) => void)[];\r\n\r\n    private _receivedHandshakeResponse: boolean;\r\n    private _handshakeResolver!: (value?: PromiseLike<{}>) => void;\r\n    private _handshakeRejecter!: (reason?: any) => void;\r\n    private _stopDuringStartError?: Error;\r\n\r\n    private _connectionState: HubConnectionState;\r\n    // connectionStarted is tracked independently from connectionState, so we can check if the\r\n    // connection ever did successfully transition from connecting to connected before disconnecting.\r\n    private _connectionStarted: boolean;\r\n    private _startPromise?: Promise<void>;\r\n    private _stopPromise?: Promise<void>;\r\n    private _nextKeepAlive: number = 0;\r\n\r\n    // The type of these a) doesn't matter and b) varies when building in browser and node contexts\r\n    // Since we're building the WebPack bundle directly from the TypeScript, this matters (previously\r\n    // we built the bundle from the compiled JavaScript).\r\n    private _reconnectDelayHandle?: any;\r\n    private _timeoutHandle?: any;\r\n    private _pingServerHandle?: any;\r\n\r\n    private _freezeEventListener = () =>\r\n    {\r\n        this._logger.log(LogLevel.Warning, \"The page is being frozen, this will likely lead to the connection being closed and messages being lost. For more information see the docs at https://docs.microsoft.com/aspnet/core/signalr/javascript-client#bsleep\");\r\n    };\r\n\r\n    /** The server timeout in milliseconds.\r\n     *\r\n     * If this timeout elapses without receiving any messages from the server, the connection will be terminated with an error.\r\n     * The default timeout value is 30,000 milliseconds (30 seconds).\r\n     */\r\n    public serverTimeoutInMilliseconds: number;\r\n\r\n    /** Default interval at which to ping the server.\r\n     *\r\n     * The default value is 15,000 milliseconds (15 seconds).\r\n     * Allows the server to detect hard disconnects (like when a client unplugs their computer).\r\n     * The ping will happen at most as often as the server pings.\r\n     * If the server pings every 5 seconds, a value lower than 5 will ping every 5 seconds.\r\n     */\r\n    public keepAliveIntervalInMilliseconds: number;\r\n\r\n    /** @internal */\r\n    // Using a public static factory method means we can have a private constructor and an _internal_\r\n    // create method that can be used by HubConnectionBuilder. An \"internal\" constructor would just\r\n    // be stripped away and the '.d.ts' file would have no constructor, which is interpreted as a\r\n    // public parameter-less constructor.\r\n    public static create(connection: IConnection, logger: ILogger, protocol: IHubProtocol, reconnectPolicy?: IRetryPolicy): HubConnection {\r\n        return new HubConnection(connection, logger, protocol, reconnectPolicy);\r\n    }\r\n\r\n    private constructor(connection: IConnection, logger: ILogger, protocol: IHubProtocol, reconnectPolicy?: IRetryPolicy) {\r\n        Arg.isRequired(connection, \"connection\");\r\n        Arg.isRequired(logger, \"logger\");\r\n        Arg.isRequired(protocol, \"protocol\");\r\n\r\n        this.serverTimeoutInMilliseconds = DEFAULT_TIMEOUT_IN_MS;\r\n        this.keepAliveIntervalInMilliseconds = DEFAULT_PING_INTERVAL_IN_MS;\r\n\r\n        this._logger = logger;\r\n        this._protocol = protocol;\r\n        this.connection = connection;\r\n        this._reconnectPolicy = reconnectPolicy;\r\n        this._handshakeProtocol = new HandshakeProtocol();\r\n\r\n        this.connection.onreceive = (data: any) => this._processIncomingData(data);\r\n        this.connection.onclose = (error?: Error) => this._connectionClosed(error);\r\n\r\n        this._callbacks = {};\r\n        this._methods = {};\r\n        this._closedCallbacks = [];\r\n        this._reconnectingCallbacks = [];\r\n        this._reconnectedCallbacks = [];\r\n        this._invocationId = 0;\r\n        this._receivedHandshakeResponse = false;\r\n        this._connectionState = HubConnectionState.Disconnected;\r\n        this._connectionStarted = false;\r\n\r\n        this._cachedPingMessage = this._protocol.writeMessage({ type: MessageType.Ping });\r\n    }\r\n\r\n    /** Indicates the state of the {@link HubConnection} to the server. */\r\n    get state(): HubConnectionState {\r\n        return this._connectionState;\r\n    }\r\n\r\n    /** Represents the connection id of the {@link HubConnection} on the server. The connection id will be null when the connection is either\r\n     *  in the disconnected state or if the negotiation step was skipped.\r\n     */\r\n    get connectionId(): string | null {\r\n        return this.connection ? (this.connection.connectionId || null) : null;\r\n    }\r\n\r\n    /** Indicates the url of the {@link HubConnection} to the server. */\r\n    get baseUrl(): string {\r\n        return this.connection.baseUrl || \"\";\r\n    }\r\n\r\n    /**\r\n     * Sets a new url for the HubConnection. Note that the url can only be changed when the connection is in either the Disconnected or\r\n     * Reconnecting states.\r\n     * @param {string} url The url to connect to.\r\n     */\r\n    set baseUrl(url: string) {\r\n        if (this._connectionState !== HubConnectionState.Disconnected && this._connectionState !== HubConnectionState.Reconnecting) {\r\n            throw new Error(\"The HubConnection must be in the Disconnected or Reconnecting state to change the url.\");\r\n        }\r\n\r\n        if (!url) {\r\n            throw new Error(\"The HubConnection url must be a valid url.\");\r\n        }\r\n\r\n        this.connection.baseUrl = url;\r\n    }\r\n\r\n    /** Starts the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully established, or rejects with an error.\r\n     */\r\n    public start(): Promise<void> {\r\n        this._startPromise = this._startWithStateTransitions();\r\n        return this._startPromise;\r\n    }\r\n\r\n    private async _startWithStateTransitions(): Promise<void> {\r\n        if (this._connectionState !== HubConnectionState.Disconnected) {\r\n            return Promise.reject(new Error(\"Cannot start a HubConnection that is not in the 'Disconnected' state.\"));\r\n        }\r\n\r\n        this._connectionState = HubConnectionState.Connecting;\r\n        this._logger.log(LogLevel.Debug, \"Starting HubConnection.\");\r\n\r\n        try {\r\n            await this._startInternal();\r\n\r\n            if (Platform.isBrowser) {\r\n                // Log when the browser freezes the tab so users know why their connection unexpectedly stopped working\r\n                window.document.addEventListener(\"freeze\", this._freezeEventListener);\r\n            }\r\n\r\n            this._connectionState = HubConnectionState.Connected;\r\n            this._connectionStarted = true;\r\n            this._logger.log(LogLevel.Debug, \"HubConnection connected successfully.\");\r\n        } catch (e) {\r\n            this._connectionState = HubConnectionState.Disconnected;\r\n            this._logger.log(LogLevel.Debug, `HubConnection failed to start successfully because of error '${e}'.`);\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n\r\n    private async _startInternal() {\r\n        this._stopDuringStartError = undefined;\r\n        this._receivedHandshakeResponse = false;\r\n        // Set up the promise before any connection is (re)started otherwise it could race with received messages\r\n        const handshakePromise = new Promise((resolve, reject) => {\r\n            this._handshakeResolver = resolve;\r\n            this._handshakeRejecter = reject;\r\n        });\r\n\r\n        await this.connection.start(this._protocol.transferFormat);\r\n\r\n        try {\r\n            const handshakeRequest: HandshakeRequestMessage = {\r\n                protocol: this._protocol.name,\r\n                version: this._protocol.version,\r\n            };\r\n\r\n            this._logger.log(LogLevel.Debug, \"Sending handshake request.\");\r\n\r\n            await this._sendMessage(this._handshakeProtocol.writeHandshakeRequest(handshakeRequest));\r\n\r\n            this._logger.log(LogLevel.Information, `Using HubProtocol '${this._protocol.name}'.`);\r\n\r\n            // defensively cleanup timeout in case we receive a message from the server before we finish start\r\n            this._cleanupTimeout();\r\n            this._resetTimeoutPeriod();\r\n            this._resetKeepAliveInterval();\r\n\r\n            await handshakePromise;\r\n\r\n            // It's important to check the stopDuringStartError instead of just relying on the handshakePromise\r\n            // being rejected on close, because this continuation can run after both the handshake completed successfully\r\n            // and the connection was closed.\r\n            if (this._stopDuringStartError) {\r\n                // It's important to throw instead of returning a rejected promise, because we don't want to allow any state\r\n                // transitions to occur between now and the calling code observing the exceptions. Returning a rejected promise\r\n                // will cause the calling continuation to get scheduled to run later.\r\n                // eslint-disable-next-line @typescript-eslint/no-throw-literal\r\n                throw this._stopDuringStartError;\r\n            }\r\n        } catch (e) {\r\n            this._logger.log(LogLevel.Debug, `Hub handshake failed with error '${e}' during start(). Stopping HubConnection.`);\r\n\r\n            this._cleanupTimeout();\r\n            this._cleanupPingTimer();\r\n\r\n            // HttpConnection.stop() should not complete until after the onclose callback is invoked.\r\n            // This will transition the HubConnection to the disconnected state before HttpConnection.stop() completes.\r\n            await this.connection.stop(e);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /** Stops the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully terminated, or rejects with an error.\r\n     */\r\n    public async stop(): Promise<void> {\r\n        // Capture the start promise before the connection might be restarted in an onclose callback.\r\n        const startPromise = this._startPromise;\r\n\r\n        this._stopPromise = this._stopInternal();\r\n        await this._stopPromise;\r\n\r\n        try {\r\n            // Awaiting undefined continues immediately\r\n            await startPromise;\r\n        } catch (e) {\r\n            // This exception is returned to the user as a rejected Promise from the start method.\r\n        }\r\n    }\r\n\r\n    private _stopInternal(error?: Error): Promise<void> {\r\n        if (this._connectionState === HubConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HubConnection.stop(${error}) ignored because it is already in the disconnected state.`);\r\n            return Promise.resolve();\r\n        }\r\n\r\n        if (this._connectionState === HubConnectionState.Disconnecting) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\r\n            return this._stopPromise!;\r\n        }\r\n\r\n        this._connectionState = HubConnectionState.Disconnecting;\r\n\r\n        this._logger.log(LogLevel.Debug, \"Stopping HubConnection.\");\r\n\r\n        if (this._reconnectDelayHandle) {\r\n            // We're in a reconnect delay which means the underlying connection is currently already stopped.\r\n            // Just clear the handle to stop the reconnect loop (which no one is waiting on thankfully) and\r\n            // fire the onclose callbacks.\r\n            this._logger.log(LogLevel.Debug, \"Connection stopped during reconnect delay. Done reconnecting.\");\r\n\r\n            clearTimeout(this._reconnectDelayHandle);\r\n            this._reconnectDelayHandle = undefined;\r\n\r\n            this._completeClose();\r\n            return Promise.resolve();\r\n        }\r\n\r\n        this._cleanupTimeout();\r\n        this._cleanupPingTimer();\r\n        this._stopDuringStartError = error || new Error(\"The connection was stopped before the hub handshake could complete.\");\r\n\r\n        // HttpConnection.stop() should not complete until after either HttpConnection.start() fails\r\n        // or the onclose callback is invoked. The onclose callback will transition the HubConnection\r\n        // to the disconnected state if need be before HttpConnection.stop() completes.\r\n        return this.connection.stop(error);\r\n    }\r\n\r\n    /** Invokes a streaming hub method on the server using the specified name and arguments.\r\n     *\r\n     * @typeparam T The type of the items returned by the server.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {IStreamResult<T>} An object that yields results from the server as they are received.\r\n     */\r\n    public stream<T = any>(methodName: string, ...args: any[]): IStreamResult<T> {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const invocationDescriptor = this._createStreamInvocation(methodName, args, streamIds);\r\n\r\n        // eslint-disable-next-line prefer-const\r\n        let promiseQueue: Promise<void>;\r\n\r\n        const subject = new Subject<T>();\r\n        subject.cancelCallback = () => {\r\n            const cancelInvocation: CancelInvocationMessage = this._createCancelInvocation(invocationDescriptor.invocationId);\r\n\r\n            delete this._callbacks[invocationDescriptor.invocationId];\r\n\r\n            return promiseQueue.then(() => {\r\n                return this._sendWithProtocol(cancelInvocation);\r\n            });\r\n        };\r\n\r\n        this._callbacks[invocationDescriptor.invocationId] = (invocationEvent: CompletionMessage | StreamItemMessage | null, error?: Error) => {\r\n            if (error) {\r\n                subject.error(error);\r\n                return;\r\n            } else if (invocationEvent) {\r\n                // invocationEvent will not be null when an error is not passed to the callback\r\n                if (invocationEvent.type === MessageType.Completion) {\r\n                    if (invocationEvent.error) {\r\n                        subject.error(new Error(invocationEvent.error));\r\n                    } else {\r\n                        subject.complete();\r\n                    }\r\n                } else {\r\n                    subject.next((invocationEvent.item) as T);\r\n                }\r\n            }\r\n        };\r\n\r\n        promiseQueue = this._sendWithProtocol(invocationDescriptor)\r\n            .catch((e) => {\r\n                subject.error(e);\r\n                delete this._callbacks[invocationDescriptor.invocationId];\r\n            });\r\n\r\n        this._launchStreams(streams, promiseQueue);\r\n\r\n        return subject;\r\n    }\r\n\r\n    private _sendMessage(message: any) {\r\n        this._resetKeepAliveInterval();\r\n        return this.connection.send(message);\r\n    }\r\n\r\n    /**\r\n     * Sends a js object to the server.\r\n     * @param message The js object to serialize and send.\r\n     */\r\n    private _sendWithProtocol(message: any) {\r\n        return this._sendMessage(this._protocol.writeMessage(message));\r\n    }\r\n\r\n    /** Invokes a hub method on the server using the specified name and arguments. Does not wait for a response from the receiver.\r\n     *\r\n     * The Promise returned by this method resolves when the client has sent the invocation to the server. The server may still\r\n     * be processing the invocation.\r\n     *\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<void>} A Promise that resolves when the invocation has been successfully sent, or rejects with an error.\r\n     */\r\n    public send(methodName: string, ...args: any[]): Promise<void> {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const sendPromise = this._sendWithProtocol(this._createInvocation(methodName, args, true, streamIds));\r\n\r\n        this._launchStreams(streams, sendPromise);\r\n\r\n        return sendPromise;\r\n    }\r\n\r\n    /** Invokes a hub method on the server using the specified name and arguments.\r\n     *\r\n     * The Promise returned by this method resolves when the server indicates it has finished invoking the method. When the promise\r\n     * resolves, the server has finished invoking the method. If the server method returns a result, it is produced as the result of\r\n     * resolving the Promise.\r\n     *\r\n     * @typeparam T The expected return type.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<T>} A Promise that resolves with the result of the server method (if any), or rejects with an error.\r\n     */\r\n    public invoke<T = any>(methodName: string, ...args: any[]): Promise<T> {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const invocationDescriptor = this._createInvocation(methodName, args, false, streamIds);\r\n\r\n        const p = new Promise<any>((resolve, reject) => {\r\n            // invocationId will always have a value for a non-blocking invocation\r\n            this._callbacks[invocationDescriptor.invocationId!] = (invocationEvent: StreamItemMessage | CompletionMessage | null, error?: Error) => {\r\n                if (error) {\r\n                    reject(error);\r\n                    return;\r\n                } else if (invocationEvent) {\r\n                    // invocationEvent will not be null when an error is not passed to the callback\r\n                    if (invocationEvent.type === MessageType.Completion) {\r\n                        if (invocationEvent.error) {\r\n                            reject(new Error(invocationEvent.error));\r\n                        } else {\r\n                            resolve(invocationEvent.result);\r\n                        }\r\n                    } else {\r\n                        reject(new Error(`Unexpected message type: ${invocationEvent.type}`));\r\n                    }\r\n                }\r\n            };\r\n\r\n            const promiseQueue = this._sendWithProtocol(invocationDescriptor)\r\n                .catch((e) => {\r\n                    reject(e);\r\n                    // invocationId will always have a value for a non-blocking invocation\r\n                    delete this._callbacks[invocationDescriptor.invocationId!];\r\n                });\r\n\r\n            this._launchStreams(streams, promiseQueue);\r\n        });\r\n\r\n        return p;\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the hub method with the specified method name is invoked.\r\n     *\r\n     * @param {string} methodName The name of the hub method to define.\r\n     * @param {Function} newMethod The handler that will be raised when the hub method is invoked.\r\n     */\r\n    public on(methodName: string, newMethod: (...args: any[]) => void): void {\r\n        if (!methodName || !newMethod) {\r\n            return;\r\n        }\r\n\r\n        methodName = methodName.toLowerCase();\r\n        if (!this._methods[methodName]) {\r\n            this._methods[methodName] = [];\r\n        }\r\n\r\n        // Preventing adding the same handler multiple times.\r\n        if (this._methods[methodName].indexOf(newMethod) !== -1) {\r\n            return;\r\n        }\r\n\r\n        this._methods[methodName].push(newMethod);\r\n    }\r\n\r\n    /** Removes all handlers for the specified hub method.\r\n     *\r\n     * @param {string} methodName The name of the method to remove handlers for.\r\n     */\r\n    public off(methodName: string): void;\r\n\r\n    /** Removes the specified handler for the specified hub method.\r\n     *\r\n     * You must pass the exact same Function instance as was previously passed to {@link @microsoft/signalr.HubConnection.on}. Passing a different instance (even if the function\r\n     * body is the same) will not remove the handler.\r\n     *\r\n     * @param {string} methodName The name of the method to remove handlers for.\r\n     * @param {Function} method The handler to remove. This must be the same Function instance as the one passed to {@link @microsoft/signalr.HubConnection.on}.\r\n     */\r\n    public off(methodName: string, method: (...args: any[]) => void): void;\r\n    public off(methodName: string, method?: (...args: any[]) => void): void {\r\n        if (!methodName) {\r\n            return;\r\n        }\r\n\r\n        methodName = methodName.toLowerCase();\r\n        const handlers = this._methods[methodName];\r\n        if (!handlers) {\r\n            return;\r\n        }\r\n        if (method) {\r\n            const removeIdx = handlers.indexOf(method);\r\n            if (removeIdx !== -1) {\r\n                handlers.splice(removeIdx, 1);\r\n                if (handlers.length === 0) {\r\n                    delete this._methods[methodName];\r\n                }\r\n            }\r\n        } else {\r\n            delete this._methods[methodName];\r\n        }\r\n\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the connection is closed.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection is closed. Optionally receives a single argument containing the error that caused the connection to close (if any).\r\n     */\r\n    public onclose(callback: (error?: Error) => void): void {\r\n        if (callback) {\r\n            this._closedCallbacks.push(callback);\r\n        }\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the connection starts reconnecting.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection starts reconnecting. Optionally receives a single argument containing the error that caused the connection to start reconnecting (if any).\r\n     */\r\n    public onreconnecting(callback: (error?: Error) => void): void {\r\n        if (callback) {\r\n            this._reconnectingCallbacks.push(callback);\r\n        }\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the connection successfully reconnects.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection successfully reconnects.\r\n     */\r\n    public onreconnected(callback: (connectionId?: string) => void): void {\r\n        if (callback) {\r\n            this._reconnectedCallbacks.push(callback);\r\n        }\r\n    }\r\n\r\n    private _processIncomingData(data: any) {\r\n        this._cleanupTimeout();\r\n\r\n        if (!this._receivedHandshakeResponse) {\r\n            data = this._processHandshakeResponse(data);\r\n            this._receivedHandshakeResponse = true;\r\n        }\r\n\r\n        // Data may have all been read when processing handshake response\r\n        if (data) {\r\n            // Parse the messages\r\n            const messages = this._protocol.parseMessages(data, this._logger);\r\n\r\n            for (const message of messages) {\r\n                switch (message.type) {\r\n                    case MessageType.Invocation:\r\n                        this._invokeClientMethod(message);\r\n                        break;\r\n                    case MessageType.StreamItem:\r\n                    case MessageType.Completion: {\r\n                        const callback = this._callbacks[message.invocationId];\r\n                        if (callback) {\r\n                            if (message.type === MessageType.Completion) {\r\n                                delete this._callbacks[message.invocationId];\r\n                            }\r\n                            try {\r\n                                callback(message);\r\n                            } catch (e) {\r\n                                this._logger.log(LogLevel.Error, `Stream callback threw error: ${getErrorString(e)}`);\r\n                            }\r\n                        }\r\n                        break;\r\n                    }\r\n                    case MessageType.Ping:\r\n                        // Don't care about pings\r\n                        break;\r\n                    case MessageType.Close: {\r\n                        this._logger.log(LogLevel.Information, \"Close message received from server.\");\r\n\r\n                        const error = message.error ? new Error(\"Server returned an error on close: \" + message.error) : undefined;\r\n\r\n                        if (message.allowReconnect === true) {\r\n                            // It feels wrong not to await connection.stop() here, but processIncomingData is called as part of an onreceive callback which is not async,\r\n                            // this is already the behavior for serverTimeout(), and HttpConnection.Stop() should catch and log all possible exceptions.\r\n\r\n                            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n                            this.connection.stop(error);\r\n                        } else {\r\n                            // We cannot await stopInternal() here, but subsequent calls to stop() will await this if stopInternal() is still ongoing.\r\n                            this._stopPromise = this._stopInternal(error);\r\n                        }\r\n\r\n                        break;\r\n                    }\r\n                    default:\r\n                        this._logger.log(LogLevel.Warning, `Invalid message type: ${message.type}.`);\r\n                        break;\r\n                }\r\n            }\r\n        }\r\n\r\n        this._resetTimeoutPeriod();\r\n    }\r\n\r\n    private _processHandshakeResponse(data: any): any {\r\n        let responseMessage: HandshakeResponseMessage;\r\n        let remainingData: any;\r\n\r\n        try {\r\n            [remainingData, responseMessage] = this._handshakeProtocol.parseHandshakeResponse(data);\r\n        } catch (e) {\r\n            const message = \"Error parsing handshake response: \" + e;\r\n            this._logger.log(LogLevel.Error, message);\r\n\r\n            const error = new Error(message);\r\n            this._handshakeRejecter(error);\r\n            throw error;\r\n        }\r\n        if (responseMessage.error) {\r\n            const message = \"Server returned handshake error: \" + responseMessage.error;\r\n            this._logger.log(LogLevel.Error, message);\r\n\r\n            const error = new Error(message);\r\n            this._handshakeRejecter(error);\r\n            throw error;\r\n        } else {\r\n            this._logger.log(LogLevel.Debug, \"Server handshake complete.\");\r\n        }\r\n\r\n        this._handshakeResolver();\r\n        return remainingData;\r\n    }\r\n\r\n    private _resetKeepAliveInterval() {\r\n        if (this.connection.features.inherentKeepAlive) {\r\n            return;\r\n        }\r\n\r\n        // Set the time we want the next keep alive to be sent\r\n        // Timer will be setup on next message receive\r\n        this._nextKeepAlive = new Date().getTime() + this.keepAliveIntervalInMilliseconds;\r\n\r\n        this._cleanupPingTimer();\r\n    }\r\n\r\n    private _resetTimeoutPeriod() {\r\n        if (!this.connection.features || !this.connection.features.inherentKeepAlive) {\r\n            // Set the timeout timer\r\n            this._timeoutHandle = setTimeout(() => this.serverTimeout(), this.serverTimeoutInMilliseconds);\r\n\r\n            // Set keepAlive timer if there isn't one\r\n            if (this._pingServerHandle === undefined)\r\n            {\r\n                let nextPing = this._nextKeepAlive - new Date().getTime();\r\n                if (nextPing < 0) {\r\n                    nextPing = 0;\r\n                }\r\n\r\n                // The timer needs to be set from a networking callback to avoid Chrome timer throttling from causing timers to run once a minute\r\n                this._pingServerHandle = setTimeout(async () => {\r\n                    if (this._connectionState === HubConnectionState.Connected) {\r\n                        try {\r\n                            await this._sendMessage(this._cachedPingMessage);\r\n                        } catch {\r\n                            // We don't care about the error. It should be seen elsewhere in the client.\r\n                            // The connection is probably in a bad or closed state now, cleanup the timer so it stops triggering\r\n                            this._cleanupPingTimer();\r\n                        }\r\n                    }\r\n                }, nextPing);\r\n            }\r\n        }\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private serverTimeout() {\r\n        // The server hasn't talked to us in a while. It doesn't like us anymore ... :(\r\n        // Terminate the connection, but we don't need to wait on the promise. This could trigger reconnecting.\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this.connection.stop(new Error(\"Server timeout elapsed without receiving a message from the server.\"));\r\n    }\r\n\r\n    private _invokeClientMethod(invocationMessage: InvocationMessage) {\r\n        const methods = this._methods[invocationMessage.target.toLowerCase()];\r\n        if (methods) {\r\n            try {\r\n                methods.forEach((m) => m.apply(this, invocationMessage.arguments));\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `A callback for the method ${invocationMessage.target.toLowerCase()} threw error '${e}'.`);\r\n            }\r\n\r\n            if (invocationMessage.invocationId) {\r\n                // This is not supported in v1. So we return an error to avoid blocking the server waiting for the response.\r\n                const message = \"Server requested a response, which is not supported in this version of the client.\";\r\n                this._logger.log(LogLevel.Error, message);\r\n\r\n                // We don't want to wait on the stop itself.\r\n                this._stopPromise = this._stopInternal(new Error(message));\r\n            }\r\n        } else {\r\n            this._logger.log(LogLevel.Warning, `No client method with the name '${invocationMessage.target}' found.`);\r\n        }\r\n    }\r\n\r\n    private _connectionClosed(error?: Error) {\r\n        this._logger.log(LogLevel.Debug, `HubConnection.connectionClosed(${error}) called while in state ${this._connectionState}.`);\r\n\r\n        // Triggering this.handshakeRejecter is insufficient because it could already be resolved without the continuation having run yet.\r\n        this._stopDuringStartError = this._stopDuringStartError || error || new Error(\"The underlying connection was closed before the hub handshake could complete.\");\r\n\r\n        // If the handshake is in progress, start will be waiting for the handshake promise, so we complete it.\r\n        // If it has already completed, this should just noop.\r\n        if (this._handshakeResolver) {\r\n            this._handshakeResolver();\r\n        }\r\n\r\n        this._cancelCallbacksWithError(error || new Error(\"Invocation canceled due to the underlying connection being closed.\"));\r\n\r\n        this._cleanupTimeout();\r\n        this._cleanupPingTimer();\r\n\r\n        if (this._connectionState === HubConnectionState.Disconnecting) {\r\n            this._completeClose(error);\r\n        } else if (this._connectionState === HubConnectionState.Connected && this._reconnectPolicy) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._reconnect(error);\r\n        } else if (this._connectionState === HubConnectionState.Connected) {\r\n            this._completeClose(error);\r\n        }\r\n\r\n        // If none of the above if conditions were true were called the HubConnection must be in either:\r\n        // 1. The Connecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail it.\r\n        // 2. The Reconnecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail the current reconnect attempt\r\n        //    and potentially continue the reconnect() loop.\r\n        // 3. The Disconnected state in which case we're already done.\r\n    }\r\n\r\n    private _completeClose(error?: Error) {\r\n        if (this._connectionStarted) {\r\n            this._connectionState = HubConnectionState.Disconnected;\r\n            this._connectionStarted = false;\r\n\r\n            if (Platform.isBrowser) {\r\n                window.document.removeEventListener(\"freeze\", this._freezeEventListener);\r\n            }\r\n\r\n            try {\r\n                this._closedCallbacks.forEach((c) => c.apply(this, [error]));\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `An onclose callback called with error '${error}' threw error '${e}'.`);\r\n            }\r\n        }\r\n    }\r\n\r\n    private async _reconnect(error?: Error) {\r\n        const reconnectStartTime = Date.now();\r\n        let previousReconnectAttempts = 0;\r\n        let retryError = error !== undefined ? error : new Error(\"Attempting to reconnect due to a unknown error.\");\r\n\r\n        let nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, 0, retryError);\r\n\r\n        if (nextRetryDelay === null) {\r\n            this._logger.log(LogLevel.Debug, \"Connection not reconnecting because the IRetryPolicy returned null on the first reconnect attempt.\");\r\n            this._completeClose(error);\r\n            return;\r\n        }\r\n\r\n        this._connectionState = HubConnectionState.Reconnecting;\r\n\r\n        if (error) {\r\n            this._logger.log(LogLevel.Information, `Connection reconnecting because of error '${error}'.`);\r\n        } else {\r\n            this._logger.log(LogLevel.Information, \"Connection reconnecting.\");\r\n        }\r\n\r\n        if (this._reconnectingCallbacks.length !== 0) {\r\n            try {\r\n                this._reconnectingCallbacks.forEach((c) => c.apply(this, [error]));\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `An onreconnecting callback called with error '${error}' threw error '${e}'.`);\r\n            }\r\n\r\n            // Exit early if an onreconnecting callback called connection.stop().\r\n            if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                this._logger.log(LogLevel.Debug, \"Connection left the reconnecting state in onreconnecting callback. Done reconnecting.\");\r\n                return;\r\n            }\r\n        }\r\n\r\n        while (nextRetryDelay !== null) {\r\n            this._logger.log(LogLevel.Information, `Reconnect attempt number ${previousReconnectAttempts} will start in ${nextRetryDelay} ms.`);\r\n\r\n            await new Promise((resolve) => {\r\n                this._reconnectDelayHandle = setTimeout(resolve, nextRetryDelay!);\r\n            });\r\n            this._reconnectDelayHandle = undefined;\r\n\r\n            if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                this._logger.log(LogLevel.Debug, \"Connection left the reconnecting state during reconnect delay. Done reconnecting.\");\r\n                return;\r\n            }\r\n\r\n            try {\r\n                await this._startInternal();\r\n\r\n                this._connectionState = HubConnectionState.Connected;\r\n                this._logger.log(LogLevel.Information, \"HubConnection reconnected successfully.\");\r\n\r\n                if (this._reconnectedCallbacks.length !== 0) {\r\n                    try {\r\n                        this._reconnectedCallbacks.forEach((c) => c.apply(this, [this.connection.connectionId]));\r\n                    } catch (e) {\r\n                        this._logger.log(LogLevel.Error, `An onreconnected callback called with connectionId '${this.connection.connectionId}; threw error '${e}'.`);\r\n                    }\r\n                }\r\n\r\n                return;\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Information, `Reconnect attempt failed because of error '${e}'.`);\r\n\r\n                if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                    this._logger.log(LogLevel.Debug, `Connection moved to the '${this._connectionState}' from the reconnecting state during reconnect attempt. Done reconnecting.`);\r\n                    // The TypeScript compiler thinks that connectionState must be Connected here. The TypeScript compiler is wrong.\r\n                    if (this._connectionState as any === HubConnectionState.Disconnecting) {\r\n                        this._completeClose();\r\n                    }\r\n                    return;\r\n                }\r\n\r\n                retryError = e instanceof Error ? e : new Error(e.toString());\r\n                nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, Date.now() - reconnectStartTime, retryError);\r\n            }\r\n        }\r\n\r\n        this._logger.log(LogLevel.Information, `Reconnect retries have been exhausted after ${Date.now() - reconnectStartTime} ms and ${previousReconnectAttempts} failed attempts. Connection disconnecting.`);\r\n\r\n        this._completeClose();\r\n    }\r\n\r\n    private _getNextRetryDelay(previousRetryCount: number, elapsedMilliseconds: number, retryReason: Error) {\r\n        try {\r\n            return this._reconnectPolicy!.nextRetryDelayInMilliseconds({\r\n                elapsedMilliseconds,\r\n                previousRetryCount,\r\n                retryReason,\r\n            });\r\n        } catch (e) {\r\n            this._logger.log(LogLevel.Error, `IRetryPolicy.nextRetryDelayInMilliseconds(${previousRetryCount}, ${elapsedMilliseconds}) threw error '${e}'.`);\r\n            return null;\r\n        }\r\n    }\r\n\r\n    private _cancelCallbacksWithError(error: Error) {\r\n        const callbacks = this._callbacks;\r\n        this._callbacks = {};\r\n\r\n        Object.keys(callbacks)\r\n            .forEach((key) => {\r\n                const callback = callbacks[key];\r\n                try {\r\n                    callback(null, error);\r\n                } catch (e) {\r\n                    this._logger.log(LogLevel.Error, `Stream 'error' callback called with '${error}' threw error: ${getErrorString(e)}`);\r\n                }\r\n            });\r\n    }\r\n\r\n    private _cleanupPingTimer(): void {\r\n        if (this._pingServerHandle) {\r\n            clearTimeout(this._pingServerHandle);\r\n            this._pingServerHandle = undefined;\r\n        }\r\n    }\r\n\r\n    private _cleanupTimeout(): void {\r\n        if (this._timeoutHandle) {\r\n            clearTimeout(this._timeoutHandle);\r\n        }\r\n    }\r\n\r\n    private _createInvocation(methodName: string, args: any[], nonblocking: boolean, streamIds: string[]): InvocationMessage {\r\n        if (nonblocking) {\r\n            if (streamIds.length !== 0) {\r\n                return {\r\n                    arguments: args,\r\n                    streamIds,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            } else {\r\n                return {\r\n                    arguments: args,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n        } else {\r\n            const invocationId = this._invocationId;\r\n            this._invocationId++;\r\n\r\n            if (streamIds.length !== 0) {\r\n                return {\r\n                    arguments: args,\r\n                    invocationId: invocationId.toString(),\r\n                    streamIds,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            } else {\r\n                return {\r\n                    arguments: args,\r\n                    invocationId: invocationId.toString(),\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n        }\r\n    }\r\n\r\n    private _launchStreams(streams: IStreamResult<any>[], promiseQueue: Promise<void>): void {\r\n        if (streams.length === 0) {\r\n            return;\r\n        }\r\n\r\n        // Synchronize stream data so they arrive in-order on the server\r\n        if (!promiseQueue) {\r\n            promiseQueue = Promise.resolve();\r\n        }\r\n\r\n        // We want to iterate over the keys, since the keys are the stream ids\r\n        // eslint-disable-next-line guard-for-in\r\n        for (const streamId in streams) {\r\n            streams[streamId].subscribe({\r\n                complete: () => {\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId)));\r\n                },\r\n                error: (err) => {\r\n                    let message: string;\r\n                    if (err instanceof Error) {\r\n                        message = err.message;\r\n                    } else if (err && err.toString) {\r\n                        message = err.toString();\r\n                    } else {\r\n                        message = \"Unknown error\";\r\n                    }\r\n\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId, message)));\r\n                },\r\n                next: (item) => {\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createStreamItemMessage(streamId, item)));\r\n                },\r\n            });\r\n        }\r\n    }\r\n\r\n    private _replaceStreamingParams(args: any[]): [IStreamResult<any>[], string[]] {\r\n        const streams: IStreamResult<any>[] = [];\r\n        const streamIds: string[] = [];\r\n        for (let i = 0; i < args.length; i++) {\r\n            const argument = args[i];\r\n            if (this._isObservable(argument)) {\r\n                const streamId = this._invocationId;\r\n                this._invocationId++;\r\n                // Store the stream for later use\r\n                streams[streamId] = argument;\r\n                streamIds.push(streamId.toString());\r\n\r\n                // remove stream from args\r\n                args.splice(i, 1);\r\n            }\r\n        }\r\n\r\n        return [streams, streamIds];\r\n    }\r\n\r\n    private _isObservable(arg: any): arg is IStreamResult<any> {\r\n        // This allows other stream implementations to just work (like rxjs)\r\n        return arg && arg.subscribe && typeof arg.subscribe === \"function\";\r\n    }\r\n\r\n    private _createStreamInvocation(methodName: string, args: any[], streamIds: string[]): StreamInvocationMessage {\r\n        const invocationId = this._invocationId;\r\n        this._invocationId++;\r\n\r\n        if (streamIds.length !== 0) {\r\n            return {\r\n                arguments: args,\r\n                invocationId: invocationId.toString(),\r\n                streamIds,\r\n                target: methodName,\r\n                type: MessageType.StreamInvocation,\r\n            };\r\n        } else {\r\n            return {\r\n                arguments: args,\r\n                invocationId: invocationId.toString(),\r\n                target: methodName,\r\n                type: MessageType.StreamInvocation,\r\n            };\r\n        }\r\n    }\r\n\r\n    private _createCancelInvocation(id: string): CancelInvocationMessage {\r\n        return {\r\n            invocationId: id,\r\n            type: MessageType.CancelInvocation,\r\n        };\r\n    }\r\n\r\n    private _createStreamItemMessage(id: string, item: any): StreamItemMessage {\r\n        return {\r\n            invocationId: id,\r\n            item,\r\n            type: MessageType.StreamItem,\r\n        };\r\n    }\r\n\r\n    private _createCompletionMessage(id: string, error?: any, result?: any): CompletionMessage {\r\n        if (error) {\r\n            return {\r\n                error,\r\n                invocationId: id,\r\n                type: MessageType.Completion,\r\n            };\r\n        }\r\n\r\n        return {\r\n            invocationId: id,\r\n            result,\r\n            type: MessageType.Completion,\r\n        };\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { IRetryPolicy, RetryContext } from \"./IRetryPolicy\";\r\n\r\n// 0, 2, 10, 30 second delays before reconnect attempts.\r\nconst DEFAULT_RETRY_DELAYS_IN_MILLISECONDS = [0, 2000, 10000, 30000, null];\r\n\r\n/** @private */\r\nexport class DefaultReconnectPolicy implements IRetryPolicy {\r\n    private readonly _retryDelays: (number | null)[];\r\n\r\n    constructor(retryDelays?: number[]) {\r\n        this._retryDelays = retryDelays !== undefined ? [...retryDelays, null] : DEFAULT_RETRY_DELAYS_IN_MILLISECONDS;\r\n    }\r\n\r\n    public nextRetryDelayInMilliseconds(retryContext: RetryContext): number | null {\r\n        return this._retryDelays[retryContext.previousRetryCount];\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nexport abstract class HeaderNames {\r\n    static readonly Authorization = \"Authorization\";\r\n    static readonly Cookie = \"<PERSON>ie\";\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// This will be treated as a bit flag in the future, so we keep it using power-of-two values.\r\n/** Specifies a specific HTTP transport type. */\r\nexport enum HttpTransportType {\r\n    /** Specifies no transport preference. */\r\n    None = 0,\r\n    /** Specifies the WebSockets transport. */\r\n    WebSockets = 1,\r\n    /** Specifies the Server-Sent Events transport. */\r\n    ServerSentEvents = 2,\r\n    /** Specifies the Long Polling transport. */\r\n    LongPolling = 4,\r\n}\r\n\r\n/** Specifies the transfer format for a connection. */\r\nexport enum TransferFormat {\r\n    /** Specifies that only text data will be transmitted over the connection. */\r\n    Text = 1,\r\n    /** Specifies that binary data will be transmitted over the connection. */\r\n    Binary = 2,\r\n}\r\n\r\n/** An abstraction over the behavior of transports. This is designed to support the framework and not intended for use by applications. */\r\nexport interface ITransport {\r\n    connect(url: string, transferFormat: TransferFormat): Promise<void>;\r\n    send(data: any): Promise<void>;\r\n    stop(): Promise<void>;\r\n    onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    onclose: ((error?: Error) => void) | null;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Rough polyfill of https://developer.mozilla.org/en-US/docs/Web/API/AbortController\r\n// We don't actually ever use the API being polyfilled, we always use the polyfill because\r\n// it's a very new API right now.\r\n\r\n// Not exported from index.\r\n/** @private */\r\nexport class AbortController implements AbortSignal {\r\n    private _isAborted: boolean = false;\r\n    public onabort: (() => void) | null = null;\r\n\r\n    public abort(): void {\r\n        if (!this._isAborted) {\r\n            this._isAborted = true;\r\n            if (this.onabort) {\r\n                this.onabort();\r\n            }\r\n        }\r\n    }\r\n\r\n    get signal(): AbortSignal {\r\n        return this;\r\n    }\r\n\r\n    get aborted(): boolean {\r\n        return this._isAborted;\r\n    }\r\n}\r\n\r\n/** Represents a signal that can be monitored to determine if a request has been aborted. */\r\nexport interface AbortSignal {\r\n    /** Indicates if the request has been aborted. */\r\n    aborted: boolean;\r\n    /** Set this to a handler that will be invoked when the request is aborted. */\r\n    onabort: (() => void) | null;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortController } from \"./AbortController\";\r\nimport { HttpError, TimeoutError } from \"./Errors\";\r\nimport { HeaderNames } from \"./HeaderNames\";\r\nimport { HttpClient, HttpRequest } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { ITransport, TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, sendMessage } from \"./Utils\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\n\r\n// Not exported from 'index', this type is internal.\r\n/** @private */\r\nexport class LongPollingTransport implements ITransport {\r\n    private readonly _httpClient: HttpClient;\r\n    private readonly _accessTokenFactory: (() => string | Promise<string>) | undefined;\r\n    private readonly _logger: ILogger;\r\n    private readonly _options: IHttpConnectionOptions;\r\n    private readonly _pollAbort: AbortController;\r\n\r\n    private _url?: string;\r\n    private _running: boolean;\r\n    private _receiving?: Promise<void>;\r\n    private _closeError?: Error;\r\n\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((error?: Error) => void) | null;\r\n\r\n    // This is an internal type, not exported from 'index' so this is really just internal.\r\n    public get pollAborted(): boolean {\r\n        return this._pollAbort.aborted;\r\n    }\r\n\r\n    constructor(httpClient: HttpClient, accessTokenFactory: (() => string | Promise<string>) | undefined, logger: ILogger, options: IHttpConnectionOptions) {\r\n        this._httpClient = httpClient;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n        this._logger = logger;\r\n        this._pollAbort = new AbortController();\r\n        this._options = options;\r\n\r\n        this._running = false;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n\r\n    public async connect(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n\r\n        this._url = url;\r\n\r\n        this._logger.log(LogLevel.Trace, \"(LongPolling transport) Connecting.\");\r\n\r\n        // Allow binary format on Node and Browsers that support binary content (indicated by the presence of responseType property)\r\n        if (transferFormat === TransferFormat.Binary &&\r\n            (typeof XMLHttpRequest !== \"undefined\" && typeof new XMLHttpRequest().responseType !== \"string\")) {\r\n            throw new Error(\"Binary protocols over XmlHttpRequest not implementing advanced features are not supported.\");\r\n        }\r\n\r\n        const [name, value] = getUserAgentHeader();\r\n        const headers = { [name]: value, ...this._options.headers };\r\n\r\n        const pollOptions: HttpRequest = {\r\n            abortSignal: this._pollAbort.signal,\r\n            headers,\r\n            timeout: 100000,\r\n            withCredentials: this._options.withCredentials,\r\n        };\r\n\r\n        if (transferFormat === TransferFormat.Binary) {\r\n            pollOptions.responseType = \"arraybuffer\";\r\n        }\r\n\r\n        const token = await this._getAccessToken();\r\n        this._updateHeaderToken(pollOptions, token);\r\n\r\n        // Make initial long polling request\r\n        // Server uses first long polling request to finish initializing connection and it returns without data\r\n        const pollUrl = `${url}&_=${Date.now()}`;\r\n        this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\r\n        const response = await this._httpClient.get(pollUrl, pollOptions);\r\n        if (response.statusCode !== 200) {\r\n            this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\r\n\r\n            // Mark running as false so that the poll immediately ends and runs the close logic\r\n            this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\r\n            this._running = false;\r\n        } else {\r\n            this._running = true;\r\n        }\r\n\r\n        this._receiving = this._poll(this._url, pollOptions);\r\n    }\r\n\r\n    private async _getAccessToken(): Promise<string | null> {\r\n        if (this._accessTokenFactory) {\r\n            return await this._accessTokenFactory();\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private _updateHeaderToken(request: HttpRequest, token: string | null) {\r\n        if (!request.headers) {\r\n            request.headers = {};\r\n        }\r\n        if (token) {\r\n            request.headers[HeaderNames.Authorization] = `Bearer ${token}`;\r\n            return;\r\n        }\r\n        if (request.headers[HeaderNames.Authorization]) {\r\n            delete request.headers[HeaderNames.Authorization];\r\n        }\r\n    }\r\n\r\n    private async _poll(url: string, pollOptions: HttpRequest): Promise<void> {\r\n        try {\r\n            while (this._running) {\r\n                // We have to get the access token on each poll, in case it changes\r\n                const token = await this._getAccessToken();\r\n                this._updateHeaderToken(pollOptions, token);\r\n\r\n                try {\r\n                    const pollUrl = `${url}&_=${Date.now()}`;\r\n                    this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\r\n                    const response = await this._httpClient.get(pollUrl, pollOptions);\r\n\r\n                    if (response.statusCode === 204) {\r\n                        this._logger.log(LogLevel.Information, \"(LongPolling transport) Poll terminated by server.\");\r\n\r\n                        this._running = false;\r\n                    } else if (response.statusCode !== 200) {\r\n                        this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\r\n\r\n                        // Unexpected status code\r\n                        this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\r\n                        this._running = false;\r\n                    } else {\r\n                        // Process the response\r\n                        if (response.content) {\r\n                            this._logger.log(LogLevel.Trace, `(LongPolling transport) data received. ${getDataDetail(response.content, this._options.logMessageContent!)}.`);\r\n                            if (this.onreceive) {\r\n                                this.onreceive(response.content);\r\n                            }\r\n                        } else {\r\n                            // This is another way timeout manifest.\r\n                            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                        }\r\n                    }\r\n                } catch (e) {\r\n                    if (!this._running) {\r\n                        // Log but disregard errors that occur after stopping\r\n                        this._logger.log(LogLevel.Trace, `(LongPolling transport) Poll errored after shutdown: ${e.message}`);\r\n                    } else {\r\n                        if (e instanceof TimeoutError) {\r\n                            // Ignore timeouts and reissue the poll.\r\n                            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                        } else {\r\n                            // Close the connection with the error as the result.\r\n                            this._closeError = e;\r\n                            this._running = false;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        } finally {\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Polling complete.\");\r\n\r\n            // We will reach here with pollAborted==false when the server returned a response causing the transport to stop.\r\n            // If pollAborted==true then client initiated the stop and the stop method will raise the close event after DELETE is sent.\r\n            if (!this.pollAborted) {\r\n                this._raiseOnClose();\r\n            }\r\n        }\r\n    }\r\n\r\n    public async send(data: any): Promise<void> {\r\n        if (!this._running) {\r\n            return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\r\n        }\r\n        return sendMessage(this._logger, \"LongPolling\", this._httpClient, this._url!, this._accessTokenFactory, data, this._options);\r\n    }\r\n\r\n    public async stop(): Promise<void> {\r\n        this._logger.log(LogLevel.Trace, \"(LongPolling transport) Stopping polling.\");\r\n\r\n        // Tell receiving loop to stop, abort any current request, and then wait for it to finish\r\n        this._running = false;\r\n        this._pollAbort.abort();\r\n\r\n        try {\r\n            await this._receiving;\r\n\r\n            // Send DELETE to clean up long polling on the server\r\n            this._logger.log(LogLevel.Trace, `(LongPolling transport) sending DELETE request to ${this._url}.`);\r\n\r\n            const headers: {[k: string]: string} = {};\r\n            const [name, value] = getUserAgentHeader();\r\n            headers[name] = value;\r\n\r\n            const deleteOptions: HttpRequest = {\r\n                headers: { ...headers, ...this._options.headers },\r\n                timeout: this._options.timeout,\r\n                withCredentials: this._options.withCredentials,\r\n            };\r\n            const token = await this._getAccessToken();\r\n            this._updateHeaderToken(deleteOptions, token);\r\n            await this._httpClient.delete(this._url!, deleteOptions);\r\n\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) DELETE request sent.\");\r\n        } finally {\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Stop finished.\");\r\n\r\n            // Raise close event here instead of in polling\r\n            // It needs to happen after the DELETE request is sent\r\n            this._raiseOnClose();\r\n        }\r\n    }\r\n\r\n    private _raiseOnClose() {\r\n        if (this.onclose) {\r\n            let logMessage = \"(LongPolling transport) Firing onclose event.\";\r\n            if (this._closeError) {\r\n                logMessage += \" Error: \" + this._closeError;\r\n            }\r\n            this._logger.log(LogLevel.Trace, logMessage);\r\n            this.onclose(this._closeError);\r\n        }\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { MessageHeaders } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { ITransport, TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, Platform, sendMessage } from \"./Utils\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\n\r\n/** @private */\r\nexport class ServerSentEventsTransport implements ITransport {\r\n    private readonly _httpClient: HttpClient;\r\n    private readonly _accessTokenFactory: (() => string | Promise<string>) | undefined;\r\n    private readonly _logger: ILogger;\r\n    private readonly _options: IHttpConnectionOptions;\r\n    private _eventSource?: EventSource;\r\n    private _url?: string;\r\n\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((error?: Error) => void) | null;\r\n\r\n    constructor(httpClient: HttpClient, accessTokenFactory: (() => string | Promise<string>) | undefined, logger: ILogger,\r\n                options: IHttpConnectionOptions) {\r\n        this._httpClient = httpClient;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n        this._logger = logger;\r\n        this._options = options;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n\r\n    public async connect(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n\r\n        this._logger.log(LogLevel.Trace, \"(SSE transport) Connecting.\");\r\n\r\n        // set url before accessTokenFactory because this.url is only for send and we set the auth header instead of the query string for send\r\n        this._url = url;\r\n\r\n        if (this._accessTokenFactory) {\r\n            const token = await this._accessTokenFactory();\r\n            if (token) {\r\n                url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(token)}`;\r\n            }\r\n        }\r\n\r\n        return new Promise<void>((resolve, reject) => {\r\n            let opened = false;\r\n            if (transferFormat !== TransferFormat.Text) {\r\n                reject(new Error(\"The Server-Sent Events transport only supports the 'Text' transfer format\"));\r\n                return;\r\n            }\r\n\r\n            let eventSource: EventSource;\r\n            if (Platform.isBrowser || Platform.isWebWorker) {\r\n                eventSource = new this._options.EventSource!(url, { withCredentials: this._options.withCredentials });\r\n            } else {\r\n                // Non-browser passes cookies via the dictionary\r\n                const cookies = this._httpClient.getCookieString(url);\r\n                const headers: MessageHeaders = {};\r\n                headers.Cookie = cookies;\r\n                const [name, value] = getUserAgentHeader();\r\n                headers[name] = value;\r\n\r\n                eventSource = new this._options.EventSource!(url, { withCredentials: this._options.withCredentials, headers: { ...headers, ...this._options.headers} } as EventSourceInit);\r\n            }\r\n\r\n            try {\r\n                eventSource.onmessage = (e: MessageEvent) => {\r\n                    if (this.onreceive) {\r\n                        try {\r\n                            this._logger.log(LogLevel.Trace, `(SSE transport) data received. ${getDataDetail(e.data, this._options.logMessageContent!)}.`);\r\n                            this.onreceive(e.data);\r\n                        } catch (error) {\r\n                            this._close(error);\r\n                            return;\r\n                        }\r\n                    }\r\n                };\r\n\r\n                // @ts-ignore: not using event on purpose\r\n                eventSource.onerror = (e: Event) => {\r\n                    // EventSource doesn't give any useful information about server side closes.\r\n                    if (opened) {\r\n                        this._close();\r\n                    } else {\r\n                        reject(new Error(\"EventSource failed to connect. The connection could not be found on the server,\"\r\n                        + \" either the connection ID is not present on the server, or a proxy is refusing/buffering the connection.\"\r\n                        + \" If you have multiple servers check that sticky sessions are enabled.\"));\r\n                    }\r\n                };\r\n\r\n                eventSource.onopen = () => {\r\n                    this._logger.log(LogLevel.Information, `SSE connected to ${this._url}`);\r\n                    this._eventSource = eventSource;\r\n                    opened = true;\r\n                    resolve();\r\n                };\r\n            } catch (e) {\r\n                reject(e);\r\n                return;\r\n            }\r\n        });\r\n    }\r\n\r\n    public async send(data: any): Promise<void> {\r\n        if (!this._eventSource) {\r\n            return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\r\n        }\r\n        return sendMessage(this._logger, \"SSE\", this._httpClient, this._url!, this._accessTokenFactory, data, this._options);\r\n    }\r\n\r\n    public stop(): Promise<void> {\r\n        this._close();\r\n        return Promise.resolve();\r\n    }\r\n\r\n    private _close(e?: Error) {\r\n        if (this._eventSource) {\r\n            this._eventSource.close();\r\n            this._eventSource = undefined;\r\n\r\n            if (this.onclose) {\r\n                this.onclose(e);\r\n            }\r\n        }\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HeaderNames } from \"./HeaderNames\";\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { MessageHeaders } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { ITransport, TransferFormat } from \"./ITransport\";\r\nimport { WebSocketConstructor } from \"./Polyfills\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, Platform } from \"./Utils\";\r\n\r\n/** @private */\r\nexport class WebSocketTransport implements ITransport {\r\n    private readonly _logger: ILogger;\r\n    private readonly _accessTokenFactory: (() => string | Promise<string>) | undefined;\r\n    private readonly _logMessageContent: boolean;\r\n    private readonly _webSocketConstructor: WebSocketConstructor;\r\n    private readonly _httpClient: HttpClient;\r\n    private _webSocket?: WebSocket;\r\n    private _headers: MessageHeaders;\r\n\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((error?: Error) => void) | null;\r\n\r\n    constructor(httpClient: HttpClient, accessTokenFactory: (() => string | Promise<string>) | undefined, logger: ILogger,\r\n                logMessageContent: boolean, webSocketConstructor: WebSocketConstructor, headers: MessageHeaders) {\r\n        this._logger = logger;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n        this._logMessageContent = logMessageContent;\r\n        this._webSocketConstructor = webSocketConstructor;\r\n        this._httpClient = httpClient;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n        this._headers = headers;\r\n    }\r\n\r\n    public async connect(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n        this._logger.log(LogLevel.Trace, \"(WebSockets transport) Connecting.\");\r\n\r\n        if (this._accessTokenFactory) {\r\n            const token = await this._accessTokenFactory();\r\n            if (token) {\r\n                url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(token)}`;\r\n            }\r\n        }\r\n\r\n        return new Promise<void>((resolve, reject) => {\r\n            url = url.replace(/^http/, \"ws\");\r\n            let webSocket: WebSocket | undefined;\r\n            const cookies = this._httpClient.getCookieString(url);\r\n            let opened = false;\r\n\r\n            if (Platform.isNode) {\r\n                const headers: {[k: string]: string} = {};\r\n                const [name, value] = getUserAgentHeader();\r\n                headers[name] = value;\r\n\r\n                if (cookies) {\r\n                    headers[HeaderNames.Cookie] = `${cookies}`;\r\n                }\r\n\r\n                // Only pass headers when in non-browser environments\r\n                webSocket = new this._webSocketConstructor(url, undefined, {\r\n                    headers: { ...headers, ...this._headers },\r\n                });\r\n            }\r\n\r\n            if (!webSocket) {\r\n                // Chrome is not happy with passing 'undefined' as protocol\r\n                webSocket = new this._webSocketConstructor(url);\r\n            }\r\n\r\n            if (transferFormat === TransferFormat.Binary) {\r\n                webSocket.binaryType = \"arraybuffer\";\r\n            }\r\n\r\n            webSocket.onopen = (_event: Event) => {\r\n                this._logger.log(LogLevel.Information, `WebSocket connected to ${url}.`);\r\n                this._webSocket = webSocket;\r\n                opened = true;\r\n                resolve();\r\n            };\r\n\r\n            webSocket.onerror = (event: Event) => {\r\n                let error: any = null;\r\n                // ErrorEvent is a browser only type we need to check if the type exists before using it\r\n                if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\r\n                    error = event.error;\r\n                } else {\r\n                    error = \"There was an error with the transport\";\r\n                }\r\n\r\n                this._logger.log(LogLevel.Information, `(WebSockets transport) ${error}.`);\r\n            };\r\n\r\n            webSocket.onmessage = (message: MessageEvent) => {\r\n                this._logger.log(LogLevel.Trace, `(WebSockets transport) data received. ${getDataDetail(message.data, this._logMessageContent)}.`);\r\n                if (this.onreceive) {\r\n                    try {\r\n                        this.onreceive(message.data);\r\n                    } catch (error) {\r\n                        this._close(error);\r\n                        return;\r\n                    }\r\n                }\r\n            };\r\n\r\n            webSocket.onclose = (event: CloseEvent) => {\r\n                // Don't call close handler if connection was never established\r\n                // We'll reject the connect call instead\r\n                if (opened) {\r\n                    this._close(event);\r\n                } else {\r\n                    let error: any = null;\r\n                    // ErrorEvent is a browser only type we need to check if the type exists before using it\r\n                    if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\r\n                        error = event.error;\r\n                    } else {\r\n                        error = \"WebSocket failed to connect. The connection could not be found on the server,\"\r\n                        + \" either the endpoint may not be a SignalR endpoint,\"\r\n                        + \" the connection ID is not present on the server, or there is a proxy blocking WebSockets.\"\r\n                        + \" If you have multiple servers check that sticky sessions are enabled.\";\r\n                    }\r\n\r\n                    reject(new Error(error));\r\n                }\r\n            };\r\n        });\r\n    }\r\n\r\n    public send(data: any): Promise<void> {\r\n        if (this._webSocket && this._webSocket.readyState === this._webSocketConstructor.OPEN) {\r\n            this._logger.log(LogLevel.Trace, `(WebSockets transport) sending data. ${getDataDetail(data, this._logMessageContent)}.`);\r\n            this._webSocket.send(data);\r\n            return Promise.resolve();\r\n        }\r\n\r\n        return Promise.reject(\"WebSocket is not in the OPEN state\");\r\n    }\r\n\r\n    public stop(): Promise<void> {\r\n        if (this._webSocket) {\r\n            // Manually invoke onclose callback inline so we know the HttpConnection was closed properly before returning\r\n            // This also solves an issue where websocket.onclose could take 18+ seconds to trigger during network disconnects\r\n            this._close(undefined);\r\n        }\r\n\r\n        return Promise.resolve();\r\n    }\r\n\r\n    private _close(event?: CloseEvent | Error): void {\r\n        // webSocket will be null if the transport did not start successfully\r\n        if (this._webSocket) {\r\n            // Clear websocket handlers because we are considering the socket closed now\r\n            this._webSocket.onclose = () => {};\r\n            this._webSocket.onmessage = () => {};\r\n            this._webSocket.onerror = () => {};\r\n            this._webSocket.close();\r\n            this._webSocket = undefined;\r\n        }\r\n\r\n        this._logger.log(LogLevel.Trace, \"(WebSockets transport) socket closed.\");\r\n        if (this.onclose) {\r\n            if (this._isCloseEvent(event) && (event.wasClean === false || event.code !== 1000)) {\r\n                this.onclose(new Error(`WebSocket closed with status code: ${event.code} (${event.reason || \"no reason given\"}).`));\r\n            } else if (event instanceof Error) {\r\n                this.onclose(event);\r\n            } else {\r\n                this.onclose();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _isCloseEvent(event?: any): event is CloseEvent {\r\n        return event && typeof event.wasClean === \"boolean\" && typeof event.code === \"number\";\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { DefaultHttpClient } from \"./DefaultHttpClient\";\r\nimport { AggregateErrors, DisabledTransportError, FailedToNegotiateWithServerError, FailedToStartTransportError, HttpError, UnsupportedTransportError } from \"./Errors\";\r\nimport { HeaderNames } from \"./HeaderNames\";\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { IConnection } from \"./IConnection\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { HttpTransportType, ITransport, TransferFormat } from \"./ITransport\";\r\nimport { LongPollingTransport } from \"./LongPollingTransport\";\r\nimport { ServerSentEventsTransport } from \"./ServerSentEventsTransport\";\r\nimport { Arg, createLogger, getUserAgentHeader, Platform } from \"./Utils\";\r\nimport { WebSocketTransport } from \"./WebSocketTransport\";\r\n\r\n/** @private */\r\nconst enum ConnectionState {\r\n    Connecting = \"Connecting\",\r\n    Connected = \"Connected\",\r\n    Disconnected = \"Disconnected\",\r\n    Disconnecting = \"Disconnecting\",\r\n}\r\n\r\n/** @private */\r\nexport interface INegotiateResponse {\r\n    connectionId?: string;\r\n    connectionToken?: string;\r\n    negotiateVersion?: number;\r\n    availableTransports?: IAvailableTransport[];\r\n    url?: string;\r\n    accessToken?: string;\r\n    error?: string;\r\n}\r\n\r\n/** @private */\r\nexport interface IAvailableTransport {\r\n    transport: keyof typeof HttpTransportType;\r\n    transferFormats: (keyof typeof TransferFormat)[];\r\n}\r\n\r\nconst MAX_REDIRECTS = 100;\r\n\r\n/** @private */\r\nexport class HttpConnection implements IConnection {\r\n    private _connectionState: ConnectionState;\r\n    // connectionStarted is tracked independently from connectionState, so we can check if the\r\n    // connection ever did successfully transition from connecting to connected before disconnecting.\r\n    private _connectionStarted: boolean;\r\n    private readonly _httpClient: HttpClient;\r\n    private readonly _logger: ILogger;\r\n    private readonly _options: IHttpConnectionOptions;\r\n    // Needs to not start with _ to be available for tests\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private transport?: ITransport;\r\n    private _startInternalPromise?: Promise<void>;\r\n    private _stopPromise?: Promise<void>;\r\n    private _stopPromiseResolver: (value?: PromiseLike<void>) => void = () => {};\r\n    private _stopError?: Error;\r\n    private _accessTokenFactory?: () => string | Promise<string>;\r\n    private _sendQueue?: TransportSendQueue;\r\n\r\n    public readonly features: any = {};\r\n    public baseUrl: string;\r\n    public connectionId?: string;\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((e?: Error) => void) | null;\r\n\r\n    private readonly _negotiateVersion: number = 1;\r\n\r\n    constructor(url: string, options: IHttpConnectionOptions = {}) {\r\n        Arg.isRequired(url, \"url\");\r\n\r\n        this._logger = createLogger(options.logger);\r\n        this.baseUrl = this._resolveUrl(url);\r\n\r\n        options = options || {};\r\n        options.logMessageContent = options.logMessageContent === undefined ? false : options.logMessageContent;\r\n        if (typeof options.withCredentials === \"boolean\" || options.withCredentials === undefined) {\r\n            options.withCredentials = options.withCredentials === undefined ? true : options.withCredentials;\r\n        } else {\r\n            throw new Error(\"withCredentials option was not a 'boolean' or 'undefined' value\");\r\n        }\r\n        options.timeout = options.timeout === undefined ? 100 * 1000 : options.timeout;\r\n\r\n        let webSocketModule: any = null;\r\n        let eventSourceModule: any = null;\r\n\r\n        if (Platform.isNode && typeof require !== \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n            webSocketModule = requireFunc(\"ws\");\r\n            eventSourceModule = requireFunc(\"eventsource\");\r\n        }\r\n\r\n        if (!Platform.isNode && typeof WebSocket !== \"undefined\" && !options.WebSocket) {\r\n            options.WebSocket = WebSocket;\r\n        } else if (Platform.isNode && !options.WebSocket) {\r\n            if (webSocketModule) {\r\n                options.WebSocket = webSocketModule;\r\n            }\r\n        }\r\n\r\n        if (!Platform.isNode && typeof EventSource !== \"undefined\" && !options.EventSource) {\r\n            options.EventSource = EventSource;\r\n        } else if (Platform.isNode && !options.EventSource) {\r\n            if (typeof eventSourceModule !== \"undefined\") {\r\n                options.EventSource = eventSourceModule;\r\n            }\r\n        }\r\n\r\n        this._httpClient = options.httpClient || new DefaultHttpClient(this._logger);\r\n        this._connectionState = ConnectionState.Disconnected;\r\n        this._connectionStarted = false;\r\n        this._options = options;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n\r\n    public start(): Promise<void>;\r\n    public start(transferFormat: TransferFormat): Promise<void>;\r\n    public async start(transferFormat?: TransferFormat): Promise<void> {\r\n        transferFormat = transferFormat || TransferFormat.Binary;\r\n\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n\r\n        this._logger.log(LogLevel.Debug, `Starting connection with transfer format '${TransferFormat[transferFormat]}'.`);\r\n\r\n        if (this._connectionState !== ConnectionState.Disconnected) {\r\n            return Promise.reject(new Error(\"Cannot start an HttpConnection that is not in the 'Disconnected' state.\"));\r\n        }\r\n\r\n        this._connectionState = ConnectionState.Connecting;\r\n\r\n        this._startInternalPromise = this._startInternal(transferFormat);\r\n        await this._startInternalPromise;\r\n\r\n        // The TypeScript compiler thinks that connectionState must be Connecting here. The TypeScript compiler is wrong.\r\n        if (this._connectionState as any === ConnectionState.Disconnecting) {\r\n            // stop() was called and transitioned the client into the Disconnecting state.\r\n            const message = \"Failed to start the HttpConnection before stop() was called.\";\r\n            this._logger.log(LogLevel.Error, message);\r\n\r\n            // We cannot await stopPromise inside startInternal since stopInternal awaits the startInternalPromise.\r\n            await this._stopPromise;\r\n\r\n            return Promise.reject(new Error(message));\r\n        } else if (this._connectionState as any !== ConnectionState.Connected) {\r\n            // stop() was called and transitioned the client into the Disconnecting state.\r\n            const message = \"HttpConnection.startInternal completed gracefully but didn't enter the connection into the connected state!\";\r\n            this._logger.log(LogLevel.Error, message);\r\n            return Promise.reject(new Error(message));\r\n        }\r\n\r\n        this._connectionStarted = true;\r\n    }\r\n\r\n    public send(data: string | ArrayBuffer): Promise<void> {\r\n        if (this._connectionState !== ConnectionState.Connected) {\r\n            return Promise.reject(new Error(\"Cannot send data if the connection is not in the 'Connected' State.\"));\r\n        }\r\n\r\n        if (!this._sendQueue) {\r\n            this._sendQueue = new TransportSendQueue(this.transport!);\r\n        }\r\n\r\n        // Transport will not be null if state is connected\r\n        return this._sendQueue.send(data);\r\n    }\r\n\r\n    public async stop(error?: Error): Promise<void> {\r\n        if (this._connectionState === ConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnected state.`);\r\n            return Promise.resolve();\r\n        }\r\n\r\n        if (this._connectionState === ConnectionState.Disconnecting) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\r\n            return this._stopPromise;\r\n        }\r\n\r\n        this._connectionState = ConnectionState.Disconnecting;\r\n\r\n        this._stopPromise = new Promise((resolve) => {\r\n            // Don't complete stop() until stopConnection() completes.\r\n            this._stopPromiseResolver = resolve;\r\n        });\r\n\r\n        // stopInternal should never throw so just observe it.\r\n        await this._stopInternal(error);\r\n        await this._stopPromise;\r\n    }\r\n\r\n    private async _stopInternal(error?: Error): Promise<void> {\r\n        // Set error as soon as possible otherwise there is a race between\r\n        // the transport closing and providing an error and the error from a close message\r\n        // We would prefer the close message error.\r\n        this._stopError = error;\r\n\r\n        try {\r\n            await this._startInternalPromise;\r\n        } catch (e) {\r\n            // This exception is returned to the user as a rejected Promise from the start method.\r\n        }\r\n\r\n        // The transport's onclose will trigger stopConnection which will run our onclose event.\r\n        // The transport should always be set if currently connected. If it wasn't set, it's likely because\r\n        // stop was called during start() and start() failed.\r\n        if (this.transport) {\r\n            try {\r\n                await this.transport.stop();\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `HttpConnection.transport.stop() threw error '${e}'.`);\r\n                this._stopConnection();\r\n            }\r\n\r\n            this.transport = undefined;\r\n        } else {\r\n            this._logger.log(LogLevel.Debug, \"HttpConnection.transport is undefined in HttpConnection.stop() because start() failed.\");\r\n        }\r\n    }\r\n\r\n    private async _startInternal(transferFormat: TransferFormat): Promise<void> {\r\n        // Store the original base url and the access token factory since they may change\r\n        // as part of negotiating\r\n        let url = this.baseUrl;\r\n        this._accessTokenFactory = this._options.accessTokenFactory;\r\n\r\n        try {\r\n            if (this._options.skipNegotiation) {\r\n                if (this._options.transport === HttpTransportType.WebSockets) {\r\n                    // No need to add a connection ID in this case\r\n                    this.transport = this._constructTransport(HttpTransportType.WebSockets);\r\n                    // We should just call connect directly in this case.\r\n                    // No fallback or negotiate in this case.\r\n                    await this._startTransport(url, transferFormat);\r\n                } else {\r\n                    throw new Error(\"Negotiation can only be skipped when using the WebSocket transport directly.\");\r\n                }\r\n            } else {\r\n                let negotiateResponse: INegotiateResponse | null = null;\r\n                let redirects = 0;\r\n\r\n                do {\r\n                    negotiateResponse = await this._getNegotiationResponse(url);\r\n                    // the user tries to stop the connection when it is being started\r\n                    if (this._connectionState === ConnectionState.Disconnecting || this._connectionState === ConnectionState.Disconnected) {\r\n                        throw new Error(\"The connection was stopped during negotiation.\");\r\n                    }\r\n\r\n                    if (negotiateResponse.error) {\r\n                        throw new Error(negotiateResponse.error);\r\n                    }\r\n\r\n                    if ((negotiateResponse as any).ProtocolVersion) {\r\n                        throw new Error(\"Detected a connection attempt to an ASP.NET SignalR Server. This client only supports connecting to an ASP.NET Core SignalR Server. See https://aka.ms/signalr-core-differences for details.\");\r\n                    }\r\n\r\n                    if (negotiateResponse.url) {\r\n                        url = negotiateResponse.url;\r\n                    }\r\n\r\n                    if (negotiateResponse.accessToken) {\r\n                        // Replace the current access token factory with one that uses\r\n                        // the returned access token\r\n                        const accessToken = negotiateResponse.accessToken;\r\n                        this._accessTokenFactory = () => accessToken;\r\n                    }\r\n\r\n                    redirects++;\r\n                }\r\n                while (negotiateResponse.url && redirects < MAX_REDIRECTS);\r\n\r\n                if (redirects === MAX_REDIRECTS && negotiateResponse.url) {\r\n                    throw new Error(\"Negotiate redirection limit exceeded.\");\r\n                }\r\n\r\n                await this._createTransport(url, this._options.transport, negotiateResponse, transferFormat);\r\n            }\r\n\r\n            if (this.transport instanceof LongPollingTransport) {\r\n                this.features.inherentKeepAlive = true;\r\n            }\r\n\r\n            if (this._connectionState === ConnectionState.Connecting) {\r\n                // Ensure the connection transitions to the connected state prior to completing this.startInternalPromise.\r\n                // start() will handle the case when stop was called and startInternal exits still in the disconnecting state.\r\n                this._logger.log(LogLevel.Debug, \"The HttpConnection connected successfully.\");\r\n                this._connectionState = ConnectionState.Connected;\r\n            }\r\n\r\n            // stop() is waiting on us via this.startInternalPromise so keep this.transport around so it can clean up.\r\n            // This is the only case startInternal can exit in neither the connected nor disconnected state because stopConnection()\r\n            // will transition to the disconnected state. start() will wait for the transition using the stopPromise.\r\n        } catch (e) {\r\n            this._logger.log(LogLevel.Error, \"Failed to start the connection: \" + e);\r\n            this._connectionState = ConnectionState.Disconnected;\r\n            this.transport = undefined;\r\n\r\n            // if start fails, any active calls to stop assume that start will complete the stop promise\r\n            this._stopPromiseResolver();\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n\r\n    private async _getNegotiationResponse(url: string): Promise<INegotiateResponse> {\r\n        const headers: {[k: string]: string} = {};\r\n        if (this._accessTokenFactory) {\r\n            const token = await this._accessTokenFactory();\r\n            if (token) {\r\n                headers[HeaderNames.Authorization] = `Bearer ${token}`;\r\n            }\r\n        }\r\n\r\n        const [name, value] = getUserAgentHeader();\r\n        headers[name] = value;\r\n\r\n        const negotiateUrl = this._resolveNegotiateUrl(url);\r\n        this._logger.log(LogLevel.Debug, `Sending negotiation request: ${negotiateUrl}.`);\r\n        try {\r\n            const response = await this._httpClient.post(negotiateUrl, {\r\n                content: \"\",\r\n                headers: { ...headers, ...this._options.headers },\r\n                timeout: this._options.timeout,\r\n                withCredentials: this._options.withCredentials,\r\n            });\r\n\r\n            if (response.statusCode !== 200) {\r\n                return Promise.reject(new Error(`Unexpected status code returned from negotiate '${response.statusCode}'`));\r\n            }\r\n\r\n            const negotiateResponse = JSON.parse(response.content as string) as INegotiateResponse;\r\n            if (!negotiateResponse.negotiateVersion || negotiateResponse.negotiateVersion < 1) {\r\n                // Negotiate version 0 doesn't use connectionToken\r\n                // So we set it equal to connectionId so all our logic can use connectionToken without being aware of the negotiate version\r\n                negotiateResponse.connectionToken = negotiateResponse.connectionId;\r\n            }\r\n            return negotiateResponse;\r\n        } catch (e) {\r\n            let errorMessage = \"Failed to complete negotiation with the server: \" + e;\r\n            if (e instanceof HttpError) {\r\n                if (e.statusCode === 404) {\r\n                    errorMessage = errorMessage + \" Either this is not a SignalR endpoint or there is a proxy blocking the connection.\";\r\n                }\r\n            }\r\n            this._logger.log(LogLevel.Error, errorMessage);\r\n\r\n            return Promise.reject(new FailedToNegotiateWithServerError(errorMessage));\r\n        }\r\n    }\r\n\r\n    private _createConnectUrl(url: string, connectionToken: string | null | undefined) {\r\n        if (!connectionToken) {\r\n            return url;\r\n        }\r\n\r\n        return url + (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + `id=${connectionToken}`;\r\n    }\r\n\r\n    private async _createTransport(url: string, requestedTransport: HttpTransportType | ITransport | undefined, negotiateResponse: INegotiateResponse, requestedTransferFormat: TransferFormat): Promise<void> {\r\n        let connectUrl = this._createConnectUrl(url, negotiateResponse.connectionToken);\r\n        if (this._isITransport(requestedTransport)) {\r\n            this._logger.log(LogLevel.Debug, \"Connection was provided an instance of ITransport, using that directly.\");\r\n            this.transport = requestedTransport;\r\n            await this._startTransport(connectUrl, requestedTransferFormat);\r\n\r\n            this.connectionId = negotiateResponse.connectionId;\r\n            return;\r\n        }\r\n\r\n        const transportExceptions: any[] = [];\r\n        const transports = negotiateResponse.availableTransports || [];\r\n        let negotiate: INegotiateResponse | undefined = negotiateResponse;\r\n        for (const endpoint of transports) {\r\n            const transportOrError = this._resolveTransportOrError(endpoint, requestedTransport, requestedTransferFormat);\r\n            if (transportOrError instanceof Error) {\r\n                // Store the error and continue, we don't want to cause a re-negotiate in these cases\r\n                transportExceptions.push(`${endpoint.transport} failed:`);\r\n                transportExceptions.push(transportOrError);\r\n            } else if (this._isITransport(transportOrError)) {\r\n                this.transport = transportOrError;\r\n                if (!negotiate) {\r\n                    try {\r\n                        negotiate = await this._getNegotiationResponse(url);\r\n                    } catch (ex) {\r\n                        return Promise.reject(ex);\r\n                    }\r\n                    connectUrl = this._createConnectUrl(url, negotiate.connectionToken);\r\n                }\r\n                try {\r\n                    await this._startTransport(connectUrl, requestedTransferFormat);\r\n                    this.connectionId = negotiate.connectionId;\r\n                    return;\r\n                } catch (ex) {\r\n                    this._logger.log(LogLevel.Error, `Failed to start the transport '${endpoint.transport}': ${ex}`);\r\n                    negotiate = undefined;\r\n                    transportExceptions.push(new FailedToStartTransportError(`${endpoint.transport} failed: ${ex}`, HttpTransportType[endpoint.transport]));\r\n\r\n                    if (this._connectionState !== ConnectionState.Connecting) {\r\n                        const message = \"Failed to select transport before stop() was called.\";\r\n                        this._logger.log(LogLevel.Debug, message);\r\n                        return Promise.reject(new Error(message));\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        if (transportExceptions.length > 0) {\r\n            return Promise.reject(new AggregateErrors(`Unable to connect to the server with any of the available transports. ${transportExceptions.join(\" \")}`, transportExceptions));\r\n        }\r\n        return Promise.reject(new Error(\"None of the transports supported by the client are supported by the server.\"));\r\n    }\r\n\r\n    private _constructTransport(transport: HttpTransportType): ITransport {\r\n        switch (transport) {\r\n            case HttpTransportType.WebSockets:\r\n                if (!this._options.WebSocket) {\r\n                    throw new Error(\"'WebSocket' is not supported in your environment.\");\r\n                }\r\n                return new WebSocketTransport(this._httpClient, this._accessTokenFactory, this._logger, this._options.logMessageContent!, this._options.WebSocket, this._options.headers || {});\r\n            case HttpTransportType.ServerSentEvents:\r\n                if (!this._options.EventSource) {\r\n                    throw new Error(\"'EventSource' is not supported in your environment.\");\r\n                }\r\n                return new ServerSentEventsTransport(this._httpClient, this._accessTokenFactory, this._logger, this._options);\r\n            case HttpTransportType.LongPolling:\r\n                return new LongPollingTransport(this._httpClient, this._accessTokenFactory, this._logger, this._options);\r\n            default:\r\n                throw new Error(`Unknown transport: ${transport}.`);\r\n        }\r\n    }\r\n\r\n    private _startTransport(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        this.transport!.onreceive = this.onreceive;\r\n        this.transport!.onclose = (e) => this._stopConnection(e);\r\n        return this.transport!.connect(url, transferFormat);\r\n    }\r\n\r\n    private _resolveTransportOrError(endpoint: IAvailableTransport, requestedTransport: HttpTransportType | undefined, requestedTransferFormat: TransferFormat): ITransport | Error {\r\n        const transport = HttpTransportType[endpoint.transport];\r\n        if (transport === null || transport === undefined) {\r\n            this._logger.log(LogLevel.Debug, `Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\r\n            return new Error(`Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\r\n        } else {\r\n            if (transportMatches(requestedTransport, transport)) {\r\n                const transferFormats = endpoint.transferFormats.map((s) => TransferFormat[s]);\r\n                if (transferFormats.indexOf(requestedTransferFormat) >= 0) {\r\n                    if ((transport === HttpTransportType.WebSockets && !this._options.WebSocket) ||\r\n                        (transport === HttpTransportType.ServerSentEvents && !this._options.EventSource)) {\r\n                        this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it is not supported in your environment.'`);\r\n                        return new UnsupportedTransportError(`'${HttpTransportType[transport]}' is not supported in your environment.`, transport);\r\n                    } else {\r\n                        this._logger.log(LogLevel.Debug, `Selecting transport '${HttpTransportType[transport]}'.`);\r\n                        try {\r\n                            return this._constructTransport(transport);\r\n                        } catch (ex) {\r\n                            return ex;\r\n                        }\r\n                    }\r\n                } else {\r\n                    this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it does not support the requested transfer format '${TransferFormat[requestedTransferFormat]}'.`);\r\n                    return new Error(`'${HttpTransportType[transport]}' does not support ${TransferFormat[requestedTransferFormat]}.`);\r\n                }\r\n            } else {\r\n                this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it was disabled by the client.`);\r\n                return new DisabledTransportError(`'${HttpTransportType[transport]}' is disabled by the client.`, transport);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _isITransport(transport: any): transport is ITransport {\r\n        return transport && typeof (transport) === \"object\" && \"connect\" in transport;\r\n    }\r\n\r\n    private _stopConnection(error?: Error): void {\r\n        this._logger.log(LogLevel.Debug, `HttpConnection.stopConnection(${error}) called while in state ${this._connectionState}.`);\r\n\r\n        this.transport = undefined;\r\n\r\n        // If we have a stopError, it takes precedence over the error from the transport\r\n        error = this._stopError || error;\r\n        this._stopError = undefined;\r\n\r\n        if (this._connectionState === ConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is already in the disconnected state.`);\r\n            return;\r\n        }\r\n\r\n        if (this._connectionState === ConnectionState.Connecting) {\r\n            this._logger.log(LogLevel.Warning, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is still in the connecting state.`);\r\n            throw new Error(`HttpConnection.stopConnection(${error}) was called while the connection is still in the connecting state.`);\r\n        }\r\n\r\n        if (this._connectionState === ConnectionState.Disconnecting) {\r\n            // A call to stop() induced this call to stopConnection and needs to be completed.\r\n            // Any stop() awaiters will be scheduled to continue after the onclose callback fires.\r\n            this._stopPromiseResolver();\r\n        }\r\n\r\n        if (error) {\r\n            this._logger.log(LogLevel.Error, `Connection disconnected with error '${error}'.`);\r\n        } else {\r\n            this._logger.log(LogLevel.Information, \"Connection disconnected.\");\r\n        }\r\n\r\n        if (this._sendQueue) {\r\n            this._sendQueue.stop().catch((e) => {\r\n                this._logger.log(LogLevel.Error, `TransportSendQueue.stop() threw error '${e}'.`);\r\n            });\r\n            this._sendQueue = undefined;\r\n        }\r\n\r\n        this.connectionId = undefined;\r\n        this._connectionState = ConnectionState.Disconnected;\r\n\r\n        if (this._connectionStarted) {\r\n            this._connectionStarted = false;\r\n            try {\r\n                if (this.onclose) {\r\n                    this.onclose(error);\r\n                }\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `HttpConnection.onclose(${error}) threw error '${e}'.`);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _resolveUrl(url: string): string {\r\n        // startsWith is not supported in IE\r\n        if (url.lastIndexOf(\"https://\", 0) === 0 || url.lastIndexOf(\"http://\", 0) === 0) {\r\n            return url;\r\n        }\r\n\r\n        if (!Platform.isBrowser) {\r\n            throw new Error(`Cannot resolve '${url}'.`);\r\n        }\r\n\r\n        // Setting the url to the href propery of an anchor tag handles normalization\r\n        // for us. There are 3 main cases.\r\n        // 1. Relative path normalization e.g \"b\" -> \"http://localhost:5000/a/b\"\r\n        // 2. Absolute path normalization e.g \"/a/b\" -> \"http://localhost:5000/a/b\"\r\n        // 3. Networkpath reference normalization e.g \"//localhost:5000/a/b\" -> \"http://localhost:5000/a/b\"\r\n        const aTag = window.document.createElement(\"a\");\r\n        aTag.href = url;\r\n\r\n        this._logger.log(LogLevel.Information, `Normalizing '${url}' to '${aTag.href}'.`);\r\n        return aTag.href;\r\n    }\r\n\r\n    private _resolveNegotiateUrl(url: string): string {\r\n        const index = url.indexOf(\"?\");\r\n        let negotiateUrl = url.substring(0, index === -1 ? url.length : index);\r\n        if (negotiateUrl[negotiateUrl.length - 1] !== \"/\") {\r\n            negotiateUrl += \"/\";\r\n        }\r\n        negotiateUrl += \"negotiate\";\r\n        negotiateUrl += index === -1 ? \"\" : url.substring(index);\r\n\r\n        if (negotiateUrl.indexOf(\"negotiateVersion\") === -1) {\r\n            negotiateUrl += index === -1 ? \"?\" : \"&\";\r\n            negotiateUrl += \"negotiateVersion=\" + this._negotiateVersion;\r\n        }\r\n        return negotiateUrl;\r\n    }\r\n}\r\n\r\nfunction transportMatches(requestedTransport: HttpTransportType | undefined, actualTransport: HttpTransportType) {\r\n    return !requestedTransport || ((actualTransport & requestedTransport) !== 0);\r\n}\r\n\r\n/** @private */\r\nexport class TransportSendQueue {\r\n    private _buffer: any[] = [];\r\n    private _sendBufferedData: PromiseSource;\r\n    private _executing: boolean = true;\r\n    private _transportResult?: PromiseSource;\r\n    private _sendLoopPromise: Promise<void>;\r\n\r\n    constructor(private readonly _transport: ITransport) {\r\n        this._sendBufferedData = new PromiseSource();\r\n        this._transportResult = new PromiseSource();\r\n\r\n        this._sendLoopPromise = this._sendLoop();\r\n    }\r\n\r\n    public send(data: string | ArrayBuffer): Promise<void> {\r\n        this._bufferData(data);\r\n        if (!this._transportResult) {\r\n            this._transportResult = new PromiseSource();\r\n        }\r\n        return this._transportResult.promise;\r\n    }\r\n\r\n    public stop(): Promise<void> {\r\n        this._executing = false;\r\n        this._sendBufferedData.resolve();\r\n        return this._sendLoopPromise;\r\n    }\r\n\r\n    private _bufferData(data: string | ArrayBuffer): void {\r\n        if (this._buffer.length && typeof(this._buffer[0]) !== typeof(data)) {\r\n            throw new Error(`Expected data to be of type ${typeof(this._buffer)} but was of type ${typeof(data)}`);\r\n        }\r\n\r\n        this._buffer.push(data);\r\n        this._sendBufferedData.resolve();\r\n    }\r\n\r\n    private async _sendLoop(): Promise<void> {\r\n        while (true) {\r\n            await this._sendBufferedData.promise;\r\n\r\n            if (!this._executing) {\r\n                if (this._transportResult) {\r\n                    this._transportResult.reject(\"Connection stopped.\");\r\n                }\r\n\r\n                break;\r\n            }\r\n\r\n            this._sendBufferedData = new PromiseSource();\r\n\r\n            const transportResult = this._transportResult!;\r\n            this._transportResult = undefined;\r\n\r\n            const data = typeof(this._buffer[0]) === \"string\" ?\r\n                this._buffer.join(\"\") :\r\n                TransportSendQueue._concatBuffers(this._buffer);\r\n\r\n            this._buffer.length = 0;\r\n\r\n            try {\r\n                await this._transport.send(data);\r\n                transportResult.resolve();\r\n            } catch (error) {\r\n                transportResult.reject(error);\r\n            }\r\n        }\r\n    }\r\n\r\n    private static _concatBuffers(arrayBuffers: ArrayBuffer[]): ArrayBuffer {\r\n        const totalLength = arrayBuffers.map((b) => b.byteLength).reduce((a, b) => a + b);\r\n        const result = new Uint8Array(totalLength);\r\n        let offset = 0;\r\n        for (const item of arrayBuffers) {\r\n            result.set(new Uint8Array(item), offset);\r\n            offset += item.byteLength;\r\n        }\r\n\r\n        return result.buffer;\r\n    }\r\n}\r\n\r\nclass PromiseSource {\r\n    private _resolver?: () => void;\r\n    private _rejecter!: (reason?: any) => void;\r\n    public promise: Promise<void>;\r\n\r\n    constructor() {\r\n        this.promise = new Promise((resolve, reject) => [this._resolver, this._rejecter] = [resolve, reject]);\r\n    }\r\n\r\n    public resolve(): void {\r\n        this._resolver!();\r\n    }\r\n\r\n    public reject(reason?: any): void {\r\n        this._rejecter!(reason);\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { CompletionMessage, HubMessage, IHubProtocol, InvocationMessage, MessageType, StreamItemMessage } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { TextMessageFormat } from \"./TextMessageFormat\";\r\n\r\nconst JSON_HUB_PROTOCOL_NAME: string = \"json\";\r\n\r\n/** Implements the JSON Hub Protocol. */\r\nexport class JsonHubProtocol implements IHubProtocol {\r\n\r\n    /** @inheritDoc */\r\n    public readonly name: string = JSON_HUB_PROTOCOL_NAME;\r\n    /** @inheritDoc */\r\n    public readonly version: number = 1;\r\n\r\n    /** @inheritDoc */\r\n    public readonly transferFormat: TransferFormat = TransferFormat.Text;\r\n\r\n    /** Creates an array of {@link @microsoft/signalr.HubMessage} objects from the specified serialized representation.\r\n     *\r\n     * @param {string} input A string containing the serialized representation.\r\n     * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n     */\r\n    public parseMessages(input: string, logger: ILogger): HubMessage[] {\r\n        // The interface does allow \"ArrayBuffer\" to be passed in, but this implementation does not. So let's throw a useful error.\r\n        if (typeof input !== \"string\") {\r\n            throw new Error(\"Invalid input for JSON hub protocol. Expected a string.\");\r\n        }\r\n\r\n        if (!input) {\r\n            return [];\r\n        }\r\n\r\n        if (logger === null) {\r\n            logger = NullLogger.instance;\r\n        }\r\n\r\n        // Parse the messages\r\n        const messages = TextMessageFormat.parse(input);\r\n\r\n        const hubMessages = [];\r\n        for (const message of messages) {\r\n            const parsedMessage = JSON.parse(message) as HubMessage;\r\n            if (typeof parsedMessage.type !== \"number\") {\r\n                throw new Error(\"Invalid payload.\");\r\n            }\r\n            switch (parsedMessage.type) {\r\n                case MessageType.Invocation:\r\n                    this._isInvocationMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.StreamItem:\r\n                    this._isStreamItemMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Completion:\r\n                    this._isCompletionMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Ping:\r\n                    // Single value, no need to validate\r\n                    break;\r\n                case MessageType.Close:\r\n                    // All optional values, no need to validate\r\n                    break;\r\n                default:\r\n                    // Future protocol changes can add message types, old clients can ignore them\r\n                    logger.log(LogLevel.Information, \"Unknown message type '\" + parsedMessage.type + \"' ignored.\");\r\n                    continue;\r\n            }\r\n            hubMessages.push(parsedMessage);\r\n        }\r\n\r\n        return hubMessages;\r\n    }\r\n\r\n    /** Writes the specified {@link @microsoft/signalr.HubMessage} to a string and returns it.\r\n     *\r\n     * @param {HubMessage} message The message to write.\r\n     * @returns {string} A string containing the serialized representation of the message.\r\n     */\r\n    public writeMessage(message: HubMessage): string {\r\n        return TextMessageFormat.write(JSON.stringify(message));\r\n    }\r\n\r\n    private _isInvocationMessage(message: InvocationMessage): void {\r\n        this._assertNotEmptyString(message.target, \"Invalid payload for Invocation message.\");\r\n\r\n        if (message.invocationId !== undefined) {\r\n            this._assertNotEmptyString(message.invocationId, \"Invalid payload for Invocation message.\");\r\n        }\r\n    }\r\n\r\n    private _isStreamItemMessage(message: StreamItemMessage): void {\r\n        this._assertNotEmptyString(message.invocationId, \"Invalid payload for StreamItem message.\");\r\n\r\n        if (message.item === undefined) {\r\n            throw new Error(\"Invalid payload for StreamItem message.\");\r\n        }\r\n    }\r\n\r\n    private _isCompletionMessage(message: CompletionMessage): void {\r\n        if (message.result && message.error) {\r\n            throw new Error(\"Invalid payload for Completion message.\");\r\n        }\r\n\r\n        if (!message.result && message.error) {\r\n            this._assertNotEmptyString(message.error, \"Invalid payload for Completion message.\");\r\n        }\r\n\r\n        this._assertNotEmptyString(message.invocationId, \"Invalid payload for Completion message.\");\r\n    }\r\n\r\n    private _assertNotEmptyString(value: any, errorMessage: string): void {\r\n        if (typeof value !== \"string\" || value === \"\") {\r\n            throw new Error(errorMessage);\r\n        }\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { DefaultReconnectPolicy } from \"./DefaultReconnectPolicy\";\r\nimport { HttpConnection } from \"./HttpConnection\";\r\nimport { HubConnection } from \"./HubConnection\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\nimport { IHubProtocol } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { IRetryPolicy } from \"./IRetryPolicy\";\r\nimport { HttpTransportType } from \"./ITransport\";\r\nimport { JsonHubProtocol } from \"./JsonHubProtocol\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { Arg, ConsoleLogger } from \"./Utils\";\r\n\r\nconst LogLevelNameMapping: {[k: string]: LogLevel} = {\r\n    trace: LogLevel.Trace,\r\n    debug: LogLevel.Debug,\r\n    info: LogLevel.Information,\r\n    information: LogLevel.Information,\r\n    warn: LogLevel.Warning,\r\n    warning: LogLevel.Warning,\r\n    error: LogLevel.Error,\r\n    critical: LogLevel.Critical,\r\n    none: LogLevel.None,\r\n};\r\n\r\nfunction parseLogLevel(name: string): LogLevel {\r\n    // Case-insensitive matching via lower-casing\r\n    // Yes, I know case-folding is a complicated problem in Unicode, but we only support\r\n    // the ASCII strings defined in LogLevelNameMapping anyway, so it's fine -anurse.\r\n    const mapping = LogLevelNameMapping[name.toLowerCase()];\r\n    if (typeof mapping !== \"undefined\") {\r\n        return mapping;\r\n    } else {\r\n        throw new Error(`Unknown log level: ${name}`);\r\n    }\r\n}\r\n\r\n/** A builder for configuring {@link @microsoft/signalr.HubConnection} instances. */\r\nexport class HubConnectionBuilder {\r\n    /** @internal */\r\n    public protocol?: IHubProtocol;\r\n    /** @internal */\r\n    public httpConnectionOptions?: IHttpConnectionOptions;\r\n    /** @internal */\r\n    public url?: string;\r\n    /** @internal */\r\n    public logger?: ILogger;\r\n\r\n    /** If defined, this indicates the client should automatically attempt to reconnect if the connection is lost. */\r\n    /** @internal */\r\n    public reconnectPolicy?: IRetryPolicy;\r\n\r\n    /** Configures console logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {LogLevel} logLevel The minimum level of messages to log. Anything at this level, or a more severe level, will be logged.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public configureLogging(logLevel: LogLevel): HubConnectionBuilder;\r\n\r\n    /** Configures custom logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {ILogger} logger An object implementing the {@link @microsoft/signalr.ILogger} interface, which will be used to write all log messages.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public configureLogging(logger: ILogger): HubConnectionBuilder;\r\n\r\n    /** Configures custom logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {string} logLevel A string representing a LogLevel setting a minimum level of messages to log.\r\n     *    See {@link https://docs.microsoft.com/aspnet/core/signalr/configuration#configure-logging|the documentation for client logging configuration} for more details.\r\n     */\r\n    public configureLogging(logLevel: string): HubConnectionBuilder;\r\n\r\n    /** Configures custom logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {LogLevel | string | ILogger} logging A {@link @microsoft/signalr.LogLevel}, a string representing a LogLevel, or an object implementing the {@link @microsoft/signalr.ILogger} interface.\r\n     *    See {@link https://docs.microsoft.com/aspnet/core/signalr/configuration#configure-logging|the documentation for client logging configuration} for more details.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public configureLogging(logging: LogLevel | string | ILogger): HubConnectionBuilder;\r\n    public configureLogging(logging: LogLevel | string | ILogger): HubConnectionBuilder {\r\n        Arg.isRequired(logging, \"logging\");\r\n\r\n        if (isLogger(logging)) {\r\n            this.logger = logging;\r\n        } else if (typeof logging === \"string\") {\r\n            const logLevel = parseLogLevel(logging);\r\n            this.logger = new ConsoleLogger(logLevel);\r\n        } else {\r\n            this.logger = new ConsoleLogger(logging);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use HTTP-based transports to connect to the specified URL.\r\n     *\r\n     * The transport will be selected automatically based on what the server and client support.\r\n     *\r\n     * @param {string} url The URL the connection will use.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withUrl(url: string): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use the specified HTTP-based transport to connect to the specified URL.\r\n     *\r\n     * @param {string} url The URL the connection will use.\r\n     * @param {HttpTransportType} transportType The specific transport to use.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withUrl(url: string, transportType: HttpTransportType): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use HTTP-based transports to connect to the specified URL.\r\n     *\r\n     * @param {string} url The URL the connection will use.\r\n     * @param {IHttpConnectionOptions} options An options object used to configure the connection.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withUrl(url: string, options: IHttpConnectionOptions): HubConnectionBuilder;\r\n    public withUrl(url: string, transportTypeOrOptions?: IHttpConnectionOptions | HttpTransportType): HubConnectionBuilder {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isNotEmpty(url, \"url\");\r\n\r\n        this.url = url;\r\n\r\n        // Flow-typing knows where it's at. Since HttpTransportType is a number and IHttpConnectionOptions is guaranteed\r\n        // to be an object, we know (as does TypeScript) this comparison is all we need to figure out which overload was called.\r\n        if (typeof transportTypeOrOptions === \"object\") {\r\n            this.httpConnectionOptions = { ...this.httpConnectionOptions, ...transportTypeOrOptions };\r\n        } else {\r\n            this.httpConnectionOptions = {\r\n                ...this.httpConnectionOptions,\r\n                transport: transportTypeOrOptions,\r\n            };\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use the specified Hub Protocol.\r\n     *\r\n     * @param {IHubProtocol} protocol The {@link @microsoft/signalr.IHubProtocol} implementation to use.\r\n     */\r\n    public withHubProtocol(protocol: IHubProtocol): HubConnectionBuilder {\r\n        Arg.isRequired(protocol, \"protocol\");\r\n\r\n        this.protocol = protocol;\r\n        return this;\r\n    }\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to automatically attempt to reconnect if the connection is lost.\r\n     * By default, the client will wait 0, 2, 10 and 30 seconds respectively before trying up to 4 reconnect attempts.\r\n     */\r\n    public withAutomaticReconnect(): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to automatically attempt to reconnect if the connection is lost.\r\n     *\r\n     * @param {number[]} retryDelays An array containing the delays in milliseconds before trying each reconnect attempt.\r\n     * The length of the array represents how many failed reconnect attempts it takes before the client will stop attempting to reconnect.\r\n     */\r\n    public withAutomaticReconnect(retryDelays: number[]): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to automatically attempt to reconnect if the connection is lost.\r\n     *\r\n     * @param {IRetryPolicy} reconnectPolicy An {@link @microsoft/signalR.IRetryPolicy} that controls the timing and number of reconnect attempts.\r\n     */\r\n    public withAutomaticReconnect(reconnectPolicy: IRetryPolicy): HubConnectionBuilder;\r\n    public withAutomaticReconnect(retryDelaysOrReconnectPolicy?: number[] | IRetryPolicy): HubConnectionBuilder {\r\n        if (this.reconnectPolicy) {\r\n            throw new Error(\"A reconnectPolicy has already been set.\");\r\n        }\r\n\r\n        if (!retryDelaysOrReconnectPolicy) {\r\n            this.reconnectPolicy = new DefaultReconnectPolicy();\r\n        } else if (Array.isArray(retryDelaysOrReconnectPolicy)) {\r\n            this.reconnectPolicy = new DefaultReconnectPolicy(retryDelaysOrReconnectPolicy);\r\n        } else {\r\n            this.reconnectPolicy = retryDelaysOrReconnectPolicy;\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Creates a {@link @microsoft/signalr.HubConnection} from the configuration options specified in this builder.\r\n     *\r\n     * @returns {HubConnection} The configured {@link @microsoft/signalr.HubConnection}.\r\n     */\r\n    public build(): HubConnection {\r\n        // If httpConnectionOptions has a logger, use it. Otherwise, override it with the one\r\n        // provided to configureLogger\r\n        const httpConnectionOptions = this.httpConnectionOptions || {};\r\n\r\n        // If it's 'null', the user **explicitly** asked for null, don't mess with it.\r\n        if (httpConnectionOptions.logger === undefined) {\r\n            // If our logger is undefined or null, that's OK, the HttpConnection constructor will handle it.\r\n            httpConnectionOptions.logger = this.logger;\r\n        }\r\n\r\n        // Now create the connection\r\n        if (!this.url) {\r\n            throw new Error(\"The 'HubConnectionBuilder.withUrl' method must be called before building the connection.\");\r\n        }\r\n        const connection = new HttpConnection(this.url, httpConnectionOptions);\r\n\r\n        return HubConnection.create(\r\n            connection,\r\n            this.logger || NullLogger.instance,\r\n            this.protocol || new JsonHubProtocol(),\r\n            this.reconnectPolicy);\r\n    }\r\n}\r\n\r\nfunction isLogger(logger: any): logger is ILogger {\r\n    return logger.log !== undefined;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Everything that users need to access must be exported here. Including interfaces.\r\nexport { AbortSignal } from \"./AbortController\";\r\nexport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nexport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nexport { DefaultHttpClient } from \"./DefaultHttpClient\";\r\nexport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\nexport { HubConnection, HubConnectionState } from \"./HubConnection\";\r\nexport { HubConnectionBuilder } from \"./HubConnectionBuilder\";\r\nexport { MessageType, MessageHeaders, HubMessage, HubMessageBase, HubInvocationMessage, InvocationMessage, StreamInvocationMessage, StreamItemMessage, CompletionMessage,\r\n    PingMessage, CloseMessage, CancelInvocationMessage, IHubProtocol } from \"./IHubProtocol\";\r\nexport { ILogger, LogLevel } from \"./ILogger\";\r\nexport { HttpTransportType, TransferFormat, ITransport } from \"./ITransport\";\r\nexport { IStreamSubscriber, IStreamResult, ISubscription } from \"./Stream\";\r\nexport { NullLogger } from \"./Loggers\";\r\nexport { JsonHubProtocol } from \"./JsonHubProtocol\";\r\nexport { Subject } from \"./Subject\";\r\nexport { IRetryPolicy, RetryContext } from \"./IRetryPolicy\";\r\nexport { VERSION } from \"./Utils\";\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// This is where we add any polyfills we'll need for the browser. It is the entry module for browser-specific builds.\r\n\r\n// Copy from Array.prototype into Uint8Array to polyfill on IE. It's OK because the implementations of indexOf and slice use properties\r\n// that exist on Uint8Array with the same name, and JavaScript is magic.\r\n// We make them 'writable' because the Buffer polyfill messes with it as well.\r\nif (!Uint8Array.prototype.indexOf) {\r\n    Object.defineProperty(Uint8Array.prototype, \"indexOf\", {\r\n        value: Array.prototype.indexOf,\r\n        writable: true,\r\n    });\r\n}\r\nif (!Uint8Array.prototype.slice) {\r\n    Object.defineProperty(Uint8Array.prototype, \"slice\", {\r\n        // wrap the slice in Uint8Array so it looks like a Uint8Array.slice call\r\n        // eslint-disable-next-line object-shorthand\r\n        value: function(start?: number, end?: number) { return new Uint8Array(Array.prototype.slice.call(this, start, end)); },\r\n        writable: true,\r\n    });\r\n}\r\nif (!Uint8Array.prototype.forEach) {\r\n    Object.defineProperty(Uint8Array.prototype, \"forEach\", {\r\n        value: Array.prototype.forEach,\r\n        writable: true,\r\n    });\r\n}\r\n\r\nexport * from \"./index\";\r\n"], "sourceRoot": ""}