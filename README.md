---
description: Documentation
---

# Bot Designer

![](https://media.giphy.com/media/h408T6Y5GfmXBKW62l/giphy.gif)

LINK TO Documented [APIs](https://designer-doc-iabushammah.vercel.app/).

LINK TO Backend [REPO](https://github.com/nlp-infotointell/bot-designer-server).

LINK TO Frontent [REPO](https://github.com/nlp-infotointell/i2i-web).

ERD \(Still under construction\) [LINK](https://lucid.app/lucidchart/invitations/accept/inv_d9e62f20-32e5-47bd-8e4a-f5d41bdc7660?viewport_loc=156%2C-14%2C2413%2C1368%2C0_0)

Diagrams \(Still under construction\) [Link](https://drive.google.com/file/d/1iDNAsuo0pcg2YmhBCXKMYHyWtxnjnFPw/view?usp=sharing)

Shell  code to repreduce the doc site \(Using <PERSON>'s Zeit account\) [HERE](https://github.com/nlp-infotointell/bot-designer-server/blob/master/doc.sh)

Backend STACK: ExpressJS, Sequalize and DigitalOcean.

Frontend Stack: React.JS, Bootstrap & GoogleCharts.

Backend Azure service name -- bot-designer-server

Frontend Azure service name -- bot-designer-web

Bot Azure service name -- mock-bot-a083

automated Mailing is done using Infotointell & SearChat Domains that are connected to <NAME_EMAIL> account, note that this 2FA



