const Custom_User = require("../Models/Custom.User");

const params = {
custom_user_id: "custom_user_id",
email: "email",
};

const getUserIdParam = (req, res, next, custom_user_id) => {
    Custom_User.findOne({ where: { custom_user_id } }).then((custom_user) => {
    if (custom_user) {
      req.custom_user = custom_user;
      return next();
    }
    return res.send({ message: "user not found" });
  });
};

const getUserEmailParam = (req, res, next, email) => {
    Custom_User.findOne({ where: { email } }).then((custom_user) => {
    if (custom_user) {
      req.custom_user = custom_user;
      return next();
    }
    return res.send({ message: "user not found" });
  });
};

module.exports = {
  methods: {
    getUserIdParam,
    getUserEmailParam,
  },
  params,
};
