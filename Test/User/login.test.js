const expect = require("chai").expect;
const request = require("supertest");
const app = require("../../app");
const { Routes } = require("../../Routes/routes.user");

test("successful login", function (done) {
  request(app)
    .post("/api" + Routes.login)
    .send({
      email: "<EMAIL>",
      company_name: "TheTechLeadHD",
      password: "the techlead lbc 1999",
      username: "the techlead",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(200);
      done();
    });
});

test("wrong login password", function (done) {
  request(app)
    .post("/api" + Routes.login)
    .send({
      email: "<EMAIL>",
      company_name: "TheTechLeadHD",
      password: "the techlead lbc 1999 123",
      username: "the techlead",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(401);
      done();
    });
});

test("missing email login", function (done) {
  request(app)
    .post("/api" + Routes.login)
    .send({
      company_name: "TheTechLeadHD",
      password: "the techlead lbc 1999 123",
      username: "the techlead",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("missing password login", function (done) {
  request(app)
    .post("/api" + Routes.login)
    .send({
      email: "<EMAIL>",
      company_name: "TheTechLeadHD",
      username: "the techlead",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});
