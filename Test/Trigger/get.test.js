const expect = require("chai").expect;
const request = require("supertest");
const app = require("../../app");
const { Routes } = require("../../Routes/routes.trigger");

test("get trigger missing trigger_id", function (done) {
  request(app)
    .get("/api" + Routes.base + "?trigger_id=")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("get trigger missing query", function (done) {
  request(app)
    .get("/api" + Routes.base + "")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("get trigger missing query delimiter", function (done) {
  request(app)
    .get("/api" + Routes.base + "trigger_id=")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(404);
      done();
    });
});

test("get trigger", function (done) {
  request(app)
    .get("/api" + Routes.base + "?trigger_id=1")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(200);
      done();
    });
});
