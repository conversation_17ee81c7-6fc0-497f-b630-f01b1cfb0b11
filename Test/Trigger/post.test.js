const expect = require("chai").expect;
const request = require("supertest");
const app = require("../../app");
const { Routes } = require("../../Routes/routes.trigger");

test("create trigger", function (done) {
  request(app)
    .post("/api" + Routes.base)
    .send({
      dialog_id: 1,
      key: "living is easy",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(200);
      done();
    });
});

test("create trigger missing dialog_id", function (done) {
  request(app)
    .post("/api" + Routes.base)
    .send({
      key: "living is easy",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("create trigger missing key", function (done) {
  request(app)
    .post("/api" + Routes.base)
    .send({
      dialog_id: 1,
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});
