const expect = require("chai").expect;
const request = require("supertest");
const app = require("../../app");
const { Routes } = require("../../Routes/routes.trigger");

test("get dialog triggers missing dialog_id", function (done) {
  request(app)
    .get("/api" + Routes.all + "?dialog_id=")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("get dialog triggers missing query", function (done) {
  request(app)
    .get("/api" + Routes.all + "")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("get dialog triggers missing query delimiter", function (done) {
  request(app)
    .get("/api" + Routes.all + "dialog_id=")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(404);
      done();
    });
});

test("get dialog triggers", function (done) {
  request(app)
    .get("/api" + Routes.all + "?dialog_id=1")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(200);
      done();
    });
});
