const expect = require("chai").expect;
const request = require("supertest");
const app = require("../../app");
const { Routes } = require("../../Routes/routes.faq");

test("get bot faqs missing bot_id", function (done) {
  request(app)
    .get("/api" + Routes.many + "?bot_id=")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("get bot faqs missing bot_id 2", function (done) {
  request(app)
    .get("/api" + Routes.many + "?bot_id")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("get bot faqs missing bot_id 3", function (done) {
  request(app)
    .get("/api" + Routes.many + "bot_id=")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(404);
      done();
    });
});

test("get bot faqs missing query bot_id", function (done) {
  request(app)
    .get("/api" + Routes.many)
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("get bot faqs", function (done) {
  request(app)
    .get("/api" + Routes.many + "?bot_id=1")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(200);
      done();
    });
});
