const expect = require("chai").expect;
const request = require("supertest");
const app = require("../../app");
const { Routes } = require("../../Routes/routes.faq");

test("get bot faqs missing faq_id", function (done) {
  request(app)
    .get("/api" + Routes.base + "?faq_id=")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("get bot faqs missing faq_id", function (done) {
  request(app)
    .get("/api" + Routes.base + "?faq_id")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("get bot faqs missing faq_id", function (done) {
  request(app)
    .get("/api" + Routes.base + "faq_id=")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(404);
      done();
    });
});

test("get bot faqs missing query faq_id", function (done) {
  request(app)
    .get("/api" + Routes.base)
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("get bot faqs missing faq_id", function (done) {
  request(app)
    .get("/api" + Routes.base + "?faq_id=1")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(200);
      done();
    });
});
