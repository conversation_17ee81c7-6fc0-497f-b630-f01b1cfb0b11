const expect = require("chai").expect;
const request = require("supertest");
const app = require("../../app");
const { Routes } = require("../../Routes/routes.faq");

test("update faq", function (done) {
  request(app)
    .put("/api" + Routes.base)
    .send({
      bot_id: 1,
      faq_id: 1,
      question: "summer time",
      answer: "living is easy",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(200);
      done();
    });
});

test("update faq missing bot_id", function (done) {
  request(app)
    .put("/api" + Routes.base)
    .send({
      faq_id: 1,
      question: "summer time",
      answer: "living is easy",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("update faq missing faq_id", function (done) {
  request(app)
    .put("/api" + Routes.base)
    .send({
      bot_id: 1,
      question: "summer time",
      answer: "living is easy",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("update faq missing body", function (done) {
  request(app)
    .put("/api" + Routes.base)
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("update faq missing answer", function (done) {
  request(app)
    .put("/api" + Routes.base)
    .send({
      faq_id: 1,
      bot_id: 1,
      question: "summer time",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("update faq missing question", function (done) {
  request(app)
    .put("/api" + Routes.base)
    .send({
      faq_id: 1,
      bot_id: 1,
      answer: "summer time",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});
