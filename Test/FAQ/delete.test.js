const expect = require("chai").expect;
const request = require("supertest");
const app = require("../../app");
const { Routes } = require("../../Routes/routes.faq");

test("delete faq", function (done) {
  request(app)
    .delete("/api" + Routes.base)
    .send({
      bot_id: 1,
      faq_id: 2,
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(200);
      done();
    });
});

test("delete faq missing bot_id", function (done) {
  request(app)
    .delete("/api" + Routes.base)
    .send({
      faq_id: 1,
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("delete faq missing faq_id", function (done) {
  request(app)
    .delete("/api" + Routes.base)
    .send({
      bot_id: 1,
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("delete faq missing body", function (done) {
  request(app)
    .delete("/api" + Routes.base)
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});
