const expect = require("chai").expect;
const request = require("supertest");
const app = require("../../app");
const { Routes } = require("../../Routes/routes.faq");

test("create faq", function (done) {
  request(app)
    .post("/api" + Routes.base)
    .send({
      bot_id: 1,
      question: "summer time",
      answer: "living is easy",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(200);
      done();
    });
});

test("create faq missing answer", function (done) {
  request(app)
    .post("/api" + Routes.base)
    .send({
      bot_id: 1,
      question: "summer time",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("create faq missing question", function (done) {
  request(app)
    .post("/api" + Routes.base)
    .send({
      bot_id: 1,
      answer: "living is easy",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("create faq missing bot_id", function (done) {
  request(app)
    .post("/api" + Routes.base)
    .send({
      question: "summer time",
      answer: "living is easy",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("create faq missing body", function (done) {
  request(app)
    .post("/api" + Routes.base)
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});
