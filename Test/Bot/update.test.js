const expect = require("chai").expect;
const request = require("supertest");
const app = require("../../app");
const { Routes } = require("../../Routes/routes.bot");

test("update bot", function (done) {
  request(app)
    .put("/api" + Routes.base)
    .send({
      user_id: 1,
      bot_id: 1,
      name: "lbc",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(200);
      done();
    });
});

test("update bot missing info", function (done) {
  request(app)
    .put("/api" + Routes.base)
    .send({})
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("update bot missing user_id", function (done) {
  request(app)
    .put("/api" + Routes.base)
    .send({
      bot_id: 1,
      name: "lbc",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("update bot missing bot_id", function (done) {
  request(app)
    .put("/api" + Routes.base)
    .send({
      user_id: 1,
      name: "lbc",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});
