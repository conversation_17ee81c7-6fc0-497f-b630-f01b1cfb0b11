const expect = require("chai").expect;
const request = require("supertest");
const app = require("../../app");
const { Routes } = require("../../Routes/routes.bot");

test("delete bot", function (done) {
  request(app)
    .delete("/api" + Routes.base)
    .send({
      user_id: 1,
      bot_id: 2,
      name: "lbc",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(200);
      done();
    });
});

test("delete bot no info", function (done) {
  request(app)
    .delete("/api" + Routes.base)
    .send({})
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("delete bot missing user_id", function (done) {
  request(app)
    .delete("/api" + Routes.base)
    .send({
      bot_id: 2,
      name: "lbc",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("delete bot missing bot_id", function (done) {
  request(app)
    .delete("/api" + Routes.base)
    .send({
      user_id: 1,
      name: "lbc",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});
