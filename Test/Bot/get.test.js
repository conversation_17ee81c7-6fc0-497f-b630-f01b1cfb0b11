const expect = require("chai").expect;
const request = require("supertest");
const app = require("../../app");
const { Routes } = require("../../Routes/routes.bot");

test("get bot", function (done) {
  request(app)
    .get("/api" + Routes.base + "?bot_id=1")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(200);
      done();
    });
});

test("get bot missing id", function (done) {
  request(app)
    .get("/api" + Routes.base + "?bot_id=")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("get bot missing id 2", function (done) {
  request(app)
    .get("/api" + Routes.base + "?bot_id")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("get bot missing id 2", function (done) {
  request(app)
    .get("/api" + Routes.base + "bot_id")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(404);
      done();
    });
});

test("get bot missing query id", function (done) {
  request(app)
    .get("/api" + Routes.base)
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});
