const expect = require("chai").expect;
const request = require("supertest");
const app = require("../../app");
const { Routes } = require("../../Routes/routes.bot");

test("add bot", function (done) {
  request(app)
    .post("/api" + Routes.base)
    .send({
      user_id: 1,
      name: "sa31",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(200);
      done();
    });
});

test("add another bot with same name", function (done) {
  request(app)
    .post("/api" + Routes.base)
    .send({
      user_id: 1,
      name: "sa31",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(200);
      done();
    });
});

test("add bot missing user id", function (done) {
  request(app)
    .post("/api" + Routes.base)
    .send({
      name: "sa31",
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("add bot missing bot name", function (done) {
  request(app)
    .post("/api" + Routes.base)
    .send({
      user_id: 1,
    })
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});
