const expect = require("chai").expect;
const request = require("supertest");
const app = require("../../app");
const { Routes } = require("../../Routes/routes.bot");

test("get all bots missing id", function (done) {
  request(app)
    .get("/api" + Routes.all + "?user_id=")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("get all bots missing id 2", function (done) {
  request(app)
    .get("/api" + Routes.all + "?user_id")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("get all bots missing id 3", function (done) {
  request(app)
    .get("/api" + Routes.all + "user_id=")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(404);
      done();
    });
});

test("get all bots missing query", function (done) {
  request(app)
    .get("/api" + Routes.all)
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});

test("get all bots successfully", function (done) {
  request(app)
    .get("/api" + Routes.all + "?user_id=1")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(200);
      done();
    });
});

test("get all bots missing query and adding bot_id", function (done) {
  request(app)
    .get("/api" + Routes.all + "?bot_id=1")
    .end(function (err, response) {
      expect(response.statusCode).to.equal(422);
      done();
    });
});
