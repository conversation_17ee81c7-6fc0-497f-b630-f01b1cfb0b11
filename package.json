{"name": "bot-designer-server", "version": "1.0.0", "description": "", "engines": {"node": ">=18.0.0 <20"}, "scripts": {"start:dev": "nodemon server.js", "start:dev:static": "node server.js", "test": "jest --silent --runInBand --detectOpenHandles --forceExit", "start": "NODE_ENV=production node bin/www", "start:prod": "NODE_ENV=production nodemon bin/www", "start:do": "NODE_ENV=production node server.js"}, "repository": {"type": "git", "url": "git+https://github.com/nlp-infotointell/bot-designer-server.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/nlp-infotointell/bot-designer-server/issues"}, "homepage": "https://github.com/nlp-infotointell/bot-designer-server#readme", "dependencies": {"@langchain/community": "^0.3.19", "@microsoft/signalr": "^6.0.9", "@sendgrid/mail": "^7.4.2", "axios": "^1.3.4", "binary-search": "^1.3.6", "body-parser": "^1.19.0", "chai": "^4.3.4", "clone": "^2.1.2", "cors": "^2.8.5", "damerau-levenshtein": "^1.0.6", "dotenv": "^16.4.7", "events": "^3.3.0", "express": "^4.17.1", "form-data": "^4.0.0", "fuzzy-search": "^3.2.1", "google-maps-image-api-url": "^1.0.3", "google-translate-api-x": "^10.7.1", "googleapis": "^95.0.0", "hnswlib-node": "^3.0.0", "i": "^0.3.6", "ioredis": "^5.2.3", "jsonwebtoken": "^9.0.2", "jssoup": "0.0.15", "multer": "^1.4.4", "natural": "^5.0.3", "nodemailer": "^6.9.15", "npm": "^7.8.0", "pdf.js-extract": "^0.2.0", "pg": "^8.8.0", "pg-hstore": "^2.3.4", "sequelize": "^5.22.5", "sharp": "^0.28.3", "supertest": "^6.1.3", "tedious": "^11.0.5", "twilio": "^3.65.0", "utf8": "^3.0.0", "winston": "^3.3.3"}}