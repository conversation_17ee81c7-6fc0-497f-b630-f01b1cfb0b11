const storageServerUrl = "https://bot-server-2.azurewebsites.net";
const serverUrl = "https://bot-designer-server-2.azurewebsites.net";
const builderUrl = "https://app.searchat.com";
const localBuilderUrl = "http://localhost:3000";
const stagingBuilderUrl =
  "https://searchat-builder-staging-eyxob.ondigitalocean.app";
const wolvesUrl = "https://platform.searchat.com";
const BOT_MESSAGING_SERVER_URL = "https://i2i-messaging.azurewebsites.net/api";

// const i2i_RAG_SERVER = "https://371bmwjc-8000.uks1.devtunnels.ms/api/v1/";
const i2i_RAG_SERVER =
  "http://i2i-rag.dubmafhwd3ekana6.uaenorth.azurecontainer.io:8080/api/v1/";
// const i2i_RAG_SERVER = 'http://localhost:5143/api/v1/'

const MEDIA_STORAGE_URL = "https://infotointell.fra1.digitaloceanspaces.com";

const AZURE_BLOB_STORAGE_CONNECTION_STRING =
  "DefaultEndpointsProtocol=https;AccountName=mock;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net";
const AZURE_BLOB_STORAGE_CONTAINER_NAME = "mock-container";

const captcha_url = "https://www.google.com/recaptcha/api/siteverify";
const captcha_secret_key = "6Le49rAqAAAAAG6z46R-Q6lGglKRtc5wNky7fLSq"; // NOTE: //  change required from raed

module.exports = {
  storageServerUrl,
  serverUrl,
  builderUrl,
  localBuilderUrl,
  wolvesUrl,
  stagingBuilderUrl,
  MEDIA_STORAGE_URL,
  AZURE_BLOB_STORAGE_CONNECTION_STRING,
  AZURE_BLOB_STORAGE_CONTAINER_NAME,
  BOT_MESSAGING_SERVER_URL,
  i2i_RAG_SERVER,
  captcha_url,
  captcha_secret_key,
};
