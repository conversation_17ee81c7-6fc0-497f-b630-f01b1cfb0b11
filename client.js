const signalR = require("./signalr");
const { v4: uuidv4 } = require("uuid");

const user_id = uuidv4();

const conversationID = "aa-bb-cc";
const bot_id = 403;
const bot_name = "ssc";
const activitiesHistory = [];

const socketServer = "https://i2i-messaging.azurewebsites.net";

console.log("user_id", user_id);

const negotiationURL = `${socketServer}/api?userId=${user_id}&conversation_id=${conversationID}&channel=webchat&bot_id=${bot_id}&type=client`;

const _connection = new signalR.HubConnectionBuilder()
  .withUrl(negotiationURL)
  .build();
_connection
  .start()
  .then(() => console.log("SHOULD BE WORKING"))
  .catch((err) => {});
_connection.onclose(() => {
  console.log("ONCLOSE");
  closeSession({
    type: "client",
    userId: user_id,
  });
});

_connection.on("chatMessage", (message) => {
  console.log("chatMessage message", message);
  //   setIncomingActivity({
  //     from: "bot",
  //     type: "message",
  //     text: message,
  //   });
});
