const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class CountryPlan extends Model {}

CountryPlan.init(
  {
    country_plan_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    conversion_ration: { type: DataTypes.FLOAT },
    country: { type: DataTypes.STRING("50") },
    currency: { type: DataTypes.STRING("10") },
  },
  { sequelize, modelName: "bot_designer_country_plan" }
);

CountryPlan.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
};

// CountryPlan.create({
//   country: "jordan",
//   conversion_ration: 0.71,
//   currency: "JOD",
// });

module.exports = CountryPlan;
