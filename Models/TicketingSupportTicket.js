const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const TicketingIntegration = require("./TicketingIntegration");
const TicketingSupportAgent = require("./TicketingSupportAgent");
const Bot = require("./Bot");
const TicketingCategory = require("./TicketingCategory");

class TicketingSupportTicket extends Model {}

TicketingSupportTicket.init(
  {
    ticket_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    bot_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    channel: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    conversation_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    status: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    customer_name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    customer_email: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    customer_phone: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    category_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    title: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    description: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    ticketing_integration_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    support_agent_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    ticket_uuid: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    time_zone: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "bot_designer_ticketing_support_tickets",
  }
);

TicketingSupportTicket.belongsTo(TicketingIntegration, {
  foreignKey: "ticketing_integration_id",
  as: "ticketing_integration",
});

TicketingIntegration.hasMany(TicketingSupportTicket, {
  foreignKey: "ticketing_integration_id",
  as: "tickets",
});

TicketingSupportTicket.belongsTo(TicketingSupportAgent, {
  foreignKey: "support_agent_id",
  as: "support_agent",
});

TicketingSupportAgent.hasMany(TicketingSupportTicket, {
  foreignKey: "support_agent_id",
  as: "tickets",
});

TicketingSupportTicket.belongsTo(Bot, {
  foreignKey: "bot_id",
  as: "bot",
});

TicketingSupportTicket.belongsTo(TicketingCategory, {
  foreignKey: "category_id",
  as: "category",
});

TicketingSupportTicket.prototype.toJSON = function toJSON() {
  return {
    ticket_id: this.ticket_id,
    bot_id: this.bot_id,
    channel: this.channel,
    conversation_id: this.conversation_id,
    status: this.status,
    customer_name: this.customer_name,
    customer_email: this.customer_email,
    customer_phone: this.customer_phone,
    category_id: this.category_id,
    title: this.title,
    description: this.description,
    ticketing_integration_id: this.ticketing_integration_id,
    support_agent_id: this.support_agent_id,
    ticket_uuid: this.ticket_uuid,
    time_zone: this.time_zone,
    category: this.category,
    comments: this.comments,
    bot: this.bot,
    createdAt: this.createdAt,
    updatedAt: this.updatedAt,
  }
}

module.exports = TicketingSupportTicket;
