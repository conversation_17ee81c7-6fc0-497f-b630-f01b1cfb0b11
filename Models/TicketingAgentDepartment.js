const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const TicketingDepartment = require("./TicketingDepartment");

class TicketingAgentDepartment extends Model {}

TicketingAgentDepartment.init(
  {
    agent_department_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    department_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    support_agent_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "bot_designer_ticketing_agents_departments",
  }
);

TicketingAgentDepartment.belongsTo(TicketingDepartment, {
  foreignKey: "department_id",
  as: "department",
});

TicketingDepartment.hasMany(TicketingAgentDepartment, {
  foreignKey: "department_id",
  as: "agents",
});

module.exports = TicketingAgentDepartment;
