const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class NamedEntities extends Model {}

NamedEntities.init(
  {
    entity_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    entity_name: {
      type: DataTypes.STRING,
      unique: true,
      allowNull: false,
    },
    entity_type: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    entity_subType: {
      type: DataTypes.STRING,
    },
  },
  { sequelize, modelName: "bot_designer_named_entities" }
);

NamedEntities.prototype.updateInfo = function updateInfo(info) {
  this.entity_type = info.entity_type ? info.entity_type : this.entity_type;
  this.entity_subType = info.entity_subType ? info.entity_subType : this.entity_subType;
};

module.exports = NamedEntities;
