const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class ItemFeature extends Model {}

ItemFeature.init(
  {
    item_feature_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    feature_value: {
      allowNull: false,
      type: DataTypes.STRING,
    }
  
  },


  { sequelize, modelName: "bot_designer_item_features" },
 
);

ItemFeature.prototype.updateInfo = function updateInfo(info) {
  this.feature_value = info.feature_value
    ? info.feature_value
    : this.feature_value;
};

module.exports = ItemFeature;
