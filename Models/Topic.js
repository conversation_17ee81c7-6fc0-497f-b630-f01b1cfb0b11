const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const Trigger = require("./Trigger");
const Dialogs = require("./Dialogs");
const FAQ = require("./FAQ");
const Bot = require("./Bot");

class Topic extends Model {}

Topic.init(
  {
    topic_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    display_name: {
      type: DataTypes.STRING,
    },
    keywords: {
      type: DataTypes.STRING,
    },
    lemmatized_keywords: {
        type: DataTypes.STRING,
    },
    bot_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
    }
  },
  { sequelize, modelName: "bot_designer_topics" }
);


Topic.prototype.updateInfo = function updateInfo(info) {
    this.display_name = info.display_name ? info.display_name : this.display_name;
    this.keywords = info.keywords ? JSON.stringify(info.keywords) : this.keywords;
    this.lemmatized_keywords = info.lemmatized_keywords ? JSON.stringify(info.lemmatized_keywords) : this.lemmatized_keywords;
};

Topic.prototype.toJSON = function toJSON() {
  return {
      topic_id: this.topic_id,
      display_name: this.display_name,
      bot_id: this.bot_id,
      keywords: JSON.parse(this.keywords),
      lemmatized_keywords: JSON.parse(this.lemmatized_keywords),
    };
};

// Topic.hasMany(Trigger, { foreignKey: "topic_id" });
// Topic.hasMany(FAQ, { foreignKey: "topic_id" });
// Topic.hasMany(Dialogs, { foreignKey: "topic_id" });
// Topic.belongsTo(Bot, { foreignKey: "bot_id" });

module.exports = Topic;
