const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Employee extends Model { }

Employee.init(
    {
        employee_id: {
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
            type: DataTypes.INTEGER,
        },
        user_gender: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        phone_number: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        user_age: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        nationality: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        employment_status: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        document_type: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        personal_number: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        national_number: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        ssc_status: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        job_type: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        governorate: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        income: {
            type: DataTypes.STRING,
            allowNull: false,
        }
    },
    { sequelize, modelName: "bot_designer_customer_ilo_employee" }
);


module.exports = Employee;
