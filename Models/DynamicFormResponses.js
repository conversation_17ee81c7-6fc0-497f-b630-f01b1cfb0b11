const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const DynamicFormSchema = require("./DynamicFormSchema");

class DynamicFormResponse extends Model {}
DynamicFormResponse.init(
  {
    dynamic_form_response_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    dynamic_form_schema_id: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    
    response: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  },
  { sequelize, modelName: "bot_designer_dynamic_form_responses" }
);

DynamicFormSchema.hasMany(DynamicFormResponse, {
  foreignKey: "dynamic_form_schema_id",
});
DynamicFormResponse.belongsTo(DynamicFormSchema, {
  foreignKey: "dynamic_form_schema_id",
});

module.exports = DynamicFormResponse;
