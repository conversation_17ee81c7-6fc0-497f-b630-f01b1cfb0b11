const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const TicketingSupportTicket = require("./TicketingSupportTicket");
const TicketingSupportAgent = require("./TicketingSupportAgent");

class TicketingComment extends Model {}

TicketingComment.init(
  {
    comment_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    ticket_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    support_agent_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    sender: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    comment: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    has_attachments: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "bot_designer_ticketing_comments",
  }
);

TicketingComment.belongsTo(TicketingSupportTicket, {
  foreignKey: "ticket_id",
  as: "ticket",
});

TicketingSupportTicket.hasMany(TicketingComment, {
  foreignKey: "ticket_id",
  as: "comments",
});

TicketingComment.belongsTo(TicketingSupportAgent, {
  foreignKey: "support_agent_id",
  as: "support_agent",
});

TicketingSupportAgent.hasMany(TicketingComment, {
  foreignKey: "support_agent_id",
  as: "comments",
});

module.exports = TicketingComment;
