const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class MonitorBot extends Model {}

MonitorBot.init(
  {
    monitor_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    active: {
      defaultValue: false,
      type: DataTypes.BOOLEAN,
    },
    dashboard: {
      defaultValue: false,
      type: DataTypes.BOOLEAN,
    },
    reservation: {
      defaultValue: false,
      type: DataTypes.BOOLEAN,
    },
    order: {
      defaultValue: false,
      type: DataTypes.BOOLEAN,
    },
  },
  { sequelize, modelName: "bot_designer_monitor_bot" }
);

MonitorBot.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
};

MonitorBot.prototype.toJSON = function () {
  return {
    monitor_id: this.monitor_id,
    order: this.order,
    dashboard: this.dashboard,
    active: this.active,
    reservation: this.reservation,
    email: this.email,
  };
};

module.exports = MonitorBot;
