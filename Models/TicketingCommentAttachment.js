const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const TicketingComment = require("./TicketingComment");

class TicketingCommentAttachment extends Model {}

TicketingCommentAttachment.init(
  {
    attachment_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    comment_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    url: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false
    }
  },
  {
    sequelize,
    modelName: "bot_designer_ticketing_comment_attacments",
  }
);

TicketingCommentAttachment.belongsTo(TicketingComment, {
  foreignKey: "comment_id",
  as: "comment",
});

TicketingComment.hasMany(TicketingCommentAttachment, {
  foreignKey: "comment_id",
  as: "attachments",
});

module.exports = TicketingCommentAttachment;
