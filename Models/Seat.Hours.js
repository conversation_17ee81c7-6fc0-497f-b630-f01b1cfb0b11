const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class SeatHours extends Model {}

SeatHours.init(
  {
    hr_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    day: {
      type: DataTypes.STRING,
    },
    from: {
      allowNull: false,
      type: DataTypes.FLOAT,
    },
    to: {
      allowNull: false,
      type: DataTypes.FLOAT,
    },
  },
  { sequelize, modelName: "bot_designer_livechat_hours" }
);

module.exports = SeatHours;
