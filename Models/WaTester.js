const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class WhatsAppTester extends Model {}

WhatsAppTester.init(
  {
    whatsapp_tester_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  },
  { sequelize, modelName: "bot_designer_whatsapp_testers" }
);

WhatsAppTester.prototype.updateInfo = function (info) {
  this.phone = info.phone;
};

module.exports = WhatsAppTester;
