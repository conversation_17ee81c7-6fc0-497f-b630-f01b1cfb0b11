const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const SeatHours = require("./Seat.Hours");
const SeatConversation = require("./Seat.Conversation");
const SeatMessage = require("./Seat.Message");

class SeatBot extends Model {}

SeatBot.init(
  {
    seatbot_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    email: {
      type: DataTypes.STRING,
    },
    current_conversation: {
      type: DataTypes.INTEGER,
    },
  },
  { sequelize, modelName: "bot_designer_livechat_bots" }
);

SeatBot.hasMany(SeatHours, { foreignKey: "seatbot_id" });

module.exports = SeatBot;
