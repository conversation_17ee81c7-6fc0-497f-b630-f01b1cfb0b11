const sequelize = require("../db");
const { DataTypes, Model, Sequelize } = require("sequelize");
const Bot = require("./Bot");

class LLMIntegration extends Model {}
LLMIntegration.init(
  {
    LLM_integration_id: {
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
        type: DataTypes.INTEGER,
    },
    bot_id:{
        type: DataTypes.INTEGER,
        allowNull: true
    },
    llm_type:{
        type: DataTypes.STRING,
        allowNull: true
    },
    llm_model:{
        type: DataTypes.STRING,
        allowNull: true
    },
    llm_key: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    llm_temperature:{
        type: DataTypes.FLOAT,
        allowNull: true,
    },
    chunk_size:{ 
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    chunk_overlap:{
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    chat_history:{
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
    },
    store_url:{ 
        type: DataTypes.STRING,
        allowNull: true,
    },
    status_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
    },
    store_type: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    collection_name: {
        type: DataTypes.STRING,
        allowNull: true
    },
    personal_vector_db: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
    },
    chunk_methodology: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    top_k:{
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 20,
    },
    persona: {
        type: DataTypes.STRING(2000),
        allowNull: true,
    },
    llm_embedding_model:{
        type: DataTypes.STRING,
        allowNull: true,
    },
    fetch_k:{
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue:50,
    },
    lambda_mult:{
        type: DataTypes.FLOAT,
        allowNull: true,
        defaultValue: 0.5,
    },

  },
  { sequelize, modelName: "bot_designer_LLM_integration" }
);
Bot.hasMany(LLMIntegration, { foreignKey: 'bot_id' })
LLMIntegration.belongsTo(Bot, { foreignKey: 'bot_id' })

module.exports = LLMIntegration;
