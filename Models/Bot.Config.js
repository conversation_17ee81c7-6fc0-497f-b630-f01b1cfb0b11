const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class BotConfig extends Model {}

BotConfig.init(
  {
    bot_config_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    stand_by_messages: {
      type: DataTypes.STRING(2000),
      defaultValue: '[{"message":"","timeout":60000}]',
    },
    welcome_dialog: {
      type: DataTypes.STRING,
    },
    suggested_actions: DataTypes.STRING,
    welcome_message_set: {
      type: DataTypes.STRING(2000),
      defaultValue: '[{"type":"message","data":""},{"type":"card","data":""}]',
    },
    globals: {
      type: DataTypes.STRING(4000),
      defaultValue: null,
    },
    bad_word_config: {
      type: DataTypes.STRING(2000),
      defaultValue: '{"active":false,"pool":"both","lang":"en","message_ar":".يرجى الامتناع عن استخدام اللغة غير اللائقة","message_en":"Please refrain from using inappropriate language."}',
    
    },
  },
  { sequelize, modelName: "bot_designer_bot_config" }
);

BotConfig.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    if (key === "stand_by_messages") {
      this[key] = JSON.stringify(info[key]);
    } else if (key === "welcome_message_set") {
      this[key] = JSON.stringify(info[key]);
    } else if (key === "welcome_dialog") {
      typeof key === "string"
      ? this[key]=info[key]
      : this[key]=this.welcome_dialog;
    }  else if (key === "suggested_actions") {
      this[key] = info[key] ? info[key].join(",") : this[key];
    } else if (key === "globals") {
      this[key] = JSON.stringify(info[key]);
    } else if (key === "bad_word_config") {
      this[key] = JSON.stringify(info[key]);
    } else {
      this[key] = info[key] ? info[key] : this[key];
    }
  });
};

BotConfig.prototype.toJSON = function () {
  return {
    bot_id: this.bot_id,
    suggested_actions: this.suggested_actions
      ? this.suggested_actions.split(",")
      : undefined,
    welcome_message_set: JSON.parse(this.welcome_message_set),
    stand_by_messages: JSON.parse(this.stand_by_messages),
    bad_word_config: JSON.parse(this.bad_word_config),
    welcome_dialog: this.welcome_dialog,
    globals: JSON.parse(this.globals),
  };
};

module.exports = BotConfig;
