const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const SanadFAQ = require("./Sanad.Faq");
const SanadTrigger = require("./Sanad.Trigger");

class SanadService extends Model {}

SanadService.init(
  {
    sanad_service_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    title: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    lemmatized_title: {
       allowNull: false,
       type: DataTypes.STRING,
    },
    sanad_approved: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    entity_approved: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    reviewed:{
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
  },
  { sequelize, modelName: "bot_designer_sanad_service" }
);

SanadService.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
  
}

SanadService.prototype.toJSON = function toJSON() {
  return {
    sanad_service_id: this.sanad_service_id,
    bot_id: this.bot_id,
    title: this.title,
    lemmatized_title: this.lemmatized_title,
    sanad_approved: this.sanad_approved,
    entity_approved: this.entity_approved,
    reviewed: this.reviewed,
    createdAt: this.createdAt,
    updatedAt: this.updatedAtS
  };
};

SanadService.hasMany(SanadFAQ, { foreignKey: "sanad_service_id" });
SanadService.hasMany(SanadTrigger, { foreignKey: "sanad_service_id" });



module.exports = SanadService;
