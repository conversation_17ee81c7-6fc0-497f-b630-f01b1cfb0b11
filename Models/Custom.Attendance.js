const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");


class Custom_Attendance extends Model {}

Custom_Attendance.init(
  {
    attendance_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    attendance_year:{
      type: DataTypes.INTEGER
    },
    attendance_month:{
      type: DataTypes.INTEGER
    },
    attendance_day:{
      type: DataTypes.INTEGER
    },
    attendance_hour:{
      type: DataTypes.INTEGER
    },
     is_attended:{
      type: DataTypes.BOOLEAN,
      defaultValue: false
     }
  },
  { sequelize, modelName: "bot_designer_customs_Attendances" }
);



Custom_Attendance.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
  
}


module.exports = Custom_Attendance;
