const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class FallbackTransaction extends Model {}

FallbackTransaction.init(
  {
    fallback_tr_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    question: {
      type: DataTypes.STRING,
    },
    resolved: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
  },
  { sequelize, modelName: "bot_designer_fallback_transactions" }
);

FallbackTransaction.prototype.updateInfo = function (info) {
  this.resolved = info.resolved ? true : false;
};

module.exports = FallbackTransaction;
