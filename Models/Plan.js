const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const PlanFunction = require("./Plan.Function");
const Bot = require("./Bot");

class Plan extends Model {}

Plan.init(
  {
    plan_id: {
      allowNull: false,
      primaryKey: true,
      type: DataTypes.INTEGER,
    },
    plan_name: DataTypes.STRING(100),
    plan_description: DataTypes.STRING(100),
    plan_price: DataTypes.INTEGER,
    plan_order: DataTypes.INTEGER,
    currency: {
      type: DataTypes.STRING(50),
      defaultValue: "$",
    },
  },
  { sequelize, modelName: "bot_designer_plans" }
);

Plan.prototype.updateInfo = function (info) {
  this.plan_id = info.plan_id;
  this.plan_order = info.plan_order;
  this.plan_name = info.plan_name;
  this.plan_description = info.plan_description;
  this.plan_price = info.plan_price;
  this.currency = info.currency;
};

Plan.hasOne(PlanFunction, { foreignKey: "plan_id" });
Plan.hasOne(<PERSON><PERSON>, { foreignKey: "plan_id" });

module.exports = Plan;
