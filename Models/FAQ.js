const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const FAQ_Logs = require("./Faq.Logs");
const Topic = require("./Topic");

class FAQ extends Model {}

FAQ.init(
  {
    faq_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    question: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    question_key_num: DataTypes.INTEGER,
    answer: {
      type: DataTypes.STRING(1000),
    },
    hidden: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    voice_path_male: {
      allowNull: true,
      type: DataTypes.STRING,
    },
    voice_path_female: {
      allowNull: true,
      type: DataTypes.STRING,
    },
    lemmatized_question: {
      allowNull: true,
      type: DataTypes.STRING,
    },
    topic_id: {
      allowNull: true,
      type: DataTypes.INTEGER,
    },
    is_main: {
      allowNull: false,
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
  },
  { sequelize, modelName: "bot_designer_faq" }
);

FAQ.prototype.updateInfo = function updateInfo(info) {
  this.question = info.question ? info.question : this.question;
  this.answer = info.answer ? info.answer : this.answer;
  this.chitchat = info.chitchat === true ? true : false;
  this.voice_path_male = info.voice_path_male ? info.voice_path_male : this.voice_path_male;
  this.voice_path_female = info.voice_path_female ? info.voice_path_female : this.voice_path_female;
  this.lemmatized_question = info.lemmatized_question ? info.lemmatized_question : this.lemmatized_question;
  this.topic_id = info.topic_id;
  this.is_main = info?.is_main ? true : false;
};

FAQ.prototype.toJSON = function toJSON() {
  return {
    faq_id: this.faq_id,
    trigger_type: "faq",
    bot_id: this.bot_id,
    question: this.question,
    hidden: this.hidden,
    answer: this.answer,
    voice_path_male: this.voice_path_male,
    voice_path_female: this.voice_path_female,
    lemmatized_question: this.lemmatized_question,
    parent_id: this.parent_id,
    question_key_num: this.question_key_num,
    type: this.type,
    is_main: this.is_main,
    createdAt: this.createdAt,
    topic_id: this.topic_id,
  };
};

FAQ.beforeSave((faq) => {
  faq.question_key_num = faq.question.split(" ").length;
});

FAQ.beforeBulkCreate((faqs) => {
  faqs.map((faq) => {
    faq.question_key_num = faq.question.split(" ").length;
  });
});

FAQ.belongsTo(FAQ, { foreignKey: "parent_id" });
FAQ.hasMany(FAQ_Logs, { foreignKey: "faq_id" });
FAQ.belongsTo(Topic, { foreignKey: "topic_id" });

module.exports = FAQ;
