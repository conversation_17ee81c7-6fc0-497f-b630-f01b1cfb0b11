const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Report extends Model {}

Report.init(
  {
    report_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    email: {
      type: DataTypes.STRING,
    },
    complain: {
      type: DataTypes.STRING(1000),
      allowNull: true,
    },
    rate: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    username: {
      type: DataTypes.STRING,
    },
    phone: {
      type: DataTypes.STRING(30),
    },
  },
  { sequelize, modelName: "bot_designer_reports" }
);

module.exports = Report;
