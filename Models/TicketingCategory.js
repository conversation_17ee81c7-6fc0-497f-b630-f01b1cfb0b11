const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const TicketingDepartment = require("./TicketingDepartment");

class TicketingCategory extends Model {}

TicketingCategory.init(
  {
    department_category_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    department_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    category: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    category_ar: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "bot_designer_ticketing_department_categories",
  }
);

TicketingCategory.belongsTo(TicketingDepartment, {
  foreignKey: "department_id",
  as: "department",
});

TicketingDepartment.hasMany(TicketingCategory, {
  foreignKey: "department_id",
  as: "categories",
});

module.exports = TicketingCategory;
