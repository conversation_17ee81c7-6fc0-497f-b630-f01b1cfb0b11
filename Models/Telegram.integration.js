const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class TelegramIntegration extends Model {}

TelegramIntegration.init(
  {
    telegram_integration_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    bot_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    token: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  { sequelize, modelName: "bot_designer_telegram_integration" }
);

TelegramIntegration.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
};

module.exports = TelegramIntegration;
