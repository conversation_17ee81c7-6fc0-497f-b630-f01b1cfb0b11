const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Location extends Model {}

Location.init(
  {
    location_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    continent: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    country: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    city: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  },
  { sequelize, modelName: "bot_designer_locations" }
);

module.exports = Location;
