const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class Shortcut extends Model {}
Shortcut.init(
  {
    shortcut_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },

    shortcuts: {
      type: DataTypes.STRING(2000),
    },
    user_id: {
      type: DataTypes.INTEGER,
      unique: true,
    },
  },
  { sequelize, modelName: "bot_designer_shortcuts" }
);

Shortcut.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    if (key !== "user_id") {
      this[key] = info[key];
    }
  });
};

module.exports = Shortcut;
