const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class GoogleSheetPlugin extends Model {}

GoogleSheetPlugin.init(
  {
    sheet_plugin_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    status_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    refresh_token: {
      type: DataTypes.STRING,
    },
    email: {
      type: DataTypes.STRING,
    },
  },
  { sequelize, modelName: "bot_designer_googlesheet_plugins" }
);

GoogleSheetPlugin.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
};

GoogleSheetPlugin.prototype.toJSON = function () {
  ///هدول برجعولي بال res
  return {
    sheet_plugin_id: this.sheet_plugin_id,
    status_active: this.status_active,
    createdAt: this.createdAt,
    refresh_token: this.refresh_token,
    email: this.email,
  };
};

module.exports = GoogleSheetPlugin;
