const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Phone extends Model {}

Phone.init(
  {
    phone_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    verified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
  },
  { sequelize, modelName: "bot_designer_phones" }
);

Phone.prototype.verify = function () {
  this.verified = true;
};

module.exports = Phone;
