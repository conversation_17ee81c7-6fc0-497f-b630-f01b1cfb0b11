const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const TicketingSupportTicket = require("./TicketingSupportTicket");

class TicketingNotification extends Model {}

TicketingNotification.init(
  {
    notification_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    ticket_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    type: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    message: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    sent_to: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    support_agent_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "bot_designer_ticketing_notifications",
  }
);

TicketingNotification.belongsTo(TicketingSupportTicket, {
  foreignKey: "ticket_id",
  as: "ticket",
});

module.exports = TicketingNotification;
