const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class UnstructuredResources extends Model {}

UnstructuredResources.init(
  {
    resource_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    resource_type: {
      type: DataTypes.ENUM,
      values: ["pdf", "ppt", "doc", "docx", "txt", "website"],
    },
    resource_name: {
      type: DataTypes.STRING,
      defaultValue: "",
    },
    resource_url: {
      type: DataTypes.STRING,
      defaultValue: "",
    },
    bot_id: {
      type: DataTypes.INTEGER,
      unique: true,
    },
  },
  { sequelize, modelName: "bot_designer_unstructured_resource" }
);

module.exports = UnstructuredResources;
