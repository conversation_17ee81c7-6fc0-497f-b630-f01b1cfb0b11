const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const TicketingSupportTicket = require("./TicketingSupportTicket");

class TicketingLog extends Model {}

TicketingLog.init(
  {
    ticket_log_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    ticket_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    changed_by: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    support_agent_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    action_type: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    old_value: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    new_value: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "bot_designer_ticketing_support_ticket_logs",
  }
);

TicketingLog.belongsTo(TicketingSupportTicket, {
  foreignKey: "ticket_id",
  as: "ticket",
});

TicketingSupportTicket.hasMany(TicketingLog, {
  foreignKey: "ticket_id",
  as: "logs",
});

module.exports = TicketingLog;
