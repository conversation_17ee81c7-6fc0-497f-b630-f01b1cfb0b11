const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class SanadFAQ extends Model { }

SanadFAQ.init(
    {
        sanad_faq_id: {
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
            type: DataTypes.INTEGER,
        },
        question: {
            allowNull: false,
            type: DataTypes.STRING,
        },
        lemmatized_question: {
            allowNull: false,
            type: DataTypes.STRING,
        },
        answer: {
            type: DataTypes.STRING("MAX"),
        },
        hidden: {
            type: DataTypes.BOOLEAN,
            defaultValue: false,
        },
        sanad_approved: {
            type: DataTypes.BOOLEAN,
            defaultValue: false,
        },
        entity_approved: {
            type: DataTypes.BOOLEAN,
            defaultValue: false,
        },
        reviewed:{
            type: DataTypes.BOOLEAN,
            defaultValue: false,
        },
        voice_path: {
            type: DataTypes.STRING,
            defaultValue: false,
        },
        voice_path_female: {
            type: DataTypes.STRING,
            defaultValue: false,
        }
    },
    { sequelize, modelName: "bot_designer_sanad_faq" }
);

SanadFAQ.prototype.updateInfo = function (info) {
    Object.keys(info).map((key) => {
      this[key] = info[key];
    });
    
  }


SanadFAQ.prototype.toJSON = function toJSON() {
    return {
        sanad_faq_id: this.sanad_faq_id,
        sanad_service_id:this.sanad_service_id,
        trigger_type: "faq",
        bot_id: this.bot_id,
        question: this.question,
        lemmatized_question: this.lemmatized_question,
        voice_path: this.voice_path,
        voice_path_female  : this.voice_path_female,
        hidden: this.hidden,
        sanad_approved: this.sanad_approved,
        entity_approved: this.entity_approved,
        reviewed: this.reviewed,
        answer: this.answer,
        updatedAt: this.updatedAt,
        createdAt: this.createdAt,
    };
};

  

module.exports = SanadFAQ;
