const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class PayoneRecord extends Model {}

PayoneRecord.init(
  {
    payone_record_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    invoice_id: {
      type: DataTypes.STRING
    },
    conversation_id: {
      type: DataTypes.STRING
    },
    channel: {
      type: DataTypes.STRING
    },
    bot_id: {
        type: DataTypes.INTEGER,
      },
    url: {
      type: DataTypes.STRING
    },
    payment: {
      type: DataTypes.STRING
    }
  },
  { sequelize, modelName: "bot_designer_payone_records" }
);

PayoneRecord.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

module.exports = PayoneRecord;
