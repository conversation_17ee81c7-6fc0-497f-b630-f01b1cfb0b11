const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class BotDashboard extends Model {}

BotDashboard.init(
  {
    bot_dashboard_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    dashboard_theme: {
      type: DataTypes.STRING,
      defaultValue: "#FFAC00,#E13102,#ec8f6e,#f3b49f,#f6c7b6",
    },
    dashboard_layout: {
      type: DataTypes.STRING(500),
      defaultValue:
        '[{"i":"a","x":0,"y":0,"w":1,"h":1},{"i":"b","x":1,"y":0,"w":1,"h":1},{"i":"c","x":2,"y":0,"w":1,"h":1},{"i":"d","x":0,"y":1,"w":1,"h":2},{"i":"e","x":1,"y":1,"w":2,"h":2},{"i":"f","x":0,"y":2,"w":1,"h":2},{"i":"g","x":1,"y":2,"w":1,"h":2},{"i":"h","x":2,"y":2,"w":1,"h":2},{"i":"i","x":0,"y":3,"w":1,"h":2},{"i":"j","x":1,"y":3,"w":2,"h":3},{"i":"k","x":0,"y":4,"w":1,"h":4},{"i":"l","x":1,"y":4,"w":2,"h":3}]',
    },
    sales_layout: {
      type: DataTypes.STRING(500),
      defaultValue:
        '[{"i":"m","x":0,"y":0,"w":1,"h":2},{"i":"n","x":1,"y":0,"w":1,"h":2},{"i":"o","x":2,"y":0,"w":1,"h":2},{"i":"p","x":0,"y":1,"w":1,"h":2},{"i":"q","x":1,"y":1,"w":1,"h":2},{"i":"r","x":2,"y":1,"w":1,"h":2},{"i":"s","x":0,"y":2,"w":1,"h":2},{"i":"t","x":1,"y":2,"w":1,"h":2}]',
    },
    totalTransactions: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    avgTransactions: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    daysSinceLaunch: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    transactionAnswered: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    transactionsByCategory: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    transactionPerDay: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },

    trendOfAnswered: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    avgPerWeek: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    avgPerHour: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    tansactionTable: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    lastTransactions: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    transactionNotAnswered: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
  },
  { sequelize, modelName: "bot_designer_bot_dashboard" }
);

BotDashboard.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    if (key === "dashboard_theme") {
      this[key] = info[key].join(",");
    } else if (key === "dashboard_layout") {
      this[key] = JSON.stringify(info[key]);
    } else {
      this[key] = info[key];
    }
  });
};

BotDashboard.prototype.toJSON = function () {
  return {
    bot_dashboard_id: this.bot_dashboard_id,
    dashboard_theme: this.dashboard_theme.split(","),
    dashboard_layout: JSON.parse(this.dashboard_layout),
    sales_layout: JSON.parse(this.sales_layout),
    totalTransactions: this.totalTransactions,
    transactionAnswered: this.transactionAnswered,
    avgTransactions: this.avgTransactions,
    daysSinceLaunch: this.daysSinceLaunch,
    transactionsByCategory: this.transactionsByCategory,
    transactionPerDay: this.transactionPerDay,
    trendOfAnswered: this.trendOfAnswered,
    avgPerWeek: this.avgPerWeek,
    avgPerHour: this.avgPerHour,
    tansactionTable: this.tansactionTable,
    lastTransactions: this.lastTransactions,
    transactionNotAnswered: this.transactionNotAnswered,
  };
};

module.exports = BotDashboard;
