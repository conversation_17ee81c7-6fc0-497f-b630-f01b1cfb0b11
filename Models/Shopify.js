const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class ShopifyPlugin extends Model {}

ShopifyPlugin.init(
  {
    shopify_plugin_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    status_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    api_key: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    api_secret: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    shop_domain: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    access_token: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    sync: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    sync_latest_date: {
      type: DataTypes.STRING,
    },
  },
  { sequelize, modelName: "bot_designer_shopify_plugins" }
);

ShopifyPlugin.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
};

module.exports = ShopifyPlugin;
