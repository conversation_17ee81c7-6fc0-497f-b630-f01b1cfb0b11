const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Weather extends Model {}

Weather.init(
  {
    weather_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    weather_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    trigger_name: DataTypes.STRING(50),
  },
  { sequelize, modelName: "bot_designer_weathers" }
);

Weather.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
};

module.exports = Weather;
