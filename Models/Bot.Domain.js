const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class BotDomain extends Model {}

BotDomain.init(
  {
    domain_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    host: {
      type: DataTypes.STRING,
    },
  },
  { sequelize, modelName: "bot_designer_domains" }
);

BotDomain.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
};

module.exports = BotDomain;
