const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class CalendlyIntegration extends Model {}
CalendlyIntegration.init(
  {
    calendly_integration_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    status_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    refresh_token: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    owner: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    organization: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    bot_id: {
      type: DataTypes.INTEGER,
    },
  },
  { sequelize, modelName: "bot_designer_calendly_integrations" }
);

CalendlyIntegration.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

module.exports = CalendlyIntegration;
