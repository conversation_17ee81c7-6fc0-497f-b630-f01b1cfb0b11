const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const Trainee_Class = require("./Custom.Class.Trainee");
const Custom_Attendance = require("./Custom.Attendance");
const Custom_Payment = require("./Custom.Payment");
const Customs_Trial = require("./Custom.Trial");
const Special_Discount = require("./Custom.Discount")
class Customs_Trainee extends Model {}

Customs_Trainee.init(
  {
    trainee_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    first_name: {
      type: DataTypes.STRING(50),
    },
    second_name: {
      type: DataTypes.STRING(50),
    },
    third_name: {
      type: DataTypes.STRING(50),
    },
    family_name: {
      type: DataTypes.STRING(50),
    },
    gender: {
      type: DataTypes.STRING(50),
    },
    date_of_birth:{
      type: DataTypes.DATE,
    },
    first_phone:{
        type: DataTypes.STRING
      },
      second_phone:{
        type: DataTypes.STRING
      },
       unique_code:{
        type: DataTypes.STRING,
        unique:true
       }
  },
  { sequelize, modelName: "bot_designer_customs_Trainees" }
);

Customs_Trainee.hasMany(Trainee_Class, { foreignKey: "trainee_id" });
Customs_Trainee.hasMany(Custom_Payment, { foreignKey: "trainee_id" });
Customs_Trainee.hasMany(Customs_Trial, { foreignKey: "trainee_id" });



Customs_Trainee.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  }); 
}

module.exports = Customs_Trainee;
