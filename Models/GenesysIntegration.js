const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class GenesysIntegration extends Model {}

GenesysIntegration.init(
  {
    genesys_integration_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    organization_id: {
      type: DataTypes.STRING
    },
    deployment_id: {
      type: DataTypes.STRING
    },
    target_Address: {
      type: DataTypes.STRING
    },
    bot_id: {
      type: DataTypes.INTEGER
    },
  },
  { sequelize, modelName: "bot_designer_genesys_integration" }
);

GenesysIntegration.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

module.exports = GenesysIntegration;
