const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const ReservationOrder = require("./Reservation.Order");

class ReservationTable extends Model {}

ReservationTable.init(
  {
    table_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    table_name: {
      type: DataTypes.STRING,
    },
    table_no: {
      type: DataTypes.STRING,
    },
    min_guests: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
    },
    max_guests: {
      type: DataTypes.INTEGER,
    },
    table_active: {
      type: DataTypes.BOOLEAN,
    },
  },
  { sequelize, modelName: "bot_designer_reservation_tables" }
);

ReservationTable.prototype.updateInfo = function (info) {
  this.table_name = info.table_name;
  this.table_no = info.table_no;
  this.min_guests = info.min_guests;
  this.max_guests = info.max_guests;
  this.table_active = info.table_active;
};

ReservationTable.hasMany(ReservationOrder, { foreignKey: "table_id" });

module.exports = ReservationTable;
