const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class SanadTrigger extends Model {}

SanadTrigger.init(
  {
    sanad_trigger_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    trigger_name: {
      type: DataTypes.STRING,
    },
    trigger: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    lemmatized_trigger: {
        allowNull: false,
        type: DataTypes.STRING,
    },
    trigger_type: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    url: {
      allowNull: false,
      type: DataTypes.STRING,
    },
       sanad_approved: {
            type: DataTypes.BOOLEAN,
            defaultValue: false,
          },
          entity_approved: {
            type: DataTypes.BOOLEAN,
            defaultValue: false,
          },
          reviewed:{
            type: DataTypes.BOOLEAN,
            defaultValue: false,
          },
  },
  { sequelize, modelName: "bot_designer_sanad_triggers" }
);

SanadTrigger.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
  
}


SanadTrigger.prototype.toJSON = function toJSON() {
  return {
    sanad_trigger_id: this.sanad_trigger_id,
    sanad_service_id:this.sanad_service_id,
    trigger_name: this.trigger_name,
    bot_id: this.bot_id,
    url: this.url,
    trigger: this.trigger,
    lemmatized_trigger: this.lemmatized_trigger,
    trigger_type: this.trigger_type,
        sanad_approved: this.sanad_approved,
        entity_approved: this.entity_approved,
        reviewed: this.reviewed,
    createdAt: this.createdAt,
  };
};

module.exports = SanadTrigger;
