const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Email extends Model {}

Email.init(
  {
    email_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    verified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
  },
  { sequelize, modelName: "bot_designer_emails" }
);

Email.prototype.verify = function () {
  this.verified = true;
};


module.exports = Email;
