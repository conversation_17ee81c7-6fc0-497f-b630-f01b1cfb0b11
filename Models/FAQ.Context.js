const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Context extends Model {}

Context.init(
  {
    context_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    context: {
      type: DataTypes.STRING(1000),
    },
    answer: {
      type: DataTypes.STRING,
    },
  },
  { sequelize, modelName: "bot_designer_faq_context" }
);

module.exports = Context;
