const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const Dialogs = require("./Dialogs");

class DialogVersionControl extends Model {}

DialogVersionControl.init(
    {
        dialog_version_control_id:{
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
            allowNull: false,
        },
        url: {
            type: DataTypes.STRING(255)
        },
        dialog_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: Dialogs,
                key: 'dialog_id'
            }
        },
        is_live:{
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false
        },
        is_staging:{
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true
        }
    },
    {
        sequelize,
        modelName: "bot_designer_dialog_version_control"
    }
);


Dialogs.hasMany(DialogVersionControl, { foreignKey: 'dialog_id' });
DialogVersionControl.belongsTo(Dialogs, { foreignKey: 'dialog_id' });

module.exports = DialogVersionControl;
