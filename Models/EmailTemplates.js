const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class EmailTemplate extends Model {}

EmailTemplate.init(
  {
    email_template_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    template_name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    template: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  },
  { sequelize, modelName: "bot_designer_email_templates" }
);


module.exports = EmailTemplate;
