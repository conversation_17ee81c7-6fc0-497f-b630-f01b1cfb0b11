//SECTION DB
const sequelize = require("../db");
const Bot = require("./Bot");
const { DataTypes, Model } = require("sequelize");
//SECTION AUTH
const crypto = require("crypto");
const jwt = require("jsonwebtoken");
const secret = require("../config/appConfig").secret;
//SECTION MAILING
const sgEmail = require("../Services/Email");
const createMessage = require("../Constants/message");
const {
  createVerifyEmail,
  createResetPassword,
} = require("../Constants/Custom.Email");


class Custom_User extends Model {}

Custom_User.init(
  {
    custom_user_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      primaryKey: true,
      unique: true,
    },
    email_verification: {
      defaultValue: false,
      type: DataTypes.BOOLEAN,
    },
    user_name: sequelize.Sequelize.STRING,
    phone_number: sequelize.Sequelize.STRING,
    reset: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    salt: DataTypes.STRING(4000),
    hash: DataTypes.STRING(4000),
  },
  { sequelize, modelName: "bot_designer_customs_users" }
);

Custom_User.beforeSave(function (user, options) {});

Custom_User.prototype.updateRecord = function () {
  this.changed("updatedAt", true);
};

Custom_User.beforeCreate(function (custom_user, options) {
    custom_user.email = custom_user.email.toLowerCase();
    custom_user.user_name = custom_user.user_name.toLowerCase();
});

Custom_User.prototype.setPassword = function (password) {
  this.salt = crypto.randomBytes(16).toString("hex");
  this.hash = crypto
    .pbkdf2Sync(password, this.salt, 10000, 512, "sha512")
    .toString("hex");
};

Custom_User.prototype.authenticate = function authenticate(password) {
  var hash = crypto
    .pbkdf2Sync(password, this.salt, 10000, 512, "sha512")
    .toString("hex");
  return this.hash === hash;
};

Custom_User.afterCreate(function (custom_user, options, cb) {
  //NOTE VEIFICATION
  custom_user.sendConfirmationInstructions();
});

Custom_User.prototype.generateAutoLoginURL = function generateAutoLoginURL() {};

Custom_User.prototype.sendConfirmationInstructions =
  function sendConfirmationInstructions() {
    sgEmail.send(
      createMessage(
        "verify",
        this.email,
        createVerifyEmail(this.email, this.custom_user_id)
      )
    );
  };

  Custom_User.prototype.sendResetPassword = function sendResetPassword() {
  sgEmail.send(
    createMessage(
      "reset password",
      this.email,
      createResetPassword(this.email, this.custom_user_id)
    )
  );
};

Custom_User.prototype.generateToken = function generateToken() {
  return jwt.sign(
    {
      custom_user_id: this.custom_user_id,
      email: this.email,
    },
    secret
  );
};

Custom_User.prototype.toAuthJSON = function toAuthJSON() {
  return {
    ...this.toJSON(),
    token: this.generateToken(),
  };
};
Custom_User.prototype.toJSON = function toJSON() {
  return {
    custom_user_id: this.custom_user_id,
    email: this.email,
    email_verification: this.email_verification,
    user_name: this.user_name,
    admin: this.admin,
    title: this.title,
    phone_number: this.phone_number,
    disabled: this.disabled
  };
};

module.exports = Custom_User;
