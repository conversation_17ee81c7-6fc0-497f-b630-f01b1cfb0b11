const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const Custom_Payment = require("./Custom.Payment");

class Custom_Discount extends Model {}

Custom_Discount.init(
  {
      discount_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    discount_description:{
    type: DataTypes.STRING
    },
    discount_percentage: {
        type: DataTypes.FLOAT,
        defaultValue: 0,
    },
    discount_value: {
        type: DataTypes.FLOAT,
        defaultValue: 0,
    }
  },
  { sequelize, modelName: "bot_designer_customs_Discount" }
);

Custom_Discount.hasMany(Custom_Payment, { foreignKey: "discount_id" });
 
Custom_Discount.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  }); 
}

module.exports = Custom_Discount;
