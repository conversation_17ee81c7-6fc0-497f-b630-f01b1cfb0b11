const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const GenesysIntegrationTransaction = require("./GenesysIntegrationTransaction");

class GenesysIntegrationSession extends Model {}

GenesysIntegrationSession.init(
  {
    genesys_integration_session_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    conversation_id: {
      type: DataTypes.STRING
    },
    eventStreamUri: {
      type: DataTypes.STRING
    },
    jwt: {
      type: DataTypes.STRING
    },
    chat_id: {
      type: DataTypes.STRING
    },
    member_id: {
      type: DataTypes.STRING
    },
    channel: {
        type: DataTypes.ENUM,
        values: ["whatsapp", "web", "facebook"],
    },
    genesys_integration_id: {
      type: DataTypes.INTEGER,
    },
  },
  { sequelize, modelName: "bot_designer_genesys_integration_session" }
);

GenesysIntegrationSession.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

GenesysIntegrationSession.hasMany(
  GenesysIntegrationTransaction, 
  { foreignKey: "genesys_integration_session_id" }
);

module.exports = GenesysIntegrationSession;
