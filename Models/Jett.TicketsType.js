const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class JettTicketsType extends Model {}

JettTicketsType.init(
  {
    jett_ticket_type_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    type: {
      type: DataTypes.STRING,
    },
  },
  { sequelize, modelName: "bot_designer_jett_tickets_types" }
);

JettTicketsType.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

module.exports = JettTicketsType;
