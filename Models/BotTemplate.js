const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class BotTemplate extends Model {}

BotTemplate.init(
  {
    bot_template_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    bot_template_title: {
      type: DataTypes.STRING,
    },
    bot_template_description: {
      type: DataTypes.STRING(2000),
    },
    bot_template_photo: {
      type: DataTypes.STRING(500),
    },
    bot_template_category: {
      type: DataTypes.STRING(500),
    },
    bot_template_made_by: {
      type: DataTypes.STRING(100),
    },
    bot_template_downloads_count: {
      type: DataTypes.INTEGER,
    },
    bot_template_slug: {
      type: DataTypes.STRING,
    },
    facebook_url: {
      type: DataTypes.STRING(500),
    },
    whatsapp_url: {
      type: DataTypes.STRING(500),
    },
    instagram_url: {
      type: DataTypes.STRING(500),
    },
    rate: {
      type: DataTypes.FLOAT,
      defaultValue: 0.0
    },
    raters_number: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    }
  },
  { sequelize, modelName: "bot_designer_bot_template" }
);

BotTemplate.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key] ? info[key] : this[key];
  });
};


module.exports = BotTemplate;
