const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const crypto = require("crypto");
const jwt = require("jsonwebtoken");
const secret = require("../config/appConfig").secret;

class Monitor extends Model {}

Monitor.init(
  {
    monitor_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      primaryKey: true,
      unique: true,
    },
    email_verification: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    salt: DataTypes.STRING(4000),
    hash: DataTypes.STRING(4000),
  },
  { sequelize, modelName: "bot_designer_monitor" }
);

Monitor.prototype.authenticate = function authenticate(password) {
  var hash = crypto
    .pbkdf2Sync(password, this.salt, 10000, 512, "sha512")
    .toString("hex");
  return this.hash === hash;
};

Monitor.prototype.setPassword = function (password) {
  this.salt = crypto.randomBytes(16).toString("hex");
  this.hash = crypto
    .pbkdf2Sync(password, this.salt, 10000, 512, "sha512")
    .toString("hex");
};

Monitor.prototype.generateToken = function generateToken() {
  return jwt.sign(
    {
      id: this.monitor_id,
      name: this.email,
    },
    secret
  );
};

Monitor.prototype.toAuthJSON = function toAuthJSON() {
  return {
    ...this.toJSON(),
    token: this.generateToken(),
  };
};

module.exports = Monitor;
