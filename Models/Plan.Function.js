const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class PlanFunction extends Model {}

PlanFunction.init(
  {
    plan_function_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },

    max_faqs: DataTypes.INTEGER,
    max_cards: DataTypes.INTEGER,
    max_suggestions: DataTypes.INTEGER,
    max_dialogs: DataTypes.INTEGER,

    max_apis: DataTypes.INTEGER,
    max_popups: DataTypes.INTEGER,

    max_items: DataTypes.INTEGER,
    max_categories: DataTypes.INTEGER,
    max_offers: DataTypes.INTEGER,
    max_orders: DataTypes.INTEGER,

    monthly_transaction: DataTypes.INTEGER,
    monthly_users: DataTypes.INTEGER,
    voice_transactions: DataTypes.INTEGER,

    voice: DataTypes.BOOLEAN,
    cart: DataTypes.BOOLEAN,
    offer: DataTypes.BOOLEAN,

    sales_dashboard: DataTypes.BOOLEAN,
    transaction_dashboard: DataTypes.<PERSON><PERSON><PERSON><PERSON><PERSON>,

    web_designer: DataTypes.B<PERSON><PERSON><PERSON><PERSON>,
    test_chat: DataTypes.BOOLEAN,

    web_channel: DataTypes.BOOLEAN,
    facebook_channel: DataTypes.BOOLEAN,
    instegram_channel: DataTypes.BOOLEAN,
    whatsapp_channel: DataTypes.BOOLEAN,

    reservation: DataTypes.BOOLEAN,
    marketing_popup: DataTypes.BOOLEAN,
    weather: DataTypes.BOOLEAN,
    fallback_popup: DataTypes.BOOLEAN,

    max_rooms: DataTypes.INTEGER,
    max_tables: DataTypes.INTEGER,
    max_reservations: DataTypes.INTEGER,

    shopify_item_integration: DataTypes.BOOLEAN,
    shopify_web_plugin: DataTypes.BOOLEAN,
    googlesheet_item_integration: DataTypes.BOOLEAN,
    googlesheet_qna_integration: DataTypes.BOOLEAN,
    zendesk_livechat_integration: DataTypes.BOOLEAN,
    zendesk_ticketing_integration: DataTypes.BOOLEAN,

    max_editors: DataTypes.INTEGER,
    max_users: DataTypes.INTEGER,

    max_features: DataTypes.INTEGER,
    max_offer_items: DataTypes.INTEGER,
    max_feature_items: DataTypes.INTEGER,
    max_item_options: DataTypes.INTEGER,
  },
  { sequelize, modelName: "bot_designer_plan_functions" }
);

PlanFunction.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

module.exports = PlanFunction;
