const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class SentimentAddon extends Model {}

SentimentAddon.init(
  {
    sentiment_addon_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    generated_by: {
      type: DataTypes.ENUM,
      values: ["gpt", "user"],
      allowNull: true,
    },
    bot_id: {
      type: DataTypes.INTEGER,
      unique: true,
    },
  },
  { sequelize, modelName: "bot_designer_sentiment_addons" }
);

SentimentAddon.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

module.exports = SentimentAddon;
