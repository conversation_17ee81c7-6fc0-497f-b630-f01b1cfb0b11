const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const SeatHours = require("./Seat.Hours");
const SeatConversation = require("./Seat.Conversation");
const SeatMessage = require("./Seat.Message");
const SeatBot = require("./Seat.Bot");
const crypto = require("crypto");

class Seat extends Model {}

Seat.init(
  {
    liveperson_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    email: {
      type: DataTypes.STRING,
      primaryKey: true,
    },
    online: {
      type: DataTypes.BOOLEAN,
    },
    salt: DataTypes.STRING(4000),
    hash: DataTypes.STRING(4000),
  },
  { sequelize, modelName: "bot_designer_livechat_seats" }
);

Seat.prototype.setOnline = function () {
  if (
    this.email !== "<EMAIL>" &&
    this.email !== "<EMAIL>"
  ) {
    this.online = true;
  }
};

Seat.prototype.setOffline = function () {
  this.online = false;
};
// Seat.hasMany(SeatHours, { foreignKey: "email" });

Seat.prototype.setPassword = function (password) {
  this.salt = crypto.randomBytes(16).toString("hex");
  this.hash = crypto
    .pbkdf2Sync(password, this.salt, 10000, 512, "sha512")
    .toString("hex");
};

Seat.prototype.authenticate = function authenticate(password) {
  var hash = crypto
    .pbkdf2Sync(password, this.salt, 10000, 512, "sha512")
    .toString("hex");
  return this.hash === hash;
};

Seat.prototype.toAuthJSON = function toAuthJSON() {
  return {
    ...this.toJSON(),
  };
};
Seat.prototype.toJSON = function toJSON() {
  return {
    liveperson_id: this.liveperson_id,
    email: this.email,
  };
};

Seat.hasMany(SeatConversation, { foreignKey: "liveperson_id" });
Seat.hasMany(SeatMessage, { foreignKey: "liveperson_id" });
Seat.hasMany(SeatBot, { foreignKey: "liveperson_id" });
Seat.hasMany(SeatHours, { foreignKey: "liveperson_id" });

module.exports = Seat;
