const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class ReportPlugin extends Model {}

ReportPlugin.init(
  {
    report_plugin_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    status_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    ask_email: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    ask_complain: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    ask_username: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    ask_phone: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
  },
  { sequelize, modelName: "bot_designer_report_plugins" }
);

ReportPlugin.prototype.updateInfo = function (info) {
  this.ask_phone = info.ask_phone ? true : false;
  this.ask_username = info.ask_username ? true : false;
  this.ask_email = info.ask_email ? true : false;
  this.ask_complain = info.ask_complain ? true : false;
  this.status_active = info.status_active ? true : false;
};

module.exports = ReportPlugin;
