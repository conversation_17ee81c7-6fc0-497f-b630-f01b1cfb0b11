const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const JettKHBSlaveTicket = require("./Jett.KHBSlaveTicket");
class JettKHBMasterTicket extends Model {}

JettKHBMasterTicket.init(
  {
    jett_khb_master_tickets_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    jett_invoice_id: {
      type: DataTypes.INTEGER,
    },
    name: {
      type: DataTypes.STRING,
    },
    email: {
      type: DataTypes.STRING,
    },
    ticket_id: {
      type: DataTypes.STRING,
    },
    amount: {
      type: DataTypes.FLOAT,
    },
    trip_date: {
      type: DataTypes.STRING,
    },
    trip_time: {
      type: DataTypes.STRING,
    },
    trip_id: {
      type: DataTypes.STRING,
    },
    luggage_count: {
      type: DataTypes.INTEGER,
    },
    extra_adult_count: {
      type: DataTypes.INTEGER,
    },
    extra_child_count: {
      type: DataTypes.INTEGER,
    },
  },
  { sequelize, modelName: "bot_designer_jett_khb_master_tickets" }
);

JettKHBMasterTicket.hasMany(JettKHBSlaveTicket, {
  foreignKey: "jett_khb_master_tickets_id",
  as: "slaveTickets",
});

JettKHBMasterTicket.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

module.exports = JettKHBMasterTicket;
