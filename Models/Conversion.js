const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Conversion extends Model {}

Conversion.init(
  {
    conversion_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    url: { type: DataTypes.STRING(2000) },
  },
  { sequelize, modelName: "bot_designer_conversions" }
);

module.exports = Conversion;
