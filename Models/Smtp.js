const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Smtp extends Model {}

Smtp.init(
  {
    smtp_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    host: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    username:{
      type: DataTypes.STRING,
      allowNull: true
    },
    password:{
      type: DataTypes.STRING,
      allowNull: true
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true
    },
    port : {
      type: DataTypes.STRING,
      allowNull: true
    },
    secure: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    bot_id:{
     type: DataTypes.INTEGER,
      allowNull: true
    }
  },
  { sequelize, modelName: "bot_designer_smtp" }
);

Smtp.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
  
}


module.exports = Smtp;
