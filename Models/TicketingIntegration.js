const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");


class TicketingIntegration extends Model {}

TicketingIntegration.init({
    ticketing_integration_id: {
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
        type: DataTypes.INTEGER,
    },
    bot_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    status: {
        type: DataTypes.ENUM("active", "inactive"),
        defaultValue: "active",
    },
    time_zone: {
        type: DataTypes.STRING,
        defaultValue: "Asia/Amman",
    },
},{
    sequelize,
    modelName: "bot_designer_ticketing_integrations",
});

module.exports = TicketingIntegration;
