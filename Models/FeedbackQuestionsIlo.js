const FeedbackLogILO = require("./FeedbackLogIlo");
const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class FeedbackQuestionsILO extends Model {}

FeedbackQuestionsILO.init(
  {
   feedback_question_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    feedback_question_en: {
      type: DataTypes.STRING,
      allowNull: false
    },
    feedback_question_ar: {
      type: DataTypes.STRING,
      allowNull: false
    }
  },
  { sequelize, modelName: "bot_designer_customer_ilo_feedback_question" }
);

FeedbackQuestionsILO.hasMany(FeedbackLogILO, { foreignKey: "feedback_question_id" });

module.exports = FeedbackQuestionsILO;
