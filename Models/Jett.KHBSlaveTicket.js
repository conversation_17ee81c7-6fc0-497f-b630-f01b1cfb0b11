const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const JettKHBMasterTicket = require("./Jett.KHBMasterTicket");
class JettKHBSlaveTicket extends Model {}

JettKHBSlaveTicket.init(
  {
    jett_khb_slave_tickets_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    jett_khb_master_tickets_id: {
      type: DataTypes.INTEGER,
    },
    name: {
      type: DataTypes.STRING,
    },
    ticket_id: {
      type: DataTypes.STRING,
    },
    master_ticket_id: {
      type: DataTypes.STRING,
    },
    amount: {
      type: DataTypes.FLOAT,
    },
    type: {
      type: DataTypes.STRING,
    },
  },
  { sequelize, modelName: "bot_designer_jett_khb_slave_tickets" }
);

// JettKHBSlaveTicket.belongsTo(JettKHBMasterTicket, {
//   foreignKey: "jett_khb_master_tickets_id",
//   as: "master",
// });

JettKHBSlaveTicket.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

module.exports = JettKHBSlaveTicket;
