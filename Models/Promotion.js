const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Promotion extends Model {}

Promotion.init(
  {
    promotion_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    promotion_percentage: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    },
    promotion_value: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    },
  },
  { sequelize, modelName: "bot_designer_promotion" }
);

Promotion.prototype.updateInfo = function (info) {
  this.promotion_percantage = info.promotion_percantage ? info.promotion_percantage  : this.promotion_percantage ;
  this.promotion_value = info.promotion_value ? info.promotion_value : this.promotion_value;
};

module.exports = Promotion;
