const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class SanadFeedback extends Model {}

SanadFeedback.init(
  {
    sanad_feedback_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    conversation_id: {
      type: DataTypes.STRING,
    },
    rating: {
      type: DataTypes.INTEGER,
    },
    bot_id: {
      type: DataTypes.INTEGER,
    },
    feedback_body: {
      type: DataTypes.STRING,
    },
  },
  { sequelize, modelName: "bot_designer_sanad_feedbacks" }
);

module.exports = SanadFeedback;
