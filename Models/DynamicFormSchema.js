const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");


class DynamicFormSchema extends Model {}
DynamicFormSchema.init(
  {
    dynamic_form_schema_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    bot_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    title: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    schema: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  { sequelize, modelName: "bot_designer_dynamic_form_schemas" }
);
// DynamicFormSchema.belongsTo(Bot, { foreignKey: "bot_id" });
// Bot.hasMany(DynamicFormSchema, { foreignKey: "bot_id" });

module.exports = DynamicFormSchema;
