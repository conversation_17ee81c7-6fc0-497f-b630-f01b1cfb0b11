const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");


class InternalLiveChatIntegration extends Model {}

InternalLiveChatIntegration.init({
    livechat_integration_id: {
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
        type: DataTypes.INTEGER,
    },
    bot_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    status: {
        type: DataTypes.ENUM("active", "inactive"),
        defaultValue: "active",
    },
    max_sessions_per_agent: {
        type: DataTypes.INTEGER,
        defaultValue: 5,
    },
    time_zone: {
        type: DataTypes.STRING,
        defaultValue: "Asia/Amman",
    },
},{
    sequelize,
    modelName: "bot_designer_internal_livechat_integrations",
});

module.exports = InternalLiveChatIntegration;
