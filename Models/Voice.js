const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Voice extends Model {}

Voice.init(
  {
    voice_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    project_name:DataTypes.STRING,
    user_id: {
        type: DataTypes.STRING,
      },  
    user_question: {
      type: DataTypes.STRING,
    },
    bot_answer: {
      type: DataTypes.STRING,
    },
    similarity: DataTypes.STRING,
  },
  { sequelize, modelName: "bot_designer_voice" }
);

module.exports = Voice;
