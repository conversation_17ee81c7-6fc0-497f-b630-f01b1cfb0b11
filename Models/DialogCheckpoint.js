const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const Dialogs = require("./Dialogs");

class DialogCheckpoint extends Model {}

DialogCheckpoint.init(
  {
    dialog_checkpoint_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    conversation_id: {
      type: DataTypes.STRING,
    },
    tag: {
      type: DataTypes.STRING,
    },
    channel: {
      type: DataTypes.STRING,
    },
    bot_id: {
      type: DataTypes.INTEGER,
    },
    dialog_id: {
      type: DataTypes.INTEGER,
      references: {
        model: Dialogs,
        key: "dialog_id",
      },
    },
  },
  {
    sequelize,
    modelName: "bot_designer_dialog_checkpoints",
    indexes: [
      {
        name: "idx_dialog_checkpoints_dialog_id", // Use the exact same name you used in the database
        fields: ["dialog_id"],
      },
      {
        name: "idx_dialog_checkpoints_created_at", // Index for date range queries
        fields: ["createdAt"],
      },
      {
        name: "idx_dialog_checkpoints_bot_id_created_at", // Composite index for bot_id + createdAt queries
        fields: ["bot_id", "createdAt"],
      },
      {
        name: "idx_dialog_checkpoints_dialog_id_created_at", // Composite index for dialog_id + createdAt queries
        fields: ["dialog_id", "createdAt"],
      },
    ],
  }
);

DialogCheckpoint.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

module.exports = DialogCheckpoint;
