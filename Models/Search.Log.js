const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class SearchLog extends Model {}

SearchLog.init(
  {
    search_log_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    search_term: {
      type: DataTypes.STRING,
    },
    city: {
      type: DataTypes.STRING,
    },
  },
  { sequelize, modelName: "bot_designer_search_logs" }
);

module.exports = SearchLog;
