const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const InternalLiveChatQueue = require("./InternalLiveChatQueue");

class InternalLiveChatPendingMessages extends Model {}

InternalLiveChatPendingMessages.init(
  {
    pending_message_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    queue_id: {
      type: DataTypes.INTEGER,
      references: {
        model: InternalLiveChatQueue,
        key: "queue_id",
      }
    },
    message: {
      type: DataTypes.STRING,
    },
  },
  {
    sequelize,
    modelName: "bot_designer_internal_livechat_pending_messages",
  }
);

module.exports = InternalLiveChatPendingMessages;
