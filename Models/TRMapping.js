const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class TRMapping extends Model {}

TRMapping.init(
  {
    tr_mapping_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    bot_response_source_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    bot_response_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
  },
  { sequelize, modelName: "bot_designer_tr_mappings" }
);

module.exports = TRMapping;
