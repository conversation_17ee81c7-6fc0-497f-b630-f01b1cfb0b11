const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const LiveChatIntegrationSession = require("./LiveChatIntegrationSession");
class LiveChatIntegration extends Model {}

LiveChatIntegration.init(
  {
    livechat_integration_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    client_id: {
      type: DataTypes.STRING
    },
    client_secret: {
      type: DataTypes.STRING
    },
    personal_access_token: {
      type: DataTypes.STRING
    },
    encoded_personal_access_token: {
        type: DataTypes.STRING
    },
    refresh_token: {
      type: DataTypes.STRING
    },
    organization_id:{
        type: DataTypes.STRING
    },
  },
  { sequelize, modelName: "bot_designer_livechat_integration" }
);

LiveChatIntegration.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

LiveChatIntegration.hasMany(LiveChatIntegrationSession, { foreignKey: 'livechat_integration_id'});


module.exports = LiveChatIntegration;
