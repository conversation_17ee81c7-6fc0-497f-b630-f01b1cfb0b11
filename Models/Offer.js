const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const OfferItem = require("./Offer.Item");
const CartLogItem = require("./Cart.Log.Item");

class Offer extends Model {}

Offer.init(
  {
    offer_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    offer_status: {
      type: DataTypes.ENUM,
      defaultValue: "active",
      values: ["active", "inactive"],
    },
    offer_description: DataTypes.STRING(500),
    fixed_value: { type: DataTypes.FLOAT, defaultValue: 0 },
    offer_type: DataTypes.STRING,
    offer_icon: DataTypes.STRING,
    offer_percentage: DataTypes.FLOAT,
    offer_date_start: DataTypes.DATE,
    offer_date_end: DataTypes.DATE,
    lemmatized_offer_description: {
      type: DataTypes.STRING(255),
    },

  },

  { sequelize, modelName: "bot_designer_offer" },
 
);

Offer.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

Offer.hasMany(OfferItem, { foreignKey: "offer_id" });
Offer.hasMany(CartLogItem, { foreignKey: "offer_id" });

module.exports = Offer;
