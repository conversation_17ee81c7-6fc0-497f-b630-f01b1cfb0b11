const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class NER extends Model {}

NER.init(
  {
    ner_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    ner_value: {
      type: DataTypes.STRING("MAX"),
      allowNull: false,
    },
    ner_type: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    language: {
      type: DataTypes.STRING(20),
      defaultValue: "en",
    },
  },
  { sequelize, modelName: "bot_designer_ners" }
);

NER.prototype.updateInfo = function updateInfo(info) {
  this.ner_value = info.ner_value;
  this.ner_type = info.ner_type;
  this.language = info.language;
};

module.exports = NER;
