const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class JettTracks extends Model {}

JettTracks.init(
  {
    jett_track_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    track_id: {
      type: DataTypes.INTEGER,
    },
    path: {
      type: DataTypes.STRING,
    },
    path_ar: {
      type: DataTypes.STRING,
    },
    from_en: {
      type: DataTypes.STRING,
    },
    from_ar: {
      type: DataTypes.STRING,
    },
    from_lemmatized_ar: {
      type: DataTypes.STRING,
    },
    destination_en: {
      type: DataTypes.STRING,
    },
    destination_ar: {
      type: DataTypes.STRING,
    },
    destination_lemmatized_ar: {
      type: DataTypes.STRING,
    },
    roundTrip_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    isInternational: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    multiRoutes: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    }
  },
  { sequelize, modelName: "bot_designer_jett_tracks" }
);

JettTracks.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key !== "jett_track_id") {
      this[key] = info[key];
    }
  });
};

module.exports = JettTracks;
