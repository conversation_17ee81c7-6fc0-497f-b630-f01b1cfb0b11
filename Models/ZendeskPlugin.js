const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class ZendeskPlugin extends Model {}

ZendeskPlugin.init(
  {
    zendesk_plugin_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    status_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    client_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    client_secret: {
      type: DataTypes.STRING(200),
      allowNull: true,
    },
    sub_domain: {
      type: DataTypes.STRING(200),
      allowNull: true,
    },
    code_from_url: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    access_token: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    snippet_key: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  { sequelize, modelName: "bot_designer_zendesk_plugins" }
);

ZendeskPlugin.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
};

module.exports = ZendeskPlugin;
