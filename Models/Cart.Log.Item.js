const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class CartLogItem extends Model {}

CartLogItem.init(
  {
    cart_item_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    title: {
      type: DataTypes.STRING(100),
    },
    price: {
      type: DataTypes.FLOAT,
    },
    sale: {
      type: DataTypes.FLOAT,
    },
    qty: {
      type: DataTypes.INTEGER,
    },
    note: {
      type: DataTypes.STRING(1000),
    },
    continent: {
      type: DataTypes.STRING,
    },
    country: {
      type: DataTypes.STRING,
    },
    city: {
      type: DataTypes.STRING,
    },
    vat_ammount: {
      type: DataTypes.FLOAT,
    },
    vat_ammount_pct: {
      type: DataTypes.FLOAT,
    },
    discount_pct: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
    },
  },
  { sequelize, modelName: "bot_designer_cart_log_items" }
);

module.exports = CartLogItem;
