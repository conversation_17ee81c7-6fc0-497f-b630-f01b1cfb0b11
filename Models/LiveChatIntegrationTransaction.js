const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class LiveChatIntegrationTransaction extends Model {}

LiveChatIntegrationTransaction.init(
  {
    livechat_integration_tr_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    from: {
        type: DataTypes.ENUM,
        values: ["customer", "agent"],
    },
    message: {
        type: DataTypes.STRING
    }
  },
  { sequelize, modelName: "bot_designer_livechat_integration_tr" }
);

LiveChatIntegrationTransaction.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

module.exports = LiveChatIntegrationTransaction;
