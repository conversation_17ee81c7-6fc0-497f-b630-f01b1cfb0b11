const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const Custom_Attendance = require("./Custom.Attendance");
const Custom_Payment = require("./Custom.Payment");

class Trainee_Class extends Model {}

Trainee_Class.init(
  {
    trainee_class_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    start_date:{
      type: DataTypes.DATE
    },
    end_date:{
      type: DataTypes.DATE
    },
    attendance_number:{
      type: DataTypes.INTEGER,
      defaultValue:0
    },
    is_paid:{
      type: DataTypes.BOOLEAN,
      defaultValue:false
     },
    note:{
      type:DataTypes.STRING
    }
 
  },
  { sequelize, modelName: "bot_designer_customs_Registrations"}
);



Trainee_Class.hasMany(Custom_Attendance, { foreignKey: "trainee_class_id" });

Trainee_Class.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
}


module.exports = Trainee_Class;
