const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class JettCardRecharge extends Model {}

JettCardRecharge.init(
  {
    jett_card_recharges_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    jett_invoice_id: {
      type: DataTypes.INTEGER,
    },
    card_number: {
      type: DataTypes.STRING,
    },
    subscription_number: {
      type: DataTypes.STRING,
    },
    amount: {
      type: DataTypes.STRING,
    },
  },
  { sequelize, modelName: "bot_designer_jett_card_recharges" }
);

JettCardRecharge.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

module.exports = JettCardRecharge;
