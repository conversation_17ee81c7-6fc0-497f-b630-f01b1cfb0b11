const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const Trainee_Class = require("./Custom.Class.Trainee");
const Custom_Attendance = require("./Custom.Attendance");
const Custom_Payment = require("./Custom.Payment");
const Custom_WorkingHours = require("./Custom.WorkingHours");

class Customs_Class extends Model {}

Customs_Class.init(
  {
    class_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    class_desc: {
      type: DataTypes.STRING,
    },
    location:{
      type: DataTypes.STRING
    }
  },
  { sequelize, modelName: "bot_designer_customs_Classes" }
);

Customs_Class.hasMany(Trainee_Class, { foreignKey: "class_id" });
Customs_Class.hasMany(Custom_WorkingHours, { foreignKey: "class_id" });
Customs_Class.hasMany(Custom_Payment, { foreignKey: "class_id" });



Customs_Class.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
  
}

module.exports = Customs_Class;
