const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Editor extends Model {}

Editor.init(
  {
    editor_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    appearance_privilege: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    builder_privilege: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    store_privilege: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    transaction_dashboard_privilege: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    sales_dashboard_privilege: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    deployment_privilege: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    editor_privilege: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    lead_privilege: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    report_privilege: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    addon_privilege: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    dialog_privilege: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    agent_privilege: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    ticketing_privilege: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
  },
  { sequelize, modelName: "bot_designer_editor" }
);

Editor.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
};

module.exports = Editor;
