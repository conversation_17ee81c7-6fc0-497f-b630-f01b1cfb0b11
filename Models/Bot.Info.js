const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class BotInfo extends Model {}

BotInfo.init(
  {
    bot_info_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    company_name: {
      type: DataTypes.STRING(150),
    },
    continent: {
      type: DataTypes.STRING(80),
    },
    country: {
      type: DataTypes.STRING(80),
    },
    city: {
      type: DataTypes.STRING(80),
    },
    suburb: {
      type: DataTypes.STRING(100),
    },
    street: {
      type: DataTypes.STRING,
    },
    building_number: {
      type: DataTypes.STRING(50),
    },
    additional_details: {
      type: DataTypes.STRING(500),
    },
    email: {
      type: DataTypes.STRING,
    },
    phone: {
      type: DataTypes.STRING,
    },
    additional_phone: {
      type: DataTypes.STRING,
    },
    timezone: {
      type: DataTypes.STRING,
    },
    website: {
      type: DataTypes.STRING,
    },
    instegram: {
      type: DataTypes.STRING,
    },
    twitter: {
      type: DataTypes.STRING,
    },
    facebook: {
      type: DataTypes.STRING,
    },
    longitude: {
      type: DataTypes.STRING(100),
    },
    latitude: {
      type: DataTypes.STRING(100),
    },
    privacy_policy: {
      type: DataTypes.STRING("2000"),
    },
    terms_conditions: {
      type: DataTypes.STRING("2000"),
    },
    replacement_policy: {
      type: DataTypes.STRING("2000"),
    },
  },
  { sequelize, modelName: "bot_designer_bot_infos" }
);

BotInfo.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key] ? info[key] : this[key];
  });
};

module.exports = BotInfo;
