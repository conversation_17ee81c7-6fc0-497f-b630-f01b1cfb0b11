const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class SendGrid extends Model {}

SendGrid.init(
  {
    sendGrid_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    apiKey:{
      type: DataTypes.STRING,
      allowNull: true
    }
  },
  { sequelize, modelName: "bot_designer_sendgrid" }
);

SendGrid.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
  
}


module.exports = SendGrid;
