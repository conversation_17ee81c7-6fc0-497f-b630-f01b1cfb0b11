const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const TicketingSupportTicket = require("./TicketingSupportTicket");
const TicketingSupportAgent = require("./TicketingSupportAgent");

class TicketingRating extends Model {}

TicketingRating.init(
  {
    rating_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    ticket_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    support_agent_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    rating: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    rating_msg: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "bot_designer_ticketing_ratings",
  }
);

TicketingRating.belongsTo(TicketingSupportTicket, {
  foreignKey: "ticket_id",
  as: "ticket",
});

TicketingSupportTicket.hasMany(TicketingRating, {
  foreignKey: "ticket_id",
  as: "ratings",
});

TicketingRating.belongsTo(TicketingSupportAgent, {
  foreignKey: "support_agent_id",
  as: "support_agent",
});

TicketingSupportAgent.hasMany(TicketingRating, {
  foreignKey: "support_agent_id",
  as: "ratings",
});

module.exports = TicketingRating;
