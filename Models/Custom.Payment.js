const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Custom_Payment extends Model {}

Custom_Payment.init(
  {
    payment_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    payment_date:{
      type: DataTypes.DATE
    },
    paid_balance:{
      type: DataTypes.FLOAT
     },
     unpaid_balance:{
      type: DataTypes.FLOAT
     },
     receipt_number:{
      type: DataTypes.INTEGER
     }
  },
  { sequelize, modelName: "bot_designer_customs_Payments" }
);

Custom_Payment.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
  
}


module.exports = Custom_Payment;
