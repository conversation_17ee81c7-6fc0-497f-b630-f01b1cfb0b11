const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class BotFeedback extends Model {}

BotFeedback.init(
  {
    bot_feedback_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    rate: {
      type: DataTypes.INTEGER,
    },
    feedback_body: {
      type: DataTypes.STRING,
    },
  },
  { sequelize, modelName: "bot_designer_bot_feedbacks" }
);

module.exports = BotFeedback;
