const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const Triggers_Logs = require("./Triggers.Logs");
const Topic = require("./Topic");
const Dialogs = require('./Dialogs')

class Trigger extends Model {}

Trigger.init(
  {
    trigger_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    trigger_name: {
      type: DataTypes.STRING,
    },
    trigger: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    trigger_type: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    url: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    is_main: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    lemmatized_trigger: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    topic_id: {
      allowNull: true,
      type: DataTypes.INTEGER,
    },
    dialog_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: null,
      references: {
          model: Dialogs,
          key: 'dialog_id'
      }
    }
  },
  { sequelize, modelName: "bot_designer_triggers" }
);

Trigger.prototype.updateInfo = function (info) {
  this.trigger = info.triggers ? info.trigger.join(",") : this.trigger;
  this.trigger = info.trigger ? info.trigger : this.trigger;
  this.trigger_name = info.trigger_name ? info.trigger_name : this.trigger_name;
  this.url = info.url ? info.url : this.url;
  this.is_main = info.is_main;
  this.lemmatized_trigger = info.lemmatized_trigger ? info.lemmatized_trigger : this.lemmatized_trigger;
  this.topic_id = info.topic_id ? info.topic_id : this.topic_id;
  this.dialog_id = info.dialog_id ? info.dialog_id : this.dialog_id;
};

Trigger.prototype.updateDialogId = function (dialog_id) {
  this.dialog_id = dialog_id;
};

Trigger.prototype.toJSON = function toJSON() {
  return {
    trigger_id: this.trigger_id,
    trigger_name: this.trigger_name,
    bot_id: this.bot_id,
    url: this.url,
    trigger: this.trigger,
    trigger_type: this.trigger_type,
    is_main: this.is_main,
    createdAt: this.createdAt,
    lemmatized_trigger: this.lemmatized_trigger,
    topic_id: this.topic_id,
    dialog_id: this.dialog_id,
    dialog: this.dialog || null
  };
};

// Dialogs.hasMany(Trigger, { foreignKey: 'dialog_id' });
Trigger.belongsTo(Dialogs, { foreignKey: 'dialog_id' , as : 'dialog'});

Trigger.hasMany(Triggers_Logs, { foreignKey: "trigger_id" });
// Trigger.belongsTo(Topic, { foreignKey: "topic_id" });

module.exports = Trigger;
