//SECTION DB
const sequelize = require("../db");
const Bot = require("./Bot");
const { DataTypes, Model } = require("sequelize");
//SECTION AUTH
const crypto = require("crypto");
const jwt = require("jsonwebtoken");
const secret = require("../config/appConfig").secret;
const resetPwdSecret = require("../config/appConfig").resetPwdSecret;
//SECTION MAILING
const sgEmail = require("../Services/Email");
const createMessage = require("../Constants/message");
const {
  createVerifyEmail,
  createResetPassword,
} = require("../Constants/Emails");
const UserFollow = require("./User.Follow");
const UserFavorite = require("./User.Favorite");
const SearchItemLog = require("./Search.item.log");
const UserAddress = require("./User.Address");
const Biller = require("./Biller");
const FAQ_Logs = require("./Faq.Logs");
const Triggers_Logs = require("./Triggers.Logs");
const Editor = require("./Editor");

class User extends Model {}

User.init(
  {
    user_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    email: {
      type: sequelize.Sequelize.STRING,
      allowNull: false,
      primaryKey: true,
      unique: true,
    },

    email_verification: {
      defaultValue: false,
      type: DataTypes.BOOLEAN,
    },
    user_name: sequelize.Sequelize.STRING,
    company_name: sequelize.Sequelize.STRING,
    phone_number: sequelize.Sequelize.STRING,

    photo: sequelize.Sequelize.STRING,

    reset: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    salt: DataTypes.STRING(4000),
    hash: DataTypes.STRING(4000),
    age: {
      type: DataTypes.INTEGER,
    },
    gender: {
      type: DataTypes.STRING(15),
    },
    city: {
      type: DataTypes.STRING(50),
    },
    country: {
      type: DataTypes.STRING(50),
    },
  },
  { sequelize, modelName: "bot_designer_user" }
);

User.beforeSave(function (user, options) {});

User.prototype.updateRecord = function () {
  this.changed("updatedAt", true);
};

User.beforeCreate(function (user, options) {
  user.email = user.email.toLowerCase();
  user.user_name = user.user_name.toLowerCase();
});

User.prototype.setPassword = function (password) {
  this.salt = crypto.randomBytes(16).toString("hex");
  this.hash = crypto
    .pbkdf2Sync(password, this.salt, 10000, 512, "sha512")
    .toString("hex");
};

User.prototype.authenticate = function authenticate(password) {
  var hash = crypto
    .pbkdf2Sync(password, this.salt, 10000, 512, "sha512")
    .toString("hex");
  return this.hash === hash;
};

User.afterCreate(function (user, options, cb) {
  //NOTE VEIFICATION
  user.sendConfirmationInstructions();
});

User.prototype.generateAutoLoginURL = function generateAutoLoginURL() {};

User.prototype.sendConfirmationInstructions =
  function sendConfirmationInstructions() {
    sgEmail.send(
      createMessage(
        "verify",
        this.email,
        createVerifyEmail(this.email, this.user_id)
      )
    );
  };

User.prototype.sendResetPassword = function sendResetPassword() {
  sgEmail.send(
    createMessage(
      "reset password",
      this.email,
      createResetPassword(this.email, this.user_id)
    )
  );
};

User.prototype.generateToken = function generateToken() {
  return jwt.sign(
    {
      id: this._id,
      name: this.user_name,
    },
    secret
  );
};

User.prototype.toAuthJSON = function toAuthJSON() {
  return {
    ...this.toJSON(),
    token: this.generateToken(),
  };
};
User.prototype.toJSON = function toJSON() {
  return {
    user_id: this.user_id,
    email: this.email,
    company_name: this.company_name,
    email_verification: this.email_verification,
    user_name: this.user_name,
    admin: this.admin,
    title: this.title,
    photo: this.photo,
    langauge: this.langauge,
    department: this.department,
    mobile: this.mobile,
    disabled: this.disabled,
    city: this.city,
    country: this.country,
    gender: this.gender,
    age: this.age,
  };
};

User.hasMany(Bot, { foreignKey: "user_id" , as: "bots"});
Bot.belongsTo(User, { foreignKey: "user_id" , as: "user"});
User.hasMany(UserFollow, { foreignKey: "user_id" });
User.hasMany(UserFavorite, { foreignKey: "user_id" });
User.hasMany(SearchItemLog, { foreignKey: "user_id" });
User.hasMany(UserAddress, { foreignKey: "user_id" });
User.hasMany(Biller, { foreignKey: "user_id" });
User.hasMany(FAQ_Logs, { foreignKey: "user_id" });
User.hasMany(Triggers_Logs, { foreignKey: "user_id" });
User.hasMany(Triggers_Logs, { foreignKey: "email" });
User.hasMany(FAQ_Logs, { foreignKey: "email" });

module.exports = User;
