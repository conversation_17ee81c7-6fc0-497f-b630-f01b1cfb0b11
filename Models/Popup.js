const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class Popup extends Model {}

Popup.init(
  {
    popup_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    popup_type: {
      type: DataTypes.STRING,
      defaultValue: "message",
    },
    popup_url: {
      type: DataTypes.STRING,
    },
    popup_message: {
      type: DataTypes.STRING,
    },
    popup_description: {
      type: DataTypes.STRING,
    },
    popup_lifetime: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    popup_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    plugin_type: {
      type: DataTypes.STRING,
      defaultValue: "marketing", //NOTE marketing/fallback and we are adding more
    },

    popup_language: {
      type: DataTypes.STRING(10),
    },
  },
  { sequelize, modelName: "bot_designer_popups" }
);

Popup.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      if (key === "popup_lifetime") {
        this.popup_lifetime = new Date(info.popup_lifetime);
      } else {
        this[key] = info[key];
      }
    }
  });
};

module.exports = Popup;
