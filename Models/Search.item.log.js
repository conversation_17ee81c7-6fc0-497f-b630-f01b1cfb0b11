const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class SearchItemLog extends Model {}

SearchItemLog.init(
  {
    item_log_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    city: {
      type: DataTypes.STRING,
    },
  },
  { sequelize, modelName: "bot_designer_search_item_logs" }
);

module.exports = SearchItemLog;
