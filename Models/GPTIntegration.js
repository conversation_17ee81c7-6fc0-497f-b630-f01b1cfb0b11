const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class GPTIntegration extends Model {}
GPTIntegration.init(
  {
    gpt_integration_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    status_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    openai_key: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    bot_id: {
      type: DataTypes.INTEGER,
    },
  },
  { sequelize, modelName: "bot_designer_gpt_integrations" }
);

GPTIntegration.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

module.exports = GPTIntegration;
