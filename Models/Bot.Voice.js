const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class <PERSON>tVoice extends Model {}

BotVoice.init(
  {
    bot_voice_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    voice_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    tts_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    stt_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    tts_gender: {
      type: DataTypes.STRING,
      defaultValue: "male",
    },
    show_stt_text: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    custom_voice_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    male_voice: {
      type: DataTypes.STRING,
      defaultValue:"ar:ar-KW-Saad_en:en-GB-James",
      allowNull: true,
    },
    female_voice: {
      type: DataTypes.STRING,
      defaultValue: "ar:ar-KW-Hind_en:en-GB-Emma",
      allowNull: true,
    },
    // pitch: {
    //   type: DataTypes.FLOAT,
    //   defaultValue: 1.0,
    // },
    // rate: {
    //   type: DataTypes.FLOAT,
    //   defaultValue: 1.0,
    // }
  },
  { sequelize, modelName: "bot_designer_bot_voices" }
);

BotVoice.prototype.updateInfo = function (info) {
  this.voice_active = info.voice_active ? true : false;
  this.stt_active = info.stt_active;
  this.tts_active = info.tts_active;
  this.tts_gender = info.tts_gender;
  this.show_stt_text = info.show_stt_text;
  this.custom_voice_active = info.custom_voice_active;
  this.male_voice = info.male_voice;
  this.female_voice = info.female_voice;
  // this.pitch = info.pitch;
  // this.rate = info.rate;
};

module.exports = BotVoice;
