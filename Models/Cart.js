const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Cart extends Model {}

Cart.init(
  {
    cart_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    cart_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    email: {
      type: DataTypes.STRING(50),
    },
    phone_number: {
      type: DataTypes.STRING(50),
    },
    phone_verified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    email_verified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    enable_api: {
      type: DataTypes.BOOLEAN,
    },
    vat_ammount: {
      type: DataTypes.FLOAT,
    },
    api: DataTypes.STRING(1000),

    trigger_name: DataTypes.STRING(50),
    decimal: DataTypes.FLOAT,
    cart_currency: DataTypes.STRING(10),
    cart_delivery: DataTypes.FLOAT,

    cart_icon: DataTypes.STRING,
    // business_address: DataTypes.STRING,
    timezone: DataTypes.STRING,
    business_name: DataTypes.STRING,
    vat_reg_num: DataTypes.STRING,
  },
  { sequelize, modelName: "bot_designer_carts" }
);

Cart.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    if (key === "api") {
      this.api = JSON.stringify(info[key]);
    } else {
      this[key] = info[key];
    }
  });
};

Cart.prototype.toJSON = function () {
  return {
    cart_id: this.cart_id,
    cart_active: this.cart_active,
    email: this.email,
    bot_id: this.bot_id,
    phone_number: this.phone_number,

    phone_verified: this.phone_verified,

    email_verified: this.email_verified,

    enable_api: this.enable_api,

    vat_ammount: this.vat_ammount,

    api: this.api ? JSON.parse(this.api) : undefined,

    trigger_name: this.trigger_name,
    decimal: this.decimal,
    cart_currency: this.cart_currency,
    cart_delivery: this.cart_delivery,

    cart_icon: this.cart_icon,
    // business_address: this.business_address,
    timezone: this.timezone,
    business_name: this.business_name,
    vat_reg_num: this.vat_reg_num,
  };
};

module.exports = Cart;
