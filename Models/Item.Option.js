const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const OfferItem = require("./Offer.Item");

class ItemOption extends Model {}

ItemOption.init(
  {
    item_option_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    option_title: DataTypes.STRING,
    option_description: DataTypes.STRING,
    option: DataTypes.STRING(1000),
 
  },
 
  { sequelize, modelName: "bot_designer_item_options" }
);

ItemOption.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

ItemOption.prototype.toJSON = function toJSON(info) {
  return {
    item_id: this.item_id,
    item_option_id: this.item_option_id,
    option_title: this.option_title,
    option_description: this.option_description,
    option: this.option ? JSON.parse(this.option) : undefined,
  };
};

module.exports = ItemOption;
