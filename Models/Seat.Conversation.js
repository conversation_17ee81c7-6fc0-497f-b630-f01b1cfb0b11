const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const SeatHours = require("./Seat.Hours");
const SeatMessage = require("./Seat.Message");

class SeatConversation extends Model {}

SeatConversation.init(
  {
    seatconversation_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    conversation_id: {
      type: DataTypes.STRING,
    },
    ended: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    channel: {
      type: DataTypes.STRING(20),
    },
  },
  { sequelize, modelName: "bot_designer_livechat_conversations" }
);

SeatConversation.prototype.endConv = function () {
  this.ended = true;
};
SeatConversation.prototype.startConv = function () {
  this.ended = false;
};

SeatConversation.hasMany(SeatMessage, { foreignKey: "seatconversation_id" });

module.exports = SeatConversation;
