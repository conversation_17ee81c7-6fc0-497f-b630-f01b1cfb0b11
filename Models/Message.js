const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Message extends Model {}

Message.init(
  {
    language: {
      type: DataTypes.STRING(25),
      primaryKey: true,
      allowNull: false,
    },
    itemsfoundMessage: {
      type: DataTypes.STRING,
    },
    verifyPhoneMessage: {
      type: DataTypes.STRING,
    },
    verifySuccessfulMessage: {
      type: DataTypes.STRING,
    },
    quantityMessage: {
      type: DataTypes.STRING,
    },
    deleteCartMessage: {
      type: DataTypes.STRING,
    },
    checkoutCartMessage: {
      type: DataTypes.STRING,
    },
    checkoutMessage: { type: DataTypes.STRING },
    deleteMessage: { type: DataTypes.STRING },
    continueMessage: { type: DataTypes.STRING },
    requestNameMessage: { type: DataTypes.STRING },
    requestAddressMessage: { type: DataTypes.STRING },
    confirmSendingVerificationCodeMessage: { type: DataTypes.STRING },
    rejectSendingVerificationCodeButtonMessage: { type: DataTypes.STRING },
    afterSendingVerificationCodeMessage: { type: DataTypes.STRING },
    wrongVerificationCodeButtonMessage: { type: DataTypes.STRING },
    processAlreadyProceedMessage: { type: DataTypes.STRING },
    processCanceledMessage: { type: DataTypes.STRING },
    cartAlreadyCheckedOutMessage: { type: DataTypes.STRING },
    conflictMessage: { type: DataTypes.STRING },
    distanceMessage: { type: DataTypes.STRING },
    checkoutCartSuggestionMessage: { type: DataTypes.STRING },
    dropCartSuggestionMessage: { type: DataTypes.STRING },
    askAboutItemSpecsMessage: { type: DataTypes.STRING },
    askAboutReservationDateMessage: { type: DataTypes.STRING },
    busniessClosedMessage: { type: DataTypes.STRING },
    requestNumberOfGuestsMessage: { type: DataTypes.STRING },
    tableNotAvailableMessage: { type: DataTypes.STRING },
    reservedSuccessfullyMessage: { type: DataTypes.STRING },

    outOfContextMessage: { type: DataTypes.STRING },
    addToCartMessage: { type: DataTypes.STRING },
    requestItemNoteMessage: { type: DataTypes.STRING },
    mainTourMicMessage: { type: DataTypes.STRING },
    mainTourToggleMicLanguageMessage: { type: DataTypes.STRING },
    mainTourTextInputMessage: { type: DataTypes.STRING },
    mainTourMainMenuMessage: { type: DataTypes.STRING },
    mainTourCloseChatbotWindowMessage: { type: DataTypes.STRING },
    mainTourResizeChatbotWindowMessage: { type: DataTypes.STRING },
    itemCardTourItemPriceMessage: { type: DataTypes.STRING },
    itemCardTourTtsMessage: { type: DataTypes.STRING },
    itemCardTourDescriptionMessage: { type: DataTypes.STRING },
    itemCardTourAskAboutSpecsMessage: { type: DataTypes.STRING },
    itemCardTourShareMessage: { type: DataTypes.STRING },
  },
  { sequelize, modelName: "bot_designer_messages" }
);

Message.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
};

module.exports = Message;
