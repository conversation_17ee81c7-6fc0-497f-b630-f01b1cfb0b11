const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const ReservationTable = require("./Reservation.Table");
const ReservationOrder = require("./Reservation.Order");

class ReservationRoom extends Model {}

ReservationRoom.init(
  {
    room_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    room_name: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    room_description: {
      type: DataTypes.STRING(300),
    },
    room_icon: {
      type: DataTypes.STRING(500),
    },
    floor: {
      type: DataTypes.INTEGER,
    },
    room_status: {
      type: DataTypes.STRING,
      defaultValue: "public", //private, family
    },
    room_active: {
      type: DataTypes.BOOLEAN,
    },
    smoking_free: {
      type: DataTypes.BOOLEAN,
    },
    placement: {
      type: DataTypes.STRING,
      defaultValue: "indoor", //outdoor
    },
  },
  { sequelize, modelName: "bot_designer_reservation_rooms" }
);

ReservationRoom.prototype.updateInfo = function (info) {
  this.room_name = info.room_name;
  this.room_description = info.room_description;
  this.room_icon = info.room_icon;
  this.floor = info.floor;
  this.room_status = info.room_status;
  this.room_active = info.room_active;
  this.placement = info.placement;
  this.smoking_free = info.smoking_free;
};

ReservationRoom.hasMany(ReservationOrder, { foreignKey: "room_id" });
ReservationRoom.hasMany(ReservationTable, { foreignKey: "room_id" });

module.exports = ReservationRoom;
