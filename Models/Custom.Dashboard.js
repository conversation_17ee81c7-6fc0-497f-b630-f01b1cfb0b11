const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class CustomDashboard extends Model {}

CustomDashboard.init(
  {
    custom_dashboard_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    bot_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    title: {
      type: DataTypes.STRING(255),
    },
    widget_type: {
      type: DataTypes.STRING(255),
    },
    description : {
      type: DataTypes.STRING(1000),
    },
    query: {
      type: DataTypes.STRING(2000),
      allowNull: true,
    },
    icon: {
      type: DataTypes.STRING(50),
      allowNull: true,
    },
    x_label: {
      type: DataTypes.STRING(50),
      allowNull: true,
    },
    y_label: {
      type: DataTypes.STRING(50),
      allowNull: true,
    },
  },
  { sequelize, modelName: "bot_designer_custom_dashboards" }
);

CustomDashboard.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
};

const Bot = require("./Bot");

CustomDashboard.belongsTo(Bot, { foreignKey: "bot_id" });

module.exports = CustomDashboard;
