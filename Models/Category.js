const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const Item = require("./Item");
class Category extends Model {}

Category.init(
  {
    category_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    category_description: {
      type: DataTypes.STRING(500),
    },
    category_name: {
      type: DataTypes.STRING,
    },
    category_icon: {
      type: DataTypes.STRING(500),
    },
    category_icon: {
      type: DataTypes.STRING(500),
    },
    lemmatized_category_name: {
      type: DataTypes.STRING(255),
    }

  },

  { sequelize, modelName: "bot_designer_category" }
);

Category.prototype.updateInfo = function (info) {
  this.category_description = info.category_description
    ? info.category_description
    : this.category_description;
  this.category_name = info.category_name
    ? info.category_name
    : this.category_name;
  this.category_icon = info.category_icon
    ? info.category_icon
    : this.category_icon;
  this.lemmatized_category_name = info.lemmatized_category_name ?
    info.lemmatized_category_name : this.lemmatized_category_name
};

Category.hasMany(Item, { foreignKey: "category_id" });

module.exports = Category;
