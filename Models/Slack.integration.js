const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class SlackIntegration extends Model {}

SlackIntegration.init(
  {
    slack_integration_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    bot_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    channel_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    channel: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    api_token: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  { sequelize, modelName: "bot_designer_slack_integration" }
);

SlackIntegration.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
};

module.exports = SlackIntegration;
