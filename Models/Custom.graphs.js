const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class Custom_graphs extends Model {}
Custom_graphs.init(
  {
    custom_graphs_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    graph_name: {
      type: DataTypes.STRING(50),
    },
  },
  { sequelize, modelName: "bot_designer_custom_graphs" }
);

Custom_graphs.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
};

module.exports = Custom_graphs;
