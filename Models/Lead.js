const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class Lead extends Model {}

Lead.init(
  {
    lead_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    lead_email: {
      type: DataTypes.STRING(50),
    },
    lead_phone: {
      type: DataTypes.STRING(20),
    },
    lead_country: {
      type: DataTypes.STRING(50),
    },
    lead_city: {
      type: DataTypes.STRING(50),
    },
  },
  { sequelize, modelName: "bot_designer_leads" }
);

module.exports = Lead;
