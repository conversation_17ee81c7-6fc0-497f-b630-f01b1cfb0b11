const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class LiveChatPlugin extends Model {}
LiveChatPlugin.init(
  {
    Live_Chat_Num_Seat_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    seat_num: {
      allowNull: false,
      type: DataTypes.INTEGER,
    },
    time_zone: {
      type: DataTypes.CHAR(40),
    },
  },
  { sequelize, modelName: "bot_designer_Livechat_Plugin" }
);

LiveChatPlugin.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
};

module.exports = LiveChatPlugin;
