const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const BotDesign = require("./Bot.Design");
const FAQ = require("./FAQ");
const Trigger = require("./Trigger");
// const Transaction = require("./Transaction");
const FaqContext = require("./FAQ.Context");
const TriggerContext = require("./Trigger.Context");
const Dashboard = require("./Bot.Dashboard");
const Config = require("./Bot.Config");
const Item = require("./Item");
const Offer = require("./Offer");
const OfferItem = require("./Offer.Item");
const Category = require("./Category");
const Facebook = require("./Facebook");
const Cart = require("./Cart");
const CartLog = require("./Cart.Log");
const Weather = require("./Weather");
const BotUser = require("./Bot.User");
const Phone = require("./Phone");
const Email = require("./Email");
const Popup = require("./Popup");
const BotVoice = require("./Bot.Voice");
const PopupPlugin = require("./PopupPlugin");
const CartLogItem = require("./Cart.Log.Item");
const Voice = require("./Voice");
const Location = require("./Location");
const Customs_Trial = require("./Custom.Trial");
const Custom_Discount = require("./Custom.Discount");
const FallbackPlugin = require("./FallbackPlugin");
const BotInfo = require("./Bot.Info");
const MonitorBot = require("./Monitor.bot");
const WorkingHours = require("./WorkingHours");
const Reservation = require("./Reservation");
const ReservationRoom = require("./Reservation.Room");
const ReservationTable = require("./Reservation.Table");
const ReservationOrder = require("./Reservation.Order");
const Feature = require("./Feature");
const ItemFeature = require("./Item.Feature");
const UserFavorite = require("./User.Favorite");
const UserFollow = require("./User.Follow");
const BotFeedback = require("./Feedback");
const Invoice = require("./Invoice");
const Conversion = require("./Conversion");
const Lead = require("./Lead");
const LeadPlugin = require("./LeadPlugin");
const Editor = require("./Editor");
const TagMatch = require("./TagMatch");
const WhatsApp = require("./WhatsApp");
const BotUniqueUser = require("./Bot.UniqueUser");
const ReportPlugin = require("./ReportPlugin");
const Report = require("./Report");
const GoogleSheetPlugin = require("./GoogleSheetPlugin");
const Shopify = require("./Shopify");
const FallbackTransaction = require("./FallbackTransaction");
const statusArray = ["restarting", "stopped", "running", "deleted"];
const ZendeskPlugin = require("./ZendeskPlugin");
const ZendeskTicketPlugin = require("./ZendeskTicketPlugin");
const WhatsAppTester = require("./WaTester");
const TrelloPlugin = require("./TrelloPlugin");
const Seat = require("./Seat"); //NOTE FOREIGN KEY MUST BE DELETED FROM DB
const SeatConversation = require("./Seat.Conversation");
const SeatHours = require("./Seat.Hours");
const SeatMessage = require("./Seat.Message");
const SendGrid = require("./SendGrid");
const SeatBot = require("./Seat.Bot");
const BotTemplate = require("./BotTemplate");
const LiveChatPlugin = require("./LiveChatPlugin");
const Customs_Trainee = require("./Customs.Trainee");
const Bot_Custom = require("./Bot.Custom");
const Customs_Class = require("./Custom.Class");
const Trainee_Class = require("./Custom.Class.Trainee");
const Custom_Attendance = require("./Custom.Attendance");
const Custom_Payment = require("./Custom.Payment");
const Custom_graphs = require("./Custom.graphs");
const Custom_WorkingHours = require("./Custom.WorkingHours");
const Customs_School = require("./Custom.School");
const BotDomain = require("./Bot.Domain");
const Custom_User = require("./Custom.User");
const Custom_HealthCenter = require("./Custom.HealthCenter");
const FAQ_Logs = require("./Faq.Logs");
const Triggers_Logs = require("./Triggers.Logs");
const SanadService = require("./Sanad.Service");
const SanadFaq = require("./Sanad.Faq");
const SanadTrigger = require("./Sanad.Trigger");
const WhatsappBroadCast = require("./Whatsapp.broadcasting");
const LiveChatIntegration = require("./LiveChatIntegration");
const Dialogs = require("./Dialogs");
const Topic = require("./Topic");
const GenesysIntegration = require("./GenesysIntegration");
const DynamicFormSchema = require("./DynamicFormSchema");
const TRUserInteraction = require("./TRUserInteractions");
const TRCube = require("./TRCubes");


class Bot extends Model {}

Bot.init(
  {
    bot_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    description: {
      type: DataTypes.STRING(500),
    },
    status: {
      type: DataTypes.ENUM,
      values: statusArray,
      defaultValue: "running",
    },
    bot_type: {
      type: DataTypes.STRING(50),
      defaultValue: "general",
    },
    bot_name: DataTypes.STRING,
    file_name: DataTypes.STRING,
    bot_icon: DataTypes.STRING,
    language: {
      type: DataTypes.STRING(20),
      defaultValue: "en",
    },
    fallback_message: DataTypes.STRING,
    fallback_dialog:DataTypes.STRING,
    domain: {
      type: DataTypes.STRING,
    },
    service_secret: {
      defaultValue: "vNx22Nea_Ys.wK-VqJfIxFyAOO8A99GUCVTU9Mr6dz_C1lamtuom9BA",
      type: DataTypes.STRING,
    },
     mock_endpoint: {
      type: DataTypes.STRING(200),
    },
    is_template: {
      defaultValue: false,
      type: DataTypes.BOOLEAN,
    },
    bot_category: {
      type: DataTypes.STRING(70),
    },
    deleted: {
      defaultValue: false,
      type: DataTypes.BOOLEAN,
    },
    is_dashboard_active: {
      defaultValue: false,
      type: DataTypes.BOOLEAN,
    }
  },
  { sequelize, modelName: "bot_designer_bot" }
);

Bot.prototype.setFileName = function setFileName() {
  this.file_name = `${this.bot_name}-${this.bot_id}`;
};

Bot.prototype.updateInfo = function updateInfo(info) {
  this.bot_name = info.bot_name ? info.bot_name : this.bot_name;
  this.mock_endpoint = info.mock_endpoint ? info.mock_endpoint : this.mock_endpoint;
  this.bot_icon = info.icon ? info.icon : this.icon;
  this.language = info.language ? info.language : this.language;
  this.domain = info.domain ? info.domain : this.domain;
  this.description = info.description ? info.description : this.description;
  this.status =
    statusArray.indexOf(info.status) !== -1 ? info.status : this.status;
  this.fallback_message =
    typeof info.fallback_message === "string"
      ? info.fallback_message
      : this.fallback_message;
      this.fallback_dialog =
      typeof info.fallback_dialog === "string"
        ? info.fallback_dialog
        : this.fallback_dialog;
  this.service_secret = info.service_secret
    ? info.service_secret
    : this.service_secret;
  this.bot_category = info.bot_category ? info.bot_category : this.bot_category;
  this.is_template = info.is_template ? info.is_template : this.is_template;
  this.plan_id = info.plan_id ? info.plan_id : this.plan_id;
  this.bot_type = info.bot_type ? info.bot_type : this.bot_type;
};

Bot.prototype.setdDeleted = function () {
  this.deleted = true;
};

Bot.prototype.toJSON = function () {
  return {
    user_id: this.user_id,
    description: this.description,
    status: this.status,
    domain: this.domain,
    bot_id: this.bot_id,
    service_secret: this.service_secret,
    bot_name: this.bot_name,
    language: this.language,
    file_name: this.file_name,
    fallback_message: this.fallback_message,
    fallback_dialog:this.fallback_dialog,
    icon: this.bot_icon,
    bot_category: this.bot_category,
    is_template: this.is_template,
    plan_id: this.plan_id,
    deleted: this.deleted,
    bot_type: this.bot_type,
    mock_endpoint: this.mock_endpoint
  };
};

Bot.hasOne(BotDesign, { foreignKey: "bot_id" });
Bot.hasMany(FAQ, { foreignKey: "bot_id" });
Bot.hasMany(Trigger, { foreignKey: "bot_id" });
Bot.hasMany(FaqContext, { foreignKey: "bot_id" });
Bot.hasMany(TriggerContext, { foreignKey: "bot_id" });
Bot.hasOne(Dashboard, { foreignKey: "bot_id" });
Bot.hasOne(Config, { foreignKey: "bot_id" });
Bot.hasMany(Facebook, { foreignKey: "bot_id" });
Bot.hasMany(Category, { foreignKey: "bot_id" });
Bot.hasMany(OfferItem, { foreignKey: "bot_id" });
Bot.hasMany(Item, { foreignKey: "bot_id" });
Bot.hasMany(Offer, { foreignKey: "bot_id" });
Bot.hasOne(Cart, { foreignKey: "bot_id" });
Bot.hasMany(CartLog, { foreignKey: "bot_id" });
Bot.hasOne(Weather, { foreignKey: "bot_id" });
Bot.hasOne(BotUser, { foreignKey: "bot_id" });
Bot.hasOne(Phone, { foreignKey: "bot_id" });
Bot.hasMany(Email, { foreignKey: "bot_id" });
Bot.hasOne(BotInfo, { foreignKey: "bot_id" });
Bot.hasOne(BotVoice, { foreignKey: "bot_id" });
Bot.hasOne(Popup, { foreignKey: "bot_id" });
Bot.hasOne(PopupPlugin, { foreignKey: "bot_id" });
Bot.hasOne(FallbackPlugin, { foreignKey: "bot_id" });
Bot.hasMany(WorkingHours, { foreignKey: "bot_id" });
Bot.hasMany(CartLogItem, { foreignKey: "bot_id" });
Bot.hasMany(Voice, { foreignKey: "bot_id" });
Bot.hasMany(Location, { foreignKey: "bot_id" });
Bot.hasMany(MonitorBot, { foreignKey: "bot_id" });
Bot.hasMany(Reservation, { foreignKey: "bot_id" });
Bot.hasMany(ReservationRoom, { foreignKey: "bot_id" });
Bot.hasMany(ReservationTable, { foreignKey: "bot_id" });
Bot.hasMany(ReservationOrder, { foreignKey: "bot_id" });
Bot.hasMany(Feature, { foreignKey: "bot_id" });
Bot.hasMany(ItemFeature, { foreignKey: "bot_id" });
Bot.hasMany(UserFollow, { foreignKey: "bot_id" }); //FIXME NOT USED
Bot.hasMany(BotFeedback, { foreignKey: "bot_id" }); //FIXME NOT USED
Bot.hasMany(Invoice, { foreignKey: "bot_id" });
Bot.hasMany(Conversion, { foreignKey: "bot_id" });
Bot.hasMany(Lead, { foreignKey: "bot_id" });
Bot.hasMany(LeadPlugin, { foreignKey: "bot_id" });
Bot.hasMany(Editor, { foreignKey: "bot_id" });
Bot.hasMany(TagMatch, { foreignKey: "bot_id" });
Bot.hasMany(WhatsApp, { foreignKey: "bot_id" });
Bot.hasMany(BotUniqueUser, { foreignKey: "bot_id" });
Bot.hasMany(ReportPlugin, { foreignKey: "bot_id" });
Bot.hasMany(Report, { foreignKey: "bot_id" });
Bot.hasMany(GoogleSheetPlugin, { foreignKey: "bot_id" });
Bot.hasMany(Shopify, { foreignKey: "bot_id" });
Bot.hasMany(ZendeskPlugin, { foreignKey: "bot_id" });
Bot.hasMany(ZendeskTicketPlugin, { foreignKey: "bot_id" });
Bot.hasMany(FallbackTransaction, { foreignKey: "bot_id" });
Bot.hasMany(WhatsAppTester, { foreignKey: "bot_id" });
Bot.hasMany(TrelloPlugin, { foreignKey: "bot_id" });
Bot.hasMany(SeatConversation, { foreignKey: "bot_id" });
Bot.hasMany(SeatMessage, { foreignKey: "bot_id" });
Bot.hasMany(SendGrid, { foreignKey: "bot_id" });
Bot.hasOne(BotTemplate, { foreignKey: "bot_id" });
Bot.hasMany(LiveChatPlugin, { foreignKey: "bot_id" });
Bot.hasMany(SeatBot, { foreignKey: "bot_id" });
Bot.hasMany(Bot_Custom, { foreignKey: "bot_id" });
Bot.hasMany(Customs_Trainee, { foreignKey: "bot_id" });
Bot.hasMany(Customs_Class, { foreignKey: "bot_id" });
Bot.hasMany(Trainee_Class, { foreignKey: "bot_id" });
Bot.hasMany(Custom_Attendance, { foreignKey: "bot_id" });
Bot.hasMany(Custom_Discount, { foreignKey: "bot_id" });
Bot.hasMany(Custom_Payment, { foreignKey: "bot_id" });
Bot.hasMany(Custom_graphs, { foreignKey: "bot_id" });
Bot.hasMany(Customs_Trial, { foreignKey: "bot_id" });
Bot.hasMany(Custom_WorkingHours, { foreignKey: "bot_id" });
Bot.hasMany(Customs_School, { foreignKey: "bot_id" });
Bot.hasMany(BotDomain, { foreignKey: "bot_id" });
Bot.hasMany(Custom_User, { foreignKey: "bot_id" });
Bot.hasMany(Custom_HealthCenter, { foreignKey: "bot_id" });
Bot.hasMany(FAQ_Logs, { foreignKey: "bot_id" });
Bot.hasMany(Triggers_Logs, { foreignKey: "bot_id" });
Bot.hasMany(SanadService, { foreignKey: "bot_id" });
Bot.hasMany(SanadFaq, { foreignKey: "bot_id" });
Bot.hasMany(SanadTrigger, { foreignKey: "bot_id" });
Bot.hasMany(WhatsappBroadCast, { foreignKey: "bot_id" });
Bot.hasMany(LiveChatIntegration, { foreignKey: "bot_id" });
Bot.hasMany(Dialogs, { foreignKey: "bot_id" });
Bot.hasMany(Topic, { foreignKey: "bot_id" });
Bot.hasOne(GenesysIntegration, { foreignKey: "bot_id" });
Bot.hasMany(DynamicFormSchema, { foreignKey: "bot_id" });
Bot.hasMany(TRCube, { foreignKey: "bot_id" });
Bot.hasMany(TRUserInteraction, { foreignKey: "bot_id" });


module.exports = Bot;
