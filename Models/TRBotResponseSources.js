const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
// const TRBotResponse = require("./TRBotResponse");
const TRMapping = require("./TRMapping");
class TRBotResponseSource extends Model {}

TRBotResponseSource.init(
  {
    bot_response_source_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    tr_cube_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    source_type: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    source_title: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    source_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    source_entity: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  { sequelize, modelName: "bot_designer_tr_bot_response_sources" }
);

// TRBotResponseSource.belongsToMany(TRBotResponse, {through: TRMapping});


module.exports = TRBotResponseSource;
