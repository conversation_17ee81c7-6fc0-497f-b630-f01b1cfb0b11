const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Context extends Model {}

Context.init(
  {
    context_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    context: {
      type: DataTypes.STRING(1000),
    },
    url: {
      type: DataTypes.STRING,
    },
    trigger_type: DataTypes.STRING(50),
  },
  { sequelize, modelName: "bot_designer_trigger_context" }
);

module.exports = Context;
