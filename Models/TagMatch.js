const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class TagMatch extends Model {}

TagMatch.init(
  {
    tag_match_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    question_id: {
      type: DataTypes.STRING(200),
    },
    question: {
      type: DataTypes.STRING(200),
    },
    tag: {
      type: DataTypes.STRING(200),
    },
    tag_type: {
      type: DataTypes.STRING(200),
    },
    total_score: {
      type: DataTypes.FLOAT,
    },
    literal_score: {
      type: DataTypes.FLOAT,
    },
    score_pct: {
      type: DataTypes.FLOAT,
    },
    match_score: {
      type: DataTypes.STRING(20),
    },
    respond: {
      type: DataTypes.BOOLEAN,
    },
  },
  { sequelize, modelName: "bot_designer_tag_match" }
);

module.exports = TagMatch;
