const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class FAQ_Logs extends Model {}

FAQ_Logs.init(
  {
    faq_log_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    faq_type: {
      type: DataTypes.STRING(1000),
    }
  },
  { sequelize, modelName: "bot_designer_faq_logs" }
);

FAQ_Logs.prototype.updateInfo = function updateInfo(info) {
    Object.keys(info).map((key) => {
        this[key] = info[key];
      }); 
};

// FAQ.prototype.toJSON = function toJSON() {
//   return {
//     faq_id: this.faq_id,
//     trigger_type: "faq",
//     bot_id: this.bot_id,
//     question: this.question,
//     hidden: this.hidden,
//     answer: this.answer,
//     parent_id: this.parent_id,
//     question_key_num: this.question_key_num,
//     type: this.type,
//     createdAt: this.createdAt,
//   };
// };


module.exports = FAQ_Logs;
