const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Custom_HealthCenter extends Model { }

Custom_HealthCenter.init(
    {
        center_id: {
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
            type: DataTypes.INTEGER,
        },
        center_type: {
            type: DataTypes.STRING
        },
        street_name: {
            type: DataTypes.STRING
        },
        phone: {
            type: DataTypes.STRING
        },
        center_address: {
            type: DataTypes.STRING
        },
        locality_name: {
            type: DataTypes.STRING
        },
        center_name: {
            type: DataTypes.STRING
        },
        from_time:{
            type: DataTypes.INTEGER
        },
        to_time:{
            type: DataTypes.INTEGER
        },
        closest_hospital:{
            type: DataTypes.STRING
        },
        governorate:{
            type: DataTypes.STRING
        },
        dist:{
            type: DataTypes.STRING
        },
        sub_dist:{
            type: DataTypes.STRING
        },
        lng:{
            type: DataTypes.FLOAT
        },
        lat:{
            type: DataTypes.FLOAT
        },
        coordinate:{
            type: DataTypes.FLOAT
        }
    },
    { sequelize, modelName: "bot_designer_customs_HealthCenters" }
);


Custom_HealthCenter.prototype.updateInfo = function (info) {
    Object.keys(info).map((key) => {
        this[key] = info[key];
    });
}

module.exports = Custom_HealthCenter;
