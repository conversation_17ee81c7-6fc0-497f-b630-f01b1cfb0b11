const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const Customs_Trainee = require("./Customs.Trainee");

class Customs_School extends Model {}

Customs_School.init(
  {
    school_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    school_name:{
      type: DataTypes.STRING,
      defaultValue:0
    }
  },
  { sequelize, modelName: "bot_designer_customs_Schools" }
);

Customs_School.hasMany(Customs_Trainee, { foreignKey: "school_id" });


Customs_School.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  }); 
}

module.exports = Customs_School;
