const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class SeatMessage extends Model {}

SeatMessage.init(
  {
    seatmessage_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    conversation_id: {
      type: DataTypes.STRING,
    },
    from: {
      type: DataTypes.STRING,
    },
    type: {
      type: DataTypes.STRING,
    },
    text: {
      type: DataTypes.STRING(2000),
    },
  },
  { sequelize, modelName: "bot_designer_livechat_messages" }
);

module.exports = SeatMessage;
