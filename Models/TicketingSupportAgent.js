const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const Editor = require("./Editor");
const TicketingAgentDepartment = require("./TicketingAgentDepartment");
const TicketingDepartment = require("./TicketingDepartment");

class TicketingSupportAgent extends Model {}

TicketingSupportAgent.init(
  {
    support_agent_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    editor_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    bot_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM("active", "inactive"),
      defaultValue: "inactive",
    },
    time_zone: {
      type: DataTypes.STRING,
      defaultValue: "Asia/Amman",
    },
  },
  {
    sequelize,
    modelName: "bot_designer_ticketing_support_agents",
  }
);

TicketingSupportAgent.belongsTo(Editor, {
  foreignKey: "editor_id",
  as: "editor",
});

Editor.hasOne(TicketingSupportAgent, {
  foreignKey: "editor_id",
  as: "support_agent",
});

TicketingDepartment.belongsToMany(TicketingSupportAgent, {
  through: TicketingAgentDepartment,
  foreignKey: "department_id",
  as: "dep_agents",
});

TicketingSupportAgent.belongsToMany(TicketingDepartment, {
  through: TicketingAgentDepartment,
  foreignKey: "support_agent_id",
  as: "departments",
});

module.exports = TicketingSupportAgent;
