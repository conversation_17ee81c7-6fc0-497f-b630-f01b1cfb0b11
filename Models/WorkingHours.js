const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class WorkingHours extends Model {}

WorkingHours.init(
  {
    working_hour_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    day: {
      type: DataTypes.STRING,
    },
    from: {
      allowNull: false,
      type: DataTypes.DATE,
    },
    to: {
      allowNull: false,
      type: DataTypes.DATE,
    },
  },
  { sequelize, modelName: "bot_designer_working_hours" }
);

module.exports = WorkingHours;
