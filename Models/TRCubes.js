const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
// const TRUserInteraction = require("./TRUserInteractions");
const TRBotResponse = require("./TRBotResponse");

class TRCube extends Model {}
TRCube.init(
  {
    tr_cube_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    bot_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    user_interaction_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    engine: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    result_status: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  { sequelize, modelName: "bot_designer_tr_cubes" }
);

TRCube.hasMany(TRBotResponse, { foreignKey: "tr_cube_id" , as: 'bot_responses'});
// TRCube.belongsTo(TRUserInteraction, { foreignKey: "user_interaction_id" });

module.exports = TRCube;
