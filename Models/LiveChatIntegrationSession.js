const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const LiveChatIntegrationTransaction = require("./LiveChatIntegrationTransaction");

class LiveChatIntegrationSession extends Model {}

LiveChatIntegrationSession.init(
  {
    livechat_integration_session_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    conversation_id: {
      type: DataTypes.STRING
    },
    chat_id: {
      type: DataTypes.STRING
    },
    channel: {
        type: DataTypes.ENUM,
        values: ["whatsapp", "web", "facebook"],
    }
  },
  { sequelize, modelName: "bot_designer_livechat_integration_session" }
);

LiveChatIntegrationSession.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

LiveChatIntegrationSession.hasMany(
  LiveChatIntegrationTransaction, 
  { foreignKey: "livechat_integration_session_id" }
);

module.exports = LiveChatIntegrationSession;
