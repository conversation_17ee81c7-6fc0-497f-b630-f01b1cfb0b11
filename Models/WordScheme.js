const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class WordScheme extends Model {}

WordScheme.init(
  {
    word_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    main_word: {
      type: DataTypes.STRING(50),
      allowNull: false,
    },
    alternative_word: {
      allowNull: false,
      type: DataTypes.STRING(50),
      primaryKey: true,
    },
    similarity_pct: {
      type: DataTypes.FLOAT,
    },
    language: {
      type: DataTypes.STRING(50),
    },
    dialect: {
      type: DataTypes.STRING(50),
    },
  },
  { sequelize, modelName: "bot_designer_word_schemes" }
);

WordScheme.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key] ? info[key] : this[key];
  });
};

module.exports = WordScheme;
