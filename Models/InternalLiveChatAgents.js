const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const Bot = require("./Bot");
const Editor = require("./Editor");

class InternalLiveChatAgents extends Model {}

InternalLiveChatAgents.init(
  {
    agent_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    editor_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
      references: {
        model: Editor,
        key: "editor_id",
      },
    },
    bot_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: Bo<PERSON>,
        key: "bot_id",
      },
    },
    status: {
      type: DataTypes.ENUM("active", "inactive"),
      defaultValue: "inactive",
    },
    current_active_chats: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    time_zone: {
      type: DataTypes.STRING,
      defaultValue: "Asia/Amman",
    },
  },
  {
    sequelize,
    modelName: "bot_designer_internal_livechat_agents",
  }
);

InternalLiveChatAgents.belongsTo(Editor, {
  foreignKey: "editor_id",
  as: "editor",
});

Editor.hasOne(InternalLiveChatAgents, {
  foreignKey: "editor_id",
  as: "agent",
});

module.exports = InternalLiveChatAgents;
