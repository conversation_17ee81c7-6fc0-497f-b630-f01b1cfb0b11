const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class WhatsApp extends Model {}

WhatsApp.init(
  {
    whatsapp_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    phone: DataTypes.STRING(20),
    waba: DataTypes.STRING(20),
    bsb: DataTypes.STRING(10),
    api_key: DataTypes.STRING,
    uri: DataTypes.STRING,
    hub_url: DataTypes.STRING,
    hub_token: DataTypes.STRING
  },
  { sequelize, modelName: "bot_designer_whatsapp" }
);


WhatsApp.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
      this[key] = info[key];
  });
};

module.exports = WhatsApp;
