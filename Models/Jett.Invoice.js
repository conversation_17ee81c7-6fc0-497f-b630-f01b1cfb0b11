const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const JettTicketsType = require("./Jett.TicketsType");
const JettBookingTicket = require("./Jett.BookingTicket");
const JettKHBMasterTicket = require("./Jett.KHBMasterTicket");
const JettCardRecharge = require("./Jett.CardRecharge");
const JettKHBSlaveTicket = require("./Jett.KHBSlaveTicket");
class JettInvoice extends Model {}

JettInvoice.init(
  {
    jett_invoice_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    invoice_id: {
      type: DataTypes.STRING,
    },
    conversation_id: {
      type: DataTypes.STRING,
    },
    channel: {
      type: DataTypes.STRING,
    },
    bot_id: {
      type: DataTypes.INTEGER,
    },
    entity_id: {
      type: DataTypes.INTEGER, 
      references :{
        model: JettTicketsType,
        key: 'jett_ticket_type_id'
      }
    },
    collected_data_url: {
      type: DataTypes.STRING,
    },
    total_price: {
      type: DataTypes.FLOAT,
    },
    status: {
      type: DataTypes.STRING,
    },
    payment: {
      type: DataTypes.STRING,
    },
  },
  { sequelize, modelName: "bot_designer_jett_invoices" }
);

JettInvoice.belongsTo(JettTicketsType, { foreignKey: 'entity_id', as : 'type' });


JettInvoice.hasMany(JettKHBMasterTicket, {
  foreignKey: "jett_invoice_id",
  as: "khbMasterTickets",
});

JettInvoice.hasMany(JettBookingTicket, {
  foreignKey: "jett_invoice_id",
  as: "bookingTickets",
});

JettInvoice.hasMany(JettCardRecharge, {
  foreignKey: "jett_invoice_id",
  as: "cardRecharges",
});


JettInvoice.prototype.getInvoiceDetails = async function () {
  const entity = await this.getType()
  console.log(entity)

  const typeModels = {
    Booking: JettBookingTicket,
    KHB: JettKHBMasterTicket,
    CardRecharge: JettCardRecharge
  };

  const TypeModel = typeModels[entity.type];
  if (!TypeModel) {
    throw new Error('Unknown invoice type');
  }
  
  let typeDetails;
  if (entity.type === 'KHB') {
    typeDetails = await TypeModel.findAll({
      where: { jett_invoice_id: this.jett_invoice_id },
      include: [{ model: JettKHBSlaveTicket, as: 'slaveTickets' }] // Include related data
    });
  } else {
    typeDetails = await TypeModel.findAll({ where: { jett_invoice_id: this.jett_invoice_id } });
  }

  return typeDetails;
};

JettInvoice.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

JettInvoice.prototype.toJsonData = function toJsonData() {
  return {
    jett_invoice_id: this.jett_invoice_id,
    invoice_id: this.invoice_id,
    conversation_id: this.conversation_id,
    channel: this.channel,
    bot_id: this.bot_id,
    entity_id: this.entity_id,
    collected_data_url: this.collected_data_url,
    total_price: this.total_price,
    status: this.status,
    payment: this.payment,
    type: this.type,
    createdAt: this.createdAt,
    updatedAt: this.updatedAt,
  };
}

module.exports = JettInvoice;
