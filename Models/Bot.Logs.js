const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Logs extends Model {}
const actionTypes = ["CREATE", "UPDATE", "DELETE"];
// const categories = ["SMALLTALK", "DIALOG", "FAQ", "facebook"];

Logs.init(
  {
    log_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    bot_id: {
      type: DataTypes.INTEGER(),
      allowNull: true,
    },
    user_id: {
      type: DataTypes.INTEGER(),
      allowNull: true,
    },
    category: {
      type: DataTypes.STRING(255),
    },
    log: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    action_type: {
      type: DataTypes.ENUM,
      values: actionTypes,
      defaultValue: "CREATE",
    },
    id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
  },
  { sequelize, modelName: "bot_designer_logs" }
);

Logs.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
};

const Bot = require("./Bot");
const User = require("./User");

Logs.belongsTo(Bot, { foreignKey: "bot_id" });
Logs.belongsTo(User, { foreignKey: "user_id" });

module.exports = Logs;
