const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class UserAddress extends Model {}

UserAddress.init(
  {
    address_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    country: {
      type: DataTypes.STRING(30),
    },
    city: {
      type: DataTypes.STRING(30),
    },
    province: {
      type: DataTypes.STRING(50),
    },
    postal_code: {
      type: DataTypes.STRING(10),
    },
    long: {
      type: DataTypes.STRING(50),
    },
    lat: {
      type: DataTypes.STRING(50),
    },
  },
  { sequelize, modelName: "bot_designer_user_addresses" }
);

UserAddress.prototype.updateInfo = function (info) {
  this.country = info.country ? info.country : this.country;
  this.long = info.long ? info.long : this.long;
  this.lat = info.lat ? info.lat : this.lat;
  this.postal_code = info.postal_code ? info.postal_code : this.postal_code;
  this.province = info.province ? info.province : this.province;
  this.city = info.city ? info.city : this.city;
};

module.exports = UserAddress;
