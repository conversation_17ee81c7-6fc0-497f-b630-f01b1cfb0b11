const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const InternalLiveChatQueue = require("./InternalLiveChatQueue");
const InternalLiveChatAgents = require("./InternalLiveChatAgents");

class InternalLiveChatSession extends Model {}

InternalLiveChatSession.init(
  {
    session_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    queue_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: InternalLiveChatQueue,
        key: "queue_id",
      },
    },
    agent_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: InternalLiveChatAgents,
        key: "agent_id",
      },
    },
    start_time: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    end_time: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM(
        "pending",
        "active",
        "closed",
        "closed due to agent inactivity",
        "closed due to customer inactivity",
        "closed due to inactivity",
        "closed due to agent rejection",
        "closed due to customer",
        "closed due to agent unavailability",
      ),
      defaultValue: "pending",
    },
    time_zone: {
      type: DataTypes.STRING,
    },
    activatedAt:{
      type: DataTypes.DATE,
      allowNull: true,
    }
  },
  {
    sequelize,
    modelName: "bot_designer_internal_livechat_sessions",
  }
);

InternalLiveChatSession.belongsTo(InternalLiveChatQueue, {
  foreignKey: "queue_id",
  as: "queue",
});

module.exports = InternalLiveChatSession;
