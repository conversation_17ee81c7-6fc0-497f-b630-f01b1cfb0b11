const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const FeedbackLogILO = require("../Models/FeedbackLogIlo");


class FeedbackILO extends Model {}

FeedbackILO.init(
  {
   feedback_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    phone_number: {
      type: DataTypes.STRING,
      allowNull: false
    },
    name: {
        type: DataTypes.STRING,
        allowNull: false
    }
  },
  { sequelize, modelName: "bot_designer_customer_ilo_feedback" }
);

FeedbackILO.hasMany(FeedbackLogILO, { foreignKey: "feedback_id" });


module.exports = FeedbackILO;
