const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Intent extends Model {}

Intent.init(
  {
    intent_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    intent_props: {
      type: DataTypes.STRING("MAX"),
      allowNull: false,
    },
    intent_type: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    intent_subType: {
      type: DataTypes.STRING,
    },
  },
  { sequelize, modelName: "bot_designer_intents" }
);

Intent.prototype.updateInfo = function updateInfo(info) {
  this.intent_props = info.intent_props ? info.intent_props : this.intent_props;
  this.intent_type = info.intent_type ? info.intent_type : this.intent_type;
  this.intent_subType = info.intent_subType
    ? info.intent_subType
    : this.intent_subType;
};

module.exports = Intent;
