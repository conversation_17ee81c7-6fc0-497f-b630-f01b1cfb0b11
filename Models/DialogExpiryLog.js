const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class DialogExpiryLog extends Model {}

DialogExpiryLog.init(
  {
    dialog_expiry_log_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    conversation_id: {
      type: DataTypes.STRING,
    },
    expired: {
      type: DataTypes.BOOLEAN,
    },
    channel: {
      type: DataTypes.STRING,
    },
    bot_id: {
      type: DataTypes.INTEGER,
    },
    dialog_id: {
      type: DataTypes.INTEGER,
    },
    expiry_min: {
      type: DataTypes.INTEGER,
    },
    user_interaction: {
      type: DataTypes.INTEGER,
    },
    dialog_url: {
      type: DataTypes.STRING,
    },
    action_config: {
      type: DataTypes.STRING,
    },
  },
  { sequelize, modelName: "bot_designer_dialog_expiry_logs" }
);

DialogExpiryLog.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

module.exports = DialogExpiryLog;
