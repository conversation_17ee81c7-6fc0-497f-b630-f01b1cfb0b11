const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Reservation extends Model {}

Reservation.init(
  {
    reservation_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    status: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    room_reservation: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    table_reservation: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
  },
  { sequelize, modelName: "bot_designer_reservations" }
);

Reservation.prototype.updateInfo = function (info) {
  this.status = info.status ? true : false;
  this.room_reservation = info.room_reservation ? true : false;
  this.table_reservation = info.table_reservation ? true : false;

};

module.exports = Reservation;
