const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const Invoice = require("./Invoice");
class Biller extends Model {}

Biller.init(
  {
    biller_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    country: {
      type: DataTypes.STRING(50),
    },
    city: { type: DataTypes.STRING(50) },
    company_name: {
      type: DataTypes.STRING(50),
    },
    email: { type: DataTypes.STRING(100) },
  },
  { sequelize, modelName: "bot_designer_biller" }
);

Biller.hasMany(Invoice, { foreignKey: "biller_id" });

Biller.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
};

module.exports = Biller;
