const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class LeadPlugin extends Model {}

LeadPlugin.init(
  {
    lead_plugin_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    check_email: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    is_optional: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    check_phone: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
  },
  { sequelize, modelName: "bot_designer_lead_plugins" }
);

LeadPlugin.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

module.exports = LeadPlugin;
