const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Employer extends Model { }

Employer.init(
    {
        employer_id: {
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
            type: DataTypes.INTEGER,
        },
        employment_status: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        phone_number: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        company_sector: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        company_location: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        insurance_number: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        employees_number: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        owner_gender: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        company_id: {
            type: DataTypes.STRING,
            allowNull: true,
        },
    },
    { sequelize, modelName: "bot_designer_customer_ilo_employer" }
);


module.exports = Employer;
