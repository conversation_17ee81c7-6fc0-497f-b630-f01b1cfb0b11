const sequelize = require("../db");
const { DataTypes, Model, Sequelize } = require("sequelize");

class WaTemplateLog extends Model {}

WaTemplateLog.init(
  {
    template_log_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    lang: {
      type: Sequelize.ENUM,
      values: ["en", "ar"],
    },
    recipient: {
      type: DataTypes.STRING,
    },
    template_name: DataTypes.STRING,
    template_type: {
      type: DataTypes.STRING,
    },
    bot_id: {
      type: DataTypes.INTEGER,
    },
  },
  { sequelize, modelName: "bot_designer_wa_template_logs" }
);

module.exports = WaTemplateLog;
