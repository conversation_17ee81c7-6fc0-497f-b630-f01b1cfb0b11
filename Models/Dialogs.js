const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const FeedbackILO = require("./FeedbackIlo");
const FeedbackQuestionsILO = require("../Models/FeedbackQuestionsIlo");

class Dialogs extends Model {}

Dialogs.init(
  {
    dialog_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    url: {
      type: DataTypes.STRING(50),
    },
    dialog_name: {
      type: DataTypes.STRING(50),
    },
    dialog_image: {
      type: DataTypes.STRING(150),
    },
    dialog_status: {
      type: DataTypes.ENUM,
      values: ["complete", "draft", "approved", "pending_review"],
      defaultValue: "draft",
    },
    topic_id: {
      allowNull: true,
      type: DataTypes.INTEGER,
    },

    fallBack: {
      type: DataTypes.BOOLEAN,
      defaultValue: 0,
    },

    welcome: {
      type: DataTypes.BOOLEAN,
      defaultValue: 0,
    },
  },
  { sequelize, modelName: "bot_designer_dialogs" }
);

// Dialogs.belongsTo(Topic, { foreignKey: "topic_id" });

Dialogs.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
};

Dialogs.hasMany(FeedbackILO, { foreignKey: "dialog_id" });
Dialogs.hasMany(FeedbackQuestionsILO, { foreignKey: "dialog_id" });

module.exports = Dialogs;
