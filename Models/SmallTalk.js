const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class SmallTalk extends Model {}

SmallTalk.init(
  {
    small_talk_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    question: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    answer: {
      type: DataTypes.STRING(1000),
    },
    bot_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    voice_path_male: {
      allowNull: true,
      type: DataTypes.STRING,
    },
    voice_path_female: {
      allowNull: true,
      type: DataTypes.STRING,
    },
  },
  { sequelize, modelName: "bot_designer_small_talks" }
);

SmallTalk.prototype.updateInfo = function updateInfo(info) {
  this.question = info.question ? info.question : this.question;
  this.answer = info.answer ? info.answer : this.answer;
  this.voice_path_male = info.voice_path_male ? info.voice_path_male : this.voice_path_male;
  this.voice_path_female = info.voice_path_female ? info.voice_path_female : this.voice_path_female;
};

module.exports = SmallTalk;
