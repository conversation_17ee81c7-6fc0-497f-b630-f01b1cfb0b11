const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class PopupPlugin extends Model {}

PopupPlugin.init(
  {
    popup_plugin_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    status_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    frequency: {
      type: DataTypes.INTEGER,
      defaultValue: 3,
    },
  },
  { sequelize, modelName: "bot_designer_popup_plugins" }
);

PopupPlugin.prototype.updateInfo = function (info) {
  this.status_active = info.status_active ? true : false;
  this.frequency = info.frequency ? info.frequency : this.frequency;

};

module.exports = PopupPlugin;
