const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const OfferItem = require("./Offer.Item");
const ItemOption = require("./Item.Option");
const CartLogItem = require("./Cart.Log.Item");
const ItemFeature = require("./Item.Feature");
const SearchItemLog = require("./Search.item.log");
const UserFavorite = require("./User.Favorite");
const Conversion = require("./Conversion");

class Item extends Model {}

Item.init(
  {
    item_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    item_title: DataTypes.STRING,

    item_description: DataTypes.STRING(500),

    item_price: DataTypes.FLOAT,
    item_qty: DataTypes.INTEGER,

    item_unit: DataTypes.STRING(50),

    currency: DataTypes.STRING(50),

    item_condition: DataTypes.STRING(50),

    item_height: { type: DataTypes.STRING },
    item_width: { type: DataTypes.STRING },
    item_weight: { type: DataTypes.STRING },

    item_url: DataTypes.STRING,
    item_hide: { type: DataTypes.BOOLEAN, defaultValue: false },
    price_hide: { type: DataTypes.BOOLEAN, defaultValue: false },
    show_url: { type: DataTypes.BOOLEAN, defaultValue: false },

    item_icons: {
      type: DataTypes.STRING(1100),
      defaultValue:
        "https://infotointell.fra1.digitaloceanspaces.com/assets/i2i-website/noimage.png",
    },

    item_instock: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    item_rank: {
      type: DataTypes.FLOAT,
      defaultValue: 1,
    },
    sku: {
      type: DataTypes.STRING,
    },
    lemmatized_item_title: {
      type: DataTypes.STRING(255),
    },
    voice_path_male: {
      allowNull: true,
      type: DataTypes.STRING,
    },
    voice_path_female: {
      allowNull: true,
      type: DataTypes.STRING,
    },
  },

  { sequelize, modelName: "bot_designer_items" },
 
);

Item.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key === "item_icons") {
      this.item_icons = info.item_icons.join(",");
    } else {
      this[key] = info[key];
    }
  });
};

Item.prototype.toJSON = function toJSON(info) {
  return {
    item_id: this.item_id,
    bot_id: this.bot_id,
    item_title: this.item_title,

    item_description: this.item_description,
    item_price: this.item_price,
    item_qty: this.item_qty,

    item_unit: this.item_unit,

    currency: this.currency,
    item_instock: this.item_instock,
    item_condition: this.item_condition,
    category_id: this.category_id,
    item_icons: this.item_icons.length ? this.item_icons.split(",") : [],
    is_cart: this.is_cart,
    item_options: this.bot_designer_item_options
      ? this.bot_designer_item_options
      : null,
    item_features: this.bot_designer_item_features
      ? this.bot_designer_item_features
      : null,
    offer: this.offer ? this.offer : undefined,
    item_url: this.item_url,
    item_hide: this.item_hide,
    show_url: this.show_url,
    price_hide: this.price_hide,
    item_height: this.item_height,
    item_width: this.item_width,
    item_weight: this.item_weight,
    sku: this.sku,
    voice_path_male: this.voice_path_male,
    voice_path_female: this.voice_path_female,
    // item_discount: this.item_discount,
    // discount_price: this.discount_price,
    // discount_percentage: this.discount_percentage,
    // discount_date_start: this.discount_date_start,
    // discount_date_end: this.discount_date_end,
  };
};

Item.hasMany(OfferItem, { foreignKey: "item_id" });
Item.hasMany(ItemOption, { foreignKey: "item_id" });
Item.hasMany(CartLogItem, { foreignKey: "item_id" });
Item.hasMany(ItemFeature, { foreignKey: "item_id" });
Item.hasMany(SearchItemLog, { foreignKey: "item_id" });
Item.hasMany(UserFavorite, { foreignKey: "item_id" });
Item.hasMany(Conversion, { foreignKey: "item_id" });

module.exports = Item;
