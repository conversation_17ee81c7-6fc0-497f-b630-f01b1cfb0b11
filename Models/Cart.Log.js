const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const CartLogItem = require("./Cart.Log.Item");

class CartLog extends Model {}

CartLog.init(
  {
    cart_log_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    phone: {
      type: DataTypes.STRING(50),
    },
    address: {
      type: DataTypes.STRING(50),
    },
    username: {
      type: DataTypes.STRING(100),
    },
    items: {
      type: DataTypes.STRING("MAX"),
    },
    price_total: DataTypes.FLOAT,
    sale_total: DataTypes.FLOAT,
    payment_type: DataTypes.STRING(100),
    continent: {
      type: DataTypes.STRING,
    },
    country: {
      type: DataTypes.STRING,
    },
    city: {
      type: DataTypes.STRING,
    },
    payment_status: {
      type: DataTypes.STRING, //NOTE cash , visa , dept
    },
    order_status: {
      type: DataTypes.STRING, //NOTE done , pending , cancelled
      defaultValue: "pending",
    },
  },
  { sequelize, modelName: "bot_designer_cart_logs" }
);

CartLog.prototype.updateInfo = function (info) {
  this.order_status = info.order_status ? info.order_status : this.order_status;
  this.payment_status = info.payment_status
    ? info.payment_status
    : this.payment_status;
};

CartLog.prototype.toJSON = function () {
  return {
    order_status: this.order_status,
    payment_status: this.payment_status,
    cart_log_id: this.cart_log_id,
    bot_id: this.bot_id,
    payment_type: this.payment_type,
    items: this.bot_designer_cart_log_items,
    price_total: this.price_total,
    sale_total: this.sale_total,
    username: this.username,
    address: this.address,
    phone: this.phone,
    bot_user_id: this.bot_user_id,
    continent: this.continent,
    country: this.country,
    city: this.city,
    createdAt: this.createdAt,
  };
};

CartLog.hasMany(CartLogItem, { foreignKey: "cart_log_id" });

module.exports = CartLog;
