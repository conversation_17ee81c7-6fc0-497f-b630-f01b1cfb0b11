const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class FeedbackLogsILO extends Model {}

FeedbackLogsILO.init(
  {
   feedback_log_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    feedback_log_entry: {
      type: DataTypes.STRING,
      allowNull: false
    }
  },
  { sequelize, modelName: "bot_designer_customer_ilo_feedback_log" }
);

module.exports = FeedbackLogsILO;
