const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class SearchSettings extends Model {}
SearchSettings.init(
  {
    search_setting_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },

    engine: {
      type: DataTypes.STRING,
      allowNull: true,
    },

    kb_type: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    bot_id: {
      type: DataTypes.INTEGER,
      unique: true,
    },
  },
  { sequelize, modelName: "bot_designer_search_settings" }
);

SearchSettings.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

module.exports = SearchSettings;
