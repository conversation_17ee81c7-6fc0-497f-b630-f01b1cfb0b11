const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const TRCube = require("./TRCubes");
// const TRCube = require("./TRCubes");
class TRUserInteraction extends Model {}

TRUserInteraction.init(
  {
    user_interaction_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    bot_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    channel: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    conversation_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    message: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    country: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    ip_address: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    city: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    continent: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    year: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    month: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    day: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    hour: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    is_voice: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    message_uuid: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    session_uuid: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    button_payload: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    user_contact_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    type: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  { sequelize, modelName: "bot_designer_tr_user_interactions" }
);

TRUserInteraction.hasOne(TRCube, { foreignKey: "user_interaction_id" });

module.exports = TRUserInteraction;
