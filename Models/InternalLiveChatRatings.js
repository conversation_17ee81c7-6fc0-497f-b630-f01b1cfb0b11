const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const LiveChatSession = require("./InternalLiveChatSession");
const LiveChatAgent = require("./InternalLiveChatAgents");


class InternalLiveChatRatings extends Model {}

InternalLiveChatRatings.init(
  {
    rating_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    session_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: LiveChatSession,
        key: "session_id",
      },
    },
    agent_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: LiveChatAgent,
        key: "agent_id",
      },
    },
    rating: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1,
        max: 5,
      },
    },
    rating_msg: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "bot_designer_internal_livechat_ratings",
  }
);

// Associations
InternalLiveChatRatings.belongsTo(LiveChatSession, {
  foreignKey: "session_id",
  as: "session",
});

InternalLiveChatRatings.belongsTo(LiveChatAgent, {
  foreignKey: "agent_id",
  as: "agent",
});

module.exports = InternalLiveChatRatings;
