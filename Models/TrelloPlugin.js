const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class TrelloPlugin extends Model {}
TrelloPlugin.init(
  {
    trello_plugin_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    status_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    key: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    token: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  { sequelize, modelName: "bot_designer_trello_plugins" }
);

TrelloPlugin.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
};

module.exports = TrelloPlugin;
