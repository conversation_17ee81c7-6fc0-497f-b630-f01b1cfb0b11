const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class OfferItem extends Model {}

OfferItem.init(
  {
    item_offer_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    }

  },

  { sequelize, modelName: "bot_designer_offer_item" },
 
);

OfferItem.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (<PERSON><PERSON><PERSON>(this[key])) {
      if (key !== "bot_id") {
        this[key] = info[key];
      }
    }
  });
};

module.exports = OfferItem;
