const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Bot_Custom extends Model {}

Bot_Custom.init(
  {
    table_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    table_name: {
      type: DataTypes.STRING,
      unique:true
    },
    form_details:{
      type: DataTypes.STRING("MAX")
    },
    pk_name:{
      type: DataTypes.STRING(100)
    }
  },
  { sequelize, modelName: "bot_designer_bot_custom" }
);

Bot_Custom.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
  
}


module.exports = Bot_Custom;
