const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const CartLog = require("./Cart.Log");
const CartLogItem = require("./Cart.Log.Item");
const ReservationOrder = require("./Reservation.Order");
const BotFeedback = require("./Feedback");

class BotUser extends Model {}

BotUser.init(
  {
    bot_user_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    continent: {
      type: DataTypes.STRING,
    },
    country: {
      type: DataTypes.STRING,
    },
    city: {
      type: DataTypes.STRING,
    },
  },
  { sequelize, modelName: "bot_designer_bot_users" }
);

BotUser.hasMany(CartLog, { foreignKey: "bot_user_id" });
BotUser.hasMany(CartLogItem, { foreignKey: "bot_user_id" });
BotUser.hasMany(ReservationOrder, { foreignKey: "bot_user_id" });
BotUser.hasMany(BotFeedback, { foreignKey: "bot_user_id" });

module.exports = BotUser;
