const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const ItemFeature = require("./Item.Feature");

class Feature extends Model {}

Feature.init(
  {
    feature_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    feature_name: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    feature_type: DataTypes.STRING, //Date, String, Number
    feature_unit: DataTypes.STRING,
    feature_description: DataTypes.STRING,
  },
  { sequelize, modelName: "bot_designer_features" }
);

Feature.prototype.updateInfo = function updateInfo(info) {
  this.feature_name = info.feature_name ? info.feature_name : this.feature_name;
  this.feature_type = info.feature_type ? info.feature_type : this.feature_type;
  this.feature_unit = info.feature_unit ? info.feature_unit : this.feature_unit;
  this.feature_description = info.feature_description ? info.feature_description : this.feature_description;

};

Feature.hasMany(ItemFeature, { foreignKey: "feature_id" });

module.exports = Feature;
