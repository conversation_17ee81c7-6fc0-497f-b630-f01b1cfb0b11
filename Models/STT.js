const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class STT extends Model {}

STT.init(
  {
    stt_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    user_input: {
      type: DataTypes.STRING(200),
    },
    lang: {
      type: DataTypes.STRING(50),
    },
    ip_address: {
      type: DataTypes.STRING(50),
    },
    country: {
      type: DataTypes.STRING(80),
    },
    city: {
      type: DataTypes.STRING(80),
    },
    long: {
      type: DataTypes.STRING(20),
    },
    lat: {
      type: DataTypes.STRING(20),
    },
  },
  { sequelize, modelName: "bot_designer_stt_logs" }
);

module.exports = STT;
