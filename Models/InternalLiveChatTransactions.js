const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const InternalLiveChatSession = require("./InternalLiveChatSession");

class InternalLiveChatTransactions extends Model {}

InternalLiveChatTransactions.init(
  {
    transaction_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    session_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      refrerences: {
        model: InternalLiveChatSession,
        key: "session_id",
      },
    },
    sender: {
      type: DataTypes.ENUM("customer", "agent"),
      allowNull: false,
    },
    message: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    sentAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: "bot_designer_internal_livechat_session_transactions",
  }
);

module.exports = InternalLiveChatTransactions;
