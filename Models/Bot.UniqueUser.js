const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class BotUniqueUser extends Model {}

BotUniqueUser.init(
  {
    bot_unique_user_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    }
  },
  { sequelize, modelName: "bot_designer_bot_unique_users" }
);

BotUniqueUser.prototype.toJSON = function () {
    return {
      bot_id: this.bot_id,
      createdAt: this.createdAt,
      bot_unique_user_id: this.bot_unique_user_id
    };
};


module.exports = BotUniqueUser;
