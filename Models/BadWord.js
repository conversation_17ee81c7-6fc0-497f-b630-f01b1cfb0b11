const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class BadWord extends Model {}

BadWord.init(
  {
    bad_word_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    bad_word: {
      allowNull: false,
      type: DataTypes.STRING(255),
    },
    bot_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
  },
  { sequelize, modelName: "bot_designer_bad_words" }
);
BadWord.prototype.updateInfo = function updateInfo(info) {
  this.bad_word = info.bad_word ? info.bad_word : this.bad_word;
};

module.exports = BadWord;
