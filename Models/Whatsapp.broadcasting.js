const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const WhatsappBroadCastLog = require("./Whatsapp.broadcast.logs");

class WhatsappBroadCast extends Model {}

WhatsappBroadCast.init(
  {
    broadcast_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    broadcast_name: {
      type: DataTypes.STRING,
    },
    message: {
      allowNull: false,
      type: DataTypes.STRING("MAX"),
    },
    image_url: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    image_id: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    sent_count: {
      allowNull: false,
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    template: {
      type: DataTypes.STRING(2000),
    },
    path: {
      allowNull: true,
      type: DataTypes.STRING,
    },
  },
  { sequelize, modelName: "bot_designer_whatsapp_broadcasts" }
);

WhatsappBroadCast.prototype.updateInfo = function (info) {
  this.broadcast_name = info.broadcast_name
    ? info.broadcast_name
    : this.broadcast_name;
  this.message = info.message ? info.message : this.message;
  this.image_url = info.image_url ? info.image_url : this.image_url;
  this.image_id = info.image_id ? info.image_id : this.image_id;
  this.sent_count = info.sent_count ? info.sent_count : this.sent_count;
  this.template = info.template ? info.template : this.template;
  this.path = info.path ? info.path : this.path;
};

WhatsappBroadCast.prototype.toJSON = function toJSON() {
  return {
    broadcast_id: this.broadcast_id,
    broadcast_name: this.broadcast_name,
    message: this.message,
    image_id: this.image_id,
    image_url: this.image_url,
    createdAt: this.createdAt,
    sent_count: this.sent_count,
    template: this.template,
    updatedAt: this.updatedAt,
    path: this.path,
  };
};

WhatsappBroadCast.hasMany(WhatsappBroadCastLog, { foreignKey: "broadcast_id" });

module.exports = WhatsappBroadCast;
