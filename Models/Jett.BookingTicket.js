const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const JettInvoice = require("./Jett.Invoice");
class JettBookingTicket extends Model {}

JettBookingTicket.init(
  {
    jett_booking_tickets_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    jett_invoice_id: {
      type: DataTypes.INTEGER,
    },
    name: {
      type: DataTypes.STRING,
    },
    phone: {
      type: DataTypes.STRING,
    },
    email: {
      type: DataTypes.STRING,
    },
    national_id: {
      type: DataTypes.STRING,
    },
    track: {
      type: DataTypes.STRING,
    },
    track_id: {
      type: DataTypes.INTEGER,
    },
    pickup_point: {
      type: DataTypes.STRING,
    },
    pickup_point_id: {
      type: DataTypes.INTEGER,
    },
    reservation_id: {
      type: DataTypes.STRING,
    },
    ticket_number: {
      type: DataTypes.STRING,
    },
    ticket_price: {
      type: DataTypes.FLOAT,
    },
    ticket_price_name: {
      type: DataTypes.STRING,
    },
    price_type_name: {
      type: DataTypes.STRING,
    },
    trip_date: {
      type: DataTypes.STRING,
    },
    trip_time: {
      type: DataTypes.STRING,
    },
    seat_no: {
      type: DataTypes.INTEGER,
    },
    is_return_trip: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    }
  },
  { sequelize, modelName: "bot_designer_jett_booking_tickets" }
);

// JettBookingTicket.belongsTo(JettInvoice, {
//   foreignKey: "jett_invoice_id",
//   as: "invoice",
// });

JettBookingTicket.prototype.updateInfo = function updateInfo(info) {
  Object.keys(info).map((key) => {
    if (key !== "bot_id") {
      this[key] = info[key];
    }
  });
};

module.exports = JettBookingTicket;
