const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Facebook extends Model {}

Facebook.init(
  {
    facebook_channel_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    pageId: {
      type: DataTypes.STRING,
      unique: true,
      allowNull: false,
    },
    page_token: {
      type: DataTypes.STRING,
      unique: true,
      allowNull: false,
    },
    channel: {
      type: DataTypes.STRING(50),
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
  },
  { sequelize, modelName: "bot_designer_facebook_channel" }
);

Facebook.prototype.updateInfo = function updateInfo(info) {
  this.page_token = info.page_token ? info.page_token : this.page_token;
  this.pageId = info.pageId ? info.pageId : this.pageId;
  this.channel = info.channel ? info.channel : this.channel;
  this.name = info.name ? info.name : this.name;
};

module.exports = Facebook;
