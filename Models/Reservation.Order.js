const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class ReservationOrder extends Model {}

ReservationOrder.init(
  {
    order_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    guests: DataTypes.INTEGER,
    name: {
      type: DataTypes.STRING,
    },
    from: DataTypes.DATE,
    to: DataTypes.DATE,
    order_status: {
      type: DataTypes.STRING,
      defaultValue: "pending",
    },
  },
  { sequelize, modelName: "bot_designer_reservation_orders" }
);
const statusArray = ["pending", "proceeding", "cancelled", "done"];
ReservationOrder.prototype.updateInfo = function (info) {
  this.order_status =
    info.order_status && statusArray.indexOf(info.order_status) !== -1
      ? info.order_status
      : this.order_status;
};

module.exports = ReservationOrder;
