const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class FallbackPlugin extends Model {}

FallbackPlugin.init(
  {
    fallback_plugin_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    status_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
  },
  { sequelize, modelName: "bot_designer_fallback_plugins" }
);

FallbackPlugin.prototype.updateInfo = function (info) {
  this.status_active = info.status_active ? true : false;
};

module.exports = FallbackPlugin;
