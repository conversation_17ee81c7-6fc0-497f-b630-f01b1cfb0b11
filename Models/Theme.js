const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Theme extends Model {}

Theme.init(
  {
    theme_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    headerColor: {
      type: DataTypes.STRING,
      defaultValue: "#14BF98",
    },
    footerColor: {
      type: DataTypes.STRING,
      defaultValue: "#14BF98",
    },
    inputBackgroundColor: {
      type: DataTypes.STRING,
      defaultValue: "#16d2a7",
    },
    iconsColor: {
      type: DataTypes.STRING,
      defaultValue: "white",
    },
    placeHoldercolor: {
      type: DataTypes.STRING,
      defaultValue: "black",
    },
    inputFontColor: {
      type: DataTypes.STRING,
      defaultValue: "black",
    },
    headerLogo: {
      type: DataTypes.STRING,
      defaultValue:
        "https://infotointell.fra1.digitaloceanspaces.com/assets/bot-designer/thumbnail_Client-Logo-Placeholder.png",
    },
    userBubbleBackground: {
      type: DataTypes.STRING,
      defaultValue: "#DAF3F4",
    },
    botBubbleBackground: {
      type: DataTypes.STRING,
      defaultValue: "#efefef",
    },
    botAvatar: {
      type: DataTypes.STRING,
      defaultValue:
        "https://infotointell.fra1.digitaloceanspaces.com/assets/bot-designer/bot-icon.png",
    },
    userAvatar: {
      type: DataTypes.STRING,
      defaultValue:
        "https://infotointell.fra1.digitaloceanspaces.com/assets/bot-designer/user-icon.png",
    },
    userBubbleNub: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    botBubbleNub: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    branding: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    userBorderRadius: {
      type: DataTypes.STRING,
      defaultValue: "0px",
    },
    botBorderRadius: {
      type: DataTypes.STRING,
      defaultValue: "0px",
    },
    headerFooterBorderRadius: {
      type: DataTypes.STRING,
      defaultValue: "0px",
    },
    userFontColor: {
      type: DataTypes.STRING,
      defaultValue: "black",
    },
    botFontColor: {
      type: DataTypes.STRING,
      defaultValue: "black",
    },
    placeholder: {
      type: DataTypes.STRING,
      defaultValue: "type your message",
    },
    tooltip: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    botIcon: {
      type: DataTypes.STRING,
      defaultValue:
        "https://infotointell.fra1.digitaloceanspaces.com/assets/bot-designer/bubble.png",
    },
    tooltipText: {
      type: DataTypes.STRING,
      defaultValue: "",
    },
    cardFontColor: {
      type: DataTypes.STRING,
      defaultValue: "black",
    },
    cardThemeColor: {
      type: DataTypes.STRING,
      defaultValue: "#14BF98",
    },
    cardButtonFontColor: {
      type: DataTypes.STRING,
      defaultValue: "white",
    },
    suggestedActionsBorderColor: {
      type: DataTypes.STRING,
      defaultValue: "white",
    },
    suggestedActionsBackground: {
      type: DataTypes.STRING,
      defaultValue: "#14BF98",
    },
    suggestedActionsFontColor: {
      type: DataTypes.STRING,
      defaultValue: "white",
    },
    chatbotHeight: {
      type: DataTypes.STRING,
      defaultValue: "550px",
    },
    chatbotWidth: {
      type: DataTypes.STRING,
      defaultValue: "400px",
    },
    suggestedActionBorderRadius: {
      type: DataTypes.STRING,
      defaultValue: "0px",
    },
    cardBorderRadius: {
      type: DataTypes.STRING,
      defaultValue: "0px",
    },
    cardBorderRadius: {
      type: DataTypes.STRING,
      defaultValue: "0px",
    },
    tooltipBorderRadius: {
      type: DataTypes.STRING,
      defaultValue: "0px",
    },
    tooltipFontSize: {
      type: DataTypes.STRING,
      defaultValue: "16px",
    },
    tooltipFontColor: {
      type: DataTypes.STRING,
      defaultValue: "#000000",
    },
    attachmentIcon: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    voiceIcon: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    cardBorder: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    BotIconAnimation: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    BotIconAnimationType: {
      type: DataTypes.STRING,
      defaultValue: "shake",
    },
    transactionBoxBackground: {
      type: DataTypes.STRING,
    },
    messagesAnimation: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    messagesAnimationType: {
      type: DataTypes.STRING(50),
      defaultValue: "popup",
    },
    scrollBarWidth: {
      type: DataTypes.STRING(50),
    },
    scrollBarColor: { type: DataTypes.STRING(50) },
    menuButtonBackgroundColor: { type: DataTypes.STRING(50) },
    menuButtonFontColor: { type: DataTypes.STRING(50) },

    menuFontColor: { type: DataTypes.STRING(50) },
    typingIndicator: { type: DataTypes.STRING(255) },
    botAvatarTyping: { type: DataTypes.STRING(255) },
    menuBackgroundColor: { type: DataTypes.STRING(50) },
    cardBackgroundColor: { type: DataTypes.STRING(50) },
    autoOpenChatbotWindow: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
  },
  { sequelize, modelName: "bot_designer_themes" }
);

Theme.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  });
};

module.exports = Theme;
