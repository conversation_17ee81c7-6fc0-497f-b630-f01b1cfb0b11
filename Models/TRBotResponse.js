const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
// const TRCube = require("./TRCubes");
const TRMapping = require("./TRMapping");
const TRBotResponseSource = require("./TRBotResponseSources");
const TRUserInteraction = require("./TRUserInteractions");
class TRBotResponse extends Model {}

TRBotResponse.init(
  {
    bot_response_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    tr_cube_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    response_type: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    response: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  { sequelize, modelName: "bot_designer_tr_bot_responses" }
);

// TRBotResponse.belongsTo(TRCube, { foreignKey: "tr_cube_id" });
TRBotResponse.belongsToMany(TRBotResponseSource, {through: TRMapping, foreignKey: "bot_response_id",  otherKey: "bot_response_source_id", as: "sources" });
// TRBotResponse.belongsTo(TRUserInteraction, {through: TRCube, foreignKey: "tr_cube_id",  otherKey: "user_interaction_id", as: "responses" });
module.exports = TRBotResponse;
