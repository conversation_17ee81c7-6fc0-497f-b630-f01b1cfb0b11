const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");


class WhatsappBroadCastLog extends Model {}

WhatsappBroadCastLog.init(
  {
    broadcast_log_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    phone_number: {
      type: DataTypes.STRING,
    },
    status: {
      allowNull: false,
      type: DataTypes.BOOLEAN
    }
  },
  { sequelize, modelName: "bot_designer_whatsapp_broadcast_logs" }
);


WhatsappBroadCastLog.prototype.toJSON = function toJSON() {
  return {
    broadcast_id: this.broadcast_id,
    phone_number: this.phone_number,
    status: this.status, 
    createdAt: this.createdAt
  };
};

module.exports = WhatsappBroadCastLog;
