const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const TicketingIntegration = require("./TicketingIntegration");
const TicketingAgentDepartment = require("./TicketingAgentDepartment");
const TicketingSupportAgent = require("./TicketingSupportAgent");

class TicketingDepartment extends Model {}

TicketingDepartment.init(
  {
    department_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    ticketing_integration_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    department: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    department_ar: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    is_native: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "bot_designer_ticketing_departments",
  }
);

TicketingDepartment.belongsTo(TicketingIntegration, {
  foreignKey: "ticketing_integration_id",
  as: "integration",
});

TicketingIntegration.hasMany(TicketingDepartment, {
  foreignKey: "ticketing_integration_id",
  as: "integ_departments",
});


module.exports = TicketingDepartment;
