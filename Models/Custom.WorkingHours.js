const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Custom_WorkingHours extends Model {}

Custom_WorkingHours.init(
  {
    date_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    day: {
      type: DataTypes.STRING,
    },
    from_time: {
      allowNull: false,
      type: DataTypes.FLOAT,
    },
    to_time: {
      allowNull: false,
      type: DataTypes.FLOAT,
    },
  },
  { sequelize, modelName: "bot_designer_customs_ClassTime"}
);

module.exports = Custom_WorkingHours;
