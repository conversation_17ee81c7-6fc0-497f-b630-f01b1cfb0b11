const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class DashboardCalculations extends Model {}

DashboardCalculations.init(
  {
    dashboard_calculation_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    bot_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    date_stamp: {
      type: DataTypes.STRING("max"),
      allowNull: true,
    },
    calculation: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  { sequelize, modelName: "bot_designer_dashboard_calculation" }
);

module.exports = DashboardCalculations;
