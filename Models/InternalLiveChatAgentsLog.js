const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const Bot = require("./Bot");
const Editor = require("./Editor");
const InternalLiveChatAgents = require("./InternalLiveChatAgents");

class InternalLiveChatAgentsLog extends Model {}

InternalLiveChatAgentsLog.init(
  {
    agent_log_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    agent_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: InternalLiveChatAgents,
        key: "agent_id",
      },
    },
    status: {
      type: DataTypes.ENUM("active", "inactive"),
      defaultValue: "inactive",
    },
    time_zone: {
      type: DataTypes.STRING,
    },
  },
  {
    sequelize,
    modelName: "bot_designer_internal_livechat_agents_logs",
  }
);

InternalLiveChatAgentsLog.belongsTo(InternalLiveChatAgents, {
  foreignKey: "agent_id",
  as: "agent",
});

module.exports = InternalLiveChatAgentsLog;
