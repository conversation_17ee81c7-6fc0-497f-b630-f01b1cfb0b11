const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
class Invoice extends Model {}

Invoice.init(
  {
    invoice_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    amount: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    currency: {
      type: DataTypes.STRING(50),
    },
  },
  { sequelize, modelName: "bot_designer_invoices" }
);

module.exports = Invoice;
