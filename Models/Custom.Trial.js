const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");

class Customs_Trial extends Model {}


Customs_Trial.init(
  {
    trial_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    trial_number:{
      type: DataTypes.INTEGER,
      defaultValue:0
    },
    trial_date:{
      type: DataTypes.DATE,
    },
    note:{
      type: DataTypes.STRING,
    },
  },
  { sequelize, modelName: "bot_designer_customs_Trial" }
);

Customs_Trial.prototype.updateInfo = function (info) {
  Object.keys(info).map((key) => {
    this[key] = info[key];
  }); 
}

module.exports = Customs_Trial;
