const sequelize = require("../db");
const { DataTypes, Model } = require("sequelize");
const InternalLiveChatIntegration = require("./InternalLiveChatIntegration");

class InternalLiveChatQueue extends Model {}

InternalLiveChatQueue.init(
  {
    queue_id: {
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
      type: DataTypes.INTEGER,
    },
    bot_id: {
      type: DataTypes.INTEGER,
    },
    channel: {
      type: DataTypes.STRING,
    },
    conversation_id: {
      type: DataTypes.STRING,
    },
    status: {
      type: DataTypes.ENUM("waiting", "assigned", "dropped"),
      defaultValue: "waiting",
    },
    livechat_integration_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: InternalLiveChatIntegration,
        key: "livechat_integration_id",
      }
    },
    customer_name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    customer_email: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    customer_phone: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "bot_designer_internal_livechat_queues",
  }
);

module.exports = InternalLiveChatQueue;
