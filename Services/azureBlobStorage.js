// const { BlobStorage } = require("botbuilder-azure");
// const {
//   AZURE_BLOB_STORAGE_CONNECTION_STRING,
//   AZURE_BLOB_STORAGE_CONTAINER_NAME,
// } = require("../constants");

// const storage = new BlobStorage({
//   containerName: AZURE_BLOB_STORAGE_CONTAINER_NAME,
//   storageAccountOrConnectionString: AZURE_BLOB_STORAGE_CONNECTION_STRING,
// });

// module.exports = {
//   setPool: function (data) {
//     return storage.write(data);
//   },
// };
