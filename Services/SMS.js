const fetch = require("node-fetch");

const host = "https://w16rly.api.infobip.com/sms/2/text/single";
const Authorization =
  "App 594b2796c720dc2f0868071a29dbfb38-e0c04517-0554-4f32-8262-a09d9fb187b0";

var twilio = require("twilio");
const sid = "**********************************";
const keyType = "Main";
const secret = "3obzR8hYPSSogwcjcM6QKDsiU2u5G0vr";
const accountSid = "**********************************";
const authToken = "24f8540732543f4c282252b59e62b3cd";
var client = new twilio(accountSid, authToken);

const createBody = (to, text, pre) => {
  return {
    from: "SearChat",
    to,
    text: pre + " " + text,
  };
};

const callSMS = (body) =>
  fetch(host, {
    method: "POST",
    headers: {
      Authorization,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(body),
  })
    .then((res) => res.json())
    .then((d) => console.log(d))
    .catch((e) => console.log(e));

const callTwillioSMS = (
  to,
  code //{
) =>
  //   const bindingOpts = {
  //     identity: authToken, // We recommend using a GUID or other anonymized identifier for Identity.
  //     bindingType: "sms",
  //     address: "(+962)*********",
  //   };

  //   return client.notify
  //     .services(sid)
  //     .bindings.create(bindingOpts)
  //     .then((binding) => console.log(binding.sid))
  //     .catch((error) => console.log("error", error))
  //     .done();
  // };
  client.messages
    .create({
      to: "+" + to,
      messagingServiceSid: "MG70532854b621dafa3a2025aefe27f29d",
      body: `Your Searchat code is ${code}`,
      from: "+17868285260",
    })
    .then((message) => console.log("message.sid", message.sid))
    .catch((e) => console.log("error ", e))
    .done();

const sendSMS = async (to, body) => {
  //await callTwillioSMS(to, body);
  return callSMS(createBody(to, body, "Your verification code is"));
};

module.exports = { sendSMS };
